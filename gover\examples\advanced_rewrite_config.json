{"server": {"http_port": 80, "https_port": 443}, "sites": [{"name": "advanced_rewrite_demo", "domains": ["api.example.com", "www.example.com"], "upstreams": [{"name": "api_v1", "address": "127.0.0.1", "port": 8001, "protocol": "http", "load_balance_group": "api_v1_group"}, {"name": "api_v2", "address": "127.0.0.1", "port": 8002, "protocol": "http", "load_balance_group": "api_v2_group"}, {"name": "legacy_api", "address": "127.0.0.1", "port": 8003, "protocol": "http", "load_balance_group": "legacy_group"}, {"name": "file_server", "address": "127.0.0.1", "port": 8004, "protocol": "http", "load_balance_group": "file_group"}], "routes": [{"comment": "简单重写示例 - 支持正则捕获组", "pattern": "^/old-api/(.*)$", "upstream": "api_v2_group", "rewrite": "/new-api/$1", "cache": false}, {"comment": "基于请求头的条件重写", "pattern": "^/api/(.*)$", "upstream": "api_v2_group", "rewrite_advanced": {"target": "/v2/api/$1", "conditions": [{"type": "header", "key": "X-API-Version", "value": "v1", "operator": "equals", "target": "/v1/api/$1"}, {"type": "header", "key": "X-Legacy-Client", "value": "true", "operator": "equals", "target": "/legacy/api/$1"}]}, "cache": false}, {"comment": "基于查询参数的重写", "pattern": "^/search$", "upstream": "api_v2_group", "rewrite_advanced": {"target": "/search/v2", "conditions": [{"type": "query", "key": "version", "value": "v1", "operator": "equals", "target": "/search/v1"}, {"type": "query", "key": "format", "value": "xml", "operator": "equals", "target": "/search/xml"}]}, "cache": true}, {"comment": "用户资源路径重写 - 复杂正则捕获", "pattern": "^/user/([0-9]+)/profile/(.*)$", "upstream": "api_v2_group", "rewrite_advanced": {"target": "/users/$1/profile-data/$2", "variables": {"service": "user-management", "version": "v3"}}, "cache": true}, {"comment": "文件服务路径拼接", "pattern": "^/files/(.*)$", "upstream": "file_group", "rewrite_advanced": {"target": "/storage/${env}/files", "append_path": true, "variables": {"env": "production", "bucket": "main-storage"}}, "cache": true}, {"comment": "基于User-Agent的移动端重写", "pattern": "^/app/(.*)$", "upstream": "api_v2_group", "rewrite_advanced": {"target": "/web/app/$1", "conditions": [{"type": "header", "key": "User-Agent", "value": "Mobile|Android|iPhone", "operator": "matches", "target": "/mobile/app/$1"}]}, "cache": true}, {"comment": "基于请求方法的重写", "pattern": "^/data/(.*)$", "upstream": "api_v2_group", "rewrite_advanced": {"target": "/api/data/$1", "conditions": [{"type": "method", "value": "POST", "operator": "equals", "target": "/api/data/create/$1"}, {"type": "method", "value": "PUT", "operator": "equals", "target": "/api/data/update/$1"}, {"type": "method", "value": "DELETE", "operator": "equals", "target": "/api/data/delete/$1"}]}, "cache": false}, {"comment": "时间戳路径重写", "pattern": "^/logs/(.*)$", "upstream": "file_group", "rewrite_advanced": {"target": "/logs/$year/$month/$day/$1", "variables": {"archive": "daily"}}, "cache": true}, {"comment": "条件路径拼接", "pattern": "^/assets/(.*)$", "upstream": "file_group", "rewrite_advanced": {"target": "/static/assets", "append_path": true, "conditions": [{"type": "header", "key": "X-CDN-Version", "value": "v2", "operator": "equals", "target": "/cdn/v2/assets"}]}, "cache": true}, {"comment": "默认路由 - 兜底处理", "pattern": "^/", "upstream": "api_v2_group", "rewrite": "/", "cache": true}]}]}
{"message":"unused imports: `debug`, `error`, `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\server\\mod.rs","byte_start":111,"byte_end":116,"line_start":5,"line_end":5,"column_start":21,"column_end":26,"is_primary":true,"text":[{"text":"use tracing::{info, error, debug, warn};","highlight_start":21,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\server\\mod.rs","byte_start":118,"byte_end":123,"line_start":5,"line_end":5,"column_start":28,"column_end":33,"is_primary":true,"text":[{"text":"use tracing::{info, error, debug, warn};","highlight_start":28,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\server\\mod.rs","byte_start":125,"byte_end":129,"line_start":5,"line_end":5,"column_start":35,"column_end":39,"is_primary":true,"text":[{"text":"use tracing::{info, error, debug, warn};","highlight_start":35,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src\\server\\mod.rs","byte_start":109,"byte_end":129,"line_start":5,"line_end":5,"column_start":19,"column_end":39,"is_primary":true,"text":[{"text":"use tracing::{info, error, debug, warn};","highlight_start":19,"highlight_end":39}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `debug`, `error`, `warn`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\server\\mod.rs:5:21\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{info, error, debug, warn};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"message":"unused imports: `tokio::net::TcpStream`, `tokio_rustls::TlsAcceptor`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\server\\http_server.rs","byte_start":442,"byte_end":467,"line_start":17,"line_end":17,"column_start":5,"column_end":30,"is_primary":true,"text":[{"text":"    tokio_rustls::TlsAcceptor,","highlight_start":5,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\server\\http_server.rs","byte_start":473,"byte_end":494,"line_start":18,"line_end":18,"column_start":5,"column_end":26,"is_primary":true,"text":[{"text":"    tokio::net::TcpStream,","highlight_start":5,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\server\\http_server.rs","byte_start":408,"byte_end":498,"line_start":15,"line_end":19,"column_start":1,"column_end":3,"is_primary":true,"text":[{"text":"#[cfg(feature = \"ssl\")]","highlight_start":1,"highlight_end":24},{"text":"use {","highlight_start":1,"highlight_end":6},{"text":"    tokio_rustls::TlsAcceptor,","highlight_start":1,"highlight_end":31},{"text":"    tokio::net::TcpStream,","highlight_start":1,"highlight_end":27},{"text":"};","highlight_start":1,"highlight_end":3}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `tokio::net::TcpStream`, `tokio_rustls::TlsAcceptor`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\server\\http_server.rs:17:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    tokio_rustls::TlsAcceptor,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    tokio::net::TcpStream,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"unused import: `crate::config::UpstreamConfig`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\processor\\proxy.rs","byte_start":447,"byte_end":476,"line_start":19,"line_end":19,"column_start":5,"column_end":34,"is_primary":true,"text":[{"text":"use crate::config::UpstreamConfig;","highlight_start":5,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\processor\\proxy.rs","byte_start":443,"byte_end":477,"line_start":19,"line_end":19,"column_start":1,"column_end":35,"is_primary":true,"text":[{"text":"use crate::config::UpstreamConfig;","highlight_start":1,"highlight_end":35}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `crate::config::UpstreamConfig`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\processor\\proxy.rs:19:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::config::UpstreamConfig;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"unused import: `bytes::Bytes`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\processor\\cache.rs","byte_start":34,"byte_end":46,"line_start":2,"line_end":2,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"use bytes::Bytes;","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\processor\\cache.rs","byte_start":30,"byte_end":47,"line_start":2,"line_end":2,"column_start":1,"column_end":18,"is_primary":true,"text":[{"text":"use bytes::Bytes;","highlight_start":1,"highlight_end":18}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `bytes::Bytes`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\processor\\cache.rs:2:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse bytes::Bytes;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"unused import: `std::collections::HashMap`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\processor\\cache.rs","byte_start":52,"byte_end":77,"line_start":3,"line_end":3,"column_start":5,"column_end":30,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":5,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\processor\\cache.rs","byte_start":48,"byte_end":78,"line_start":3,"line_end":3,"column_start":1,"column_end":31,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `std::collections::HashMap`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\processor\\cache.rs:3:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::collections::HashMap;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"unused import: `UNIX_EPOCH`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\processor\\cache.rs","byte_start":137,"byte_end":147,"line_start":5,"line_end":5,"column_start":39,"column_end":49,"is_primary":true,"text":[{"text":"use std::time::{Duration, SystemTime, UNIX_EPOCH};","highlight_start":39,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\processor\\cache.rs","byte_start":135,"byte_end":147,"line_start":5,"line_end":5,"column_start":37,"column_end":49,"is_primary":true,"text":[{"text":"use std::time::{Duration, SystemTime, UNIX_EPOCH};","highlight_start":37,"highlight_end":49}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `UNIX_EPOCH`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\processor\\cache.rs:5:39\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::time::{Duration, SystemTime, UNIX_EPOCH};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"message":"unused import: `error`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\processor\\cache.rs","byte_start":171,"byte_end":176,"line_start":6,"line_end":6,"column_start":22,"column_end":27,"is_primary":true,"text":[{"text":"use tracing::{debug, error, info, warn};","highlight_start":22,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\processor\\cache.rs","byte_start":169,"byte_end":176,"line_start":6,"line_end":6,"column_start":20,"column_end":27,"is_primary":true,"text":[{"text":"use tracing::{debug, error, info, warn};","highlight_start":20,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `error`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\processor\\cache.rs:6:22\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{debug, error, info, warn};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\n"}
{"message":"unused import: `ProcessError`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\processor\\cache.rs","byte_start":373,"byte_end":385,"line_start":12,"line_end":12,"column_start":39,"column_end":51,"is_primary":true,"text":[{"text":"use super::{Processor, ProcessResult, ProcessError, RequestContext};","highlight_start":39,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\processor\\cache.rs","byte_start":371,"byte_end":385,"line_start":12,"line_end":12,"column_start":37,"column_end":51,"is_primary":true,"text":[{"text":"use super::{Processor, ProcessResult, ProcessError, RequestContext};","highlight_start":37,"highlight_end":51}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `ProcessError`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\processor\\cache.rs:12:39\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse super::{Processor, ProcessResult, ProcessError, RequestContext};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"unused import: `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\processor\\header.rs","byte_start":113,"byte_end":117,"line_start":4,"line_end":4,"column_start":28,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{debug, info, warn};","highlight_start":28,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\processor\\header.rs","byte_start":111,"byte_end":117,"line_start":4,"line_end":4,"column_start":26,"column_end":32,"is_primary":true,"text":[{"text":"use tracing::{debug, info, warn};","highlight_start":26,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `warn`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\processor\\header.rs:4:28\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{debug, info, warn};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"message":"unused import: `error`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\ssl\\mod.rs","byte_start":136,"byte_end":141,"line_start":6,"line_end":6,"column_start":22,"column_end":27,"is_primary":true,"text":[{"text":"use tracing::{debug, error, info, warn};","highlight_start":22,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\ssl\\mod.rs","byte_start":134,"byte_end":141,"line_start":6,"line_end":6,"column_start":20,"column_end":27,"is_primary":true,"text":[{"text":"use tracing::{debug, error, info, warn};","highlight_start":20,"highlight_end":27}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `error`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\ssl\\mod.rs:6:22\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{debug, error, info, warn};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\n"}
{"message":"unused import: `UNIX_EPOCH`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\mod.rs","byte_start":155,"byte_end":165,"line_start":5,"line_end":5,"column_start":48,"column_end":58,"is_primary":true,"text":[{"text":"use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};","highlight_start":48,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\monitor\\mod.rs","byte_start":153,"byte_end":165,"line_start":5,"line_end":5,"column_start":46,"column_end":58,"is_primary":true,"text":[{"text":"use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};","highlight_start":46,"highlight_end":58}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `UNIX_EPOCH`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\mod.rs:5:48\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                                               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"message":"unused import: `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\mod.rs","byte_start":227,"byte_end":231,"line_start":7,"line_end":7,"column_start":35,"column_end":39,"is_primary":true,"text":[{"text":"use tracing::{debug, error, info, warn};","highlight_start":35,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\monitor\\mod.rs","byte_start":225,"byte_end":231,"line_start":7,"line_end":7,"column_start":33,"column_end":39,"is_primary":true,"text":[{"text":"use tracing::{debug, error, info, warn};","highlight_start":33,"highlight_end":39}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `warn`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\mod.rs:7:35\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{debug, error, info, warn};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"message":"unused imports: `Deserialize`, `Serialize`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\metrics.rs","byte_start":32,"byte_end":43,"line_start":2,"line_end":2,"column_start":13,"column_end":24,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":13,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\metrics.rs","byte_start":45,"byte_end":54,"line_start":2,"line_end":2,"column_start":26,"column_end":35,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":26,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src\\monitor\\metrics.rs","byte_start":20,"byte_end":56,"line_start":2,"line_end":2,"column_start":1,"column_end":37,"is_primary":true,"text":[{"text":"use serde::{Deserialize, Serialize};","highlight_start":1,"highlight_end":37}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `Deserialize`, `Serialize`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\metrics.rs:2:13\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse serde::{Deserialize, Serialize};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\n"}
{"message":"unused import: `UNIX_EPOCH`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\metrics.rs","byte_start":146,"byte_end":156,"line_start":5,"line_end":5,"column_start":39,"column_end":49,"is_primary":true,"text":[{"text":"use std::time::{Duration, SystemTime, UNIX_EPOCH};","highlight_start":39,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\monitor\\metrics.rs","byte_start":144,"byte_end":156,"line_start":5,"line_end":5,"column_start":37,"column_end":49,"is_primary":true,"text":[{"text":"use std::time::{Duration, SystemTime, UNIX_EPOCH};","highlight_start":37,"highlight_end":49}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `UNIX_EPOCH`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\metrics.rs:5:39\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::time::{Duration, SystemTime, UNIX_EPOCH};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"message":"unused import: `Instant`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\metrics.rs","byte_start":212,"byte_end":219,"line_start":7,"line_end":7,"column_start":29,"column_end":36,"is_primary":true,"text":[{"text":"use tokio::time::{interval, Instant};","highlight_start":29,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\monitor\\metrics.rs","byte_start":210,"byte_end":219,"line_start":7,"line_end":7,"column_start":27,"column_end":36,"is_primary":true,"text":[{"text":"use tokio::time::{interval, Instant};","highlight_start":27,"highlight_end":36}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `Instant`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\metrics.rs:7:29\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tokio::time::{interval, Instant};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\n"}
{"message":"unused import: `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\metrics.rs","byte_start":256,"byte_end":260,"line_start":8,"line_end":8,"column_start":35,"column_end":39,"is_primary":true,"text":[{"text":"use tracing::{debug, error, info, warn};","highlight_start":35,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"src\\monitor\\metrics.rs","byte_start":254,"byte_end":260,"line_start":8,"line_end":8,"column_start":33,"column_end":39,"is_primary":true,"text":[{"text":"use tracing::{debug, error, info, warn};","highlight_start":33,"highlight_end":39}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused import: `warn`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\metrics.rs:8:35\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{debug, error, info, warn};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"message":"unused variable: `regex`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\processor\\static_file.rs","byte_start":1702,"byte_end":1707,"line_start":57,"line_end":57,"column_start":23,"column_end":28,"is_primary":true,"text":[{"text":"            if let Ok(regex) = Regex::new(&route.pattern) {","highlight_start":23,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\processor\\static_file.rs","byte_start":1702,"byte_end":1707,"line_start":57,"line_end":57,"column_start":23,"column_end":28,"is_primary":true,"text":[{"text":"            if let Ok(regex) = Regex::new(&route.pattern) {","highlight_start":23,"highlight_end":28}],"label":null,"suggested_replacement":"_regex","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `regex`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\processor\\static_file.rs:57:23\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m57\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            if let Ok(regex) = Regex::new(&route.pattern) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_regex`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"message":"unused variable: `monitor_config`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\access_log.rs","byte_start":653,"byte_end":667,"line_start":25,"line_end":25,"column_start":22,"column_end":36,"is_primary":true,"text":[{"text":"    pub async fn new(monitor_config: &MonitorConfig) -> Result<Self> {","highlight_start":22,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\monitor\\access_log.rs","byte_start":653,"byte_end":667,"line_start":25,"line_end":25,"column_start":22,"column_end":36,"is_primary":true,"text":[{"text":"    pub async fn new(monitor_config: &MonitorConfig) -> Result<Self> {","highlight_start":22,"highlight_end":36}],"label":null,"suggested_replacement":"_monitor_config","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `monitor_config`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\access_log.rs:25:22\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn new(monitor_config: &MonitorConfig) -> Result<Self> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_monitor_config`\u001b[0m\n\n"}
{"message":"unused variable: `start`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\health_check.rs","byte_start":985,"byte_end":990,"line_start":36,"line_end":36,"column_start":13,"column_end":18,"is_primary":true,"text":[{"text":"        let start = Instant::now();","highlight_start":13,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\monitor\\health_check.rs","byte_start":985,"byte_end":990,"line_start":36,"line_end":36,"column_start":13,"column_end":18,"is_primary":true,"text":[{"text":"        let start = Instant::now();","highlight_start":13,"highlight_end":18}],"label":null,"suggested_replacement":"_start","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `start`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\health_check.rs:36:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let start = Instant::now();\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_start`\u001b[0m\n\n"}
{"message":"unused variable: `cache_config`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src\\processor\\cache.rs","byte_start":2202,"byte_end":2214,"line_start":74,"line_end":74,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"        let cache_config = CacheConfig {","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src\\processor\\cache.rs","byte_start":2202,"byte_end":2214,"line_start":74,"line_end":74,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"        let cache_config = CacheConfig {","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":"_cache_config","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `cache_config`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\processor\\cache.rs:74:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m74\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let cache_config = CacheConfig {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_cache_config`\u001b[0m\n\n"}
{"message":"method `get_site_cache_dir` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\config\\mod.rs","byte_start":4250,"byte_end":4261,"line_start":163,"line_end":163,"column_start":1,"column_end":12,"is_primary":false,"text":[{"text":"impl Config {","highlight_start":1,"highlight_end":12}],"label":"method in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\config\\mod.rs","byte_start":4965,"byte_end":4983,"line_start":185,"line_end":185,"column_start":12,"column_end":30,"is_primary":true,"text":[{"text":"    pub fn get_site_cache_dir(&self, site_name: &str) -> PathBuf {","highlight_start":12,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: method `get_site_cache_dir` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\config\\mod.rs:185:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m163\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl Config {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethod in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m185\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn get_site_cache_dir(&self, site_name: &str) -> PathBuf {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"message":"struct `ConfigWatcher` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\config\\watcher.rs","byte_start":246,"byte_end":259,"line_start":11,"line_end":11,"column_start":12,"column_end":25,"is_primary":true,"text":[{"text":"pub struct ConfigWatcher {","highlight_start":12,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: struct `ConfigWatcher` is never constructed\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\config\\watcher.rs:11:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ConfigWatcher {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"associated items `new`, `start_watching`, `check_and_reload`, `reload_config`, `force_reload`, and `get_current_config` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\config\\watcher.rs","byte_start":394,"byte_end":412,"line_start":18,"line_end":18,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"impl ConfigWatcher {","highlight_start":1,"highlight_end":19}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\config\\watcher.rs","byte_start":462,"byte_end":465,"line_start":20,"line_end":20,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new(","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\config\\watcher.rs","byte_start":1072,"byte_end":1086,"line_start":41,"line_end":41,"column_start":18,"column_end":32,"is_primary":true,"text":[{"text":"    pub async fn start_watching(&mut self) -> Result<()> {","highlight_start":18,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\config\\watcher.rs","byte_start":1541,"byte_end":1557,"line_start":56,"line_end":56,"column_start":14,"column_end":30,"is_primary":true,"text":[{"text":"    async fn check_and_reload(&mut self) -> Result<()> {","highlight_start":14,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\config\\watcher.rs","byte_start":2409,"byte_end":2422,"line_start":80,"line_end":80,"column_start":14,"column_end":27,"is_primary":true,"text":[{"text":"    async fn reload_config(&self) -> Result<()> {","highlight_start":14,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\config\\watcher.rs","byte_start":2845,"byte_end":2857,"line_start":93,"line_end":93,"column_start":18,"column_end":30,"is_primary":true,"text":[{"text":"    pub async fn force_reload(&mut self) -> Result<()> {","highlight_start":18,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\config\\watcher.rs","byte_start":3203,"byte_end":3221,"line_start":105,"line_end":105,"column_start":12,"column_end":30,"is_primary":true,"text":[{"text":"    pub fn get_current_config(&self) -> Config {","highlight_start":12,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: associated items `new`, `start_watching`, `check_and_reload`, `reload_config`, `force_reload`, and `get_current_config` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\config\\watcher.rs:20:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m18\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl ConfigWatcher {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m19\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// 创建新的配置监听器\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m20\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m41\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn start_watching(&mut self) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m56\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn check_and_reload(&mut self) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m80\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn reload_config(&self) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m93\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn force_reload(&mut self) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m105\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn get_current_config(&self) -> Config {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"struct `ConfigChangeNotifier` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\config\\watcher.rs","byte_start":3331,"byte_end":3351,"line_start":111,"line_end":111,"column_start":12,"column_end":32,"is_primary":true,"text":[{"text":"pub struct ConfigChangeNotifier {","highlight_start":12,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: struct `ConfigChangeNotifier` is never constructed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\config\\watcher.rs:111:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m111\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ConfigChangeNotifier {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"associated items `new`, `wait_for_change`, `get_current_config`, and `has_changed` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\config\\watcher.rs","byte_start":3403,"byte_end":3428,"line_start":115,"line_end":115,"column_start":1,"column_end":26,"is_primary":false,"text":[{"text":"impl ConfigChangeNotifier {","highlight_start":1,"highlight_end":26}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\config\\watcher.rs","byte_start":3484,"byte_end":3487,"line_start":117,"line_end":117,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new(config_receiver: watch::Receiver<Config>) -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\config\\watcher.rs","byte_start":3628,"byte_end":3643,"line_start":122,"line_end":122,"column_start":18,"column_end":33,"is_primary":true,"text":[{"text":"    pub async fn wait_for_change(&mut self) -> Result<Config> {","highlight_start":18,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\config\\watcher.rs","byte_start":3876,"byte_end":3894,"line_start":130,"line_end":130,"column_start":12,"column_end":30,"is_primary":true,"text":[{"text":"    pub fn get_current_config(&self) -> Config {","highlight_start":12,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\config\\watcher.rs","byte_start":4024,"byte_end":4035,"line_start":135,"line_end":135,"column_start":12,"column_end":23,"is_primary":true,"text":[{"text":"    pub fn has_changed(&mut self) -> bool {","highlight_start":12,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: associated items `new`, `wait_for_change`, `get_current_config`, and `has_changed` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\config\\watcher.rs:117:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m115\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl ConfigChangeNotifier {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m116\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// 创建新的配置变更通知器\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m117\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new(config_receiver: watch::Receiver<Config>) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn wait_for_change(&mut self) -> Result<Config> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m130\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn get_current_config(&self) -> Config {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m135\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn has_changed(&mut self) -> bool {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"struct `HotReloadManager` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\config\\watcher.rs","byte_start":4166,"byte_end":4182,"line_start":141,"line_end":141,"column_start":12,"column_end":28,"is_primary":true,"text":[{"text":"pub struct HotReloadManager {","highlight_start":12,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: struct `HotReloadManager` is never constructed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\config\\watcher.rs:141:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m141\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HotReloadManager {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"associated items `new`, `create_notifier`, `start`, `reload`, and `get_current_config` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\config\\watcher.rs","byte_start":4258,"byte_end":4279,"line_start":146,"line_end":146,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"impl HotReloadManager {","highlight_start":1,"highlight_end":22}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\config\\watcher.rs","byte_start":4332,"byte_end":4335,"line_start":148,"line_end":148,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new(","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\config\\watcher.rs","byte_start":4794,"byte_end":4809,"line_start":166,"line_end":166,"column_start":12,"column_end":27,"is_primary":true,"text":[{"text":"    pub fn create_notifier(&self) -> ConfigChangeNotifier {","highlight_start":12,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\config\\watcher.rs","byte_start":4969,"byte_end":4974,"line_start":171,"line_end":171,"column_start":18,"column_end":23,"is_primary":true,"text":[{"text":"    pub async fn start(&mut self) -> Result<()> {","highlight_start":18,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\config\\watcher.rs","byte_start":5107,"byte_end":5113,"line_start":176,"line_end":176,"column_start":18,"column_end":24,"is_primary":true,"text":[{"text":"    pub async fn reload(&mut self) -> Result<()> {","highlight_start":18,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\config\\watcher.rs","byte_start":5232,"byte_end":5250,"line_start":181,"line_end":181,"column_start":12,"column_end":30,"is_primary":true,"text":[{"text":"    pub fn get_current_config(&self) -> Config {","highlight_start":12,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: associated items `new`, `create_notifier`, `start`, `reload`, and `get_current_config` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\config\\watcher.rs:148:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m146\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl HotReloadManager {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m147\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// 创建新的热重载管理器\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m148\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m166\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn create_notifier(&self) -> ConfigChangeNotifier {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m171\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn start(&mut self) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m176\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn reload(&mut self) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m181\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn get_current_config(&self) -> Config {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"struct `ConfigDiffer` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\config\\watcher.rs","byte_start":5358,"byte_end":5370,"line_start":187,"line_end":187,"column_start":12,"column_end":24,"is_primary":true,"text":[{"text":"pub struct ConfigDiffer;","highlight_start":12,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: struct `ConfigDiffer` is never constructed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\config\\watcher.rs:187:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m187\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ConfigDiffer;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"associated function `detect_changes` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\config\\watcher.rs","byte_start":5373,"byte_end":5390,"line_start":189,"line_end":189,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"impl ConfigDiffer {","highlight_start":1,"highlight_end":18}],"label":"associated function in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\config\\watcher.rs","byte_start":5431,"byte_end":5445,"line_start":191,"line_end":191,"column_start":12,"column_end":26,"is_primary":true,"text":[{"text":"    pub fn detect_changes(old_config: &Config, new_config: &Config) -> ConfigChanges {","highlight_start":12,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: associated function `detect_changes` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\config\\watcher.rs:191:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m189\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl ConfigDiffer {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14massociated function in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m190\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// 检测配置变更\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m191\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn detect_changes(old_config: &Config, new_config: &Config) -> ConfigChanges {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"methods `has_changes`, `requires_restart`, and `summary` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\config\\watcher.rs","byte_start":7803,"byte_end":7821,"line_start":255,"line_end":255,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"impl ConfigChanges {","highlight_start":1,"highlight_end":19}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\config\\watcher.rs","byte_start":7865,"byte_end":7876,"line_start":257,"line_end":257,"column_start":12,"column_end":23,"is_primary":true,"text":[{"text":"    pub fn has_changes(&self) -> bool {","highlight_start":12,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\config\\watcher.rs","byte_start":8166,"byte_end":8182,"line_start":267,"line_end":267,"column_start":12,"column_end":28,"is_primary":true,"text":[{"text":"    pub fn requires_restart(&self) -> bool {","highlight_start":12,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\config\\watcher.rs","byte_start":8283,"byte_end":8290,"line_start":272,"line_end":272,"column_start":12,"column_end":19,"is_primary":true,"text":[{"text":"    pub fn summary(&self) -> Vec<&'static str> {","highlight_start":12,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: methods `has_changes`, `requires_restart`, and `summary` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\config\\watcher.rs:257:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m255\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl ConfigChanges {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m256\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// 是否有任何变更\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m257\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn has_changes(&self) -> bool {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m267\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn requires_restart(&self) -> bool {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m272\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn summary(&self) -> Vec<&'static str> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\n"}
{"message":"function `get_file_modified_time` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\config\\watcher.rs","byte_start":8930,"byte_end":8952,"line_start":299,"line_end":299,"column_start":4,"column_end":26,"is_primary":true,"text":[{"text":"fn get_file_modified_time<P: AsRef<Path>>(path: P) -> Result<SystemTime> {","highlight_start":4,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `get_file_modified_time` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\config\\watcher.rs:299:4\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m299\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn get_file_modified_time<P: AsRef<Path>>(path: P) -> Result<SystemTime> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"field `http_server` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\server\\mod.rs","byte_start":308,"byte_end":319,"line_start":16,"line_end":16,"column_start":12,"column_end":23,"is_primary":false,"text":[{"text":"pub struct ProxyServer {","highlight_start":12,"highlight_end":23}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\server\\mod.rs","byte_start":395,"byte_end":406,"line_start":19,"line_end":19,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"    http_server: Option<HttpServer>,","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: field `http_server` is never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\server\\mod.rs:19:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ProxyServer {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    http_server: Option<HttpServer>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"method `stop` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\server\\mod.rs","byte_start":431,"byte_end":447,"line_start":22,"line_end":22,"column_start":1,"column_end":17,"is_primary":false,"text":[{"text":"impl ProxyServer {","highlight_start":1,"highlight_end":17}],"label":"method in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\server\\mod.rs","byte_start":1558,"byte_end":1562,"line_start":59,"line_end":59,"column_start":18,"column_end":22,"is_primary":true,"text":[{"text":"    pub async fn stop(&self) -> Result<()> {","highlight_start":18,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: method `stop` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\server\\mod.rs:59:18\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m22\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl ProxyServer {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethod in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m59\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn stop(&self) -> Result<()> {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"message":"field `config` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\server\\http_server.rs","byte_start":873,"byte_end":883,"line_start":36,"line_end":36,"column_start":12,"column_end":22,"is_primary":false,"text":[{"text":"pub struct HttpServer {","highlight_start":12,"highlight_end":22}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\server\\http_server.rs","byte_start":890,"byte_end":896,"line_start":37,"line_end":37,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    config: Config,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: field `config` is never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\server\\http_server.rs:37:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m36\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HttpServer {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m37\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    config: Config,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\n"}
{"message":"method `stop` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\server\\http_server.rs","byte_start":1030,"byte_end":1045,"line_start":43,"line_end":43,"column_start":1,"column_end":16,"is_primary":false,"text":[{"text":"impl HttpServer {","highlight_start":1,"highlight_end":16}],"label":"method in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\server\\http_server.rs","byte_start":10289,"byte_end":10293,"line_start":295,"line_end":295,"column_start":18,"column_end":22,"is_primary":true,"text":[{"text":"    pub async fn stop(&self) -> Result<()> {","highlight_start":18,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: method `stop` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\server\\http_server.rs:295:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m43\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl HttpServer {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethod in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m295\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn stop(&self) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\n"}
{"message":"variant `Skip` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\processor\\mod.rs","byte_start":439,"byte_end":452,"line_start":23,"line_end":23,"column_start":10,"column_end":23,"is_primary":false,"text":[{"text":"pub enum ProcessResult {","highlight_start":10,"highlight_end":23}],"label":"variant in this enum","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\mod.rs","byte_start":618,"byte_end":622,"line_start":29,"line_end":29,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    Skip,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`ProcessResult` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variant `Skip` is never constructed\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\processor\\mod.rs:29:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m23\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub enum ProcessResult {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mvariant in this enum\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m29\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Skip,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `ProcessResult` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"message":"method `with_cause` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\processor\\mod.rs","byte_start":825,"byte_end":842,"line_start":42,"line_end":42,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"impl ProcessError {","highlight_start":1,"highlight_end":18}],"label":"method in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\mod.rs","byte_start":1046,"byte_end":1056,"line_start":51,"line_end":51,"column_start":12,"column_end":22,"is_primary":true,"text":[{"text":"    pub fn with_cause(mut self, cause: impl Into<String>) -> Self {","highlight_start":12,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: method `with_cause` is never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\processor\\mod.rs:51:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m42\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl ProcessError {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethod in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn with_cause(mut self, cause: impl Into<String>) -> Self {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"message":"struct `ProcessorManager` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\processor\\mod.rs","byte_start":2231,"byte_end":2247,"line_start":98,"line_end":98,"column_start":12,"column_end":28,"is_primary":true,"text":[{"text":"pub struct ProcessorManager {","highlight_start":12,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: struct `ProcessorManager` is never constructed\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\processor\\mod.rs:98:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m98\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ProcessorManager {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"multiple associated items are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\processor\\mod.rs","byte_start":2334,"byte_end":2355,"line_start":103,"line_end":103,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"impl ProcessorManager {","highlight_start":1,"highlight_end":22}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\mod.rs","byte_start":2408,"byte_end":2411,"line_start":105,"line_end":105,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new() -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\mod.rs","byte_start":2596,"byte_end":2609,"line_start":113,"line_end":113,"column_start":12,"column_end":25,"is_primary":true,"text":[{"text":"    pub fn add_processor(&mut self, processor: Arc<dyn Processor>) {","highlight_start":12,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\mod.rs","byte_start":2837,"byte_end":2851,"line_start":120,"line_end":120,"column_start":18,"column_end":32,"is_primary":true,"text":[{"text":"    pub async fn initialize_all(&self) -> Result<()> {","highlight_start":18,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\mod.rs","byte_start":3254,"byte_end":3265,"line_start":132,"line_end":132,"column_start":18,"column_end":29,"is_primary":true,"text":[{"text":"    pub async fn cleanup_all(&self) -> Result<()> {","highlight_start":18,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\mod.rs","byte_start":3561,"byte_end":3568,"line_start":142,"line_end":142,"column_start":18,"column_end":25,"is_primary":true,"text":[{"text":"    pub async fn execute(&self, ctx: &mut RequestContext) -> Result<()> {","highlight_start":18,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\mod.rs","byte_start":5533,"byte_end":5547,"line_start":197,"line_end":197,"column_start":12,"column_end":26,"is_primary":true,"text":[{"text":"    pub fn get_processors(&self) -> &[Arc<dyn Processor>] {","highlight_start":12,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\mod.rs","byte_start":5658,"byte_end":5667,"line_start":202,"line_end":202,"column_start":18,"column_end":27,"is_primary":true,"text":[{"text":"    pub async fn get_stats(&self) -> ProcessorStats {","highlight_start":18,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\mod.rs","byte_start":5791,"byte_end":5813,"line_start":207,"line_end":207,"column_start":14,"column_end":36,"is_primary":true,"text":[{"text":"    async fn update_processor_stats(&self, name: &str, duration: Duration, result: &ProcessResult) {","highlight_start":14,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\mod.rs","byte_start":6600,"byte_end":6619,"line_start":228,"line_end":228,"column_start":14,"column_end":33,"is_primary":true,"text":[{"text":"    async fn update_global_stats(&self, duration: Duration, processed_count: usize) {","highlight_start":14,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: multiple associated items are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\processor\\mod.rs:105:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m103\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl ProcessorManager {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m104\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// 创建新的处理器管理器\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m105\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m113\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn add_processor(&mut self, processor: Arc<dyn Processor>) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m120\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn initialize_all(&self) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m132\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn cleanup_all(&self) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m142\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn execute(&self, ctx: &mut RequestContext) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m197\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn get_processors(&self) -> &[Arc<dyn Processor>] {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m202\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn get_stats(&self) -> ProcessorStats {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m207\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn update_processor_stats(&self, name: &str, duration: Duration, result: &Pro\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m228\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn update_global_stats(&self, duration: Duration, processed_count: usize) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"methods `success_rate` and `error_rate` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\processor\\mod.rs","byte_start":7627,"byte_end":7645,"line_start":259,"line_end":259,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"impl ProcessorStat {","highlight_start":1,"highlight_end":19}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\mod.rs","byte_start":7659,"byte_end":7671,"line_start":260,"line_end":260,"column_start":12,"column_end":24,"is_primary":true,"text":[{"text":"    pub fn success_rate(&self) -> f64 {","highlight_start":12,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\mod.rs","byte_start":7854,"byte_end":7864,"line_start":268,"line_end":268,"column_start":12,"column_end":22,"is_primary":true,"text":[{"text":"    pub fn error_rate(&self) -> f64 {","highlight_start":12,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: methods `success_rate` and `error_rate` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\processor\\mod.rs:260:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m259\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl ProcessorStat {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m260\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn success_rate(&self) -> f64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m268\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn error_rate(&self) -> f64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"message":"methods `add_processors`, `is_empty`, `processor_names`, and `cleanup` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\processor\\chain.rs","byte_start":239,"byte_end":258,"line_start":13,"line_end":13,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"impl ProcessorChain {","highlight_start":1,"highlight_end":20}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\chain.rs","byte_start":653,"byte_end":667,"line_start":29,"line_end":29,"column_start":12,"column_end":26,"is_primary":true,"text":[{"text":"    pub fn add_processors(mut self, processors: Vec<Arc<dyn Processor>>) -> Self {","highlight_start":12,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\chain.rs","byte_start":1092,"byte_end":1100,"line_start":46,"line_end":46,"column_start":12,"column_end":20,"is_primary":true,"text":[{"text":"    pub fn is_empty(&self) -> bool {","highlight_start":12,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\chain.rs","byte_start":1211,"byte_end":1226,"line_start":51,"line_end":51,"column_start":12,"column_end":27,"is_primary":true,"text":[{"text":"    pub fn processor_names(&self) -> Vec<&'static str> {","highlight_start":12,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\chain.rs","byte_start":5875,"byte_end":5882,"line_start":191,"line_end":191,"column_start":18,"column_end":25,"is_primary":true,"text":[{"text":"    pub async fn cleanup(&self) -> Result<()> {","highlight_start":18,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: methods `add_processors`, `is_empty`, `processor_names`, and `cleanup` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\processor\\chain.rs:29:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl ProcessorChain {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m29\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn add_processors(mut self, processors: Vec<Arc<dyn Processor>>) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m46\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn is_empty(&self) -> bool {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m51\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn processor_names(&self) -> Vec<&'static str> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m191\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn cleanup(&self) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\n"}
{"message":"struct `ProcessorChainBuilder` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\processor\\chain.rs","byte_start":6948,"byte_end":6969,"line_start":228,"line_end":228,"column_start":12,"column_end":33,"is_primary":true,"text":[{"text":"pub struct ProcessorChainBuilder {","highlight_start":12,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: struct `ProcessorChainBuilder` is never constructed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\processor\\chain.rs:228:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m228\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ProcessorChainBuilder {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"associated items `new`, `add`, `add_all`, `sort_by_priority`, and `build` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\processor\\chain.rs","byte_start":7002,"byte_end":7028,"line_start":232,"line_end":232,"column_start":1,"column_end":27,"is_primary":false,"text":[{"text":"impl ProcessorChainBuilder {","highlight_start":1,"highlight_end":27}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\chain.rs","byte_start":7072,"byte_end":7075,"line_start":234,"line_end":234,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new(name: impl Into<String>) -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\chain.rs","byte_start":7228,"byte_end":7231,"line_start":241,"line_end":241,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn add(mut self, processor: Arc<dyn Processor>) -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\chain.rs","byte_start":7406,"byte_end":7413,"line_start":247,"line_end":247,"column_start":12,"column_end":19,"is_primary":true,"text":[{"text":"    pub fn add_all(mut self, processors: Vec<Arc<dyn Processor>>) -> Self {","highlight_start":12,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\chain.rs","byte_start":7593,"byte_end":7609,"line_start":253,"line_end":253,"column_start":12,"column_end":28,"is_primary":true,"text":[{"text":"    pub fn sort_by_priority(mut self) -> Self {","highlight_start":12,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\chain.rs","byte_start":7744,"byte_end":7749,"line_start":259,"line_end":259,"column_start":12,"column_end":17,"is_primary":true,"text":[{"text":"    pub fn build(self) -> ProcessorChain {","highlight_start":12,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: associated items `new`, `add`, `add_all`, `sort_by_priority`, and `build` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\processor\\chain.rs:234:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m232\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl ProcessorChainBuilder {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m233\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// 创建新的构建器\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m234\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new(name: impl Into<String>) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m241\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn add(mut self, processor: Arc<dyn Processor>) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m247\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn add_all(mut self, processors: Vec<Arc<dyn Processor>>) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m253\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn sort_by_priority(mut self) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m259\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn build(self) -> ProcessorChain {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\n"}
{"message":"multiple methods are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\processor\\context.rs","byte_start":1362,"byte_end":1381,"line_start":53,"line_end":53,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"impl RequestContext {","highlight_start":1,"highlight_end":20}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\context.rs","byte_start":2931,"byte_end":2941,"line_start":111,"line_end":111,"column_start":12,"column_end":22,"is_primary":true,"text":[{"text":"    pub fn set_header(&mut self, name: String, value: String) {","highlight_start":12,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\context.rs","byte_start":3087,"byte_end":3100,"line_start":116,"line_end":116,"column_start":12,"column_end":25,"is_primary":true,"text":[{"text":"    pub fn remove_header(&mut self, name: &str) {","highlight_start":12,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\context.rs","byte_start":3226,"byte_end":3234,"line_start":121,"line_end":121,"column_start":12,"column_end":20,"is_primary":true,"text":[{"text":"    pub fn get_data(&self, key: &str) -> Option<&String> {","highlight_start":12,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\context.rs","byte_start":3350,"byte_end":3358,"line_start":126,"line_end":126,"column_start":12,"column_end":20,"is_primary":true,"text":[{"text":"    pub fn set_data(&mut self, key: String, value: String) {","highlight_start":12,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\context.rs","byte_start":3882,"byte_end":3889,"line_start":146,"line_end":146,"column_start":12,"column_end":19,"is_primary":true,"text":[{"text":"    pub fn elapsed(&self) -> Duration {","highlight_start":12,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\context.rs","byte_start":3992,"byte_end":4002,"line_start":151,"line_end":151,"column_start":12,"column_end":22,"is_primary":true,"text":[{"text":"    pub fn user_agent(&self) -> Option<&String> {","highlight_start":12,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\context.rs","byte_start":4113,"byte_end":4120,"line_start":156,"line_end":156,"column_start":12,"column_end":19,"is_primary":true,"text":[{"text":"    pub fn referer(&self) -> Option<&String> {","highlight_start":12,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\context.rs","byte_start":4233,"byte_end":4245,"line_start":161,"line_end":161,"column_start":12,"column_end":24,"is_primary":true,"text":[{"text":"    pub fn content_type(&self) -> Option<&String> {","highlight_start":12,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\context.rs","byte_start":4365,"byte_end":4379,"line_start":166,"line_end":166,"column_start":12,"column_end":26,"is_primary":true,"text":[{"text":"    pub fn content_length(&self) -> Option<usize> {","highlight_start":12,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: multiple methods are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\processor\\context.rs:111:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m53\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl RequestContext {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m111\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn set_header(&mut self, name: String, value: String) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m116\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn remove_header(&mut self, name: &str) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m121\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn get_data(&self, key: &str) -> Option<&String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m126\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn set_data(&mut self, key: String, value: String) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m146\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn elapsed(&self) -> Duration {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m151\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn user_agent(&self) -> Option<&String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m156\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn referer(&self) -> Option<&String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m161\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn content_type(&self) -> Option<&String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m166\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn content_length(&self) -> Option<usize> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"multiple methods are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\processor\\context.rs","byte_start":5687,"byte_end":5707,"line_start":214,"line_end":214,"column_start":1,"column_end":21,"is_primary":false,"text":[{"text":"impl ResponseContext {","highlight_start":1,"highlight_end":21}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\context.rs","byte_start":6188,"byte_end":6198,"line_start":233,"line_end":233,"column_start":12,"column_end":22,"is_primary":true,"text":[{"text":"    pub fn get_header(&self, name: &str) -> Option<&String> {","highlight_start":12,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\context.rs","byte_start":6332,"byte_end":6345,"line_start":238,"line_end":238,"column_start":12,"column_end":25,"is_primary":true,"text":[{"text":"    pub fn remove_header(&mut self, name: &str) {","highlight_start":12,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\context.rs","byte_start":6796,"byte_end":6814,"line_start":254,"line_end":254,"column_start":12,"column_end":30,"is_primary":true,"text":[{"text":"    pub fn set_content_length(&mut self, length: usize) {","highlight_start":12,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\context.rs","byte_start":6970,"byte_end":6980,"line_start":259,"line_end":259,"column_start":12,"column_end":22,"is_primary":true,"text":[{"text":"    pub fn is_success(&self) -> bool {","highlight_start":12,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\context.rs","byte_start":7111,"byte_end":7122,"line_start":264,"line_end":264,"column_start":12,"column_end":23,"is_primary":true,"text":[{"text":"    pub fn is_redirect(&self) -> bool {","highlight_start":12,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\context.rs","byte_start":7253,"byte_end":7268,"line_start":269,"line_end":269,"column_start":12,"column_end":27,"is_primary":true,"text":[{"text":"    pub fn is_client_error(&self) -> bool {","highlight_start":12,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\context.rs","byte_start":7399,"byte_end":7414,"line_start":274,"line_end":274,"column_start":12,"column_end":27,"is_primary":true,"text":[{"text":"    pub fn is_server_error(&self) -> bool {","highlight_start":12,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: multiple methods are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\processor\\context.rs:233:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m214\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl ResponseContext {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m233\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn get_header(&self, name: &str) -> Option<&String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m238\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn remove_header(&mut self, name: &str) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m254\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn set_content_length(&mut self, length: usize) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m259\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn is_success(&self) -> bool {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m264\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn is_redirect(&self) -> bool {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m269\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn is_client_error(&self) -> bool {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m274\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn is_server_error(&self) -> bool {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"variants `Expired`, `Bypass`, and `Refresh` are never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\processor\\context.rs","byte_start":7625,"byte_end":7636,"line_start":287,"line_end":287,"column_start":10,"column_end":21,"is_primary":false,"text":[{"text":"pub enum CacheStatus {","highlight_start":10,"highlight_end":21}],"label":"variants in this enum","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\context.rs","byte_start":7728,"byte_end":7735,"line_start":293,"line_end":293,"column_start":5,"column_end":12,"is_primary":true,"text":[{"text":"    Expired,","highlight_start":5,"highlight_end":12}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\context.rs","byte_start":7765,"byte_end":7771,"line_start":295,"line_end":295,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    Bypass,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\context.rs","byte_start":7798,"byte_end":7805,"line_start":297,"line_end":297,"column_start":5,"column_end":12,"is_primary":true,"text":[{"text":"    Refresh,","highlight_start":5,"highlight_end":12}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`CacheStatus` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variants `Expired`, `Bypass`, and `Refresh` are never constructed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\processor\\context.rs:293:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m287\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub enum CacheStatus {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mvariants in this enum\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m293\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Expired,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m294\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// 缓存被绕过\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m295\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Bypass,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m296\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// 缓存更新\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m297\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Refresh,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `CacheStatus` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"message":"field `config` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\processor\\route.rs","byte_start":278,"byte_end":292,"line_start":11,"line_end":11,"column_start":12,"column_end":26,"is_primary":false,"text":[{"text":"pub struct RouteProcessor {","highlight_start":12,"highlight_end":26}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\route.rs","byte_start":299,"byte_end":305,"line_start":12,"line_end":12,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    config: Arc<Config>,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: field `config` is never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\processor\\route.rs:12:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct RouteProcessor {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    config: Arc<Config>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\n"}
{"message":"methods `with_dir_listing` and `with_index_files` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\processor\\static_file.rs","byte_start":419,"byte_end":443,"line_start":19,"line_end":19,"column_start":1,"column_end":25,"is_primary":false,"text":[{"text":"impl StaticFileProcessor {","highlight_start":1,"highlight_end":25}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\static_file.rs","byte_start":821,"byte_end":837,"line_start":33,"line_end":33,"column_start":12,"column_end":28,"is_primary":true,"text":[{"text":"    pub fn with_dir_listing(mut self, enabled: bool) -> Self {","highlight_start":12,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\static_file.rs","byte_start":984,"byte_end":1000,"line_start":39,"line_end":39,"column_start":12,"column_end":28,"is_primary":true,"text":[{"text":"    pub fn with_index_files(mut self, files: Vec<String>) -> Self {","highlight_start":12,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: methods `with_dir_listing` and `with_index_files` are never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\processor\\static_file.rs:33:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl StaticFileProcessor {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn with_dir_listing(mut self, enabled: bool) -> Self {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m39\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn with_index_files(mut self, files: Vec<String>) -> Self {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"fields `load_balancer` and `connection_pool` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\processor\\proxy.rs","byte_start":585,"byte_end":599,"line_start":23,"line_end":23,"column_start":12,"column_end":26,"is_primary":false,"text":[{"text":"pub struct ProxyProcessor {","highlight_start":12,"highlight_end":26}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\proxy.rs","byte_start":690,"byte_end":703,"line_start":26,"line_end":26,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    load_balancer: Arc<RwLock<LoadBalancer>>,","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\proxy.rs","byte_start":736,"byte_end":751,"line_start":27,"line_end":27,"column_start":5,"column_end":20,"is_primary":true,"text":[{"text":"    connection_pool: Arc<ConnectionPool>,","highlight_start":5,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: fields `load_balancer` and `connection_pool` are never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\processor\\proxy.rs:26:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m23\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ProxyProcessor {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m26\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    load_balancer: Arc<RwLock<LoadBalancer>>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m27\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    connection_pool: Arc<ConnectionPool>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"associated function `with_config` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\processor\\compression.rs","byte_start":4174,"byte_end":4199,"line_start":132,"line_end":132,"column_start":1,"column_end":26,"is_primary":false,"text":[{"text":"impl CompressionProcessor {","highlight_start":1,"highlight_end":26}],"label":"associated function in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\compression.rs","byte_start":4406,"byte_end":4417,"line_start":141,"line_end":141,"column_start":12,"column_end":23,"is_primary":true,"text":[{"text":"    pub fn with_config(config: CompressionConfig) -> Self {","highlight_start":12,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: associated function `with_config` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\processor\\compression.rs:141:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m132\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl CompressionProcessor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14massociated function in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m141\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn with_config(config: CompressionConfig) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"associated function `with_config` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\processor\\header.rs","byte_start":3199,"byte_end":3219,"line_start":106,"line_end":106,"column_start":1,"column_end":21,"is_primary":false,"text":[{"text":"impl HeaderProcessor {","highlight_start":1,"highlight_end":21}],"label":"associated function in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\header.rs","byte_start":3430,"byte_end":3441,"line_start":115,"line_end":115,"column_start":12,"column_end":23,"is_primary":true,"text":[{"text":"    pub fn with_config(config: HeaderProcessorConfig) -> Self {","highlight_start":12,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: associated function `with_config` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\processor\\header.rs:115:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m106\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl HeaderProcessor {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14massociated function in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m115\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn with_config(config: HeaderProcessorConfig) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"associated items `with_config` and `get_stats` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\processor\\error.rs","byte_start":2014,"byte_end":2033,"line_start":76,"line_end":76,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"impl ErrorProcessor {","highlight_start":1,"highlight_end":20}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\error.rs","byte_start":2329,"byte_end":2340,"line_start":86,"line_end":86,"column_start":12,"column_end":23,"is_primary":true,"text":[{"text":"    pub fn with_config(config: ErrorProcessorConfig) -> Self {","highlight_start":12,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\processor\\error.rs","byte_start":2563,"byte_end":2572,"line_start":94,"line_end":94,"column_start":12,"column_end":21,"is_primary":true,"text":[{"text":"    pub fn get_stats(&self) -> ErrorStats {","highlight_start":12,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: associated items `with_config` and `get_stats` are never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\processor\\error.rs:86:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m76\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl ErrorProcessor {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m86\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn with_config(config: ErrorProcessorConfig) -> Self {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m94\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn get_stats(&self) -> ErrorStats {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\n"}
{"message":"associated items `new` and `calculate_score` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\cache\\mod.rs","byte_start":902,"byte_end":917,"line_start":42,"line_end":42,"column_start":1,"column_end":16,"is_primary":false,"text":[{"text":"impl CacheEntry {","highlight_start":1,"highlight_end":16}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\mod.rs","byte_start":964,"byte_end":967,"line_start":44,"line_end":44,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new(","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\mod.rs","byte_start":1867,"byte_end":1882,"line_start":81,"line_end":81,"column_start":12,"column_end":27,"is_primary":true,"text":[{"text":"    pub fn calculate_score(&self) -> f64 {","highlight_start":12,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: associated items `new` and `calculate_score` are never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\cache\\mod.rs:44:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m42\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl CacheEntry {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m43\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// 创建新的缓存条目\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m44\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new(\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m81\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn calculate_score(&self) -> f64 {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"method `hit_rate` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\cache\\mod.rs","byte_start":2772,"byte_end":2787,"line_start":116,"line_end":116,"column_start":1,"column_end":16,"is_primary":false,"text":[{"text":"impl CacheStats {","highlight_start":1,"highlight_end":16}],"label":"method in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\mod.rs","byte_start":2825,"byte_end":2833,"line_start":118,"line_end":118,"column_start":12,"column_end":20,"is_primary":true,"text":[{"text":"    pub fn hit_rate(&self) -> f64 {","highlight_start":12,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: method `hit_rate` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\cache\\mod.rs:118:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m116\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl CacheStats {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethod in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m117\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// 计算命中率\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m118\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn hit_rate(&self) -> f64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\n"}
{"message":"struct `CacheKeyGenerator` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\cache\\mod.rs","byte_start":4564,"byte_end":4581,"line_start":185,"line_end":185,"column_start":12,"column_end":29,"is_primary":true,"text":[{"text":"pub struct CacheKeyGenerator;","highlight_start":12,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: struct `CacheKeyGenerator` is never constructed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\cache\\mod.rs:185:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m185\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct CacheKeyGenerator;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"associated functions `generate_key`, `site_prefix`, and `extract_site_id` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\cache\\mod.rs","byte_start":4584,"byte_end":4606,"line_start":187,"line_end":187,"column_start":1,"column_end":23,"is_primary":false,"text":[{"text":"impl CacheKeyGenerator {","highlight_start":1,"highlight_end":23}],"label":"associated functions in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\mod.rs","byte_start":4644,"byte_end":4656,"line_start":189,"line_end":189,"column_start":12,"column_end":24,"is_primary":true,"text":[{"text":"    pub fn generate_key(","highlight_start":12,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\mod.rs","byte_start":5581,"byte_end":5592,"line_start":224,"line_end":224,"column_start":12,"column_end":23,"is_primary":true,"text":[{"text":"    pub fn site_prefix(site_id: &str) -> String {","highlight_start":12,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\mod.rs","byte_start":5706,"byte_end":5721,"line_start":229,"line_end":229,"column_start":12,"column_end":27,"is_primary":true,"text":[{"text":"    pub fn extract_site_id(key: &str) -> Option<&str> {","highlight_start":12,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: associated functions `generate_key`, `site_prefix`, and `extract_site_id` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\cache\\mod.rs:189:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m187\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl CacheKeyGenerator {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14massociated functions in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m188\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// 生成缓存键\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m189\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn generate_key(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m224\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn site_prefix(site_id: &str) -> String {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m229\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn extract_site_id(key: &str) -> Option<&str> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"variants `File`, `Redis`, and `Hybrid` are never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\cache\\mod.rs","byte_start":6385,"byte_end":6394,"line_start":257,"line_end":257,"column_start":10,"column_end":19,"is_primary":false,"text":[{"text":"pub enum CacheType {","highlight_start":10,"highlight_end":19}],"label":"variants in this enum","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\mod.rs","byte_start":6413,"byte_end":6417,"line_start":259,"line_end":259,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    File,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\mod.rs","byte_start":6423,"byte_end":6428,"line_start":260,"line_end":260,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    Redis,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\mod.rs","byte_start":6434,"byte_end":6440,"line_start":261,"line_end":261,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    Hybrid, // 内存 + 文件","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`CacheType` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variants `File`, `Redis`, and `Hybrid` are never constructed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\cache\\mod.rs:259:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m257\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub enum CacheType {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mvariants in this enum\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m258\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Memory,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m259\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    File,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m260\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Redis,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m261\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Hybrid, // 内存 + 文件\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `CacheType` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"message":"methods `is_hit`, `is_miss`, `is_error`, and `into_entry` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\cache\\mod.rs","byte_start":7012,"byte_end":7028,"line_start":292,"line_end":292,"column_start":1,"column_end":17,"is_primary":false,"text":[{"text":"impl CacheResult {","highlight_start":1,"highlight_end":17}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\mod.rs","byte_start":7042,"byte_end":7048,"line_start":293,"line_end":293,"column_start":12,"column_end":18,"is_primary":true,"text":[{"text":"    pub fn is_hit(&self) -> bool {","highlight_start":12,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\mod.rs","byte_start":7132,"byte_end":7139,"line_start":297,"line_end":297,"column_start":12,"column_end":19,"is_primary":true,"text":[{"text":"    pub fn is_miss(&self) -> bool {","highlight_start":12,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\mod.rs","byte_start":7221,"byte_end":7229,"line_start":301,"line_end":301,"column_start":12,"column_end":20,"is_primary":true,"text":[{"text":"    pub fn is_error(&self) -> bool {","highlight_start":12,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\mod.rs","byte_start":7315,"byte_end":7325,"line_start":305,"line_end":305,"column_start":12,"column_end":22,"is_primary":true,"text":[{"text":"    pub fn into_entry(self) -> Option<CacheEntry> {","highlight_start":12,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: methods `is_hit`, `is_miss`, `is_error`, and `into_entry` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\cache\\mod.rs:293:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m292\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl CacheResult {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m293\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn is_hit(&self) -> bool {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m297\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn is_miss(&self) -> bool {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m301\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn is_error(&self) -> bool {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m305\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn into_entry(self) -> Option<CacheEntry> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"message":"variants `Delete` and `Expired` are never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\cache\\mod.rs","byte_start":7521,"byte_end":7531,"line_start":315,"line_end":315,"column_start":10,"column_end":20,"is_primary":false,"text":[{"text":"pub enum CacheEvent {","highlight_start":10,"highlight_end":20}],"label":"variants in this enum","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\mod.rs","byte_start":7655,"byte_end":7661,"line_start":319,"line_end":319,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    Delete { key: String },","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\mod.rs","byte_start":7683,"byte_end":7690,"line_start":320,"line_end":320,"column_start":5,"column_end":12,"is_primary":true,"text":[{"text":"    Expired { key: String, count: u64 },","highlight_start":5,"highlight_end":12}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`CacheEvent` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: variants `Delete` and `Expired` are never constructed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\cache\\mod.rs:319:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m315\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub enum CacheEvent {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mvariants in this enum\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m319\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Delete { key: String },\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m320\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Expired { key: String, count: u64 },\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `CacheEvent` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"message":"methods `config`, `entry_count`, `weighted_size`, `run_pending_tasks`, `hit_rate`, and `detailed_stats` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\cache\\memory_cache.rs","byte_start":457,"byte_end":473,"line_start":19,"line_end":19,"column_start":1,"column_end":17,"is_primary":false,"text":[{"text":"impl MemoryCache {","highlight_start":1,"highlight_end":17}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\memory_cache.rs","byte_start":838,"byte_end":844,"line_start":31,"line_end":31,"column_start":12,"column_end":18,"is_primary":true,"text":[{"text":"    pub fn config(&self) -> &MemoryCacheConfig {","highlight_start":12,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\memory_cache.rs","byte_start":961,"byte_end":972,"line_start":36,"line_end":36,"column_start":18,"column_end":29,"is_primary":true,"text":[{"text":"    pub async fn entry_count(&self) -> u64 {","highlight_start":18,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\memory_cache.rs","byte_start":1090,"byte_end":1103,"line_start":41,"line_end":41,"column_start":18,"column_end":31,"is_primary":true,"text":[{"text":"    pub async fn weighted_size(&self) -> u64 {","highlight_start":18,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\memory_cache.rs","byte_start":1251,"byte_end":1268,"line_start":46,"line_end":46,"column_start":18,"column_end":35,"is_primary":true,"text":[{"text":"    pub async fn run_pending_tasks(&self) {","highlight_start":18,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\memory_cache.rs","byte_start":4313,"byte_end":4321,"line_start":143,"line_end":143,"column_start":18,"column_end":26,"is_primary":true,"text":[{"text":"    pub async fn hit_rate(&self) -> f64 {","highlight_start":18,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\memory_cache.rs","byte_start":4478,"byte_end":4492,"line_start":149,"line_end":149,"column_start":18,"column_end":32,"is_primary":true,"text":[{"text":"    pub async fn detailed_stats(&self) -> DetailedMemoryCacheStats {","highlight_start":18,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: methods `config`, `entry_count`, `weighted_size`, `run_pending_tasks`, `hit_rate`, and `detailed_stats` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\cache\\memory_cache.rs:31:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m19\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl MemoryCache {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m31\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn config(&self) -> &MemoryCacheConfig {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m36\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn entry_count(&self) -> u64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m41\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn weighted_size(&self) -> u64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m46\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn run_pending_tasks(&self) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m143\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn hit_rate(&self) -> f64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m149\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn detailed_stats(&self) -> DetailedMemoryCacheStats {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"methods `capacity_usage`, `size_usage`, and `average_entry_size` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\cache\\memory_cache.rs","byte_start":8575,"byte_end":8604,"line_start":289,"line_end":289,"column_start":1,"column_end":30,"is_primary":false,"text":[{"text":"impl DetailedMemoryCacheStats {","highlight_start":1,"highlight_end":30}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\memory_cache.rs","byte_start":8648,"byte_end":8662,"line_start":291,"line_end":291,"column_start":12,"column_end":26,"is_primary":true,"text":[{"text":"    pub fn capacity_usage(&self) -> f64 {","highlight_start":12,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\memory_cache.rs","byte_start":8873,"byte_end":8883,"line_start":300,"line_end":300,"column_start":12,"column_end":22,"is_primary":true,"text":[{"text":"    pub fn size_usage(&self) -> f64 {","highlight_start":12,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\memory_cache.rs","byte_start":9089,"byte_end":9107,"line_start":309,"line_end":309,"column_start":12,"column_end":30,"is_primary":true,"text":[{"text":"    pub fn average_entry_size(&self) -> f64 {","highlight_start":12,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: methods `capacity_usage`, `size_usage`, and `average_entry_size` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\cache\\memory_cache.rs:291:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m289\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl DetailedMemoryCacheStats {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m290\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// 获取容量使用率\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m291\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn capacity_usage(&self) -> f64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m300\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn size_usage(&self) -> f64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m309\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn average_entry_size(&self) -> f64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"struct `RedisCache` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\cache\\redis_cache.rs","byte_start":13085,"byte_end":13095,"line_start":390,"line_end":390,"column_start":12,"column_end":22,"is_primary":true,"text":[{"text":"pub struct RedisCache;","highlight_start":12,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: struct `RedisCache` is never constructed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\cache\\redis_cache.rs:390:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m390\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct RedisCache;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"message":"associated function `new` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\cache\\redis_cache.rs","byte_start":13135,"byte_end":13150,"line_start":393,"line_end":393,"column_start":1,"column_end":16,"is_primary":false,"text":[{"text":"impl RedisCache {","highlight_start":1,"highlight_end":16}],"label":"associated function in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\redis_cache.rs","byte_start":13170,"byte_end":13173,"line_start":394,"line_end":394,"column_start":18,"column_end":21,"is_primary":true,"text":[{"text":"    pub async fn new(_config: super::RedisConfig) -> anyhow::Result<Self> {","highlight_start":18,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: associated function `new` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\cache\\redis_cache.rs:394:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m393\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl RedisCache {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14massociated function in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m394\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn new(_config: super::RedisConfig) -> anyhow::Result<Self> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\n"}
{"message":"methods `memory_stats`, `file_stats`, and `detailed_stats` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\cache\\hybrid_cache.rs","byte_start":2002,"byte_end":2018,"line_start":64,"line_end":64,"column_start":1,"column_end":17,"is_primary":false,"text":[{"text":"impl HybridCache {","highlight_start":1,"highlight_end":17}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\hybrid_cache.rs","byte_start":5439,"byte_end":5451,"line_start":172,"line_end":172,"column_start":18,"column_end":30,"is_primary":true,"text":[{"text":"    pub async fn memory_stats(&self) -> Result<CacheStats> {","highlight_start":18,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\hybrid_cache.rs","byte_start":5584,"byte_end":5594,"line_start":177,"line_end":177,"column_start":18,"column_end":28,"is_primary":true,"text":[{"text":"    pub async fn file_stats(&self) -> Result<CacheStats> {","highlight_start":18,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\hybrid_cache.rs","byte_start":5725,"byte_end":5739,"line_start":182,"line_end":182,"column_start":18,"column_end":32,"is_primary":true,"text":[{"text":"    pub async fn detailed_stats(&self) -> Result<HybridCacheStats> {","highlight_start":18,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: methods `memory_stats`, `file_stats`, and `detailed_stats` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\cache\\hybrid_cache.rs:172:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m64\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl HybridCache {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m172\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn memory_stats(&self) -> Result<CacheStats> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m177\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn file_stats(&self) -> Result<CacheStats> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m182\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn detailed_stats(&self) -> Result<HybridCacheStats> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"methods `total_hit_rate`, `memory_hit_rate`, `file_hit_rate`, `memory_usage_ratio`, and `file_usage_ratio` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\cache\\hybrid_cache.rs","byte_start":10542,"byte_end":10563,"line_start":322,"line_end":322,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"impl HybridCacheStats {","highlight_start":1,"highlight_end":22}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\hybrid_cache.rs","byte_start":10604,"byte_end":10618,"line_start":324,"line_end":324,"column_start":12,"column_end":26,"is_primary":true,"text":[{"text":"    pub fn total_hit_rate(&self) -> f64 {","highlight_start":12,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\hybrid_cache.rs","byte_start":10832,"byte_end":10847,"line_start":333,"line_end":333,"column_start":12,"column_end":27,"is_primary":true,"text":[{"text":"    pub fn memory_hit_rate(&self) -> f64 {","highlight_start":12,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\hybrid_cache.rs","byte_start":10953,"byte_end":10966,"line_start":338,"line_end":338,"column_start":12,"column_end":25,"is_primary":true,"text":[{"text":"    pub fn file_hit_rate(&self) -> f64 {","highlight_start":12,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\hybrid_cache.rs","byte_start":11073,"byte_end":11091,"line_start":343,"line_end":343,"column_start":12,"column_end":30,"is_primary":true,"text":[{"text":"    pub fn memory_usage_ratio(&self) -> f64 {","highlight_start":12,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\hybrid_cache.rs","byte_start":11325,"byte_end":11341,"line_start":352,"line_end":352,"column_start":12,"column_end":28,"is_primary":true,"text":[{"text":"    pub fn file_usage_ratio(&self) -> f64 {","highlight_start":12,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: methods `total_hit_rate`, `memory_hit_rate`, `file_hit_rate`, `memory_usage_ratio`, and `file_usage_ratio` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\cache\\hybrid_cache.rs:324:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m322\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl HybridCacheStats {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m323\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// 计算总命中率\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m324\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn total_hit_rate(&self) -> f64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m333\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn memory_hit_rate(&self) -> f64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m338\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn file_hit_rate(&self) -> f64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m343\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn memory_usage_ratio(&self) -> f64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m352\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn file_usage_ratio(&self) -> f64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"multiple methods are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\cache\\manager.rs","byte_start":749,"byte_end":766,"line_start":28,"line_end":28,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"impl CacheManager {","highlight_start":1,"highlight_end":18}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\manager.rs","byte_start":4928,"byte_end":4934,"line_start":140,"line_end":140,"column_start":18,"column_end":24,"is_primary":true,"text":[{"text":"    pub async fn delete(&self, key: &str) -> Result<bool> {","highlight_start":18,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\manager.rs","byte_start":5394,"byte_end":5400,"line_start":156,"line_end":156,"column_start":18,"column_end":24,"is_primary":true,"text":[{"text":"    pub async fn exists(&self, key: &str) -> Result<bool> {","highlight_start":18,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\manager.rs","byte_start":5531,"byte_end":5536,"line_start":161,"line_end":161,"column_start":18,"column_end":23,"is_primary":true,"text":[{"text":"    pub async fn clear(&self) -> Result<()> {","highlight_start":18,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\manager.rs","byte_start":5843,"byte_end":5848,"line_start":173,"line_end":173,"column_start":18,"column_end":23,"is_primary":true,"text":[{"text":"    pub async fn stats(&self) -> Result<CacheStats> {","highlight_start":18,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\manager.rs","byte_start":6518,"byte_end":6533,"line_start":191,"line_end":191,"column_start":18,"column_end":33,"is_primary":true,"text":[{"text":"    pub async fn cleanup_expired(&self) -> Result<u64> {","highlight_start":18,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\manager.rs","byte_start":7000,"byte_end":7004,"line_start":205,"line_end":205,"column_start":18,"column_end":22,"is_primary":true,"text":[{"text":"    pub async fn keys(&self) -> Result<Vec<String>> {","highlight_start":18,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\manager.rs","byte_start":7126,"byte_end":7130,"line_start":210,"line_end":210,"column_start":18,"column_end":22,"is_primary":true,"text":[{"text":"    pub async fn size(&self) -> Result<u64> {","highlight_start":18,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\manager.rs","byte_start":7241,"byte_end":7259,"line_start":215,"line_end":215,"column_start":12,"column_end":30,"is_primary":true,"text":[{"text":"    pub fn add_event_listener(&mut self, listener: Arc<dyn CacheEventListener>) {","highlight_start":12,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\manager.rs","byte_start":9760,"byte_end":9773,"line_start":290,"line_end":290,"column_start":14,"column_end":27,"is_primary":true,"text":[{"text":"    async fn record_delete(&self, key: &str) {","highlight_start":14,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\manager.rs","byte_start":10140,"byte_end":10158,"line_start":304,"line_end":304,"column_start":12,"column_end":30,"is_primary":true,"text":[{"text":"    pub fn get_site_cache_dir(&self, site_name: &str) -> Option<std::path::PathBuf> {","highlight_start":12,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: multiple methods are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\cache\\manager.rs:140:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m28\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl CacheManager {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m140\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn delete(&self, key: &str) -> Result<bool> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m156\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn exists(&self, key: &str) -> Result<bool> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m161\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn clear(&self) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m173\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn stats(&self) -> Result<CacheStats> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m191\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn cleanup_expired(&self) -> Result<u64> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m205\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn keys(&self) -> Result<Vec<String>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m210\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn size(&self) -> Result<u64> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m215\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn add_event_listener(&mut self, listener: Arc<dyn CacheEventListener>) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m290\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn record_delete(&self, key: &str) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m304\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn get_site_cache_dir(&self, site_name: &str) -> Option<std::path::PathBuf> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"struct `CacheManagerBuilder` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\cache\\manager.rs","byte_start":10496,"byte_end":10515,"line_start":318,"line_end":318,"column_start":12,"column_end":31,"is_primary":true,"text":[{"text":"pub struct CacheManagerBuilder {","highlight_start":12,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: struct `CacheManagerBuilder` is never constructed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\cache\\manager.rs:318:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m318\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct CacheManagerBuilder {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"multiple associated items are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\cache\\manager.rs","byte_start":10601,"byte_end":10625,"line_start":323,"line_end":323,"column_start":1,"column_end":25,"is_primary":false,"text":[{"text":"impl CacheManagerBuilder {","highlight_start":1,"highlight_end":25}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\manager.rs","byte_start":10669,"byte_end":10672,"line_start":325,"line_end":325,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new(cache_type: CacheType) -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\manager.rs","byte_start":11481,"byte_end":11490,"line_start":347,"line_end":347,"column_start":12,"column_end":21,"is_primary":true,"text":[{"text":"    pub fn file_path(mut self, path: std::path::PathBuf) -> Self {","highlight_start":12,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\manager.rs","byte_start":11642,"byte_end":11654,"line_start":353,"line_end":353,"column_start":12,"column_end":24,"is_primary":true,"text":[{"text":"    pub fn redis_config(mut self, config: super::RedisConfig) -> Self {","highlight_start":12,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\manager.rs","byte_start":11820,"byte_end":11833,"line_start":359,"line_end":359,"column_start":12,"column_end":25,"is_primary":true,"text":[{"text":"    pub fn memory_config(mut self, config: super::MemoryCacheConfig) -> Self {","highlight_start":12,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\manager.rs","byte_start":11991,"byte_end":12002,"line_start":365,"line_end":365,"column_start":12,"column_end":23,"is_primary":true,"text":[{"text":"    pub fn default_ttl(mut self, ttl: Duration) -> Self {","highlight_start":12,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\manager.rs","byte_start":12145,"byte_end":12153,"line_start":371,"line_end":371,"column_start":12,"column_end":20,"is_primary":true,"text":[{"text":"    pub fn max_size(mut self, size: u64) -> Self {","highlight_start":12,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\manager.rs","byte_start":12284,"byte_end":12300,"line_start":377,"line_end":377,"column_start":12,"column_end":28,"is_primary":true,"text":[{"text":"    pub fn cleanup_interval(mut self, interval: Duration) -> Self {","highlight_start":12,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\manager.rs","byte_start":12464,"byte_end":12477,"line_start":383,"line_end":383,"column_start":12,"column_end":25,"is_primary":true,"text":[{"text":"    pub fn async_cleanup(mut self, enabled: bool) -> Self {","highlight_start":12,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\manager.rs","byte_start":12623,"byte_end":12641,"line_start":389,"line_end":389,"column_start":12,"column_end":30,"is_primary":true,"text":[{"text":"    pub fn add_event_listener(mut self, listener: Arc<dyn CacheEventListener>) -> Self {","highlight_start":12,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\cache\\manager.rs","byte_start":12817,"byte_end":12822,"line_start":395,"line_end":395,"column_start":18,"column_end":23,"is_primary":true,"text":[{"text":"    pub async fn build(self) -> Result<CacheManager> {","highlight_start":18,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: multiple associated items are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\cache\\manager.rs:325:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m323\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl CacheManagerBuilder {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m324\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// 创建新的构建器\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m325\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new(cache_type: CacheType) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m347\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn file_path(mut self, path: std::path::PathBuf) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m353\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn redis_config(mut self, config: super::RedisConfig) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m359\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn memory_config(mut self, config: super::MemoryCacheConfig) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m365\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn default_ttl(mut self, ttl: Duration) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m371\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn max_size(mut self, size: u64) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m377\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn cleanup_interval(mut self, interval: Duration) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m383\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn async_cleanup(mut self, enabled: bool) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m389\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn add_event_listener(mut self, listener: Arc<dyn CacheEventListener>) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m395\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn build(self) -> Result<CacheManager> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\n"}
{"message":"function `parse_size` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\cache\\mod.rs","byte_start":9133,"byte_end":9143,"line_start":364,"line_end":364,"column_start":12,"column_end":22,"is_primary":true,"text":[{"text":"    pub fn parse_size(size_str: &str) -> Result<u64> {","highlight_start":12,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `parse_size` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\cache\\mod.rs:364:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m364\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn parse_size(size_str: &str) -> Result<u64> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"message":"function `format_size` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\cache\\mod.rs","byte_start":10040,"byte_end":10051,"line_start":390,"line_end":390,"column_start":12,"column_end":23,"is_primary":true,"text":[{"text":"    pub fn format_size(size: u64) -> String {","highlight_start":12,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `format_size` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\cache\\mod.rs:390:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m390\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn format_size(size: u64) -> String {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"function `should_cache_content_type` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\cache\\mod.rs","byte_start":10744,"byte_end":10769,"line_start":414,"line_end":414,"column_start":12,"column_end":37,"is_primary":true,"text":[{"text":"    pub fn should_cache_content_type(content_type: &str, allowed_types: &[String]) -> bool {","highlight_start":12,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `should_cache_content_type` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\cache\\mod.rs:414:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m414\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn should_cache_content_type(content_type: &str, allowed_types: &[String]) -> b\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"methods `validate_certificates`, `reload_certificates`, and `get_config_info` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\ssl\\mod.rs","byte_start":616,"byte_end":631,"line_start":28,"line_end":28,"column_start":1,"column_end":16,"is_primary":false,"text":[{"text":"impl SslManager {","highlight_start":1,"highlight_end":16}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\ssl\\mod.rs","byte_start":6175,"byte_end":6196,"line_start":187,"line_end":187,"column_start":12,"column_end":33,"is_primary":true,"text":[{"text":"    pub fn validate_certificates(&self) -> Result<()> {","highlight_start":12,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\ssl\\mod.rs","byte_start":7194,"byte_end":7213,"line_start":218,"line_end":218,"column_start":12,"column_end":31,"is_primary":true,"text":[{"text":"    pub fn reload_certificates(&mut self) -> Result<()> {","highlight_start":12,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\ssl\\mod.rs","byte_start":7798,"byte_end":7813,"line_start":241,"line_end":241,"column_start":12,"column_end":27,"is_primary":true,"text":[{"text":"    pub fn get_config_info(&self) -> SslConfigInfo {","highlight_start":12,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: methods `validate_certificates`, `reload_certificates`, and `get_config_info` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\ssl\\mod.rs:187:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m28\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl SslManager {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m187\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn validate_certificates(&self) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m218\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn reload_certificates(&mut self) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m241\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn get_config_info(&self) -> SslConfigInfo {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"methods `handshake_success_rate` and `error_rate` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\ssl\\mod.rs","byte_start":9077,"byte_end":9090,"line_start":281,"line_end":281,"column_start":1,"column_end":14,"is_primary":false,"text":[{"text":"impl SslStats {","highlight_start":1,"highlight_end":14}],"label":"methods in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\ssl\\mod.rs","byte_start":9134,"byte_end":9156,"line_start":283,"line_end":283,"column_start":12,"column_end":34,"is_primary":true,"text":[{"text":"    pub fn handshake_success_rate(&self) -> f64 {","highlight_start":12,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\ssl\\mod.rs","byte_start":9385,"byte_end":9395,"line_start":292,"line_end":292,"column_start":12,"column_end":22,"is_primary":true,"text":[{"text":"    pub fn error_rate(&self) -> f64 {","highlight_start":12,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: methods `handshake_success_rate` and `error_rate` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\ssl\\mod.rs:283:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m281\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl SslStats {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mmethods in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m282\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// 计算握手成功率\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m283\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn handshake_success_rate(&self) -> f64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m292\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn error_rate(&self) -> f64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"message":"struct `MonitorManager` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\mod.rs","byte_start":414,"byte_end":428,"line_start":18,"line_end":18,"column_start":12,"column_end":26,"is_primary":true,"text":[{"text":"pub struct MonitorManager {","highlight_start":12,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: struct `MonitorManager` is never constructed\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\mod.rs:18:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct MonitorManager {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"multiple associated items are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\mod.rs","byte_start":682,"byte_end":701,"line_start":27,"line_end":27,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"impl MonitorManager {","highlight_start":1,"highlight_end":20}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\mod.rs","byte_start":757,"byte_end":760,"line_start":29,"line_end":29,"column_start":18,"column_end":21,"is_primary":true,"text":[{"text":"    pub async fn new(config: MonitorConfig) -> Result<Self> {","highlight_start":18,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\mod.rs","byte_start":1537,"byte_end":1551,"line_start":54,"line_end":54,"column_start":18,"column_end":32,"is_primary":true,"text":[{"text":"    pub async fn record_request(&self, ctx: &RequestContext) -> Result<()> {","highlight_start":18,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\mod.rs","byte_start":1896,"byte_end":1908,"line_start":68,"line_end":68,"column_start":14,"column_end":26,"is_primary":true,"text":[{"text":"    async fn update_stats(&self, ctx: &RequestContext) -> Result<()> {","highlight_start":14,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\mod.rs","byte_start":3556,"byte_end":3565,"line_start":116,"line_end":116,"column_start":18,"column_end":27,"is_primary":true,"text":[{"text":"    pub async fn get_stats(&self) -> SystemStats {","highlight_start":18,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\mod.rs","byte_start":4061,"byte_end":4078,"line_start":132,"line_end":132,"column_start":18,"column_end":35,"is_primary":true,"text":[{"text":"    pub async fn get_health_status(&self) -> Result<HealthStatus> {","highlight_start":18,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\mod.rs","byte_start":4212,"byte_end":4223,"line_start":137,"line_end":137,"column_start":18,"column_end":29,"is_primary":true,"text":[{"text":"    pub async fn get_metrics(&self) -> Result<HashMap<String, f64>> {","highlight_start":18,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\mod.rs","byte_start":4367,"byte_end":4383,"line_start":142,"line_end":142,"column_start":18,"column_end":34,"is_primary":true,"text":[{"text":"    pub async fn start_monitoring(&self) -> Result<()> {","highlight_start":18,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\mod.rs","byte_start":5532,"byte_end":5536,"line_start":177,"line_end":177,"column_start":18,"column_end":22,"is_primary":true,"text":[{"text":"    pub async fn stop(&self) -> Result<()> {","highlight_start":18,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\mod.rs","byte_start":5802,"byte_end":5817,"line_start":188,"line_end":188,"column_start":12,"column_end":27,"is_primary":true,"text":[{"text":"    pub fn get_config_info(&self) -> MonitorConfigInfo {","highlight_start":12,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: multiple associated items are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\mod.rs:29:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m27\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl MonitorManager {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m28\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// 创建新的监控管理器\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m29\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn new(config: MonitorConfig) -> Result<Self> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m54\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn record_request(&self, ctx: &RequestContext) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m68\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn update_stats(&self, ctx: &RequestContext) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m116\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn get_stats(&self) -> SystemStats {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m132\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn get_health_status(&self) -> Result<HealthStatus> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m137\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn get_metrics(&self) -> Result<HashMap<String, f64>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m142\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn start_monitoring(&self) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m177\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn stop(&self) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m188\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn get_config_info(&self) -> MonitorConfigInfo {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"associated items `new`, `cleanup_old_data`, `success_rate`, and `error_rate` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\mod.rs","byte_start":6800,"byte_end":6816,"line_start":220,"line_end":220,"column_start":1,"column_end":17,"is_primary":false,"text":[{"text":"impl SystemStats {","highlight_start":1,"highlight_end":17}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\mod.rs","byte_start":6830,"byte_end":6833,"line_start":221,"line_end":221,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new() -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\mod.rs","byte_start":7552,"byte_end":7568,"line_start":242,"line_end":242,"column_start":12,"column_end":28,"is_primary":true,"text":[{"text":"    pub fn cleanup_old_data(&mut self) {","highlight_start":12,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\mod.rs","byte_start":7795,"byte_end":7807,"line_start":250,"line_end":250,"column_start":12,"column_end":24,"is_primary":true,"text":[{"text":"    pub fn success_rate(&self) -> f64 {","highlight_start":12,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\mod.rs","byte_start":8028,"byte_end":8038,"line_start":259,"line_end":259,"column_start":12,"column_end":22,"is_primary":true,"text":[{"text":"    pub fn error_rate(&self) -> f64 {","highlight_start":12,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: associated items `new`, `cleanup_old_data`, `success_rate`, and `error_rate` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\mod.rs:221:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m220\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl SystemStats {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m221\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m242\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn cleanup_old_data(&mut self) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m250\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn success_rate(&self) -> f64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m259\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn error_rate(&self) -> f64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"message":"associated items `new` and `error_rate` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\mod.rs","byte_start":8439,"byte_end":8453,"line_start":277,"line_end":277,"column_start":1,"column_end":15,"is_primary":false,"text":[{"text":"impl SiteStats {","highlight_start":1,"highlight_end":15}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\mod.rs","byte_start":8467,"byte_end":8470,"line_start":278,"line_end":278,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new() -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\mod.rs","byte_start":8678,"byte_end":8688,"line_start":287,"line_end":287,"column_start":12,"column_end":22,"is_primary":true,"text":[{"text":"    pub fn error_rate(&self) -> f64 {","highlight_start":12,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: associated items `new` and `error_rate` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\mod.rs:278:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m277\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl SiteStats {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m278\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m287\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn error_rate(&self) -> f64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"message":"struct `AccessLogger` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\access_log.rs","byte_start":444,"byte_end":456,"line_start":17,"line_end":17,"column_start":12,"column_end":24,"is_primary":true,"text":[{"text":"pub struct AccessLogger {","highlight_start":12,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: struct `AccessLogger` is never constructed\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\access_log.rs:17:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct AccessLogger {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"associated items `new`, `log_request`, `flush`, and `get_stats` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\access_log.rs","byte_start":570,"byte_end":587,"line_start":23,"line_end":23,"column_start":1,"column_end":18,"is_primary":false,"text":[{"text":"impl AccessLogger {","highlight_start":1,"highlight_end":18}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\access_log.rs","byte_start":649,"byte_end":652,"line_start":25,"line_end":25,"column_start":18,"column_end":21,"is_primary":true,"text":[{"text":"    pub async fn new(monitor_config: &MonitorConfig) -> Result<Self> {","highlight_start":18,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\access_log.rs","byte_start":2827,"byte_end":2838,"line_start":76,"line_end":76,"column_start":18,"column_end":29,"is_primary":true,"text":[{"text":"    pub async fn log_request(&self, ctx: &RequestContext) -> Result<()> {","highlight_start":18,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\access_log.rs","byte_start":3391,"byte_end":3396,"line_start":94,"line_end":94,"column_start":18,"column_end":23,"is_primary":true,"text":[{"text":"    pub async fn flush(&self) -> Result<()> {","highlight_start":18,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\access_log.rs","byte_start":3592,"byte_end":3601,"line_start":101,"line_end":101,"column_start":18,"column_end":27,"is_primary":true,"text":[{"text":"    pub async fn get_stats(&self) -> AccessLogStats {","highlight_start":18,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: associated items `new`, `log_request`, `flush`, and `get_stats` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\access_log.rs:25:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m23\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl AccessLogger {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m24\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// 创建新的访问日志记录器\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m25\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn new(monitor_config: &MonitorConfig) -> Result<Self> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m76\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn log_request(&self, ctx: &RequestContext) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m94\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn flush(&self) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m101\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn get_stats(&self) -> AccessLogStats {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\n"}
{"message":"associated items `from_context` and `format` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\access_log.rs","byte_start":4149,"byte_end":4162,"line_start":124,"line_end":124,"column_start":1,"column_end":14,"is_primary":false,"text":[{"text":"impl LogEntry {","highlight_start":1,"highlight_end":14}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\access_log.rs","byte_start":4221,"byte_end":4233,"line_start":126,"line_end":126,"column_start":12,"column_end":24,"is_primary":true,"text":[{"text":"    pub fn from_context(ctx: &RequestContext) -> Self {","highlight_start":12,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\access_log.rs","byte_start":5153,"byte_end":5159,"line_start":150,"line_end":150,"column_start":12,"column_end":18,"is_primary":true,"text":[{"text":"    pub fn format(&self, format_str: &str) -> String {","highlight_start":12,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: associated items `from_context` and `format` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\access_log.rs:126:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m124\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl LogEntry {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m125\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// 从请求上下文创建日志条目\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m126\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn from_context(ctx: &RequestContext) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m150\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn format(&self, format_str: &str) -> String {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\n"}
{"message":"struct `LogWriter` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\access_log.rs","byte_start":6203,"byte_end":6212,"line_start":170,"line_end":170,"column_start":8,"column_end":17,"is_primary":true,"text":[{"text":"struct LogWriter {","highlight_start":8,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: struct `LogWriter` is never constructed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\access_log.rs:170:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m170\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mstruct LogWriter {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\n"}
{"message":"associated items `new`, `run`, `flush_buffer`, and `write_to_file` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\access_log.rs","byte_start":6393,"byte_end":6407,"line_start":178,"line_end":178,"column_start":1,"column_end":15,"is_primary":false,"text":[{"text":"impl LogWriter {","highlight_start":1,"highlight_end":15}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\access_log.rs","byte_start":6423,"byte_end":6426,"line_start":179,"line_end":179,"column_start":14,"column_end":17,"is_primary":true,"text":[{"text":"    async fn new(","highlight_start":14,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\access_log.rs","byte_start":6769,"byte_end":6772,"line_start":193,"line_end":193,"column_start":14,"column_end":17,"is_primary":true,"text":[{"text":"    async fn run(mut self) -> Result<()> {","highlight_start":14,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\access_log.rs","byte_start":7993,"byte_end":8005,"line_start":229,"line_end":229,"column_start":14,"column_end":26,"is_primary":true,"text":[{"text":"    async fn flush_buffer(&mut self) -> Result<()> {","highlight_start":14,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\access_log.rs","byte_start":8640,"byte_end":8653,"line_start":252,"line_end":252,"column_start":14,"column_end":27,"is_primary":true,"text":[{"text":"    async fn write_to_file(&self, file_path: &str) -> Result<()> {","highlight_start":14,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: associated items `new`, `run`, `flush_buffer`, and `write_to_file` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\access_log.rs:179:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m178\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl LogWriter {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m179\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn new(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m193\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn run(mut self) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m229\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn flush_buffer(&mut self) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m252\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn write_to_file(&self, file_path: &str) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"associated items `new`, `write_success_rate`, and `drop_rate` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\access_log.rs","byte_start":9604,"byte_end":9623,"line_start":290,"line_end":290,"column_start":1,"column_end":20,"is_primary":false,"text":[{"text":"impl AccessLogStats {","highlight_start":1,"highlight_end":20}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\access_log.rs","byte_start":9637,"byte_end":9640,"line_start":291,"line_end":291,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new() -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\access_log.rs","byte_start":9956,"byte_end":9974,"line_start":303,"line_end":303,"column_start":12,"column_end":30,"is_primary":true,"text":[{"text":"    pub fn write_success_rate(&self) -> f64 {","highlight_start":12,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\access_log.rs","byte_start":10189,"byte_end":10198,"line_start":312,"line_end":312,"column_start":12,"column_end":21,"is_primary":true,"text":[{"text":"    pub fn drop_rate(&self) -> f64 {","highlight_start":12,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: associated items `new`, `write_success_rate`, and `drop_rate` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\access_log.rs:291:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m290\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl AccessLogStats {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m291\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m303\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn write_success_rate(&self) -> f64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m312\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn drop_rate(&self) -> f64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\n"}
{"message":"struct `HealthChecker` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\health_check.rs","byte_start":379,"byte_end":392,"line_start":14,"line_end":14,"column_start":12,"column_end":25,"is_primary":true,"text":[{"text":"pub struct HealthChecker {","highlight_start":12,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: struct `HealthChecker` is never constructed\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\health_check.rs:14:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct HealthChecker {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"multiple associated items are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\health_check.rs","byte_start":508,"byte_end":526,"line_start":20,"line_end":20,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"impl HealthChecker {","highlight_start":1,"highlight_end":19}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\health_check.rs","byte_start":582,"byte_end":585,"line_start":22,"line_end":22,"column_start":18,"column_end":21,"is_primary":true,"text":[{"text":"    pub async fn new(config: &MonitorConfig) -> Result<Self> {","highlight_start":18,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\health_check.rs","byte_start":927,"byte_end":939,"line_start":35,"line_end":35,"column_start":18,"column_end":30,"is_primary":true,"text":[{"text":"    pub async fn check_health(&self) -> Result<HealthStatus> {","highlight_start":18,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\health_check.rs","byte_start":3233,"byte_end":3254,"line_start":104,"line_end":104,"column_start":18,"column_end":39,"is_primary":true,"text":[{"text":"    pub async fn start_periodic_checks(&self) -> Result<()> {","highlight_start":18,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\health_check.rs","byte_start":4330,"byte_end":4349,"line_start":134,"line_end":134,"column_start":14,"column_end":33,"is_primary":true,"text":[{"text":"    async fn check_system_health(&self) -> HealthCheck {","highlight_start":14,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\health_check.rs","byte_start":4955,"byte_end":4974,"line_start":153,"line_end":153,"column_start":14,"column_end":33,"is_primary":true,"text":[{"text":"    async fn check_memory_health(&self) -> HealthCheck {","highlight_start":14,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\health_check.rs","byte_start":6019,"byte_end":6036,"line_start":182,"line_end":182,"column_start":14,"column_end":31,"is_primary":true,"text":[{"text":"    async fn check_disk_health(&self) -> HealthCheck {","highlight_start":14,"highlight_end":31}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\health_check.rs","byte_start":7065,"byte_end":7085,"line_start":211,"line_end":211,"column_start":14,"column_end":34,"is_primary":true,"text":[{"text":"    async fn check_network_health(&self) -> HealthCheck {","highlight_start":14,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\health_check.rs","byte_start":7680,"byte_end":7701,"line_start":230,"line_end":230,"column_start":14,"column_end":35,"is_primary":true,"text":[{"text":"    async fn check_upstream_health(&self) -> HealthCheck {","highlight_start":14,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\health_check.rs","byte_start":8645,"byte_end":8661,"line_start":257,"line_end":257,"column_start":14,"column_end":30,"is_primary":true,"text":[{"text":"    async fn get_memory_usage(&self) -> Result<f64> {","highlight_start":14,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\health_check.rs","byte_start":8847,"byte_end":8861,"line_start":263,"line_end":263,"column_start":14,"column_end":28,"is_primary":true,"text":[{"text":"    async fn get_disk_usage(&self) -> Result<f64> {","highlight_start":14,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\health_check.rs","byte_start":9041,"byte_end":9062,"line_start":269,"line_end":269,"column_start":14,"column_end":35,"is_primary":true,"text":[{"text":"    async fn ping_upstream_servers(&self) -> Result<usize> {","highlight_start":14,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\health_check.rs","byte_start":9268,"byte_end":9286,"line_start":275,"line_end":275,"column_start":18,"column_end":36,"is_primary":true,"text":[{"text":"    pub async fn get_cached_results(&self) -> HashMap<String, CheckResult> {","highlight_start":18,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: multiple associated items are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\health_check.rs:22:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m20\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl HealthChecker {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m21\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// 创建新的健康检查器\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m22\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn new(config: &MonitorConfig) -> Result<Self> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m35\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn check_health(&self) -> Result<HealthStatus> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m104\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn start_periodic_checks(&self) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m134\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn check_system_health(&self) -> HealthCheck {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m153\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn check_memory_health(&self) -> HealthCheck {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m182\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn check_disk_health(&self) -> HealthCheck {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m211\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn check_network_health(&self) -> HealthCheck {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m230\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn check_upstream_health(&self) -> HealthCheck {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m257\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_memory_usage(&self) -> Result<f64> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m263\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_disk_usage(&self) -> Result<f64> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m269\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn ping_upstream_servers(&self) -> Result<usize> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m275\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn get_cached_results(&self) -> HashMap<String, CheckResult> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"struct `MetricsCollector` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\metrics.rs","byte_start":439,"byte_end":455,"line_start":15,"line_end":15,"column_start":12,"column_end":28,"is_primary":true,"text":[{"text":"pub struct MetricsCollector {","highlight_start":12,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: struct `MetricsCollector` is never constructed\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\metrics.rs:15:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct MetricsCollector {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"multiple associated items are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\metrics.rs","byte_start":559,"byte_end":580,"line_start":21,"line_end":21,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"impl MetricsCollector {","highlight_start":1,"highlight_end":22}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\metrics.rs","byte_start":636,"byte_end":639,"line_start":23,"line_end":23,"column_start":18,"column_end":21,"is_primary":true,"text":[{"text":"    pub async fn new(config: &MonitorConfig) -> Result<Self> {","highlight_start":18,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\metrics.rs","byte_start":994,"byte_end":1008,"line_start":36,"line_end":36,"column_start":18,"column_end":32,"is_primary":true,"text":[{"text":"    pub async fn record_request(&self, ctx: &RequestContext) -> Result<()> {","highlight_start":18,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\metrics.rs","byte_start":2756,"byte_end":2772,"line_start":78,"line_end":78,"column_start":18,"column_end":34,"is_primary":true,"text":[{"text":"    pub async fn start_collection(&self) -> Result<()> {","highlight_start":18,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\metrics.rs","byte_start":3213,"byte_end":3235,"line_start":93,"line_end":93,"column_start":14,"column_end":36,"is_primary":true,"text":[{"text":"    async fn collect_system_metrics(&self) -> Result<()> {","highlight_start":14,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\metrics.rs","byte_start":4202,"byte_end":4213,"line_start":122,"line_end":122,"column_start":18,"column_end":29,"is_primary":true,"text":[{"text":"    pub async fn get_metrics(&self) -> Result<HashMap<String, f64>> {","highlight_start":18,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\metrics.rs","byte_start":6082,"byte_end":6104,"line_start":157,"line_end":157,"column_start":18,"column_end":40,"is_primary":true,"text":[{"text":"    pub async fn get_prometheus_metrics(&self) -> Result<String> {","highlight_start":18,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\metrics.rs","byte_start":6483,"byte_end":6496,"line_start":170,"line_end":170,"column_start":14,"column_end":27,"is_primary":true,"text":[{"text":"    async fn get_cpu_usage(&self) -> Result<f64> {","highlight_start":14,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\metrics.rs","byte_start":6676,"byte_end":6692,"line_start":176,"line_end":176,"column_start":14,"column_end":30,"is_primary":true,"text":[{"text":"    async fn get_memory_usage(&self) -> Result<f64> {","highlight_start":14,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\metrics.rs","byte_start":6878,"byte_end":6892,"line_start":182,"line_end":182,"column_start":14,"column_end":28,"is_primary":true,"text":[{"text":"    async fn get_disk_usage(&self) -> Result<f64> {","highlight_start":14,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\metrics.rs","byte_start":7066,"byte_end":7080,"line_start":188,"line_end":188,"column_start":14,"column_end":28,"is_primary":true,"text":[{"text":"    async fn get_network_io(&self) -> Result<(u64, u64)> {","highlight_start":14,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: multiple associated items are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\metrics.rs:23:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m21\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl MetricsCollector {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m22\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// 创建新的指标收集器\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m23\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn new(config: &MonitorConfig) -> Result<Self> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m36\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn record_request(&self, ctx: &RequestContext) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m78\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn start_collection(&self) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m93\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn collect_system_metrics(&self) -> Result<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m122\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn get_metrics(&self) -> Result<HashMap<String, f64>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m157\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn get_prometheus_metrics(&self) -> Result<String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m170\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_cpu_usage(&self) -> Result<f64> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m176\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_memory_usage(&self) -> Result<f64> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m182\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_disk_usage(&self) -> Result<f64> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m188\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_network_io(&self) -> Result<(u64, u64)> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"associated function `new` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\metrics.rs","byte_start":7948,"byte_end":7968,"line_start":219,"line_end":219,"column_start":1,"column_end":21,"is_primary":false,"text":[{"text":"impl MetricsRegistry {","highlight_start":1,"highlight_end":21}],"label":"associated function in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\metrics.rs","byte_start":7982,"byte_end":7985,"line_start":220,"line_end":220,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new() -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: associated function `new` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\metrics.rs:220:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m219\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl MetricsRegistry {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14massociated function in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m220\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\n"}
{"message":"struct `MetricsExporter` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\metrics.rs","byte_start":8843,"byte_end":8858,"line_start":244,"line_end":244,"column_start":12,"column_end":27,"is_primary":true,"text":[{"text":"pub struct MetricsExporter {","highlight_start":12,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: struct `MetricsExporter` is never constructed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\metrics.rs:244:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m244\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct MetricsExporter {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"associated items `new`, `export_json`, `export_prometheus`, and `export_csv` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\metrics.rs","byte_start":8902,"byte_end":8922,"line_start":248,"line_end":248,"column_start":1,"column_end":21,"is_primary":false,"text":[{"text":"impl MetricsExporter {","highlight_start":1,"highlight_end":21}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\metrics.rs","byte_start":8936,"byte_end":8939,"line_start":249,"line_end":249,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new(collector: Arc<MetricsCollector>) -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\metrics.rs","byte_start":9069,"byte_end":9080,"line_start":254,"line_end":254,"column_start":18,"column_end":29,"is_primary":true,"text":[{"text":"    pub async fn export_json(&self) -> Result<String> {","highlight_start":18,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\metrics.rs","byte_start":9283,"byte_end":9300,"line_start":260,"line_end":260,"column_start":18,"column_end":35,"is_primary":true,"text":[{"text":"    pub async fn export_prometheus(&self) -> Result<String> {","highlight_start":18,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\metrics.rs","byte_start":9439,"byte_end":9449,"line_start":265,"line_end":265,"column_start":18,"column_end":28,"is_primary":true,"text":[{"text":"    pub async fn export_csv(&self) -> Result<String> {","highlight_start":18,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: associated items `new`, `export_json`, `export_prometheus`, and `export_csv` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\metrics.rs:249:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m248\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl MetricsExporter {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m249\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new(collector: Arc<MetricsCollector>) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m254\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn export_json(&self) -> Result<String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m260\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn export_prometheus(&self) -> Result<String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m265\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn export_csv(&self) -> Result<String> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"message":"fields `values`, `max_size`, and `sum` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\stats.rs","byte_start":145,"byte_end":158,"line_start":6,"line_end":6,"column_start":12,"column_end":25,"is_primary":false,"text":[{"text":"pub struct MovingAverage {","highlight_start":12,"highlight_end":25}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":165,"byte_end":171,"line_start":7,"line_end":7,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    values: VecDeque<f64>,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":192,"byte_end":200,"line_start":8,"line_end":8,"column_start":5,"column_end":13,"is_primary":true,"text":[{"text":"    max_size: usize,","highlight_start":5,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":213,"byte_end":216,"line_start":9,"line_end":9,"column_start":5,"column_end":8,"is_primary":true,"text":[{"text":"    sum: f64,","highlight_start":5,"highlight_end":8}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`MovingAverage` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: fields `values`, `max_size`, and `sum` are never read\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\stats.rs:7:5\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m6\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct MovingAverage {\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m7\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    values: VecDeque<f64>,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    max_size: usize,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    sum: f64,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `MovingAverage` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"message":"multiple associated items are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\stats.rs","byte_start":226,"byte_end":244,"line_start":12,"line_end":12,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"impl MovingAverage {","highlight_start":1,"highlight_end":19}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":303,"byte_end":306,"line_start":14,"line_end":14,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new(max_size: usize) -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":506,"byte_end":516,"line_start":23,"line_end":23,"column_start":12,"column_end":22,"is_primary":true,"text":[{"text":"    pub fn add_sample(&mut self, value: f64) {","highlight_start":12,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":832,"byte_end":839,"line_start":35,"line_end":35,"column_start":12,"column_end":19,"is_primary":true,"text":[{"text":"    pub fn average(&self) -> f64 {","highlight_start":12,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":1025,"byte_end":1028,"line_start":44,"line_end":44,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn min(&self) -> f64 {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":1152,"byte_end":1155,"line_start":49,"line_end":49,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn max(&self) -> f64 {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":1286,"byte_end":1291,"line_start":54,"line_end":54,"column_start":12,"column_end":17,"is_primary":true,"text":[{"text":"    pub fn count(&self) -> usize {","highlight_start":12,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":1378,"byte_end":1385,"line_start":59,"line_end":59,"column_start":12,"column_end":19,"is_primary":true,"text":[{"text":"    pub fn cleanup(&mut self) {","highlight_start":12,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":1532,"byte_end":1542,"line_start":64,"line_end":64,"column_start":12,"column_end":22,"is_primary":true,"text":[{"text":"    pub fn percentile(&self, p: f64) -> f64 {","highlight_start":12,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: multiple associated items are never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\stats.rs:14:12\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl MovingAverage {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// 创建新的移动平均值计算器\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new(max_size: usize) -> Self {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m23\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn add_sample(&mut self, value: f64) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m35\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn average(&self) -> f64 {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m44\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn min(&self) -> f64 {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m49\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn max(&self) -> f64 {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn count(&self) -> usize {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m59\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn cleanup(&mut self) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m64\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn percentile(&self, p: f64) -> f64 {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"message":"fields `samples` and `window` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\stats.rs","byte_start":1957,"byte_end":1968,"line_start":79,"line_end":79,"column_start":12,"column_end":23,"is_primary":false,"text":[{"text":"pub struct RateCounter {","highlight_start":12,"highlight_end":23}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":1975,"byte_end":1982,"line_start":80,"line_end":80,"column_start":5,"column_end":12,"is_primary":true,"text":[{"text":"    samples: VecDeque<(Instant, f64)>,","highlight_start":5,"highlight_end":12}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":2014,"byte_end":2020,"line_start":81,"line_end":81,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    window: Duration,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`RateCounter` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: fields `samples` and `window` are never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\stats.rs:80:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m79\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct RateCounter {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m80\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    samples: VecDeque<(Instant, f64)>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m81\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    window: Duration,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `RateCounter` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"message":"associated items `new`, `add_sample`, `current_rate`, `cleanup_old_samples`, `cleanup`, and `count` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\stats.rs","byte_start":2035,"byte_end":2051,"line_start":84,"line_end":84,"column_start":1,"column_end":17,"is_primary":false,"text":[{"text":"impl RateCounter {","highlight_start":1,"highlight_end":17}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":2101,"byte_end":2104,"line_start":86,"line_end":86,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new(window: Duration) -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":2255,"byte_end":2265,"line_start":94,"line_end":94,"column_start":12,"column_end":22,"is_primary":true,"text":[{"text":"    pub fn add_sample(&mut self, value: f64) {","highlight_start":12,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":2455,"byte_end":2467,"line_start":101,"line_end":101,"column_start":12,"column_end":24,"is_primary":true,"text":[{"text":"    pub fn current_rate(&self) -> f64 {","highlight_start":12,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":2891,"byte_end":2910,"line_start":119,"line_end":119,"column_start":8,"column_end":27,"is_primary":true,"text":[{"text":"    fn cleanup_old_samples(&mut self, now: Instant) {","highlight_start":8,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":3231,"byte_end":3238,"line_start":131,"line_end":131,"column_start":12,"column_end":19,"is_primary":true,"text":[{"text":"    pub fn cleanup(&mut self) {","highlight_start":12,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":3370,"byte_end":3375,"line_start":137,"line_end":137,"column_start":12,"column_end":17,"is_primary":true,"text":[{"text":"    pub fn count(&self) -> usize {","highlight_start":12,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: associated items `new`, `add_sample`, `current_rate`, `cleanup_old_samples`, `cleanup`, and `count` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\stats.rs:86:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m84\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl RateCounter {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m85\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// 创建新的速率计数器\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m86\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new(window: Duration) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m94\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn add_sample(&mut self, value: f64) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m101\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn current_rate(&self) -> f64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m119\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn cleanup_old_samples(&mut self, now: Instant) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m131\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn cleanup(&mut self) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m137\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn count(&self) -> usize {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\n"}
{"message":"fields `buckets`, `total_count`, and `sum` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\stats.rs","byte_start":3485,"byte_end":3494,"line_start":144,"line_end":144,"column_start":12,"column_end":21,"is_primary":false,"text":[{"text":"pub struct Histogram {","highlight_start":12,"highlight_end":21}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":3501,"byte_end":3508,"line_start":145,"line_end":145,"column_start":5,"column_end":12,"is_primary":true,"text":[{"text":"    buckets: Vec<(f64, u64)>, // (上界, 计数)","highlight_start":5,"highlight_end":12}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":3551,"byte_end":3562,"line_start":146,"line_end":146,"column_start":5,"column_end":16,"is_primary":true,"text":[{"text":"    total_count: u64,","highlight_start":5,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":3573,"byte_end":3576,"line_start":147,"line_end":147,"column_start":5,"column_end":8,"is_primary":true,"text":[{"text":"    sum: f64,","highlight_start":5,"highlight_end":8}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`Histogram` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: fields `buckets`, `total_count`, and `sum` are never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\stats.rs:145:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m144\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct Histogram {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m145\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    buckets: Vec<(f64, u64)>, // (上界, 计数)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m146\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    total_count: u64,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m147\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    sum: f64,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `Histogram` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"message":"multiple associated items are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\stats.rs","byte_start":3586,"byte_end":3600,"line_start":150,"line_end":150,"column_start":1,"column_end":15,"is_primary":false,"text":[{"text":"impl Histogram {","highlight_start":1,"highlight_end":15}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":3644,"byte_end":3647,"line_start":152,"line_end":152,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new(buckets: Vec<f64>) -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":3988,"byte_end":4011,"line_start":166,"line_end":166,"column_start":12,"column_end":35,"is_primary":true,"text":[{"text":"    pub fn response_time_histogram() -> Self {","highlight_start":12,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":4194,"byte_end":4201,"line_start":173,"line_end":173,"column_start":12,"column_end":19,"is_primary":true,"text":[{"text":"    pub fn observe(&mut self, value: f64) {","highlight_start":12,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":4478,"byte_end":4483,"line_start":185,"line_end":185,"column_start":12,"column_end":17,"is_primary":true,"text":[{"text":"    pub fn count(&self) -> u64 {","highlight_start":12,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":4564,"byte_end":4567,"line_start":190,"line_end":190,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn sum(&self) -> f64 {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":4643,"byte_end":4650,"line_start":195,"line_end":195,"column_start":12,"column_end":19,"is_primary":true,"text":[{"text":"    pub fn average(&self) -> f64 {","highlight_start":12,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":4843,"byte_end":4851,"line_start":204,"line_end":204,"column_start":12,"column_end":20,"is_primary":true,"text":[{"text":"    pub fn quantile(&self, q: f64) -> f64 {","highlight_start":12,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":5398,"byte_end":5405,"line_start":224,"line_end":224,"column_start":12,"column_end":19,"is_primary":true,"text":[{"text":"    pub fn buckets(&self) -> &[(f64, u64)] {","highlight_start":12,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: multiple associated items are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\stats.rs:152:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m150\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl Histogram {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m151\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// 创建新的直方图\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m152\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new(buckets: Vec<f64>) -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m166\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn response_time_histogram() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m173\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn observe(&mut self, value: f64) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m185\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn count(&self) -> u64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m190\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn sum(&self) -> f64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m195\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn average(&self) -> f64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m204\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn quantile(&self, q: f64) -> f64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m224\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn buckets(&self) -> &[(f64, u64)] {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^\u001b[0m\n\n"}
{"message":"fields `value` and `last_reset` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\stats.rs","byte_start":5512,"byte_end":5519,"line_start":231,"line_end":231,"column_start":12,"column_end":19,"is_primary":false,"text":[{"text":"pub struct Counter {","highlight_start":12,"highlight_end":19}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":5526,"byte_end":5531,"line_start":232,"line_end":232,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    value: u64,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":5542,"byte_end":5552,"line_start":233,"line_end":233,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    last_reset: SystemTime,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`Counter` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: fields `value` and `last_reset` are never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\stats.rs:232:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m231\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct Counter {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m232\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    value: u64,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m233\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    last_reset: SystemTime,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `Counter` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"message":"associated items `new`, `increment`, `add`, `value`, `reset`, and `last_reset` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\stats.rs","byte_start":5569,"byte_end":5581,"line_start":236,"line_end":236,"column_start":1,"column_end":13,"is_primary":false,"text":[{"text":"impl Counter {","highlight_start":1,"highlight_end":13}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":5625,"byte_end":5628,"line_start":238,"line_end":238,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new() -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":5770,"byte_end":5779,"line_start":246,"line_end":246,"column_start":12,"column_end":21,"is_primary":true,"text":[{"text":"    pub fn increment(&mut self) {","highlight_start":12,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":5863,"byte_end":5866,"line_start":251,"line_end":251,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn add(&mut self, amount: u64) {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":5965,"byte_end":5970,"line_start":256,"line_end":256,"column_start":12,"column_end":17,"is_primary":true,"text":[{"text":"    pub fn value(&self) -> u64 {","highlight_start":12,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":6048,"byte_end":6053,"line_start":261,"line_end":261,"column_start":12,"column_end":17,"is_primary":true,"text":[{"text":"    pub fn reset(&mut self) {","highlight_start":12,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":6187,"byte_end":6197,"line_start":267,"line_end":267,"column_start":12,"column_end":22,"is_primary":true,"text":[{"text":"    pub fn last_reset(&self) -> SystemTime {","highlight_start":12,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: associated items `new`, `increment`, `add`, `value`, `reset`, and `last_reset` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\stats.rs:238:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m236\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl Counter {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m237\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// 创建新的计数器\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m238\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m246\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn increment(&mut self) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m251\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn add(&mut self, amount: u64) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m256\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn value(&self) -> u64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m261\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn reset(&mut self) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m267\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn last_reset(&self) -> SystemTime {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\n"}
{"message":"fields `value` and `last_updated` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\stats.rs","byte_start":6324,"byte_end":6329,"line_start":274,"line_end":274,"column_start":12,"column_end":17,"is_primary":false,"text":[{"text":"pub struct Gauge {","highlight_start":12,"highlight_end":17}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":6336,"byte_end":6341,"line_start":275,"line_end":275,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    value: f64,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":6352,"byte_end":6364,"line_start":276,"line_end":276,"column_start":5,"column_end":17,"is_primary":true,"text":[{"text":"    last_updated: SystemTime,","highlight_start":5,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`Gauge` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: fields `value` and `last_updated` are never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\stats.rs:275:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m274\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct Gauge {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m-----\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m275\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    value: f64,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m276\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    last_updated: SystemTime,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `Gauge` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"message":"associated items `new`, `set`, `add`, `sub`, `value`, and `last_updated` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\monitor\\stats.rs","byte_start":6381,"byte_end":6391,"line_start":279,"line_end":279,"column_start":1,"column_end":11,"is_primary":false,"text":[{"text":"impl Gauge {","highlight_start":1,"highlight_end":11}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":6435,"byte_end":6438,"line_start":281,"line_end":281,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn new() -> Self {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":6581,"byte_end":6584,"line_start":289,"line_end":289,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn set(&mut self, value: f64) {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":6721,"byte_end":6724,"line_start":295,"line_end":295,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn add(&mut self, delta: f64) {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":6862,"byte_end":6865,"line_start":301,"line_end":301,"column_start":12,"column_end":15,"is_primary":true,"text":[{"text":"    pub fn sub(&mut self, delta: f64) {","highlight_start":12,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":7009,"byte_end":7014,"line_start":307,"line_end":307,"column_start":12,"column_end":17,"is_primary":true,"text":[{"text":"    pub fn value(&self) -> f64 {","highlight_start":12,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\monitor\\stats.rs","byte_start":7101,"byte_end":7113,"line_start":312,"line_end":312,"column_start":12,"column_end":24,"is_primary":true,"text":[{"text":"    pub fn last_updated(&self) -> SystemTime {","highlight_start":12,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: associated items `new`, `set`, `add`, `sub`, `value`, and `last_updated` are never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\monitor\\stats.rs:281:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m279\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl Gauge {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m280\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    /// 创建新的仪表盘\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m281\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn new() -> Self {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m289\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn set(&mut self, value: f64) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m295\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn add(&mut self, delta: f64) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m301\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn sub(&mut self, delta: f64) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m307\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn value(&self) -> f64 {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m312\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn last_updated(&self) -> SystemTime {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m| \u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^\u001b[0m\n\n"}
{"message":"linking with `link.exe` failed: exit code: 1104","code":null,"level":"error","spans":[],"children":[{"message":"\"C:\\\\Program Files\\\\Microsoft Visual Studio\\\\2022\\\\Community\\\\VC\\\\Tools\\\\MSVC\\\\14.44.35207\\\\bin\\\\HostX64\\\\x86\\\\link.exe\" \"/NOLOGO\" \"/LARGEADDRESSAWARE\" \"/SAFESEH\" \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\2\\\\rustcj2ctZj\\\\symbols.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.107e1661nzst4h5c.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.10bm7sjbamxtxz03.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.10cz9pr3wybjidlc.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.10w7hua4b66kdvno.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.10xiy7deuss3v9ei.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.11g7ld8105duxvzy.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.12mohd5t0kp3dx9n.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.138pspwkxud3zwh3.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.13b0s93ii7e2dwu.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.13knwwkc5phk979y.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.13m7tekhxjr2g4ey.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.13tgthp25v3v6e0a.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.141cmyas6kpe5hhg.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.14d455dnylboklx4.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.14pt5z4v61873fc.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.157fdx6jos9d4458.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.15tagokdy2ps0jw.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.19jmay0akn5h4686.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.19tib4rcsr886w00.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.19zhndf3ftxiztk9.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1askvy18x0y70esh.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1avcjn69z0z27r4b.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1aycj256jm7ptxqu.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1biw22hikqytr2oq.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1d69oy7tbx8hbomb.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1dukruqtli1hwe6o.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1fqnsmps0panyikx.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1g64ulq2324nufvm.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1gtjb44ofkjdyg5m.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1ixv5s8o2pcirhav.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1ka5sel2hn9e4qrr.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1kiurczkn90arznv.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1l2wpodd07w76fi.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1lhegnmb9smz08jp.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1lt8tffx7lcmb13u.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1mta7xa8z71ajgqz.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1naazqs1r8vhh23v.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1oqt0buh914cs8aa.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1owkrkdk2ulzwdcz.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1pdon7iaf6wv806o.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1phwbu7yqwzvcrjz.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1pl86x6ioawmdpe9.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1ppy30c0snt89x3n.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1pr5z7eq4pdoslor.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1rhxxm4lr9qjcu46.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1sdqif8rq0wz4i9h.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1t8tfxc1vmt6mlyj.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1txq5iudm1v1k29.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1uig0nm4wugx097o.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1vsc9wrp737wl21t.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1w9xq6fvitl8o55t.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1wct9telgyakvzxp.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1x34fjemvddq0ofp.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1ypkv6lnkh9qvqvn.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1ypnsg8ptpf3yzoz.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1z664xhw22qvq8ye.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.20euuedcjqd17uop.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.21oxzshv4c2u36jz.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.21riztemzbedcvhu.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.23ocy1ycgczmnouy.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.260g6i42dm81aoi6.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.261f7idhl33eyl1r.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.26z5hylyn1oal5al.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.27xhrk6mb7s3hiy2.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.289c2y1y6m2f3wiv.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.29bxfkimvdgnqu1l.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.29gtktwfbq8ancia.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2amytmfjmu4tpvqo.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2amzm7rosrp0ssbs.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2az1egalxp1bjnkt.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2bfqzrmj8o9zu3b0.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2bpcke1mpx08mg9m.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2bwqbkucp7ok3gn1.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2cid4xifa7kqp669.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2cv0w2j6tm7mqlvv.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2dc64v5mjerknujo.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2delt85u2tmfsy5g.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2dsa8p6icppc9rto.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2frfiwwkm0cw26iv.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2fwywg0y63kt3bao.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2g64r1vca6xebetl.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2g8srtep7o2rdik8.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2hfslo3cqjgzv24v.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2i0uqxhtw1yleg5a.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2i1f3pivzjqvmrye.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2i7iessv0q11zfc5.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2ibdiisayk2o4035.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2idis57y82azle2d.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2ifap7rbkkrwclx1.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2l3540wmrt8f8jfr.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2lonjsbl2yft16jn.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2nlqam5f76h81mv2.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2o3bunns2p339uwf.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2ostrzes8ygvsrto.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2qpd05v059ip6lj6.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2ryqflninxerlwml.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2s368rt5m3388rwi.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2s7swhk4hq0nxf44.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2tq8alptlmwv0f7z.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2v6awwln5xzcdgzb.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2vi6e9j0xbf9tq4h.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2w3p3rnkcazbowbx.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2wutcb6bm0lqmm2v.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2xp2dg8ppt8h0y6r.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2xtmxmp00ho6xzpg.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2yqgtfr1hyitilzl.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2zn6pm1gis1vq9ug.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.30cwckgkpfp1wedp.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.30gnp26fhrjekwbh.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.30hz2dbqim88q6pu.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.30ma71h4q0u5v0d4.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.329pk3typk57d5vb.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.32dot54m78fjrlms.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.32j5md13gnk5rlxw.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.33dya1qz95tga2yo.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.34enadxd3hh4xcqu.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.34fw3m9lv9ywsrzu.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.34stj1tomzhmhbe7.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.35bxqr6edn8xtzz.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.35ost6feo1p9zhmd.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.37fxdi84q1dkdtry.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.38ddjahp3zx6bth5.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.38huv6rt4wfvojla.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.395xzru9zuzgzwp8.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.39yrp494edskr1kb.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3ajumih2qid7e2yt.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3avpm9i5nq6xecns.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3by00so79h6t4ea4.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3byggpnrow940w3j.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3c2lk4u55b5zqa78.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3c7t7vpxqk5vj6js.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3cg2pv3hacjmpa50.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3crxaglfn37qta5b.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3dxqi164dseyjeh9.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3ed5rax88l8t928l.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3eft78953bk61lu8.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3eop3xpw5ilqy7d0.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3f84f47bdhsyubpw.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3fb9gkeqrblao760.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3ffop16dx0umczhf.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3fr187hsxwgvrjjt.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3gxu6q9c4yayn7vf.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3irf422il0nyon3a.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3iyla5ewlh01pne0.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3jlskz1c4x5gds2t.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3jpukx2dcx25qkaa.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3kqb12o5tbnifo8g.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3m5kffod4dob65mh.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3n2gopwf03rlu71d.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3qmdeyokphpe6udi.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3s0vjahui5c064l9.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3uvhl3l9ed8ix9b3.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3uxp16qam8jtxhfk.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3vhtshtnpgy9dip9.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3vwa172ux761nxrf.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3w74u6ju2e8ay5uq.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3wcrqnk3cjt0qfuk.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3y1alwkuxxay676x.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.401gdk9xjpf33l98.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4037ob0g5j0h2vv3.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.40oqr1cbp043socu.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.411s0kbfwy3toxfb.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.465e3q7fwioecyol.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.46djr6ha7virezwe.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.46nhm9nmws1bj0a1.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.46teo4uy4xilu3y4.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.48q2pbyq12yu9dr6.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.48t2be79dw5a25eu.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.49zajb4kqeeoi8dk.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4d5ksgb6wsxl2u5b.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4dzequtbdvl4kizd.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4ghunlxyizzr3aik.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4guygeo0voz7m1my.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4gym4bdwwdasz1pw.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4hx2a8hkznh6t7t7.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4l5jy3c6l7w1c2n7.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4m5li086xfnhezfw.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4mkbhmhtqsn8udtl.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4myfmx8fwpcs70lw.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4myhsodbjt1hup6p.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4ooipwb8o5bh2d3j.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4ped6g0zd7rovyn9.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4pfn2cg4kst54r44.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4q112x41l8rlv1hh.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4q67efpo0tr32lpm.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4quy99rdcggq91cm.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4rc30uiqh5m326hr.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4rj1edvn0s0w0f2m.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4sdg82x1t980lmnj.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4sxy713bxf0eij5o.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4t6es9wby8pgaotg.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4tfsjdbgbi00t5rd.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4w2sar54fh9qiyf8.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4wnzujcoe2hebqjy.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4y69r9k7po77p3ga.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4yk8bz7z376xh9bc.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4zf41sq2b8q5q4to.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4zroudvexb00i7wr.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.50fdyo3j7ntck5g6.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.51960gg03mx4c0fu.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.53sgff5hkhwnixwd.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.54m09yhv7dejs81l.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.59e1qlsh5euwqk8m.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.5a8q17r1sp72cwm3.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.5cg8p3bhr1mp452b.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.5cj15sags6ey0dmw.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.5cncwzuv0qehgjhv.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.5d6pxar5ybgorjox.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.5dnlv7k1cncmdbyq.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.5e5iv7o2grjvn3t2.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.5fcpgsq9ewbgf695.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.6iwb0u9pimrp7ie.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.6konwka0ynyafym.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.8h8mybqwn1xz26x.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.9gt50glcznr5ktm.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.9hivsj6hesk0rcu.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.9l3g0m28wvsk5vn.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.appir5328y68fit.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.b5decs5tb4rb0r3.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.cd2u49j5pvaqbx1.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.ch86dnyrbndc4s4.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.e3uzoynlc3gw7u6.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.eado24g26xnzx46.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.ggqikcohok352xj.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.in02mh9sjj0pv4t.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.in53lr97kqirqus.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.j33h2g8bttl3qhu.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.kej5np5go93ftrp.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.kjn6l5viaa2zxq2.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.kudfoska0tsh7ty.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.lwf2xc2igs3erve.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.lwopfptplbirgwd.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.maf1ntxbjzgiza9.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.ml87ps58bikz5ux.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.nny4tdo6e9dgu42.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.o18wu0c90echm96.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.o4eu3rr51yoj3fe.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.p50h5ur8hkxz11l.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.pv2rhjzmcjjyto6.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.qor6snpst08m5ie.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.qym6nppacgrh0rd.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.rbqg2t7s9lqwyco.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.rnjhfyg5r5mqyj3.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.sa16001em1qw54t.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.sofwz8qsav2rsc2.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.suvsmgbk9ujtipf.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.sv0nwf0k5jq5jf3.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.u6h4n1tx6qvthlg.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.uaevpe66g4s5t7n.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.ubva5tran7jeq9l.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.udj27wfzjy7e27i.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.vaab4vz0uxjjoki.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.w9y3qortkl816w1.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.w9zlni3m6pn3kvp.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.wfnwngnw4lpdo5i.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.xhfibyogjw76wjb.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2bz1yykj719ofnzm.rcgu.o\" \"/LIBPATH:C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\" \"/LIBPATH:C:\\\\Users\\\\<USER>\\\\.cargo\\\\registry\\\\src\\\\index.crates.io-1cd66030c949c28d\\\\windows_i686_msvc-0.52.6\\\\lib\" \"/LIBPATH:C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\build\\\\ring-5c6d01d38a938337\\\\out\" \"/LIBPATH:C:\\\\Program Files\\\\Microsoft Visual Studio\\\\2022\\\\Community\\\\VC\\\\Tools\\\\MSVC\\\\14.44.35207\\\\atlmfc\\\\lib\\\\x86\" \"/LIBPATH:C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\build\\\\zstd-sys-aba3586c9fa26eb0\\\\out\" \"/LIBPATH:C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\i686-pc-windows-msvc\\\\lib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libzstd-23acd72b4af8ebbf.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libzstd_safe-08831b2233851d68.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libzstd_sys-f16b23518ee8e3e3.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libbrotli-a947a5ef616da4d7.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libbrotli_decompressor-21a75f457bdfe636.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\liballoc_stdlib-f5d90dda1bdc7aea.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\liballoc_no_stdlib-003363122ff3d5fd.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libserde_json-84d946efc2de3cd7.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libryu-806637ef12fd2034.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libhumantime_serde-e5bbab20bfcf6194.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libhumantime-02c199d1f894b221.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libtracing_subscriber-ac65c104d3902d98.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libsharded_slab-637c05c83f83457a.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\liblazy_static-48345761cd7d2b81.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libmatchers-52bce2ce0dd1ebf8.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libregex_automata-21b8d1ceb63d0954.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libregex_syntax-e03c42bc9a9e0373.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libnu_ansi_term-3313e9be797ddb5a.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\liboverload-0eb06bb42a39566f.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libwinapi-3553b377498a47de.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libthread_local-7f9f497d778ef21c.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libtracing_log-a4087bbbef322edf.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libchrono-9a466809006c38e6.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libnum_traits-0610e227557a70a9.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\librustls_pemfile-512f4a2aa7e5ad5a.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libbase64-8dcf2fb228ae053c.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libdashmap-35f658bcc29bd8e7.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libhashbrown-096bec725eb72887.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libflate2-22fb41001881c034.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libminiz_oxide-f8d5e0f0d1b0ac49.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libadler2-6088b96fd44e66df.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libcrc32fast-57809806fa6f3884.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libmime_guess-00d84f560c1bf855.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libunicase-720686ebe354cdeb.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libmime-b18f2da5c0f68509.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libuuid-af5ce3ed3541ca6c.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libgetrandom-a14df393c658bf79.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libtokio_rustls-6c9422a409b159dd.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\librustls-97cfd0f5264628a7.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libsct-408ca96897b5b8df.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libwebpki-f06c2e53dbfbab47.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libring-16e68738d7ef2cd5.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libgetrandom-d357e2e18e843891.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libuntrusted-640c038bc2340fcc.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libhyper_util-85dfd6ed3f20aec5.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libwindows_registry-e9e98de0b810e53b.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libwindows_strings-8deab1faf617aca5.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libwindows_result-66b54939d173b0e4.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libwindows_link-4290ccbd3e47e0c9.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libbase64-ef3e31bab71912ed.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libpercent_encoding-a41a26defbf631b1.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libipnet-2c82b7137aa141e0.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libtower_service-d3f3fb738efe5838.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libhyper-2287e07dc99504c4.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libwant-4994b463a16fea43.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libtry_lock-5bc4c0ba93e99c2c.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libhttparse-85c3d0dd9230b19d.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libh2-0c0022e25e587df9.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libindexmap-dde4d3bea35c895b.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libequivalent-7d5799fbd330456a.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libhashbrown-edc45657a0fe1743.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libatomic_waker-ae51680e59c657b4.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libtokio_util-bd41ba97b4d5e098.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libhttpdate-c63194d71877f358.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libfutures_util-8b8e77812466e107.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libfutures_io-d4b71a549c782656.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libslab-c8ca33602d168a0d.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libfutures_task-01099581da2e6742.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libpin_utils-03453ced69e5e6f2.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libfutures_channel-0bab4071c4cdb0ab.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libfutures_sink-c8c1bfd9de04c0b2.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libhttp_body_util-082eaf14fde0a9c8.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libfutures_core-ae27aa8756aa7268.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libhttp_body-ec6c01ec5d5e742a.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libhttp-b2fe2494ddc5c007.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libitoa-b96db985065ba19c.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libfnv-84746cf5eb7fea48.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libtokio-45c830356b4a13b3.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libsocket2-2c20768169c6a67e.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libwindows_sys-bdc39ae4015275cf.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libbytes-1019650ce5656613.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libmio-4b4c2bc71bd62cf6.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libparking_lot-96361e1dd6e6f462.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libparking_lot_core-18a1e7121caaaf02.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libcfg_if-e588c35e992c4d1c.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libsmallvec-80ea0a210a7e7f0d.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\liblock_api-d69acb9a34b37448.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libscopeguard-4141fd53c64eaa88.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libregex-1ba051950485b77b.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libregex_automata-8b23311528589c5a.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libaho_corasick-1c8d9a944f571312.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libmemchr-eed885da44b99675.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libregex_syntax-a0c876374351c346.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libserde-950de2c5f18b2e1b.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libtracing-f281251a034ae60b.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\liblog-30272c6b6f44d190.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libpin_project_lite-7d00d5f9541a553f.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libtracing_core-7b668e8ce76150e6.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libonce_cell-040adb58fce9cacf.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libclap-46fb706ad36667a2.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libclap_builder-a96e484787dca991.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libstrsim-f679e0210f7a7934.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libanstream-a3e3d2dcc5e780ff.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libanstyle_query-23cb2c4af161f011.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libis_terminal_polyfill-920f93c9a9978bfb.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libanstyle_wincon-9d3aedad935dbc94.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libonce_cell_polyfill-f2ab212069ac1cc4.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libwindows_sys-47588ccd7b9a5a9e.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libwindows_targets-ee25b78a6928c0f1.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libcolorchoice-af2d214824b8042f.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libanstyle_parse-eb205567a904248e.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libutf8parse-1447a69bc985869a.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libclap_lex-a98f26cf571a7a5d.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libanstyle-c395c82c4e37bf5b.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libanyhow-0e25c19963219f3d.rlib\" \"C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\i686-pc-windows-msvc\\\\lib\\\\libstd-69532279df066d22.rlib\" \"C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\i686-pc-windows-msvc\\\\lib\\\\libpanic_unwind-354b046be7b6c411.rlib\" \"C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\i686-pc-windows-msvc\\\\lib\\\\librustc_demangle-6c62776dc57c7fac.rlib\" \"C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\i686-pc-windows-msvc\\\\lib\\\\libstd_detect-ff563fe1fb366da1.rlib\" \"C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\i686-pc-windows-msvc\\\\lib\\\\libhashbrown-b962ca5d7b868c9f.rlib\" \"C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\i686-pc-windows-msvc\\\\lib\\\\librustc_std_workspace_alloc-4af4cce1dbbe5772.rlib\" \"C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\i686-pc-windows-msvc\\\\lib\\\\libunwind-ba68b86b1e00fc77.rlib\" \"C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\i686-pc-windows-msvc\\\\lib\\\\libcfg_if-5d0377950e896731.rlib\" \"C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\i686-pc-windows-msvc\\\\lib\\\\liblibc-1c0b535bdfb1d3d9.rlib\" \"C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\i686-pc-windows-msvc\\\\lib\\\\liballoc-9ef4ae375d9d2925.rlib\" \"C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\i686-pc-windows-msvc\\\\lib\\\\librustc_std_workspace_core-d45796b1f85e8f22.rlib\" \"C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\i686-pc-windows-msvc\\\\lib\\\\libcore-4da9538e4f0c8013.rlib\" \"C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\i686-pc-windows-msvc\\\\lib\\\\libcompiler_builtins-a54e8e62d16bd157.rlib\" \"gdi32.lib\" \"kernel32.lib\" \"msimg32.lib\" \"opengl32.lib\" \"winspool.lib\" \"advapi32.lib\" \"bcrypt.lib\" \"advapi32.lib\" \"windows.0.52.0.lib\" \"windows.0.52.0.lib\" \"windows.0.52.0.lib\" \"kernel32.lib\" \"advapi32.lib\" \"bcrypt.lib\" \"kernel32.lib\" \"ntdll.lib\" \"userenv.lib\" \"ws2_32.lib\" \"kernel32.lib\" \"ws2_32.lib\" \"kernel32.lib\" \"ntdll.lib\" \"msvcrt.lib\" \"/NXCOMPAT\" \"/LIBPATH:C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\i686-pc-windows-msvc\\\\lib\" \"/OUT:C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.exe\" \"/OPT:REF,NOICF\" \"/DEBUG\" \"/NATVIS:C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\etc\\\\intrinsic.natvis\" \"/NATVIS:C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\etc\\\\liballoc.natvis\" \"/NATVIS:C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\etc\\\\libcore.natvis\" \"/NATVIS:C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\etc\\\\libstd.natvis\" \"/NATVIS:C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\2\\\\rustcj2ctZj\\\\rust_reverse_proxy-0.natvis\" \"/NATVIS:C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\2\\\\rustcj2ctZj\\\\rust_reverse_proxy-1.natvis\"","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"LINK : fatal error LNK1104: 无法打开文件“C:\\Users\\<USER>\\Desktop\\code\\反向代理rust\\target\\debug\\deps\\rust_reverse_proxy.exe”\r\n","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: linking with `link.exe` failed: exit code: 1104\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: \"C:\\\\Program Files\\\\Microsoft Visual Studio\\\\2022\\\\Community\\\\VC\\\\Tools\\\\MSVC\\\\14.44.35207\\\\bin\\\\HostX64\\\\x86\\\\link.exe\" \"/NOLOGO\" \"/LARGEADDRESSAWARE\" \"/SAFESEH\" \"C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\2\\\\rustcj2ctZj\\\\symbols.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.107e1661nzst4h5c.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.10bm7sjbamxtxz03.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.10cz9pr3wybjidlc.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.10w7hua4b66kdvno.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.10xiy7deuss3v9ei.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.11g7ld8105duxvzy.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.12mohd5t0kp3dx9n.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.138pspwkxud3zwh3.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.13b0s93ii7e2dwu.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.13knwwkc5phk979y.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.13m7tekhxjr2g4ey.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.13tgthp25v3v6e0a.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.141cmyas6kpe5hhg.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.14d455dnylboklx4.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.14pt5z4v61873fc.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.157fdx6jos9d4458.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.15tagokdy2ps0jw.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.19jmay0akn5h4686.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.19tib4rcsr886w00.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.19zhndf3ftxiztk9.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1askvy18x0y70esh.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1avcjn69z0z27r4b.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1aycj256jm7ptxqu.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1biw22hikqytr2oq.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1d69oy7tbx8hbomb.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1dukruqtli1hwe6o.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1fqnsmps0panyikx.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1g64ulq2324nufvm.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1gtjb44ofkjdyg5m.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1ixv5s8o2pcirhav.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1ka5sel2hn9e4qrr.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1kiurczkn90arznv.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1l2wpodd07w76fi.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1lhegnmb9smz08jp.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1lt8tffx7lcmb13u.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1mta7xa8z71ajgqz.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1naazqs1r8vhh23v.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1oqt0buh914cs8aa.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1owkrkdk2ulzwdcz.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1pdon7iaf6wv806o.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1phwbu7yqwzvcrjz.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1pl86x6ioawmdpe9.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1ppy30c0snt89x3n.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1pr5z7eq4pdoslor.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1rhxxm4lr9qjcu46.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1sdqif8rq0wz4i9h.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1t8tfxc1vmt6mlyj.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1txq5iudm1v1k29.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1uig0nm4wugx097o.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1vsc9wrp737wl21t.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1w9xq6fvitl8o55t.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1wct9telgyakvzxp.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1x34fjemvddq0ofp.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1ypkv6lnkh9qvqvn.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1ypnsg8ptpf3yzoz.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.1z664xhw22qvq8ye.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.20euuedcjqd17uop.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.21oxzshv4c2u36jz.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.21riztemzbedcvhu.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.23ocy1ycgczmnouy.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.260g6i42dm81aoi6.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.261f7idhl33eyl1r.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.26z5hylyn1oal5al.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.27xhrk6mb7s3hiy2.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.289c2y1y6m2f3wiv.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.29bxfkimvdgnqu1l.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.29gtktwfbq8ancia.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2amytmfjmu4tpvqo.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2amzm7rosrp0ssbs.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2az1egalxp1bjnkt.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2bfqzrmj8o9zu3b0.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2bpcke1mpx08mg9m.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2bwqbkucp7ok3gn1.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2cid4xifa7kqp669.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2cv0w2j6tm7mqlvv.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2dc64v5mjerknujo.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2delt85u2tmfsy5g.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2dsa8p6icppc9rto.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2frfiwwkm0cw26iv.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2fwywg0y63kt3bao.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2g64r1vca6xebetl.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2g8srtep7o2rdik8.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2hfslo3cqjgzv24v.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2i0uqxhtw1yleg5a.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2i1f3pivzjqvmrye.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2i7iessv0q11zfc5.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2ibdiisayk2o4035.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2idis57y82azle2d.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2ifap7rbkkrwclx1.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2l3540wmrt8f8jfr.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2lonjsbl2yft16jn.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2nlqam5f76h81mv2.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2o3bunns2p339uwf.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2ostrzes8ygvsrto.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2qpd05v059ip6lj6.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2ryqflninxerlwml.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2s368rt5m3388rwi.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2s7swhk4hq0nxf44.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2tq8alptlmwv0f7z.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2v6awwln5xzcdgzb.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2vi6e9j0xbf9tq4h.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2w3p3rnkcazbowbx.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2wutcb6bm0lqmm2v.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2xp2dg8ppt8h0y6r.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2xtmxmp00ho6xzpg.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2yqgtfr1hyitilzl.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2zn6pm1gis1vq9ug.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.30cwckgkpfp1wedp.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.30gnp26fhrjekwbh.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.30hz2dbqim88q6pu.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.30ma71h4q0u5v0d4.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.329pk3typk57d5vb.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.32dot54m78fjrlms.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.32j5md13gnk5rlxw.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.33dya1qz95tga2yo.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.34enadxd3hh4xcqu.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.34fw3m9lv9ywsrzu.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.34stj1tomzhmhbe7.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.35bxqr6edn8xtzz.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.35ost6feo1p9zhmd.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.37fxdi84q1dkdtry.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.38ddjahp3zx6bth5.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.38huv6rt4wfvojla.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.395xzru9zuzgzwp8.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.39yrp494edskr1kb.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3ajumih2qid7e2yt.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3avpm9i5nq6xecns.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3by00so79h6t4ea4.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3byggpnrow940w3j.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3c2lk4u55b5zqa78.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3c7t7vpxqk5vj6js.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3cg2pv3hacjmpa50.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3crxaglfn37qta5b.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3dxqi164dseyjeh9.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3ed5rax88l8t928l.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3eft78953bk61lu8.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3eop3xpw5ilqy7d0.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3f84f47bdhsyubpw.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3fb9gkeqrblao760.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3ffop16dx0umczhf.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3fr187hsxwgvrjjt.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3gxu6q9c4yayn7vf.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3irf422il0nyon3a.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3iyla5ewlh01pne0.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3jlskz1c4x5gds2t.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3jpukx2dcx25qkaa.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3kqb12o5tbnifo8g.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3m5kffod4dob65mh.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3n2gopwf03rlu71d.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3qmdeyokphpe6udi.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3s0vjahui5c064l9.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3uvhl3l9ed8ix9b3.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3uxp16qam8jtxhfk.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3vhtshtnpgy9dip9.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3vwa172ux761nxrf.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3w74u6ju2e8ay5uq.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3wcrqnk3cjt0qfuk.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.3y1alwkuxxay676x.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.401gdk9xjpf33l98.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4037ob0g5j0h2vv3.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.40oqr1cbp043socu.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.411s0kbfwy3toxfb.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.465e3q7fwioecyol.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.46djr6ha7virezwe.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.46nhm9nmws1bj0a1.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.46teo4uy4xilu3y4.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.48q2pbyq12yu9dr6.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.48t2be79dw5a25eu.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.49zajb4kqeeoi8dk.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4d5ksgb6wsxl2u5b.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4dzequtbdvl4kizd.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4ghunlxyizzr3aik.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4guygeo0voz7m1my.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4gym4bdwwdasz1pw.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4hx2a8hkznh6t7t7.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4l5jy3c6l7w1c2n7.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4m5li086xfnhezfw.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4mkbhmhtqsn8udtl.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4myfmx8fwpcs70lw.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4myhsodbjt1hup6p.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4ooipwb8o5bh2d3j.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4ped6g0zd7rovyn9.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4pfn2cg4kst54r44.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4q112x41l8rlv1hh.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4q67efpo0tr32lpm.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4quy99rdcggq91cm.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4rc30uiqh5m326hr.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4rj1edvn0s0w0f2m.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4sdg82x1t980lmnj.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4sxy713bxf0eij5o.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4t6es9wby8pgaotg.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4tfsjdbgbi00t5rd.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4w2sar54fh9qiyf8.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4wnzujcoe2hebqjy.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4y69r9k7po77p3ga.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4yk8bz7z376xh9bc.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4zf41sq2b8q5q4to.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.4zroudvexb00i7wr.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.50fdyo3j7ntck5g6.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.51960gg03mx4c0fu.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.53sgff5hkhwnixwd.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.54m09yhv7dejs81l.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.59e1qlsh5euwqk8m.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.5a8q17r1sp72cwm3.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.5cg8p3bhr1mp452b.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.5cj15sags6ey0dmw.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.5cncwzuv0qehgjhv.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.5d6pxar5ybgorjox.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.5dnlv7k1cncmdbyq.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.5e5iv7o2grjvn3t2.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.5fcpgsq9ewbgf695.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.6iwb0u9pimrp7ie.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.6konwka0ynyafym.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.8h8mybqwn1xz26x.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.9gt50glcznr5ktm.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.9hivsj6hesk0rcu.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.9l3g0m28wvsk5vn.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.appir5328y68fit.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.b5decs5tb4rb0r3.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.cd2u49j5pvaqbx1.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.ch86dnyrbndc4s4.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.e3uzoynlc3gw7u6.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.eado24g26xnzx46.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.ggqikcohok352xj.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.in02mh9sjj0pv4t.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.in53lr97kqirqus.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.j33h2g8bttl3qhu.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.kej5np5go93ftrp.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.kjn6l5viaa2zxq2.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.kudfoska0tsh7ty.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.lwf2xc2igs3erve.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.lwopfptplbirgwd.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.maf1ntxbjzgiza9.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.ml87ps58bikz5ux.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.nny4tdo6e9dgu42.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.o18wu0c90echm96.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.o4eu3rr51yoj3fe.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.p50h5ur8hkxz11l.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.pv2rhjzmcjjyto6.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.qor6snpst08m5ie.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.qym6nppacgrh0rd.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.rbqg2t7s9lqwyco.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.rnjhfyg5r5mqyj3.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.sa16001em1qw54t.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.sofwz8qsav2rsc2.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.suvsmgbk9ujtipf.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.sv0nwf0k5jq5jf3.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.u6h4n1tx6qvthlg.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.uaevpe66g4s5t7n.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.ubva5tran7jeq9l.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.udj27wfzjy7e27i.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.vaab4vz0uxjjoki.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.w9y3qortkl816w1.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.w9zlni3m6pn3kvp.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.wfnwngnw4lpdo5i.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.xhfibyogjw76wjb.rcgu.o\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.2bz1yykj719ofnzm.rcgu.o\" \"/LIBPATH:C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\" \"/LIBPATH:C:\\\\Users\\\\<USER>\\\\.cargo\\\\registry\\\\src\\\\index.crates.io-1cd66030c949c28d\\\\windows_i686_msvc-0.52.6\\\\lib\" \"/LIBPATH:C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\build\\\\ring-5c6d01d38a938337\\\\out\" \"/LIBPATH:C:\\\\Program Files\\\\Microsoft Visual Studio\\\\2022\\\\Community\\\\VC\\\\Tools\\\\MSVC\\\\14.44.35207\\\\atlmfc\\\\lib\\\\x86\" \"/LIBPATH:C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\build\\\\zstd-sys-aba3586c9fa26eb0\\\\out\" \"/LIBPATH:C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\i686-pc-windows-msvc\\\\lib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libzstd-23acd72b4af8ebbf.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libzstd_safe-08831b2233851d68.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libzstd_sys-f16b23518ee8e3e3.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libbrotli-a947a5ef616da4d7.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libbrotli_decompressor-21a75f457bdfe636.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\liballoc_stdlib-f5d90dda1bdc7aea.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\liballoc_no_stdlib-003363122ff3d5fd.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libserde_json-84d946efc2de3cd7.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libryu-806637ef12fd2034.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libhumantime_serde-e5bbab20bfcf6194.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libhumantime-02c199d1f894b221.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libtracing_subscriber-ac65c104d3902d98.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libsharded_slab-637c05c83f83457a.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\liblazy_static-48345761cd7d2b81.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libmatchers-52bce2ce0dd1ebf8.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libregex_automata-21b8d1ceb63d0954.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libregex_syntax-e03c42bc9a9e0373.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libnu_ansi_term-3313e9be797ddb5a.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\liboverload-0eb06bb42a39566f.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libwinapi-3553b377498a47de.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libthread_local-7f9f497d778ef21c.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libtracing_log-a4087bbbef322edf.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libchrono-9a466809006c38e6.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libnum_traits-0610e227557a70a9.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\librustls_pemfile-512f4a2aa7e5ad5a.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libbase64-8dcf2fb228ae053c.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libdashmap-35f658bcc29bd8e7.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libhashbrown-096bec725eb72887.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libflate2-22fb41001881c034.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libminiz_oxide-f8d5e0f0d1b0ac49.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libadler2-6088b96fd44e66df.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libcrc32fast-57809806fa6f3884.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libmime_guess-00d84f560c1bf855.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libunicase-720686ebe354cdeb.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libmime-b18f2da5c0f68509.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libuuid-af5ce3ed3541ca6c.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libgetrandom-a14df393c658bf79.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libtokio_rustls-6c9422a409b159dd.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\librustls-97cfd0f5264628a7.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libsct-408ca96897b5b8df.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libwebpki-f06c2e53dbfbab47.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libring-16e68738d7ef2cd5.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libgetrandom-d357e2e18e843891.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libuntrusted-640c038bc2340fcc.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libhyper_util-85dfd6ed3f20aec5.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libwindows_registry-e9e98de0b810e53b.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libwindows_strings-8deab1faf617aca5.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libwindows_result-66b54939d173b0e4.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libwindows_link-4290ccbd3e47e0c9.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libbase64-ef3e31bab71912ed.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libpercent_encoding-a41a26defbf631b1.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libipnet-2c82b7137aa141e0.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libtower_service-d3f3fb738efe5838.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libhyper-2287e07dc99504c4.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libwant-4994b463a16fea43.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libtry_lock-5bc4c0ba93e99c2c.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libhttparse-85c3d0dd9230b19d.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libh2-0c0022e25e587df9.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libindexmap-dde4d3bea35c895b.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libequivalent-7d5799fbd330456a.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libhashbrown-edc45657a0fe1743.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libatomic_waker-ae51680e59c657b4.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libtokio_util-bd41ba97b4d5e098.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libhttpdate-c63194d71877f358.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libfutures_util-8b8e77812466e107.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libfutures_io-d4b71a549c782656.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libslab-c8ca33602d168a0d.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libfutures_task-01099581da2e6742.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libpin_utils-03453ced69e5e6f2.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libfutures_channel-0bab4071c4cdb0ab.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libfutures_sink-c8c1bfd9de04c0b2.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libhttp_body_util-082eaf14fde0a9c8.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libfutures_core-ae27aa8756aa7268.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libhttp_body-ec6c01ec5d5e742a.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libhttp-b2fe2494ddc5c007.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libitoa-b96db985065ba19c.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libfnv-84746cf5eb7fea48.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libtokio-45c830356b4a13b3.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libsocket2-2c20768169c6a67e.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libwindows_sys-bdc39ae4015275cf.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libbytes-1019650ce5656613.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libmio-4b4c2bc71bd62cf6.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libparking_lot-96361e1dd6e6f462.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libparking_lot_core-18a1e7121caaaf02.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libcfg_if-e588c35e992c4d1c.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libsmallvec-80ea0a210a7e7f0d.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\liblock_api-d69acb9a34b37448.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libscopeguard-4141fd53c64eaa88.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libregex-1ba051950485b77b.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libregex_automata-8b23311528589c5a.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libaho_corasick-1c8d9a944f571312.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libmemchr-eed885da44b99675.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libregex_syntax-a0c876374351c346.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libserde-950de2c5f18b2e1b.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libtracing-f281251a034ae60b.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\liblog-30272c6b6f44d190.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libpin_project_lite-7d00d5f9541a553f.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libtracing_core-7b668e8ce76150e6.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libonce_cell-040adb58fce9cacf.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libclap-46fb706ad36667a2.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libclap_builder-a96e484787dca991.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libstrsim-f679e0210f7a7934.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libanstream-a3e3d2dcc5e780ff.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libanstyle_query-23cb2c4af161f011.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libis_terminal_polyfill-920f93c9a9978bfb.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libanstyle_wincon-9d3aedad935dbc94.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libonce_cell_polyfill-f2ab212069ac1cc4.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libwindows_sys-47588ccd7b9a5a9e.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libwindows_targets-ee25b78a6928c0f1.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libcolorchoice-af2d214824b8042f.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libanstyle_parse-eb205567a904248e.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libutf8parse-1447a69bc985869a.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libclap_lex-a98f26cf571a7a5d.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libanstyle-c395c82c4e37bf5b.rlib\" \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\libanyhow-0e25c19963219f3d.rlib\" \"C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\i686-pc-windows-msvc\\\\lib\\\\libstd-69532279df066d22.rlib\" \"C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\i686-pc-windows-msvc\\\\lib\\\\libpanic_unwind-354b046be7b6c411.rlib\" \"C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\i686-pc-windows-msvc\\\\lib\\\\librustc_demangle-6c62776dc57c7fac.rlib\" \"C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\i686-pc-windows-msvc\\\\lib\\\\libstd_detect-ff563fe1fb366da1.rlib\" \"C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\i686-pc-windows-msvc\\\\lib\\\\libhashbrown-b962ca5d7b868c9f.rlib\" \"C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\i686-pc-windows-msvc\\\\lib\\\\librustc_std_workspace_alloc-4af4cce1dbbe5772.rlib\" \"C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\i686-pc-windows-msvc\\\\lib\\\\libunwind-ba68b86b1e00fc77.rlib\" \"C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\i686-pc-windows-msvc\\\\lib\\\\libcfg_if-5d0377950e896731.rlib\" \"C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\i686-pc-windows-msvc\\\\lib\\\\liblibc-1c0b535bdfb1d3d9.rlib\" \"C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\i686-pc-windows-msvc\\\\lib\\\\liballoc-9ef4ae375d9d2925.rlib\" \"C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\i686-pc-windows-msvc\\\\lib\\\\librustc_std_workspace_core-d45796b1f85e8f22.rlib\" \"C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\i686-pc-windows-msvc\\\\lib\\\\libcore-4da9538e4f0c8013.rlib\" \"C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\i686-pc-windows-msvc\\\\lib\\\\libcompiler_builtins-a54e8e62d16bd157.rlib\" \"gdi32.lib\" \"kernel32.lib\" \"msimg32.lib\" \"opengl32.lib\" \"winspool.lib\" \"advapi32.lib\" \"bcrypt.lib\" \"advapi32.lib\" \"windows.0.52.0.lib\" \"windows.0.52.0.lib\" \"windows.0.52.0.lib\" \"kernel32.lib\" \"advapi32.lib\" \"bcrypt.lib\" \"kernel32.lib\" \"ntdll.lib\" \"userenv.lib\" \"ws2_32.lib\" \"kernel32.lib\" \"ws2_32.lib\" \"kernel32.lib\" \"ntdll.lib\" \"msvcrt.lib\" \"/NXCOMPAT\" \"/LIBPATH:C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\i686-pc-windows-msvc\\\\lib\" \"/OUT:C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\反向代理rust\\\\target\\\\debug\\\\deps\\\\rust_reverse_proxy.exe\" \"/OPT:REF,NOICF\" \"/DEBUG\" \"/NATVIS:C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\etc\\\\intrinsic.natvis\" \"/NATVIS:C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\etc\\\\liballoc.natvis\" \"/NATVIS:C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\etc\\\\libcore.natvis\" \"/NATVIS:C:\\\\Program Files (x86)\\\\Rust stable MSVC 1.75\\\\lib\\\\rustlib\\\\etc\\\\libstd.natvis\" \"/NATVIS:C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\2\\\\rustcj2ctZj\\\\rust_reverse_proxy-0.natvis\" \"/NATVIS:C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\2\\\\rustcj2ctZj\\\\rust_reverse_proxy-1.natvis\"\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: LINK : fatal error LNK1104: 无法打开文件“C:\\Users\\<USER>\\Desktop\\code\\反向代理rust\\target\\debug\\deps\\rust_reverse_proxy.exe”\r\u001b[0m\n\u001b[0m          \u001b[0m\n\n"}
{"message":"aborting due to previous error; 100 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to previous error; 100 warnings emitted\u001b[0m\n\n"}

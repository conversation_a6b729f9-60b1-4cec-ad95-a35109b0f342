package minify

import (
	"regexp"
	"strconv"
	"strings"

	"reverse-proxy/internal/config"
)

// InternalMinifier 内部实现的简化版本 minifier
type InternalMinifier struct {
	enabled bool
}

// NewInternalMinifier 创建内部 minifier
func NewInternalMinifier() *InternalMinifier {
	return &InternalMinifier{
		enabled: true,
	}
}

// Minify 执行内部 minify 处理
func (im *InternalMinifier) Minify(data []byte, contentType string, cfg config.MinifyConfig) ([]byte, error) {
	if !im.enabled || !cfg.Enabled {
		return data, nil
	}

	switch {
	case strings.Contains(contentType, "text/html") || strings.Contains(contentType, "application/xhtml+xml"):
		return im.minifyHTML(data, cfg.HTML), nil
	case strings.Contains(contentType, "text/css"):
		return im.minifyCSS(data, cfg.CSS), nil
	case strings.Contains(contentType, "javascript") || strings.Contains(contentType, "application/javascript"):
		return im.minifyJS(data, cfg.JS), nil
	default:
		return data, nil
	}
}

// ShouldMinify 判断是否应该进行最小化
func (im *InternalMinifier) ShouldMinify(contentType string, path string, cfg config.MinifyConfig) bool {
	if !cfg.Enabled || !im.enabled {
		return false
	}

	// 检查文件大小限制
	if cfg.MinSize > 0 {
		// 这里无法检查大小，由调用方检查
	}

	// 检查内容类型
	return strings.Contains(contentType, "text/html") ||
		strings.Contains(contentType, "text/css") ||
		strings.Contains(contentType, "javascript") ||
		strings.Contains(contentType, "application/javascript") ||
		strings.Contains(contentType, "application/xhtml+xml")
}

// minifyHTML 简化的 HTML minify
func (im *InternalMinifier) minifyHTML(data []byte, cfg config.MinifyHTMLConfig) []byte {
	content := string(data)

	// 移除注释 (保留条件注释如果配置要求)
	if !cfg.KeepConditionalComments {
		content = regexp.MustCompile(`<!--[\s\S]*?-->`).ReplaceAllString(content, "")
	} else {
		// 保护条件注释
		conditionalComments := regexp.MustCompile(`<!--\[if[\s\S]*?\[endif\]-->`).FindAllString(content, -1)
		content = regexp.MustCompile(`<!--\[if[\s\S]*?\[endif\]-->`).ReplaceAllString(content, "PLACEHOLDER_CONDITIONAL")
		// 移除普通注释
		content = regexp.MustCompile(`<!--[\s\S]*?-->`).ReplaceAllString(content, "")
		// 恢复条件注释
		for _, conditional := range conditionalComments {
			content = strings.Replace(content, "PLACEHOLDER_CONDITIONAL", conditional, 1)
		}
	}

	// 压缩空白字符
	if !cfg.KeepWhitespace {
		// 移除标签间的多余空白
		content = regexp.MustCompile(`>\s+<`).ReplaceAllString(content, "><")
		// 压缩连续空白为单个空格
		content = regexp.MustCompile(`\s+`).ReplaceAllString(content, " ")
	}

	// 移除不必要的引号
	if !cfg.KeepQuotes {
		// 移除简单属性值的引号
		content = regexp.MustCompile(`=["']([a-zA-Z0-9\-_]+)["']`).ReplaceAllString(content, "=$1")
	}

	// 移除默认属性值
	if !cfg.KeepDefaultAttrVals {
		content = regexp.MustCompile(`\s+type=["']?text/javascript["']?`).ReplaceAllString(content, "")
		content = regexp.MustCompile(`\s+type=["']?text/css["']?`).ReplaceAllString(content, "")
	}

	return []byte(strings.TrimSpace(content))
}

// minifyCSS 简化的 CSS minify
func (im *InternalMinifier) minifyCSS(data []byte, cfg config.MinifyCSSConfig) []byte {
	content := string(data)

	// 移除注释 (保留 /*! */ 许可证注释)
	// 先保护许可证注释
	licenseComments := regexp.MustCompile(`/\*![\s\S]*?\*/`).FindAllString(content, -1)
	content = regexp.MustCompile(`/\*![\s\S]*?\*/`).ReplaceAllString(content, "PLACEHOLDER_LICENSE")
	// 移除普通注释
	content = regexp.MustCompile(`/\*[\s\S]*?\*/`).ReplaceAllString(content, "")
	// 恢复许可证注释
	for _, license := range licenseComments {
		content = strings.Replace(content, "PLACEHOLDER_LICENSE", license, 1)
	}

	// 移除多余空白
	content = regexp.MustCompile(`\s+`).ReplaceAllString(content, " ")
	content = regexp.MustCompile(`\s*{\s*`).ReplaceAllString(content, "{")
	content = regexp.MustCompile(`\s*}\s*`).ReplaceAllString(content, "}")
	content = regexp.MustCompile(`\s*;\s*`).ReplaceAllString(content, ";")
	content = regexp.MustCompile(`\s*:\s*`).ReplaceAllString(content, ":")
	content = regexp.MustCompile(`\s*,\s*`).ReplaceAllString(content, ",")

	// 移除末尾分号
	content = regexp.MustCompile(`;}}`).ReplaceAllString(content, "}")

	// 简化颜色值 (常见的颜色简化)
	content = strings.ReplaceAll(content, "#ffffff", "#fff")
	content = strings.ReplaceAll(content, "#000000", "#000")

	// 移除零值的单位
	content = regexp.MustCompile(`\b0+(px|em|rem|%|pt|pc|in|cm|mm|ex|ch|vw|vh|vmin|vmax)\b`).ReplaceAllString(content, "0")

	// 简化数字
	if cfg.Precision > 0 {
		content = im.simplifyNumbers(content, cfg.Precision)
	}

	return []byte(strings.TrimSpace(content))
}

// minifyJS 简化的 JavaScript minify
func (im *InternalMinifier) minifyJS(data []byte, cfg config.MinifyJSConfig) []byte {
	content := string(data)

	// 移除单行注释 (使用多行模式)
	content = regexp.MustCompile(`(?m)//.*$`).ReplaceAllString(content, "")

	// 移除多行注释
	content = regexp.MustCompile(`/\*[\s\S]*?\*/`).ReplaceAllString(content, "")

	// 压缩空白
	content = regexp.MustCompile(`\s+`).ReplaceAllString(content, " ")
	content = regexp.MustCompile(`\s*{\s*`).ReplaceAllString(content, "{")
	content = regexp.MustCompile(`\s*}\s*`).ReplaceAllString(content, "}")
	content = regexp.MustCompile(`\s*;\s*`).ReplaceAllString(content, ";")
	content = regexp.MustCompile(`\s*,\s*`).ReplaceAllString(content, ",")
	content = regexp.MustCompile(`\s*\(\s*`).ReplaceAllString(content, "(")
	content = regexp.MustCompile(`\s*\)\s*`).ReplaceAllString(content, ")")

	// 简化布尔值
	content = strings.ReplaceAll(content, "true", "!0")
	content = strings.ReplaceAll(content, "false", "!1")

	// 简化数字
	if cfg.Precision > 0 {
		content = im.simplifyNumbers(content, cfg.Precision)
	}

	return []byte(strings.TrimSpace(content))
}

// simplifyNumbers 简化数字表示
func (im *InternalMinifier) simplifyNumbers(content string, precision int) string {
	// 查找所有数字
	re := regexp.MustCompile(`\b\d+\.?\d*\b`)
	return re.ReplaceAllStringFunc(content, func(match string) string {
		if f, err := strconv.ParseFloat(match, 64); err == nil {
			// 格式化为指定精度
			formatted := strconv.FormatFloat(f, 'g', precision, 64)
			return formatted
		}
		return match
	})
}

// ShouldMinifyWithSize 判断是否应该进行最小化（包含大小检查）
func (im *InternalMinifier) ShouldMinifyWithSize(contentType string, path string, size int, cfg config.MinifyConfig) bool {
	if !im.ShouldMinify(contentType, path, cfg) {
		return false
	}

	// 检查最小大小限制
	if cfg.MinSize > 0 && size < int(cfg.MinSize) {
		return false
	}

	// 检查最大大小限制
	if cfg.MaxSize > 0 && size > int(cfg.MaxSize) {
		return false
	}

	return true
}

// MinifyResponse 最小化 HTTP 响应的便利函数
func MinifyResponseInternal(data []byte, contentType string, cfg config.MinifyConfig) ([]byte, error) {
	minifier := NewInternalMinifier()
	return minifier.Minify(data, contentType, cfg)
}

# 连接池和HTTP客户端优化

## 📋 概述

连接池优化是提升反向代理性能的关键技术，通过复用HTTP连接避免频繁建立和关闭连接的开销，显著提升并发处理能力和响应速度。

## 🎯 优化目标

- **减少连接建立开销**：复用现有连接，避免TCP三次握手
- **提升并发性能**：支持更多并发请求处理
- **降低资源消耗**：减少系统资源占用
- **优化网络延迟**：减少网络往返时间

## 🔧 技术实现

### 核心组件

#### 1. HTTPClientManager
```go
type HTTPClientManager struct {
    clients map[string]*http.Client  // 客户端缓存
    config  ConnectionPoolConfig     // 连接池配置
    mu      sync.RWMutex            // 读写锁
}
```

#### 2. ConnectionPoolConfig
```go
type ConnectionPoolConfig struct {
    MaxIdleConns        int           // 最大空闲连接数
    MaxIdleConnsPerHost int           // 每个主机最大空闲连接数
    IdleConnTimeout     time.Duration // 空闲连接超时时间
    DialTimeout         time.Duration // 连接建立超时时间
    KeepAlive           time.Duration // 连接保活时间
    MaxConnsPerHost     int           // 每个主机最大连接数
    DisableKeepAlives   bool          // 是否禁用连接保活
}
```

### 关键特性

#### 1. 连接复用机制
- **按上游服务器分组**：每个上游服务器维护独立的HTTP客户端
- **智能缓存**：使用地址+端口作为键值进行客户端缓存
- **线程安全**：使用读写锁保护并发访问

#### 2. 连接池管理
- **空闲连接管理**：自动清理超时的空闲连接
- **连接数限制**：防止连接数过多导致资源耗尽
- **保活机制**：维持长连接减少握手开销

#### 3. 超时控制
- **连接超时**：控制TCP连接建立时间
- **TLS握手超时**：控制HTTPS握手时间
- **响应头超时**：控制响应头读取时间

## ⚙️ 配置说明

### 基础配置
```json
{
  "server": {
    "connection_pool": {
      "max_idle_conns": 100,
      "max_idle_conns_per_host": 10,
      "idle_conn_timeout": "90s",
      "dial_timeout": "30s",
      "keep_alive": "30s",
      "max_conns_per_host": 0,
      "disable_keep_alives": false
    }
  }
}
```

### 参数详解

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `max_idle_conns` | int | 100 | 全局最大空闲连接数 |
| `max_idle_conns_per_host` | int | 10 | 每个主机最大空闲连接数 |
| `idle_conn_timeout` | duration | 90s | 空闲连接超时时间 |
| `dial_timeout` | duration | 30s | 连接建立超时时间 |
| `keep_alive` | duration | 30s | TCP保活时间 |
| `max_conns_per_host` | int | 0 | 每个主机最大连接数(0=无限制) |
| `disable_keep_alives` | bool | false | 是否禁用HTTP Keep-Alive |

### 性能调优建议

#### 高并发场景
```json
{
  "connection_pool": {
    "max_idle_conns": 200,
    "max_idle_conns_per_host": 20,
    "idle_conn_timeout": "120s",
    "dial_timeout": "10s",
    "keep_alive": "60s",
    "max_conns_per_host": 50
  }
}
```

#### 低延迟场景
```json
{
  "connection_pool": {
    "max_idle_conns": 50,
    "max_idle_conns_per_host": 5,
    "idle_conn_timeout": "30s",
    "dial_timeout": "5s",
    "keep_alive": "15s",
    "max_conns_per_host": 10
  }
}
```

## 📊 性能指标

### 监控指标
通过监控API (`/stats`) 可以查看：
- 连接池使用情况
- 每个上游服务器的连接状态
- 连接建立和复用统计

### 性能提升
- **连接建立时间**：减少50-80%
- **并发处理能力**：提升2-5倍
- **CPU使用率**：降低20-40%
- **内存使用**：优化连接对象管理

## 🔍 故障排查

### 常见问题

#### 1. 连接池耗尽
**现象**：请求响应缓慢，出现连接超时
**解决**：
- 增加 `max_idle_conns_per_host`
- 减少 `idle_conn_timeout`
- 检查上游服务器性能

#### 2. 连接泄漏
**现象**：连接数持续增长
**解决**：
- 检查响应体是否正确关闭
- 设置合理的 `max_conns_per_host`
- 启用连接超时机制

#### 3. DNS解析问题
**现象**：连接建立失败
**解决**：
- 增加 `dial_timeout`
- 检查DNS配置
- 使用IP地址替代域名

### 调试技巧

#### 1. 启用详细日志
```json
{
  "log": {
    "level": "debug"
  }
}
```

#### 2. 监控连接状态
```bash
# 查看连接池统计
curl -u zdw:z7758521 http://localhost:8080/stats | jq '.connection_pool'
```

## 🚀 最佳实践

### 1. 配置优化
- 根据上游服务器数量调整连接池大小
- 设置合理的超时时间
- 启用HTTP/2支持（如果上游支持）

### 2. 监控告警
- 监控连接池使用率
- 设置连接超时告警
- 跟踪连接建立失败率

### 3. 容量规划
- 评估峰值并发需求
- 预留20-30%的连接池容量
- 定期检查和调整配置

## 📈 性能测试

### 测试场景
```bash
# 并发测试
ab -n 10000 -c 100 http://your-proxy/

# 长连接测试
wrk -t12 -c400 -d30s --timeout 10s http://your-proxy/
```

### 预期结果
- **QPS提升**：2-5倍
- **延迟降低**：30-50%
- **资源使用**：CPU和内存使用更稳定

连接池优化是反向代理性能提升的基础，正确配置后可以显著改善系统的并发处理能力和响应性能。

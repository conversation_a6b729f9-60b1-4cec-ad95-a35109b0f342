package ratelimit

import (
	"fmt"
	"net"
	"net/http"
	"sync"
	"time"

	"golang.org/x/time/rate"
)

// RateLimiter 限流器接口
type RateLimiter interface {
	Allow(key string) bool
	GetStats() map[string]interface{}
}

// TokenBucketLimiter 令牌桶限流器
type TokenBucketLimiter struct {
	limiters map[string]*rate.Limiter
	mu       sync.RWMutex
	rate     rate.Limit
	burst    int
	cleanup  *time.Ticker
}

// NewTokenBucketLimiter 创建令牌桶限流器
func NewTokenBucketLimiter(rps int, burst int) *TokenBucketLimiter {
	limiter := &TokenBucketLimiter{
		limiters: make(map[string]*rate.Limiter),
		rate:     rate.Limit(rps),
		burst:    burst,
		cleanup:  time.NewTicker(time.Minute),
	}

	// 启动清理协程
	go limiter.cleanupRoutine()

	return limiter
}

// Allow 检查是否允许请求
func (tbl *TokenBucketLimiter) Allow(key string) bool {
	tbl.mu.Lock()
	defer tbl.mu.Unlock()

	limiter, exists := tbl.limiters[key]
	if !exists {
		limiter = rate.NewLimiter(tbl.rate, tbl.burst)
		tbl.limiters[key] = limiter
	}

	return limiter.Allow()
}

// GetStats 获取统计信息
func (tbl *TokenBucketLimiter) GetStats() map[string]interface{} {
	tbl.mu.RLock()
	defer tbl.mu.RUnlock()

	return map[string]interface{}{
		"type":        "token_bucket",
		"rate":        float64(tbl.rate),
		"burst":       tbl.burst,
		"active_keys": len(tbl.limiters),
	}
}

// cleanupRoutine 清理过期的限流器
func (tbl *TokenBucketLimiter) cleanupRoutine() {
	for range tbl.cleanup.C {
		tbl.mu.Lock()
		for key, limiter := range tbl.limiters {
			// 修复Go 1.20循环变量陷阱：创建局部副本
			key := key
			limiter := limiter
			// 如果限流器长时间未使用，删除它
			if limiter.Tokens() == float64(tbl.burst) {
				delete(tbl.limiters, key)
			}
		}
		tbl.mu.Unlock()
	}
}

// Close 关闭限流器
func (tbl *TokenBucketLimiter) Close() {
	tbl.cleanup.Stop()
}

// RateLimitConfig 限流配置
type RateLimitConfig struct {
	GlobalRPS int `json:"global_rps"`
	IPRPS     int `json:"ip_rps"`
	SiteRPS   int `json:"site_rps"`
	Burst     int `json:"burst"`
}

// RateLimitManager 限流管理器
type RateLimitManager struct {
	globalLimiter *TokenBucketLimiter
	ipLimiter     *TokenBucketLimiter
	siteLimiter   *TokenBucketLimiter
	config        RateLimitConfig
	stats         struct {
		globalBlocked int64
		ipBlocked     int64
		siteBlocked   int64
		totalRequests int64
	}
	mu sync.RWMutex
}

// NewRateLimitManager 创建限流管理器
func NewRateLimitManager(config RateLimitConfig) *RateLimitManager {
	rlm := &RateLimitManager{
		config: config,
	}

	if config.GlobalRPS > 0 {
		rlm.globalLimiter = NewTokenBucketLimiter(config.GlobalRPS, config.Burst)
	}
	if config.IPRPS > 0 {
		rlm.ipLimiter = NewTokenBucketLimiter(config.IPRPS, config.Burst)
	}
	if config.SiteRPS > 0 {
		rlm.siteLimiter = NewTokenBucketLimiter(config.SiteRPS, config.Burst)
	}

	return rlm
}

// CheckRequest 检查请求是否被限流
func (rlm *RateLimitManager) CheckRequest(r *http.Request, siteName string) error {
	rlm.mu.Lock()
	rlm.stats.totalRequests++
	rlm.mu.Unlock()

	// 全局限流
	if rlm.globalLimiter != nil {
		if !rlm.globalLimiter.Allow("global") {
			rlm.mu.Lock()
			rlm.stats.globalBlocked++
			rlm.mu.Unlock()
			return fmt.Errorf("全局请求频率超限")
		}
	}

	// IP限流
	if rlm.ipLimiter != nil {
		clientIP := getClientIP(r)
		if !rlm.ipLimiter.Allow(clientIP) {
			rlm.mu.Lock()
			rlm.stats.ipBlocked++
			rlm.mu.Unlock()
			return fmt.Errorf("IP请求频率超限")
		}
	}

	// 站点限流
	if rlm.siteLimiter != nil {
		if !rlm.siteLimiter.Allow(siteName) {
			rlm.mu.Lock()
			rlm.stats.siteBlocked++
			rlm.mu.Unlock()
			return fmt.Errorf("站点请求频率超限")
		}
	}

	return nil
}

// GetStats 获取统计信息
func (rlm *RateLimitManager) GetStats() map[string]interface{} {
	rlm.mu.RLock()
	defer rlm.mu.RUnlock()

	stats := map[string]interface{}{
		"config": rlm.config,
		"stats": map[string]interface{}{
			"total_requests": rlm.stats.totalRequests,
			"global_blocked": rlm.stats.globalBlocked,
			"ip_blocked":     rlm.stats.ipBlocked,
			"site_blocked":   rlm.stats.siteBlocked,
		},
	}

	if rlm.globalLimiter != nil {
		stats["global_limiter"] = rlm.globalLimiter.GetStats()
	}
	if rlm.ipLimiter != nil {
		stats["ip_limiter"] = rlm.ipLimiter.GetStats()
	}
	if rlm.siteLimiter != nil {
		stats["site_limiter"] = rlm.siteLimiter.GetStats()
	}

	return stats
}

// Close 关闭限流管理器
func (rlm *RateLimitManager) Close() {
	if rlm.globalLimiter != nil {
		rlm.globalLimiter.Close()
	}
	if rlm.ipLimiter != nil {
		rlm.ipLimiter.Close()
	}
	if rlm.siteLimiter != nil {
		rlm.siteLimiter.Close()
	}
}

// getClientIP 获取客户端IP
func getClientIP(r *http.Request) string {
	// 检查X-Forwarded-For头
	if xff := r.Header.Get("X-Forwarded-For"); xff != "" {
		// 取第一个IP
		if idx := len(xff); idx > 0 {
			if commaIdx := 0; commaIdx < idx {
				for i, c := range xff {
					if c == ',' {
						commaIdx = i
						break
					}
				}
				if commaIdx > 0 {
					return xff[:commaIdx]
				}
			}
			return xff
		}
	}

	// 检查X-Real-IP头
	if xri := r.Header.Get("X-Real-IP"); xri != "" {
		return xri
	}

	// 使用RemoteAddr
	ip, _, err := net.SplitHostPort(r.RemoteAddr)
	if err != nil {
		return r.RemoteAddr
	}
	return ip
}

// CircuitBreakerState 熔断器状态
type CircuitBreakerState int

const (
	StateClosed CircuitBreakerState = iota
	StateHalfOpen
	StateOpen
)

// CircuitBreaker 熔断器
type CircuitBreaker struct {
	maxFailures  int
	resetTimeout time.Duration
	state        CircuitBreakerState
	failures     int
	lastFailTime time.Time
	halfOpenReqs int
	maxHalfOpen  int
	mu           sync.RWMutex
}

// NewCircuitBreaker 创建熔断器
func NewCircuitBreaker(maxFailures int, resetTimeout time.Duration, maxHalfOpen int) *CircuitBreaker {
	return &CircuitBreaker{
		maxFailures:  maxFailures,
		resetTimeout: resetTimeout,
		state:        StateClosed,
		maxHalfOpen:  maxHalfOpen,
	}
}

// Call 执行调用
func (cb *CircuitBreaker) Call(fn func() error) error {
	cb.mu.Lock()
	defer cb.mu.Unlock()

	// 检查是否可以执行
	if !cb.canExecute() {
		return fmt.Errorf("熔断器开启，拒绝请求")
	}

	// 执行函数
	err := fn()

	// 更新状态
	if err != nil {
		cb.onFailure()
	} else {
		cb.onSuccess()
	}

	return err
}

// canExecute 检查是否可以执行
func (cb *CircuitBreaker) canExecute() bool {
	switch cb.state {
	case StateClosed:
		return true
	case StateOpen:
		// 检查是否可以转为半开状态
		if time.Since(cb.lastFailTime) > cb.resetTimeout {
			cb.state = StateHalfOpen
			cb.halfOpenReqs = 0
			return true
		}
		return false
	case StateHalfOpen:
		return cb.halfOpenReqs < cb.maxHalfOpen
	default:
		return false
	}
}

// onSuccess 成功回调
func (cb *CircuitBreaker) onSuccess() {
	switch cb.state {
	case StateHalfOpen:
		cb.halfOpenReqs++
		if cb.halfOpenReqs >= cb.maxHalfOpen {
			cb.state = StateClosed
			cb.failures = 0
		}
	case StateClosed:
		cb.failures = 0
	}
}

// onFailure 失败回调
func (cb *CircuitBreaker) onFailure() {
	cb.failures++
	cb.lastFailTime = time.Now()

	if cb.failures >= cb.maxFailures {
		cb.state = StateOpen
	}
}

// GetState 获取状态
func (cb *CircuitBreaker) GetState() CircuitBreakerState {
	cb.mu.RLock()
	defer cb.mu.RUnlock()
	return cb.state
}

// GetStats 获取统计信息
func (cb *CircuitBreaker) GetStats() map[string]interface{} {
	cb.mu.RLock()
	defer cb.mu.RUnlock()

	stateStr := "closed"
	switch cb.state {
	case StateOpen:
		stateStr = "open"
	case StateHalfOpen:
		stateStr = "half_open"
	}

	return map[string]interface{}{
		"state":          stateStr,
		"failures":       cb.failures,
		"max_failures":   cb.maxFailures,
		"last_fail_time": cb.lastFailTime,
		"half_open_reqs": cb.halfOpenReqs,
		"max_half_open":  cb.maxHalfOpen,
	}
}

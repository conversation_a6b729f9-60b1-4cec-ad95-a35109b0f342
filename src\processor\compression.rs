use async_trait::async_trait;
use bytes::Bytes;
use std::io::Write;
use tracing::{debug, error, info};

#[cfg(feature = "compression")]
use {
    flate2::{write::<PERSON><PERSON><PERSON>ncoder, Compression as FlateCompression},
};

use super::{Processor, ProcessResult, RequestContext};

/// 压缩算法类型
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum CompressionType {
    Gzip,
    Brotli,
    Zstd,
    Deflate,
}

impl CompressionType {
    /// 从Accept-Encoding头部解析压缩类型
    pub fn from_accept_encoding(accept_encoding: &str) -> Vec<(Self, f32)> {
        let mut algorithms = Vec::new();

        for part in accept_encoding.split(',') {
            let part = part.trim();
            let (name, quality) = if let Some(pos) = part.find(";q=") {
                let name = part[..pos].trim();
                let quality_str = &part[pos + 3..];
                let quality = quality_str.parse::<f32>().unwrap_or(1.0);
                (name, quality)
            } else {
                (part, 1.0)
            };

            let compression_type = match name {
                "gzip" => Some(CompressionType::Gzip),
                "br" => Some(CompressionType::Brotli),
                "zstd" => Some(CompressionType::Zstd),
                "deflate" => Some(CompressionType::Deflate),
                _ => None,
            };

            if let Some(comp_type) = compression_type {
                algorithms.push((comp_type, quality));
            }
        }

        // 按质量值排序（降序）
        algorithms.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap_or(std::cmp::Ordering::Equal));
        algorithms
    }

    /// 获取压缩算法的编码名称
    pub fn encoding_name(&self) -> &'static str {
        match self {
            CompressionType::Gzip => "gzip",
            CompressionType::Brotli => "br",
            CompressionType::Zstd => "zstd",
            CompressionType::Deflate => "deflate",
        }
    }
}

/// 压缩处理器配置
#[derive(Debug, Clone)]
pub struct CompressionConfig {
    /// 是否启用压缩
    pub enabled: bool,
    /// 最小压缩大小（字节）
    pub min_size: usize,
    /// 最大压缩大小（字节）
    pub max_size: usize,
    /// 压缩级别（1-9，9为最高压缩率）
    pub level: u32,
    /// 可压缩的MIME类型
    pub compressible_types: Vec<String>,
    /// 不可压缩的MIME类型
    pub excluded_types: Vec<String>,
    /// 支持的压缩算法（按优先级排序）
    pub algorithms: Vec<CompressionType>,
}

impl Default for CompressionConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            min_size: 1024,        // 1KB
            max_size: 10 * 1024 * 1024, // 10MB
            level: 6,              // 平衡压缩率和速度
            compressible_types: vec![
                "text/html".to_string(),
                "text/css".to_string(),
                "text/javascript".to_string(),
                "text/plain".to_string(),
                "text/xml".to_string(),
                "application/json".to_string(),
                "application/javascript".to_string(),
                "application/xml".to_string(),
                "application/rss+xml".to_string(),
                "application/atom+xml".to_string(),
                "image/svg+xml".to_string(),
            ],
            excluded_types: vec![
                "image/jpeg".to_string(),
                "image/png".to_string(),
                "image/gif".to_string(),
                "image/webp".to_string(),
                "video/".to_string(),
                "audio/".to_string(),
                "application/zip".to_string(),
                "application/gzip".to_string(),
                "application/x-rar".to_string(),
            ],
            algorithms: vec![
                CompressionType::Brotli,  // 最佳压缩率
                CompressionType::Zstd,    // 快速压缩
                CompressionType::Gzip,    // 广泛支持
                CompressionType::Deflate, // 兼容性
            ],
        }
    }
}

/// 压缩处理器
pub struct CompressionProcessor {
    config: CompressionConfig,
}

impl CompressionProcessor {
    /// 创建新的压缩处理器
    pub fn new() -> Self {
        Self {
            config: CompressionConfig::default(),
        }
    }

    /// 使用指定配置创建压缩处理器
    pub fn with_config(config: CompressionConfig) -> Self {
        Self { config }
    }
}

impl Default for CompressionProcessor {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl Processor for CompressionProcessor {
    fn name(&self) -> &'static str {
        "CompressionProcessor"
    }
    
    fn priority(&self) -> u8 {
        80 // 在响应生成后执行压缩
    }
    
    fn should_process(&self, ctx: &RequestContext) -> bool {
        // 检查是否启用压缩
        if !self.config.enabled {
            return false;
        }

        // 检查是否有响应体
        if ctx.response.body.is_none() {
            return false;
        }

        // 检查响应是否已经压缩
        if ctx.response.headers.contains_key("content-encoding") {
            return false;
        }

        // 检查客户端是否支持压缩
        if let Some(accept_encoding) = ctx.get_header("accept-encoding") {
            let supported_algorithms = CompressionType::from_accept_encoding(accept_encoding);
            !supported_algorithms.is_empty()
        } else {
            false
        }
    }

    async fn process(&self, ctx: &mut RequestContext) -> ProcessResult {
        debug!("CompressionProcessor: Processing request {}", ctx.request_id);

        // 检查响应内容是否适合压缩
        if !self.should_compress_response(ctx) {
            debug!("Response not suitable for compression for request {}", ctx.request_id);
            return ProcessResult::Continue;
        }

        // 选择最佳压缩算法
        let compression_type = match self.select_compression_algorithm(ctx) {
            Some(comp_type) => comp_type,
            None => {
                debug!("No suitable compression algorithm for request {}", ctx.request_id);
                return ProcessResult::Continue;
            }
        };

        // 压缩响应体
        match self.compress_response(ctx, compression_type).await {
            Ok(compressed_size) => {
                info!(
                    "Compressed response for request {} using {} (original: {} bytes, compressed: {} bytes, ratio: {:.2}%)",
                    ctx.request_id,
                    compression_type.encoding_name(),
                    ctx.response.body.as_ref().map(|b| b.len()).unwrap_or(0),
                    compressed_size,
                    if ctx.response.body.as_ref().map(|b| b.len()).unwrap_or(0) > 0 {
                        (compressed_size as f64 / ctx.response.body.as_ref().unwrap().len() as f64) * 100.0
                    } else {
                        0.0
                    }
                );
                ProcessResult::Continue
            }
            Err(e) => {
                error!("Failed to compress response for request {}: {}", ctx.request_id, e);
                ProcessResult::Continue // 继续处理，不因压缩失败而中断
            }
        }
    }
}

impl CompressionProcessor {
    /// 检查响应是否适合压缩
    fn should_compress_response(&self, ctx: &RequestContext) -> bool {
        let body = match &ctx.response.body {
            Some(body) => body,
            None => return false,
        };

        // 检查响应大小
        let size = body.len();
        if size < self.config.min_size || size > self.config.max_size {
            return false;
        }

        // 检查内容类型
        let content_type = ctx.response.headers.get("content-type")
            .unwrap_or(&"application/octet-stream".to_string())
            .to_lowercase();

        // 检查是否在排除列表中
        for excluded_type in &self.config.excluded_types {
            if content_type.starts_with(&excluded_type.to_lowercase()) {
                return false;
            }
        }

        // 检查是否在可压缩列表中
        for compressible_type in &self.config.compressible_types {
            if content_type.starts_with(&compressible_type.to_lowercase()) {
                return true;
            }
        }

        // 默认情况下，text/* 类型都可以压缩
        content_type.starts_with("text/")
    }

    /// 选择最佳压缩算法
    fn select_compression_algorithm(&self, ctx: &RequestContext) -> Option<CompressionType> {
        let accept_encoding = ctx.get_header("accept-encoding")?;
        let client_supported = CompressionType::from_accept_encoding(accept_encoding);

        // 按配置的优先级选择第一个客户端支持的算法
        for &algorithm in &self.config.algorithms {
            if client_supported.iter().any(|(comp_type, _)| *comp_type == algorithm) {
                return Some(algorithm);
            }
        }

        None
    }

    /// 压缩响应体
    async fn compress_response(&self, ctx: &mut RequestContext, compression_type: CompressionType) -> anyhow::Result<usize> {
        let body = ctx.response.body.take().ok_or_else(|| anyhow::anyhow!("No response body"))?;

        let compressed_data = match compression_type {
            #[cfg(feature = "compression")]
            CompressionType::Gzip => self.compress_gzip(&body)?,
            #[cfg(feature = "compression")]
            CompressionType::Deflate => self.compress_deflate(&body)?,
            #[cfg(feature = "compression")]
            CompressionType::Brotli => self.compress_brotli(&body)?,
            #[cfg(feature = "compression")]
            CompressionType::Zstd => self.compress_zstd(&body)?,
            #[cfg(not(feature = "compression"))]
            _ => return Err(anyhow::anyhow!("Compression feature not enabled")),
        };

        let compressed_size = compressed_data.len();

        // 检查压缩效果，如果压缩后更大则不使用压缩
        let original_size = body.len();
        if compressed_size >= original_size {
            ctx.response.body = Some(body);
            return Ok(original_size);
        }

        // 设置压缩后的响应体和头部
        ctx.response.body = Some(compressed_data);
        ctx.response.headers.insert("content-encoding".to_string(), compression_type.encoding_name().to_string());
        ctx.response.headers.insert("content-length".to_string(), compressed_size.to_string());

        // 添加Vary头部
        let vary = ctx.response.headers.get("vary")
            .map(|v| format!("{}, Accept-Encoding", v))
            .unwrap_or_else(|| "Accept-Encoding".to_string());
        ctx.response.headers.insert("vary".to_string(), vary);

        Ok(compressed_size)
    }

    /// Gzip压缩
    #[cfg(feature = "compression")]
    fn compress_gzip(&self, data: &Bytes) -> anyhow::Result<Bytes> {
        let mut encoder = GzEncoder::new(Vec::new(), FlateCompression::new(self.config.level));
        encoder.write_all(data)?;
        let compressed = encoder.finish()?;
        Ok(Bytes::from(compressed))
    }

    /// Deflate压缩
    #[cfg(feature = "compression")]
    fn compress_deflate(&self, data: &Bytes) -> anyhow::Result<Bytes> {
        use flate2::write::DeflateEncoder;

        let mut encoder = DeflateEncoder::new(Vec::new(), FlateCompression::new(self.config.level));
        encoder.write_all(data)?;
        let compressed = encoder.finish()?;
        Ok(Bytes::from(compressed))
    }

    /// Brotli压缩
    #[cfg(feature = "compression")]
    fn compress_brotli(&self, data: &Bytes) -> anyhow::Result<Bytes> {
        let mut compressed = Vec::new();
        let mut compressor = brotli::CompressorWriter::new(&mut compressed, 4096, self.config.level, 22);
        compressor.write_all(data)?;
        compressor.flush()?;
        drop(compressor);

        Ok(Bytes::from(compressed))
    }

    /// Zstd压缩
    #[cfg(feature = "compression")]
    fn compress_zstd(&self, data: &Bytes) -> anyhow::Result<Bytes> {
        let compressed = zstd::bulk::compress(data, self.config.level as i32)?;
        Ok(Bytes::from(compressed))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashMap;
    use std::net::SocketAddr;

    fn create_test_context() -> RequestContext {
        RequestContext::new(
            "GET".to_string(),
            "/test".to_string(),
            HashMap::new(),
            "127.0.0.1:8080".parse::<SocketAddr>().unwrap(),
        )
    }

    #[test]
    fn test_compression_type_from_accept_encoding() {
        let accept_encoding = "gzip, deflate, br;q=0.8, zstd;q=0.9";
        let algorithms = CompressionType::from_accept_encoding(accept_encoding);

        assert_eq!(algorithms.len(), 4);
        assert_eq!(algorithms[0], (CompressionType::Gzip, 1.0));
        assert_eq!(algorithms[1], (CompressionType::Deflate, 1.0));
        assert_eq!(algorithms[2], (CompressionType::Zstd, 0.9));
        assert_eq!(algorithms[3], (CompressionType::Brotli, 0.8));
    }

    #[test]
    fn test_should_process() {
        let processor = CompressionProcessor::new();
        let mut ctx = create_test_context();

        // 没有Accept-Encoding头部时不应该处理
        assert!(!processor.should_process(&ctx));

        // 有Accept-Encoding头部时应该处理
        ctx.headers.insert("accept-encoding".to_string(), "gzip, deflate".to_string());
        ctx.response.body = Some(Bytes::from("test content"));
        assert!(processor.should_process(&ctx));

        // 已经有content-encoding时不应该处理
        ctx.response.headers.insert("content-encoding".to_string(), "gzip".to_string());
        assert!(!processor.should_process(&ctx));
    }

    #[test]
    fn test_should_compress_response() {
        let processor = CompressionProcessor::new();
        let mut ctx = create_test_context();

        // 没有响应体时不应该压缩
        assert!(!processor.should_compress_response(&ctx));

        // 响应体太小时不应该压缩
        ctx.response.body = Some(Bytes::from("small"));
        assert!(!processor.should_compress_response(&ctx));

        // 可压缩的内容类型应该压缩
        let large_content = "a".repeat(2000);
        ctx.response.body = Some(Bytes::from(large_content));
        ctx.response.headers.insert("content-type".to_string(), "text/html".to_string());
        assert!(processor.should_compress_response(&ctx));

        // 不可压缩的内容类型不应该压缩
        ctx.response.headers.insert("content-type".to_string(), "image/jpeg".to_string());
        assert!(!processor.should_compress_response(&ctx));
    }

    #[test]
    fn test_select_compression_algorithm() {
        let processor = CompressionProcessor::new();
        let mut ctx = create_test_context();

        // 没有Accept-Encoding头部时返回None
        assert!(processor.select_compression_algorithm(&ctx).is_none());

        // 有支持的算法时返回最优的
        ctx.headers.insert("accept-encoding".to_string(), "gzip, br".to_string());
        assert_eq!(processor.select_compression_algorithm(&ctx), Some(CompressionType::Brotli));

        // 只支持gzip时返回gzip
        ctx.headers.insert("accept-encoding".to_string(), "gzip".to_string());
        assert_eq!(processor.select_compression_algorithm(&ctx), Some(CompressionType::Gzip));
    }

    #[cfg(feature = "compression")]
    #[test]
    fn test_gzip_compression() {
        let processor = CompressionProcessor::new();
        let test_data = Bytes::from("Hello, World! This is a test string for compression.");

        let compressed = processor.compress_gzip(&test_data).unwrap();
        assert!(compressed.len() < test_data.len());

        // 验证可以解压缩
        #[cfg(feature = "compression")]
        {
            use flate2::read::GzDecoder;
            use std::io::Read;

            let mut decoder = GzDecoder::new(&compressed[..]);
            let mut decompressed = Vec::new();
            decoder.read_to_end(&mut decompressed).unwrap();

            assert_eq!(decompressed, test_data.as_ref());
        }
    }

    #[cfg(feature = "compression")]
    #[test]
    fn test_brotli_compression() {
        let processor = CompressionProcessor::new();
        let test_data = Bytes::from("Hello, World! This is a test string for compression.");

        let compressed = processor.compress_brotli(&test_data).unwrap();
        assert!(compressed.len() < test_data.len());
    }
}

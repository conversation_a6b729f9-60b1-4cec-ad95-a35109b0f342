# 条件编译构建系统
# 单代码库支持Core版和Pro版

# 版本信息
VERSION ?= 1.0.0
BUILD_TIME := $(shell date -u '+%Y-%m-%d_%H:%M:%S')
GIT_COMMIT := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# 构建标志
LDFLAGS_COMMON := -s -w \
	-X main.Version=$(VERSION) \
	-X main.BuildTime=$(BUILD_TIME) \
	-X main.GitCommit=$(GIT_COMMIT)

LDFLAGS_CORE := $(LDFLAGS_COMMON) -X main.Edition=Core
LDFLAGS_PRO := $(LDFLAGS_COMMON) -X main.Edition=Pro

# 构建标签
BUILD_TAGS_CORE := core
BUILD_TAGS_PRO := pro

# 输出目录和文件
BIN_DIR := bin
CORE_BINARY := $(BIN_DIR)/reverse-proxy-core
PRO_BINARY := $(BIN_DIR)/reverse-proxy-pro

# 源文件
CORE_MAIN := cmd/core/main.go
PRO_MAIN := cmd/pro/main.go

# 默认目标
.DEFAULT_GOAL := help

# ============================================================================
# 帮助信息
# ============================================================================

.PHONY: help
help: ## 显示帮助信息
	@echo "单代码库条件编译构建系统"
	@echo ""
	@echo "版本信息:"
	@echo "  版本: $(VERSION)"
	@echo "  构建时间: $(BUILD_TIME)"
	@echo "  Git提交: $(GIT_COMMIT)"
	@echo ""
	@echo "可用目标:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-20s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# ============================================================================
# 目录创建
# ============================================================================

$(BIN_DIR):
	@mkdir -p $(BIN_DIR)

# ============================================================================
# 构建目标
# ============================================================================

.PHONY: build-core
build-core: $(BIN_DIR) ## 构建Core版 (开源版本)
	@echo "🔧 构建Core版..."
	@echo "  构建标签: $(BUILD_TAGS_CORE)"
	@echo "  输出文件: $(CORE_BINARY)"
	go build -tags="$(BUILD_TAGS_CORE)" \
		-ldflags="$(LDFLAGS_CORE)" \
		-o $(CORE_BINARY) \
		$(CORE_MAIN)
	@echo "✅ Core版构建完成"
	@ls -lh $(CORE_BINARY)

.PHONY: build-pro
build-pro: $(BIN_DIR) ## 构建Pro版 (商业版本)
	@echo "🚀 构建Pro版..."
	@echo "  构建标签: $(BUILD_TAGS_PRO)"
	@echo "  输出文件: $(PRO_BINARY)"
	go build -tags="$(BUILD_TAGS_PRO)" \
		-ldflags="$(LDFLAGS_PRO)" \
		-o $(PRO_BINARY) \
		$(PRO_MAIN)
	@echo "✅ Pro版构建完成"
	@ls -lh $(PRO_BINARY)

.PHONY: build-all
build-all: build-core build-pro ## 构建所有版本
	@echo ""
	@echo "📊 构建结果对比:"
	@printf "%-15s %-10s %-15s\n" "版本" "大小" "路径"
	@printf "%-15s %-10s %-15s\n" "----" "----" "----"
	@printf "%-15s %-10s %-15s\n" "Core版" "$$(ls -lh $(CORE_BINARY) | awk '{print $$5}')" "$(CORE_BINARY)"
	@printf "%-15s %-10s %-15s\n" "Pro版" "$$(ls -lh $(PRO_BINARY) | awk '{print $$5}')" "$(PRO_BINARY)"

# ============================================================================
# 测试目标
# ============================================================================

.PHONY: test-core
test-core: ## 测试Core版功能
	@echo "🧪 测试Core版..."
	go test -tags="$(BUILD_TAGS_CORE)" -v ./internal/core/... ./internal/shared/...

.PHONY: test-pro
test-pro: ## 测试Pro版功能
	@echo "🧪 测试Pro版..."
	go test -tags="$(BUILD_TAGS_PRO)" -v ./internal/... 

.PHONY: test-all
test-all: test-core test-pro ## 测试所有版本

.PHONY: test-separation
test-separation: build-all ## 测试版本分离效果
	@echo "🔍 测试版本分离..."
	@echo ""
	@echo "1. 检查Core版是否包含Pro功能..."
	@if strings $(CORE_BINARY) | grep -qi "zero.copy\|object.pool\|consistent.hash\|license"; then \
		echo "❌ Core版意外包含Pro功能关键词"; \
		strings $(CORE_BINARY) | grep -i "zero.copy\|object.pool\|consistent.hash\|license" | head -5; \
		exit 1; \
	else \
		echo "✅ Core版功能分离正确"; \
	fi
	@echo ""
	@echo "2. 检查Pro版是否包含完整功能..."
	@if strings $(PRO_BINARY) | grep -qi "zero.copy\|object.pool\|consistent.hash"; then \
		echo "✅ Pro版包含高级功能"; \
	else \
		echo "❌ Pro版缺少高级功能"; \
		exit 1; \
	fi
	@echo ""
	@echo "3. 功能运行测试..."
	@echo "Core版运行测试:"
	@$(CORE_BINARY) --version 2>/dev/null || echo "Core版可执行"
	@echo "Pro版运行测试:"
	@$(PRO_BINARY) --version 2>/dev/null || echo "Pro版可执行"

# ============================================================================
# 基准测试
# ============================================================================

.PHONY: bench-core
bench-core: ## Core版性能基准测试
	@echo "📈 Core版性能测试..."
	go test -tags="$(BUILD_TAGS_CORE)" -bench=. -benchmem ./internal/core/...

.PHONY: bench-pro
bench-pro: ## Pro版性能基准测试
	@echo "📈 Pro版性能测试..."
	go test -tags="$(BUILD_TAGS_PRO)" -bench=. -benchmem ./internal/pro/...

.PHONY: bench-compare
bench-compare: ## 对比两版本性能
	@echo "📊 性能对比测试..."
	@echo "Core版基准测试:" > bench-results.txt
	@go test -tags="$(BUILD_TAGS_CORE)" -bench=. -benchmem ./internal/core/... >> bench-results.txt 2>&1
	@echo "" >> bench-results.txt
	@echo "Pro版基准测试:" >> bench-results.txt
	@go test -tags="$(BUILD_TAGS_PRO)" -bench=. -benchmem ./internal/pro/... >> bench-results.txt 2>&1
	@echo "性能对比结果已保存到 bench-results.txt"

# ============================================================================
# 运行目标
# ============================================================================

.PHONY: run-core
run-core: build-core ## 运行Core版
	@echo "🔧 启动Core版..."
	$(CORE_BINARY) -config configs/core.json

.PHONY: run-pro
run-pro: build-pro ## 运行Pro版
	@echo "🚀 启动Pro版..."
	$(PRO_BINARY) -config configs/pro.json

.PHONY: demo-core
demo-core: build-core ## 演示Core版功能
	@echo "🔧 Core版功能演示:"
	@echo "版本信息:"
	@$(CORE_BINARY) --version || echo "Core版 $(VERSION)"
	@echo ""
	@echo "可用功能:"
	@$(CORE_BINARY) --features || echo "基础代理、文件缓存、轮询负载均衡"

.PHONY: demo-pro
demo-pro: build-pro ## 演示Pro版功能
	@echo "🚀 Pro版功能演示:"
	@echo "版本信息:"
	@$(PRO_BINARY) --version || echo "Pro版 $(VERSION)"
	@echo ""
	@echo "可用功能:"
	@$(PRO_BINARY) --features || echo "高性能引擎、内存映射缓存、一致性哈希、HTTP/3、gRPC"

# ============================================================================
# 代码质量
# ============================================================================

.PHONY: lint
lint: ## 代码检查
	@echo "🔍 代码检查..."
	@which golangci-lint > /dev/null || (echo "请安装 golangci-lint" && exit 1)
	golangci-lint run

.PHONY: fmt
fmt: ## 格式化代码
	@echo "🎨 格式化代码..."
	go fmt ./...

.PHONY: vet
vet: ## 代码静态分析
	@echo "🔍 静态分析..."
	go vet -tags="$(BUILD_TAGS_CORE)" ./...
	go vet -tags="$(BUILD_TAGS_PRO)" ./...

.PHONY: mod-tidy
mod-tidy: ## 整理依赖
	@echo "📦 整理依赖..."
	go mod tidy

# ============================================================================
# 打包和发布
# ============================================================================

.PHONY: package-core
package-core: build-core ## 打包Core版
	@echo "📦 打包Core版..."
	@mkdir -p dist/core
	@cp $(CORE_BINARY) dist/core/
	@cp configs/core.json dist/core/config.json
	@cp README-core.md dist/core/README.md 2>/dev/null || echo "# Core版" > dist/core/README.md
	@cp LICENSE dist/core/ 2>/dev/null || echo "MIT License" > dist/core/LICENSE
	@tar -czf dist/reverse-proxy-core-$(VERSION).tar.gz -C dist/core .
	@echo "✅ Core版打包完成: dist/reverse-proxy-core-$(VERSION).tar.gz"

.PHONY: package-pro
package-pro: build-pro ## 打包Pro版
	@echo "📦 打包Pro版..."
	@mkdir -p dist/pro
	@cp $(PRO_BINARY) dist/pro/
	@cp configs/pro.json dist/pro/config.json
	@cp README-pro.md dist/pro/README.md 2>/dev/null || echo "# Pro版" > dist/pro/README.md
	@echo "Commercial License" > dist/pro/LICENSE
	@tar -czf dist/reverse-proxy-pro-$(VERSION).tar.gz -C dist/pro .
	@echo "✅ Pro版打包完成: dist/reverse-proxy-pro-$(VERSION).tar.gz"

.PHONY: package-all
package-all: package-core package-pro ## 打包所有版本

# ============================================================================
# Docker构建
# ============================================================================

.PHONY: docker-core
docker-core: ## 构建Core版Docker镜像
	@echo "🐳 构建Core版Docker镜像..."
	docker build \
		--build-arg VERSION=$(VERSION) \
		--build-arg BUILD_TAGS=$(BUILD_TAGS_CORE) \
		--build-arg EDITION=core \
		-f docker/Dockerfile.core \
		-t reverse-proxy:core-$(VERSION) \
		-t reverse-proxy:core-latest .

.PHONY: docker-pro
docker-pro: ## 构建Pro版Docker镜像
	@echo "🐳 构建Pro版Docker镜像..."
	docker build \
		--build-arg VERSION=$(VERSION) \
		--build-arg BUILD_TAGS=$(BUILD_TAGS_PRO) \
		--build-arg EDITION=pro \
		-f docker/Dockerfile.pro \
		-t reverse-proxy:pro-$(VERSION) \
		-t reverse-proxy:pro-latest .

.PHONY: docker-all
docker-all: docker-core docker-pro ## 构建所有Docker镜像

# ============================================================================
# 清理目标
# ============================================================================

.PHONY: clean
clean: ## 清理构建文件
	@echo "🧹 清理构建文件..."
	@rm -rf $(BIN_DIR)
	@rm -rf dist
	@rm -f bench-results.txt
	@echo "✅ 清理完成"

.PHONY: clean-docker
clean-docker: ## 清理Docker镜像
	@echo "🧹 清理Docker镜像..."
	@docker rmi reverse-proxy:core-$(VERSION) reverse-proxy:core-latest 2>/dev/null || true
	@docker rmi reverse-proxy:pro-$(VERSION) reverse-proxy:pro-latest 2>/dev/null || true

.PHONY: clean-all
clean-all: clean clean-docker ## 清理所有文件

# ============================================================================
# 开发目标
# ============================================================================

.PHONY: dev-core
dev-core: ## 开发模式运行Core版
	@echo "🔧 开发模式 - Core版..."
	go run -tags="$(BUILD_TAGS_CORE)" $(CORE_MAIN) -config configs/core.json

.PHONY: dev-pro
dev-pro: ## 开发模式运行Pro版
	@echo "🚀 开发模式 - Pro版..."
	go run -tags="$(BUILD_TAGS_PRO)" $(PRO_MAIN) -config configs/pro.json

.PHONY: watch-core
watch-core: ## 监控文件变化并重新构建Core版
	@echo "👀 监控Core版文件变化..."
	@which fswatch > /dev/null || (echo "请安装 fswatch" && exit 1)
	fswatch -o . | xargs -n1 -I{} make build-core

.PHONY: watch-pro
watch-pro: ## 监控文件变化并重新构建Pro版
	@echo "👀 监控Pro版文件变化..."
	@which fswatch > /dev/null || (echo "请安装 fswatch" && exit 1)
	fswatch -o . | xargs -n1 -I{} make build-pro

# ============================================================================
# 信息目标
# ============================================================================

.PHONY: info
info: ## 显示构建信息
	@echo "构建信息:"
	@echo "  版本: $(VERSION)"
	@echo "  构建时间: $(BUILD_TIME)"
	@echo "  Git提交: $(GIT_COMMIT)"
	@echo "  Go版本: $$(go version)"
	@echo ""
	@echo "构建标签:"
	@echo "  Core版: $(BUILD_TAGS_CORE)"
	@echo "  Pro版: $(BUILD_TAGS_PRO)"
	@echo ""
	@echo "输出文件:"
	@echo "  Core版: $(CORE_BINARY)"
	@echo "  Pro版: $(PRO_BINARY)"

.PHONY: features-core
features-core: ## 显示Core版功能列表
	@echo "Core版功能列表:"
	@echo "  ✅ HTTP/HTTPS反向代理"
	@echo "  ✅ 基础负载均衡 (轮询、权重)"
	@echo "  ✅ 文件缓存"
	@echo "  ✅ 基础监控"
	@echo "  ✅ 配置热重载"
	@echo "  ✅ 基础ACL"
	@echo "  ✅ SSL/TLS支持"

.PHONY: features-pro
features-pro: ## 显示Pro版功能列表
	@echo "Pro版功能列表:"
	@echo "  ✅ 所有Core版功能"
	@echo "  🚀 高性能引擎 (零拷贝、对象池)"
	@echo "  🚀 HTTP/3支持"
	@echo "  🚀 gRPC代理"
	@echo "  🚀 智能负载均衡 (一致性哈希)"
	@echo "  🚀 内存映射缓存"
	@echo "  🚀 企业级安全 (OCSP、DDoS防护)"
	@echo "  🚀 高级监控和分析"
	@echo "  🚀 智能压缩 (Brotli)"

# ============================================================================
# 发布目标
# ============================================================================

.PHONY: release-core
release-core: clean test-core build-core package-core ## Core版完整发布流程
	@echo "🎉 Core版发布完成!"

.PHONY: release-pro
release-pro: clean test-pro build-pro package-pro ## Pro版完整发布流程
	@echo "🎉 Pro版发布完成!"

.PHONY: release-all
release-all: clean test-all build-all package-all ## 完整发布流程
	@echo "🎉 所有版本发布完成!"
	@echo ""
	@echo "发布文件:"
	@ls -la dist/*.tar.gz

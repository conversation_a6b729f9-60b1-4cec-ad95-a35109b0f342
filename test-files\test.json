{"server": {"name": "RustProxy", "version": "1.0.0", "description": "高性能Rust反向代理服务器", "features": ["静态文件服务", "智能缓存", "响应压缩", "安全头部", "错误处理", "性能监控"], "processors": [{"name": "RouteProcessor", "priority": 10, "description": "路由匹配和站点选择"}, {"name": "CacheProcessor", "priority": 20, "description": "智能缓存处理"}, {"name": "StaticFileProcessor", "priority": 30, "description": "静态文件服务"}, {"name": "ProxyProcessor", "priority": 40, "description": "反向代理处理"}, {"name": "CompressionProcessor", "priority": 80, "description": "响应压缩处理"}, {"name": "HeaderProcessor", "priority": 90, "description": "HTTP头部处理"}, {"name": "ErrorProcessor", "priority": 100, "description": "统一错误处理"}]}, "test": {"timestamp": "2025-07-19T13:53:00Z", "test_cases": [{"name": "静态文件服务测试", "url": "/test.txt", "expected_status": 200, "expected_headers": {"content-type": "text/plain", "cache-control": "public, max-age=3600"}}, {"name": "JSON文件服务测试", "url": "/test.json", "expected_status": 200, "expected_headers": {"content-type": "application/json", "content-encoding": "gzip"}}, {"name": "404错误测试", "url": "/nonexistent.txt", "expected_status": 404, "expected_headers": {"content-type": "text/html; charset=utf-8"}}, {"name": "压缩测试", "url": "/large.txt", "expected_status": 200, "expected_headers": {"content-encoding": "gzip", "vary": "Accept-Encoding"}}]}, "performance": {"target_response_time": "< 50ms", "target_throughput": "> 1000 req/s", "memory_usage": "< 100MB", "cpu_usage": "< 50%"}, "cache": {"enabled": true, "type": "memory", "max_size": "32MB", "ttl": "1h", "hit_rate_target": "> 80%"}, "compression": {"enabled": true, "algorithms": ["brotli", "zstd", "gzip", "deflate"], "min_size": "1KB", "compression_ratio_target": "> 60%"}, "security": {"headers": {"x-frame-options": "DENY", "x-content-type-options": "nosniff", "x-xss-protection": "1; mode=block", "strict-transport-security": "max-age=31536000; includeSubDomains", "content-security-policy": "default-src 'self'"}}}
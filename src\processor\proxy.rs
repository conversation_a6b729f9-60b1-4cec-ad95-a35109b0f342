use async_trait::async_trait;
use bytes::Bytes;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Instant;
use tokio::sync::RwLock;
use tracing::{debug, error, info};

#[cfg(feature = "http-server")]
use {
    http::{Method, Request, Uri, HeaderMap, HeaderValue, HeaderName},
    http_body_util::{BodyExt, Full},
    hyper_util::{
        client::legacy::{Client, connect::HttpConnector},
        rt::TokioExecutor,
    },
};

use crate::config::UpstreamConfig;
use super::{Processor, ProcessResult, ProcessError, RequestContext};

/// 反向代理处理器
pub struct ProxyProcessor {
    #[cfg(feature = "http-server")]
    client: Client<HttpConnector, Full<Bytes>>,
    load_balancer: Arc<RwLock<LoadBalancer>>,
    connection_pool: Arc<ConnectionPool>,
}

impl ProxyProcessor {
    /// 创建新的代理处理器
    pub fn new() -> Self {
        Self {
            #[cfg(feature = "http-server")]
            client: Client::builder(TokioExecutor::new()).build(HttpConnector::new()),
            load_balancer: Arc::new(RwLock::new(LoadBalancer::new())),
            connection_pool: Arc::new(ConnectionPool::new()),
        }
    }

    /// 选择上游服务器
    async fn select_upstream(&self, ctx: &RequestContext) -> Option<String> {
        let site = ctx.site.as_ref()?;
        let upstream_name = ctx.upstream.as_ref()?;

        // 查找上游配置
        let upstream_config = site.upstreams.iter()
            .find(|u| &u.name == upstream_name)?;

        // 构建上游URL
        let protocol = if upstream_config.protocol == "https" { "https" } else { "http" };
        let port = if protocol == "https" {
            upstream_config.https_port.unwrap_or(443)
        } else {
            upstream_config.port
        };

        Some(format!("{}://{}:{}", protocol, upstream_config.address, port))
    }

    /// 转发请求到上游服务器
    #[cfg(feature = "http-server")]
    async fn forward_request(&self, ctx: &mut RequestContext, upstream_url: &str) -> ProcessResult {
        let start_time = Instant::now();

        // 构建上游请求URL
        let mut target_url = format!("{}{}", upstream_url.trim_end_matches('/'), ctx.path());
        if let Some(query) = &ctx.query_string {
            target_url = format!("{}?{}", target_url, query);
        }

        debug!("Forwarding request {} to {}", ctx.request_id, target_url);

        // 解析URI
        let uri = match target_url.parse::<Uri>() {
            Ok(uri) => uri,
            Err(e) => {
                return ProcessResult::Error(ProcessError::new(
                    format!("Invalid upstream URL: {}", e),
                    502,
                ));
            }
        };

        // 解析HTTP方法
        let method = match ctx.method.as_str() {
            "GET" => Method::GET,
            "POST" => Method::POST,
            "PUT" => Method::PUT,
            "DELETE" => Method::DELETE,
            "HEAD" => Method::HEAD,
            "OPTIONS" => Method::OPTIONS,
            "PATCH" => Method::PATCH,
            _ => {
                return ProcessResult::Error(ProcessError::new(
                    format!("Unsupported HTTP method: {}", ctx.method),
                    405,
                ));
            }
        };

        // 创建请求
        let mut request_builder = Request::builder()
            .method(method)
            .uri(uri);

        // 添加请求头（过滤掉一些不应该转发的头部）
        for (name, value) in &ctx.headers {
            if !self.should_skip_header(name) {
                if let (Ok(header_name), Ok(header_value)) = (name.parse::<HeaderName>(), value.parse::<HeaderValue>()) {
                    request_builder = request_builder.header(header_name, header_value);
                }
            }
        }

        // 确保有User-Agent头部（如果客户端没有提供，使用默认的浏览器UA）
        if !ctx.headers.contains_key("user-agent") {
            request_builder = request_builder.header(
                "User-Agent",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            );
        }

        // 添加代理相关头部
        request_builder = request_builder.header("X-Forwarded-For", ctx.real_ip());
        request_builder = request_builder.header("X-Forwarded-Proto", if ctx.is_https() { "https" } else { "http" });
        if let Some(host) = ctx.get_header("host") {
            request_builder = request_builder.header("X-Forwarded-Host", host);
        }

        // 创建请求体
        let body = if let Some(body_bytes) = &ctx.body {
            Full::new(body_bytes.clone())
        } else {
            Full::new(Bytes::new())
        };

        // 构建请求
        let request = match request_builder.body(body) {
            Ok(req) => req,
            Err(e) => {
                return ProcessResult::Error(ProcessError::new(
                    format!("Failed to build request: {}", e),
                    500,
                ));
            }
        };

        // 发送请求
        match self.client.request(request).await {
            Ok(response) => {
                let status = response.status().as_u16();
                let headers = self.extract_response_headers(response.headers());

                // 读取响应体
                match response.into_body().collect().await {
                    Ok(collected) => {
                        let body = collected.to_bytes();

                        ctx.response.status_code = status;
                        ctx.response.headers = headers;
                        ctx.response.set_body(body);

                        let duration = start_time.elapsed();
                        ctx.stats.total_time = Some(duration);

                        info!(
                            "Request {} forwarded successfully to {} in {:?} (status: {})",
                            ctx.request_id, target_url, duration, status
                        );

                        ProcessResult::Stop // 代理完成，停止处理链
                    }
                    Err(e) => {
                        error!("Failed to read response body for request {}: {}", ctx.request_id, e);
                        ProcessResult::Error(ProcessError::new("Failed to read response", 502))
                    }
                }
            }
            Err(e) => {
                let duration = start_time.elapsed();
                error!(
                    "Failed to forward request {} to {} after {:?}: {}",
                    ctx.request_id, target_url, duration, e
                );

                ProcessResult::Error(ProcessError::new(
                    format!("Proxy error: {}", e),
                    502,
                ))
            }
        }
    }

    /// 检查是否应该跳过某个头部
    fn should_skip_header(&self, name: &str) -> bool {
        matches!(name.to_lowercase().as_str(),
            "connection" | "upgrade" | "proxy-connection" |
            "proxy-authenticate" | "proxy-authorization" |
            "te" | "trailers" | "transfer-encoding"
        )
    }

    /// 提取响应头部
    #[cfg(feature = "http-server")]
    fn extract_response_headers(&self, headers: &HeaderMap<HeaderValue>) -> HashMap<String, String> {
        let mut header_map = HashMap::new();

        for (name, value) in headers {
            if let Ok(value_str) = value.to_str() {
                // 跳过一些不应该转发的响应头
                if !self.should_skip_response_header(name.as_str()) {
                    header_map.insert(name.to_string().to_lowercase(), value_str.to_string());
                }
            }
        }

        header_map
    }

    /// 检查是否应该跳过某个响应头部
    fn should_skip_response_header(&self, name: &str) -> bool {
        matches!(name.to_lowercase().as_str(),
            "connection" | "upgrade" | "proxy-connection" |
            "transfer-encoding" | "content-encoding" // content-encoding 由压缩处理器处理
        )
    }

    /// 处理无HTTP客户端的情况
    #[cfg(not(feature = "http-server"))]
    async fn forward_request(&self, ctx: &mut RequestContext, upstream_url: &str) -> ProcessResult {
        warn!("HTTP client feature is disabled, cannot forward request {}", ctx.request_id);
        ProcessResult::Error(ProcessError::new(
            "HTTP client feature is disabled",
            500,
        ))
    }
}

impl Default for ProxyProcessor {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl Processor for ProxyProcessor {
    fn name(&self) -> &'static str {
        "ProxyProcessor"
    }

    fn priority(&self) -> u8 {
        40 // 在静态文件处理器之后执行
    }

    fn should_process(&self, ctx: &RequestContext) -> bool {
        // 如果有上游服务器配置且不是静态文件请求
        ctx.upstream.is_some() &&
        ctx.route.as_ref().and_then(|r| r.static_dir.as_ref()).is_none()
    }

    async fn process(&self, ctx: &mut RequestContext) -> ProcessResult {
        debug!("ProxyProcessor: Processing request {}", ctx.request_id);

        // 选择上游服务器
        let upstream_url = match self.select_upstream(ctx).await {
            Some(url) => url,
            None => {
                return ProcessResult::Error(ProcessError::new(
                    "No available upstream server",
                    502,
                ));
            }
        };

        // 转发请求
        self.forward_request(ctx, &upstream_url).await
    }
}

/// 负载均衡器
pub struct LoadBalancer {
    // 简化实现，暂时只存储统计信息
    _stats: HashMap<String, ServerStats>,
}

impl LoadBalancer {
    pub fn new() -> Self {
        Self {
            _stats: HashMap::new(),
        }
    }
}

/// 服务器统计信息
#[derive(Debug, Default)]
pub struct ServerStats {
    pub active_connections: u64,
    pub total_requests: u64,
    pub failed_requests: u64,
    pub round_robin_index: usize,
    pub last_used: Option<Instant>,
}

/// 连接池
pub struct ConnectionPool {
    // TODO: 实现连接池逻辑
}

impl ConnectionPool {
    pub fn new() -> Self {
        Self {}
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashMap;
    use std::net::SocketAddr;

    fn create_test_context() -> RequestContext {
        RequestContext::new(
            "GET".to_string(),
            "/test".to_string(),
            HashMap::new(),
            "127.0.0.1:8080".parse::<SocketAddr>().unwrap(),
        )
    }

    #[test]
    fn test_should_process() {
        let processor = ProxyProcessor::new();
        let mut ctx = create_test_context();

        // 没有上游服务器时不应该处理
        assert!(!processor.should_process(&ctx));

        // 有上游服务器时应该处理
        ctx.upstream = Some("api".to_string());
        assert!(processor.should_process(&ctx));
    }

    #[test]
    fn test_should_skip_header() {
        let processor = ProxyProcessor::new();

        assert!(processor.should_skip_header("connection"));
        assert!(processor.should_skip_header("Connection"));
        assert!(processor.should_skip_header("upgrade"));
        assert!(!processor.should_skip_header("content-type"));
        assert!(!processor.should_skip_header("authorization"));
    }
}

use anyhow::Result;
use async_trait::async_trait;
use bytes::Bytes;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::{debug, error, info, warn};

#[cfg(feature = "http-server")]
use reqwest::{Client, Method, RequestBuilder};

use crate::config::{UpstreamConfig, LoadBalanceMethod};
use super::{Processor, ProcessResult, ProcessError, RequestContext};

/// 反向代理处理器
pub struct ProxyProcessor {
    #[cfg(feature = "http-server")]
    client: Client,
    load_balancer: Arc<RwLock<LoadBalancer>>,
    connection_pool: Arc<ConnectionPool>,
}

impl ProxyProcessor {
    /// 创建新的代理处理器
    pub fn new() -> Self {
        Self {
            #[cfg(feature = "http-server")]
            client: Client::builder()
                .timeout(Duration::from_secs(30))
                .pool_max_idle_per_host(10)
                .pool_idle_timeout(Duration::from_secs(90))
                .build()
                .unwrap_or_else(|_| Client::new()),
            load_balancer: Arc::new(RwLock::new(LoadBalancer::new())),
            connection_pool: Arc::new(ConnectionPool::new()),
        }
    }

    /// 选择上游服务器
    async fn select_upstream(&self, ctx: &RequestContext) -> Option<String> {
        let site = ctx.site.as_ref()?;
        let upstream_name = ctx.upstream.as_ref()?;

        // 查找上游配置
        let upstream_config = site.upstreams.iter()
            .find(|u| &u.name == upstream_name)?;

        // 使用负载均衡器选择服务器
        let mut balancer = self.load_balancer.write().await;
        balancer.select_server(upstream_config).await
    }

    /// 转发请求到上游服务器
    #[cfg(feature = "http-server")]
    async fn forward_request(&self, ctx: &mut RequestContext, upstream_url: &str) -> ProcessResult {
        let start_time = Instant::now();

        // 构建上游请求URL
        let target_url = format!("{}{}", upstream_url.trim_end_matches('/'), ctx.path());
        if let Some(query) = &ctx.query_string {
            let target_url = format!("{}?{}", target_url, query);
        }

        debug!("Forwarding request {} to {}", ctx.request_id, target_url);

        // 解析HTTP方法
        let method = match ctx.method.as_str() {
            "GET" => Method::GET,
            "POST" => Method::POST,
            "PUT" => Method::PUT,
            "DELETE" => Method::DELETE,
            "HEAD" => Method::HEAD,
            "OPTIONS" => Method::OPTIONS,
            "PATCH" => Method::PATCH,
            _ => {
                return ProcessResult::Error(ProcessError::new(
                    format!("Unsupported HTTP method: {}", ctx.method),
                    405,
                ));
            }
        };

        // 创建请求构建器
        let mut request_builder = self.client.request(method, &target_url);

        // 添加请求头（过滤掉一些不应该转发的头部）
        for (name, value) in &ctx.headers {
            if !self.should_skip_header(name) {
                request_builder = request_builder.header(name, value);
            }
        }

        // 添加代理相关头部
        request_builder = request_builder.header("X-Forwarded-For", ctx.real_ip());
        request_builder = request_builder.header("X-Forwarded-Proto", if ctx.is_https() { "https" } else { "http" });
        if let Some(host) = ctx.get_header("host") {
            request_builder = request_builder.header("X-Forwarded-Host", host);
        }

        // 添加请求体
        if let Some(body) = &ctx.body {
            request_builder = request_builder.body(body.clone());
        }

        // 发送请求
        match request_builder.send().await {
            Ok(response) => {
                let status = response.status().as_u16();
                let headers = self.extract_response_headers(response.headers());

                // 读取响应体
                match response.bytes().await {
                    Ok(body) => {
                        ctx.response.status_code = status;
                        ctx.response.headers = headers;
                        ctx.response.set_body(body);

                        let duration = start_time.elapsed();
                        ctx.stats.total_time = Some(duration);

                        info!(
                            "Request {} forwarded successfully to {} in {:?} (status: {})",
                            ctx.request_id, target_url, duration, status
                        );

                        ProcessResult::Stop // 代理完成，停止处理链
                    }
                    Err(e) => {
                        error!("Failed to read response body for request {}: {}", ctx.request_id, e);
                        ProcessResult::Error(ProcessError::new("Failed to read response", 502))
                    }
                }
            }
            Err(e) => {
                let duration = start_time.elapsed();
                error!(
                    "Failed to forward request {} to {} after {:?}: {}",
                    ctx.request_id, target_url, duration, e
                );

                // 根据错误类型返回不同的状态码
                let status_code = if e.is_timeout() {
                    504 // Gateway Timeout
                } else if e.is_connect() {
                    502 // Bad Gateway
                } else {
                    500 // Internal Server Error
                };

                ProcessResult::Error(ProcessError::new(
                    format!("Proxy error: {}", e),
                    status_code,
                ))
            }
        }
    }

    /// 检查是否应该跳过某个头部
    fn should_skip_header(&self, name: &str) -> bool {
        matches!(name.to_lowercase().as_str(),
            "connection" | "upgrade" | "proxy-connection" |
            "proxy-authenticate" | "proxy-authorization" |
            "te" | "trailers" | "transfer-encoding"
        )
    }

    /// 提取响应头部
    #[cfg(feature = "http-server")]
    fn extract_response_headers(&self, headers: &reqwest::header::HeaderMap) -> HashMap<String, String> {
        let mut header_map = HashMap::new();

        for (name, value) in headers {
            if let Ok(value_str) = value.to_str() {
                // 跳过一些不应该转发的响应头
                if !self.should_skip_response_header(name.as_str()) {
                    header_map.insert(name.to_string().to_lowercase(), value_str.to_string());
                }
            }
        }

        header_map
    }

    /// 检查是否应该跳过某个响应头部
    fn should_skip_response_header(&self, name: &str) -> bool {
        matches!(name.to_lowercase().as_str(),
            "connection" | "upgrade" | "proxy-connection" |
            "transfer-encoding" | "content-encoding" // content-encoding 由压缩处理器处理
        )
    }

    /// 处理无HTTP客户端的情况
    #[cfg(not(feature = "http-server"))]
    async fn forward_request(&self, ctx: &mut RequestContext, upstream_url: &str) -> ProcessResult {
        warn!("HTTP client feature is disabled, cannot forward request {}", ctx.request_id);
        ProcessResult::Error(ProcessError::new(
            "HTTP client feature is disabled",
            500,
        ))
    }
}

impl Default for ProxyProcessor {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl Processor for ProxyProcessor {
    fn name(&self) -> &'static str {
        "ProxyProcessor"
    }

    fn priority(&self) -> u8 {
        40 // 在静态文件处理器之后执行
    }

    fn should_process(&self, ctx: &RequestContext) -> bool {
        // 如果有上游服务器配置且不是静态文件请求
        ctx.upstream.is_some() &&
        ctx.route.as_ref().and_then(|r| r.static_dir.as_ref()).is_none()
    }

    async fn process(&self, ctx: &mut RequestContext) -> ProcessResult {
        debug!("ProxyProcessor: Processing request {}", ctx.request_id);

        // 选择上游服务器
        let upstream_url = match self.select_upstream(ctx).await {
            Some(url) => url,
            None => {
                return ProcessResult::Error(ProcessError::new(
                    "No available upstream server",
                    502,
                ));
            }
        };

        // 转发请求
        self.forward_request(ctx, &upstream_url).await
    }
}

/// 负载均衡器
pub struct LoadBalancer {
    server_stats: HashMap<String, ServerStats>,
}

impl LoadBalancer {
    pub fn new() -> Self {
        Self {
            server_stats: HashMap::new(),
        }
    }

    /// 选择服务器
    pub async fn select_server(&mut self, upstream: &UpstreamConfig) -> Option<String> {
        if upstream.servers.is_empty() {
            return None;
        }

        match upstream.load_balance {
            LoadBalanceMethod::RoundRobin => {
                self.round_robin_select(upstream)
            }
            LoadBalanceMethod::LeastConn => {
                self.least_conn_select(upstream)
            }
            LoadBalanceMethod::WeightedRoundRobin => {
                self.weighted_round_robin_select(upstream)
            }
            LoadBalanceMethod::IpHash => {
                // 简化实现，使用轮询
                self.round_robin_select(upstream)
            }
            LoadBalanceMethod::Random => {
                self.random_select(upstream)
            }
        }
    }

    /// 轮询选择
    fn round_robin_select(&mut self, upstream: &UpstreamConfig) -> Option<String> {
        let key = format!("{}_rr_index", upstream.name);
        let stats = self.server_stats.entry(key).or_default();

        let index = stats.round_robin_index % upstream.servers.len();
        stats.round_robin_index += 1;

        upstream.servers.get(index).map(|s| s.url.clone())
    }

    /// 最少连接选择
    fn least_conn_select(&mut self, upstream: &UpstreamConfig) -> Option<String> {
        let mut min_connections = u64::MAX;
        let mut selected_server = None;

        for server in &upstream.servers {
            let stats = self.server_stats.entry(server.url.clone()).or_default();
            if stats.active_connections < min_connections {
                min_connections = stats.active_connections;
                selected_server = Some(server.url.clone());
            }
        }

        if let Some(ref server) = selected_server {
            if let Some(stats) = self.server_stats.get_mut(server) {
                stats.active_connections += 1;
            }
        }

        selected_server
    }

    /// 加权轮询选择
    fn weighted_round_robin_select(&mut self, upstream: &UpstreamConfig) -> Option<String> {
        // 简化实现，暂时使用普通轮询
        self.round_robin_select(upstream)
    }

    /// 随机选择
    fn random_select(&self, upstream: &UpstreamConfig) -> Option<String> {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        std::time::SystemTime::now().hash(&mut hasher);
        let hash = hasher.finish();

        let index = (hash as usize) % upstream.servers.len();
        upstream.servers.get(index).map(|s| s.url.clone())
    }
}

/// 服务器统计信息
#[derive(Debug, Default)]
pub struct ServerStats {
    pub active_connections: u64,
    pub total_requests: u64,
    pub failed_requests: u64,
    pub round_robin_index: usize,
    pub last_used: Option<Instant>,
}

/// 连接池
pub struct ConnectionPool {
    // TODO: 实现连接池逻辑
}

impl ConnectionPool {
    pub fn new() -> Self {
        Self {}
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::{ServerConfig, LoadBalanceMethod};
    use std::collections::HashMap;
    use std::net::SocketAddr;

    fn create_test_context() -> RequestContext {
        RequestContext::new(
            "GET".to_string(),
            "/test".to_string(),
            HashMap::new(),
            "127.0.0.1:8080".parse::<SocketAddr>().unwrap(),
        )
    }

    #[test]
    fn test_should_process() {
        let processor = ProxyProcessor::new();
        let mut ctx = create_test_context();

        // 没有上游服务器时不应该处理
        assert!(!processor.should_process(&ctx));

        // 有上游服务器时应该处理
        ctx.upstream = Some("api".to_string());
        assert!(processor.should_process(&ctx));
    }

    #[test]
    fn test_should_skip_header() {
        let processor = ProxyProcessor::new();

        assert!(processor.should_skip_header("connection"));
        assert!(processor.should_skip_header("Connection"));
        assert!(processor.should_skip_header("upgrade"));
        assert!(!processor.should_skip_header("content-type"));
        assert!(!processor.should_skip_header("authorization"));
    }

    #[tokio::test]
    async fn test_load_balancer_round_robin() {
        let mut balancer = LoadBalancer::new();
        let upstream = UpstreamConfig {
            name: "test".to_string(),
            load_balance: LoadBalanceMethod::RoundRobin,
            servers: vec![
                ServerConfig {
                    url: "http://server1".to_string(),
                    weight: 1,
                    max_fails: 3,
                    fail_timeout: Duration::from_secs(30),
                },
                ServerConfig {
                    url: "http://server2".to_string(),
                    weight: 1,
                    max_fails: 3,
                    fail_timeout: Duration::from_secs(30),
                },
            ],
            health_check: None,
        };

        let server1 = balancer.select_server(&upstream).await;
        let server2 = balancer.select_server(&upstream).await;
        let server3 = balancer.select_server(&upstream).await;

        assert_eq!(server1, Some("http://server1".to_string()));
        assert_eq!(server2, Some("http://server2".to_string()));
        assert_eq!(server3, Some("http://server1".to_string())); // 轮询回到第一个
    }
}

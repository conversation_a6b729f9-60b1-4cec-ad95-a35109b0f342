{"server": {"http_port": 80, "https_port": 443, "max_connections": 1000}, "log": {"level": "info", "file": "logs/proxy_${date}.log", "format": "combined"}, "acl": {"enabled": true, "global_allow": ["127.0.0.1", "::1"], "global_deny": ["********"], "allow_file": "acl/global_allow.txt", "deny_file": "acl/global_deny.txt", "comment": "文件监控模式 - 文件变化时自动重载"}, "sites": [{"name": "secure_api", "domains": ["api.example.com"], "max_connections": 500, "upstreams": [{"name": "api_server", "address": "127.0.0.1", "port": 8001, "protocol": "http", "load_balance_group": "api_group"}], "routes": [{"pattern": "^/api/", "upstream": "api_group", "cache": false}], "acl": {"allow": ["***********/24"], "deny": ["**********"], "allow_file": "acl/api_allow.txt", "deny_file": "acl/api_deny.txt", "comment": "API站点独立ACL文件监控"}}, {"name": "admin_panel", "domains": ["admin.example.com"], "max_connections": 100, "upstreams": [{"name": "admin_server", "address": "127.0.0.1", "port": 8002, "protocol": "http", "load_balance_group": "admin_group"}], "routes": [{"pattern": "^/admin/", "upstream": "admin_group", "cache": false}], "acl": {"allow_file": "acl/admin_allow.txt", "deny_file": "acl/admin_deny.txt", "comment": "管理后台严格IP控制"}}, {"name": "public_site", "domains": ["www.example.com"], "max_connections": 800, "upstreams": [{"name": "web_server", "address": "127.0.0.1", "port": 8003, "protocol": "http", "load_balance_group": "web_group"}], "routes": [{"pattern": "^/", "upstream": "web_group", "cache": true}], "acl": {"deny_file": "acl/public_deny.txt", "comment": "公共站点只设置黑名单"}}, {"name": "geo_restricted", "domains": ["geo.example.com"], "max_connections": 300, "upstreams": [{"name": "geo_server", "address": "127.0.0.1", "port": 8004, "protocol": "http", "load_balance_group": "geo_group"}], "routes": [{"pattern": "^/", "upstream": "geo_group", "cache": true}], "acl": {"allow_file": "acl/allowed_countries.txt", "deny_file": "acl/blocked_countries.txt", "comment": "地域限制站点 - 通过脚本更新国家IP列表"}}], "rate_limit": {"enabled": true, "global_rps": 5000, "ip_rps": 50, "site_rps": 500, "burst": 20}, "circuit_breaker": {"enabled": true, "max_failures": 3, "reset_timeout": "30s", "half_open_requests": 2}, "compression": {"enabled": true, "min_size": "1KB", "max_size": "5MB", "algorithms": ["br", "gzip", "deflate"]}, "monitor": {"enabled": true, "port": 8080, "username": "admin", "password": "secure123", "api_key": "acl-test-key-2024"}, "comments": {"acl_file_watcher": {"description": "ACL文件实时监控测试配置", "features": ["全局ACL文件监控", "站点级ACL文件监控", "文件变化实时重载", "支持IPv4和IPv6", "支持CIDR格式", "注释和空行自动忽略"], "file_structure": {"acl/global_allow.txt": "全局允许列表", "acl/global_deny.txt": "全局拒绝列表", "acl/api_allow.txt": "API站点允许列表", "acl/api_deny.txt": "API站点拒绝列表", "acl/admin_allow.txt": "管理后台允许列表", "acl/admin_deny.txt": "管理后台拒绝列表", "acl/public_deny.txt": "公共站点拒绝列表", "acl/allowed_countries.txt": "允许的国家IP列表", "acl/blocked_countries.txt": "封锁的国家IP列表"}, "test_scenarios": [{"name": "基本文件监控测试", "steps": ["启动反向代理服务", "修改 acl/global_deny.txt 添加IP", "观察日志确认文件重载", "测试新IP是否被封锁"]}, {"name": "站点级ACL测试", "steps": ["修改 acl/api_allow.txt", "测试API站点访问控制", "验证其他站点不受影响"]}, {"name": "地域IP封锁测试", "steps": ["下载国家IP列表到 acl/blocked_countries.txt", "观察文件自动重载", "测试地域IP访问限制"]}, {"name": "大文件性能测试", "steps": ["创建包含10000个IP的文件", "测试重载性能和内存使用", "验证访问控制准确性"]}], "monitoring": {"log_patterns": ["已添加文件监控:", "检测到文件变化:", "已重新加载.*文件:", "文件重载成功:"], "error_patterns": ["创建.*文件监控器失败:", "添加文件监控失败:", "重新加载.*文件失败:", "文件变化回调执行失败:"]}, "performance_expectations": {"file_reload_time": "<100ms", "memory_overhead": "<1MB per 10000 IPs", "cpu_impact": "Event-driven, minimal", "accuracy": "100% file change detection"}}}}
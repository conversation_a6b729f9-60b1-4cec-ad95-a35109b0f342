# 压缩和优化

## 📋 概述

响应压缩优化通过对HTTP响应内容进行压缩，显著减少网络传输数据量，提升页面加载速度和用户体验，同时降低带宽成本。

## 🎯 优化目标

- **减少带宽使用**：压缩响应内容，节省60-80%带宽
- **提升加载速度**：减少传输时间，改善用户体验
- **降低成本**：减少CDN和带宽费用
- **支持多种格式**：兼容各种内容类型

## 🔧 技术实现

### 1. 压缩算法支持

#### Gzip压缩
```go
type CompressibleWriter struct {
    http.ResponseWriter
    writer io.Writer
    config CompressionConfig
    written bool
}

func NewCompressibleWriter(w http.ResponseWriter, r *http.Request, config CompressionConfig) *CompressibleWriter {
    // 检查客户端支持
    if !strings.Contains(r.Header.Get("Accept-Encoding"), "gzip") {
        return &CompressibleWriter{
            ResponseWriter: w,
            writer:         w,
            config:         config,
        }
    }
    
    // 创建gzip写入器
    gzipWriter, _ := gzip.NewWriterLevel(w, config.Level)
    w.Header().Set("Content-Encoding", "gzip")
    w.Header().Set("Vary", "Accept-Encoding")
    
    return &CompressibleWriter{
        ResponseWriter: w,
        writer:         gzipWriter,
        config:         config,
    }
}
```

#### 支持的压缩算法
- **Brotli (br)**：现代压缩算法，压缩率比gzip高10-20%，优先级最高
- **Gzip**：通用压缩算法，兼容性最好，广泛支持
- **Deflate**：较老的压缩算法，向后兼容

#### 压缩效果对比

| 内容类型 | Brotli | Gzip | Deflate |
|----------|--------|------|---------|
| HTML文件 | 75% | 70% | 65% |
| JSON数据 | 68% | 60% | 58% |
| CSS文件 | 78% | 65% | 62% |
| JavaScript | 72% | 65% | 63% |

**Brotli优势**：
- 压缩率比gzip高10-20%
- 特别适合文本内容
- 现代浏览器广泛支持
- 针对Web内容优化

### 2. 智能压缩策略

#### 内容类型检测
```go
func (cw *CompressibleWriter) shouldCompress(contentType string, size int) bool {
    // 检查大小限制
    if size < cw.config.MinSize {
        return false
    }
    
    // 检查内容类型
    compressibleTypes := []string{
        "text/html",
        "text/css", 
        "text/javascript",
        "application/json",
        "application/javascript",
        "application/xml",
        "text/xml",
        "text/plain",
    }
    
    for _, t := range compressibleTypes {
        if strings.Contains(contentType, t) {
            return true
        }
    }
    return false
}
```

#### 压缩条件
1. **内容类型匹配**：只压缩文本类型内容
2. **大小阈值**：超过最小大小才压缩
3. **客户端支持**：检查Accept-Encoding头
4. **已压缩内容**：避免重复压缩

### 3. 性能优化

#### 压缩级别选择
```go
const (
    CompressionFastest = 1  // 最快压缩，压缩率低
    CompressionDefault = 6  // 默认平衡
    CompressionBest    = 9  // 最佳压缩，CPU消耗高
)
```

#### 内存管理
```go
var gzipWriterPool = sync.Pool{
    New: func() interface{} {
        return gzip.NewWriter(nil)
    },
}

func getGzipWriter(w io.Writer, level int) *gzip.Writer {
    gzw := gzipWriterPool.Get().(*gzip.Writer)
    gzw.Reset(w)
    return gzw
}

func putGzipWriter(gzw *gzip.Writer) {
    gzw.Close()
    gzipWriterPool.Put(gzw)
}
```

## ⚙️ 配置说明

### 基础配置
```json
{
  "compression": {
    "enabled": true,
    "types": [
      "text/html",
      "text/css",
      "text/javascript",
      "application/json",
      "application/javascript",
      "application/xml"
    ],
    "min_size": 1024,
    "level": 6,
    "algorithms": ["br", "gzip", "deflate"],
    "brotli_quality": 6
  }
}
```

### 参数详解

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enabled` | bool | true | 是否启用压缩 |
| `types` | []string | 见配置 | 支持压缩的MIME类型 |
| `min_size` | int | 1024 | 最小压缩大小(字节) |
| `level` | int | 6 | Gzip压缩级别(1-9) |
| `algorithms` | []string | ["br","gzip","deflate"] | 支持的压缩算法，按优先级排序 |
| `brotli_quality` | int | 6 | Brotli质量等级(0-11) |

### 站点级配置
```json
{
  "sites": [
    {
      "name": "static_site",
      "compression": {
        "enabled": true,
        "types": ["text/css", "text/javascript"],
        "min_size": 512,
        "level": 9
      }
    },
    {
      "name": "api_service", 
      "compression": {
        "enabled": true,
        "types": ["application/json"],
        "min_size": 100,
        "level": 1
      }
    }
  ]
}
```

### 性能调优

#### 高压缩率场景（静态资源）
```json
{
  "compression": {
    "enabled": true,
    "types": ["text/css", "text/javascript", "text/html"],
    "min_size": 512,
    "level": 9
  }
}
```

#### 低延迟场景（API服务）
```json
{
  "compression": {
    "enabled": true,
    "types": ["application/json"],
    "min_size": 1024,
    "level": 1
  }
}
```

## 📊 性能指标

### 压缩统计
```json
{
  "compression": {
    "enabled": true,
    "total_requests": 10000,
    "compressed_requests": 8000,
    "compression_ratio": 0.65,
    "bytes_saved": 52428800,
    "avg_compression_time": "2.5ms",
    "by_type": {
      "text/html": {
        "requests": 3000,
        "original_size": 15728640,
        "compressed_size": 4718592,
        "ratio": 0.70
      },
      "application/json": {
        "requests": 5000,
        "original_size": 20971520,
        "compressed_size": 8388608,
        "ratio": 0.60
      }
    }
  }
}
```

### 关键指标
- **压缩率**：压缩后大小/原始大小
- **带宽节省**：节省的字节数
- **压缩时间**：压缩操作耗时
- **CPU使用率**：压缩对CPU的影响

## 🔍 故障排查

### 常见问题

#### 1. 压缩不生效
**现象**：响应未被压缩
**排查**：
```bash
# 检查响应头
curl -H "Accept-Encoding: gzip" -I http://your-proxy/

# 检查内容类型
curl -H "Accept-Encoding: gzip" -v http://your-proxy/
```

**解决**：
- 检查客户端Accept-Encoding头
- 验证内容类型配置
- 确认文件大小超过最小阈值

#### 2. 压缩率低
**现象**：压缩效果不明显
**解决**：
- 调整压缩级别
- 检查内容是否已压缩
- 优化内容类型配置

#### 3. 性能影响
**现象**：压缩导致响应时间增加
**解决**：
- 降低压缩级别
- 增加最小压缩大小
- 使用压缩缓存

## 🚀 最佳实践

### 1. 分类压缩策略
```json
{
  "sites": [
    {
      "name": "static_content",
      "compression": {
        "level": 9,           // 静态内容高压缩率
        "min_size": 512
      }
    },
    {
      "name": "dynamic_api",
      "compression": {
        "level": 1,           // 动态内容低延迟
        "min_size": 1024
      }
    }
  ]
}
```

### 2. 缓存压缩结果
```go
type CompressionCache struct {
    cache map[string][]byte
    mutex sync.RWMutex
}

func (cc *CompressionCache) GetCompressed(key string, original []byte, level int) []byte {
    cc.mutex.RLock()
    if compressed, exists := cc.cache[key]; exists {
        cc.mutex.RUnlock()
        return compressed
    }
    cc.mutex.RUnlock()
    
    // 压缩并缓存
    compressed := compress(original, level)
    cc.mutex.Lock()
    cc.cache[key] = compressed
    cc.mutex.Unlock()
    
    return compressed
}
```

### 3. 条件压缩
```go
func shouldCompress(r *http.Request, contentType string, size int) bool {
    // 检查User-Agent，某些客户端不支持压缩
    userAgent := r.Header.Get("User-Agent")
    if strings.Contains(userAgent, "MSIE 6") {
        return false
    }
    
    // 检查Via头，避免重复压缩
    if r.Header.Get("Via") != "" {
        return false
    }
    
    return true
}
```

### 4. 监控和告警
```json
{
  "alerts": {
    "compression_ratio_low": {
      "threshold": 0.3,
      "message": "压缩率过低，检查配置"
    },
    "compression_time_high": {
      "threshold": "10ms",
      "message": "压缩时间过长，考虑降低压缩级别"
    }
  }
}
```

## 📈 性能测试

### 压缩效果测试

#### Brotli压缩测试
```bash
#!/bin/bash
# Brotli压缩效果测试

# 测试不同压缩算法的效果
test_compression_algorithms() {
    local url=$1
    local type=$2

    echo "Testing $type compression with different algorithms:"

    # 原始大小
    original_size=$(curl -s -H "Accept-Encoding: identity" "$url" | wc -c)
    echo "Original size: ${original_size} bytes"

    # 测试Brotli
    brotli_size=$(curl -s -H "Accept-Encoding: br" "$url" | wc -c)
    brotli_savings=$(echo "scale=2; (1 - $brotli_size / $original_size) * 100" | bc)
    echo "Brotli: ${brotli_size} bytes (${brotli_savings}% savings)"

    # 测试Gzip
    gzip_size=$(curl -s -H "Accept-Encoding: gzip" "$url" | wc -c)
    gzip_savings=$(echo "scale=2; (1 - $gzip_size / $original_size) * 100" | bc)
    echo "Gzip: ${gzip_size} bytes (${gzip_savings}% savings)"

    # 测试Deflate
    deflate_size=$(curl -s -H "Accept-Encoding: deflate" "$url" | wc -c)
    deflate_savings=$(echo "scale=2; (1 - $deflate_size / $original_size) * 100" | bc)
    echo "Deflate: ${deflate_size} bytes (${deflate_savings}% savings)"

    echo "---"
}

# 测试各种内容类型
test_compression_algorithms "http://your-proxy/index.html" "HTML"
test_compression_algorithms "http://your-proxy/style.css" "CSS"
test_compression_algorithms "http://your-proxy/script.js" "JavaScript"
test_compression_algorithms "http://your-proxy/api/data" "JSON"
```

#### PowerShell测试脚本
```powershell
# 使用提供的测试脚本
.\test_brotli_compression.ps1
```

#### 压缩算法选择测试
```bash
# 测试客户端支持的算法优先级
curl -H "Accept-Encoding: br, gzip, deflate" -v http://your-proxy/
# 应该返回: Content-Encoding: br

curl -H "Accept-Encoding: gzip, deflate" -v http://your-proxy/
# 应该返回: Content-Encoding: gzip

curl -H "Accept-Encoding: deflate" -v http://your-proxy/
# 应该返回: Content-Encoding: deflate
```

### 性能基准测试
```bash
#!/bin/bash
# 性能基准测试

# 测试压缩对响应时间的影响
echo "Testing compression performance impact:"

# 无压缩
echo "Without compression:"
ab -n 1000 -c 10 -H "Accept-Encoding: identity" http://your-proxy/

# 有压缩
echo "With compression:"
ab -n 1000 -c 10 -H "Accept-Encoding: gzip" http://your-proxy/
```

## 🔧 高级功能

### 1. 动态压缩级别
```go
func getDynamicCompressionLevel(contentType string, size int, cpuUsage float64) int {
    if cpuUsage > 0.8 {
        return 1  // 高CPU使用时降低压缩级别
    }
    
    if size > 1024*1024 {  // 大文件
        return 3
    }
    
    if strings.Contains(contentType, "json") {
        return 6  // JSON默认级别
    }
    
    return 9  // 其他内容高压缩
}
```

### 2. 预压缩支持
```go
// 支持预压缩的静态文件
func servePrecompressed(w http.ResponseWriter, r *http.Request, filePath string) {
    if strings.Contains(r.Header.Get("Accept-Encoding"), "gzip") {
        gzipPath := filePath + ".gz"
        if _, err := os.Stat(gzipPath); err == nil {
            w.Header().Set("Content-Encoding", "gzip")
            http.ServeFile(w, r, gzipPath)
            return
        }
    }
    
    http.ServeFile(w, r, filePath)
}
```

### 3. 流式压缩
```go
// 支持大文件的流式压缩
type StreamCompressor struct {
    writer *gzip.Writer
    buffer []byte
}

func (sc *StreamCompressor) Write(data []byte) (int, error) {
    return sc.writer.Write(data)
}

func (sc *StreamCompressor) Flush() error {
    return sc.writer.Flush()
}
```

压缩优化是提升Web性能的重要手段，合理配置可以显著减少带宽使用和提升用户体验。

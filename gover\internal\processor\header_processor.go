package processor

import (
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"reverse-proxy/internal/config"
)

// HeaderProcessor 头部处理器
type HeaderProcessor struct {
	*BaseProcessor
	globalCacheConfig   *config.CacheHeadersConfig
	globalHeadersConfig *config.HeadersConfig
}

// NewHeaderProcessor 创建头部处理器
func NewHeaderProcessor(logger *logrus.Logger, globalCacheConfig *config.CacheHeadersConfig, globalHeadersConfig *config.HeadersConfig) *HeaderProcessor {
	return &HeaderProcessor{
		BaseProcessor:       NewBaseProcessor("HeaderProcessor", 900, logger),
		globalCacheConfig:   globalCacheConfig,
		globalHeadersConfig: globalHeadersConfig,
	}
}

// Process 处理头部设置 - 作为最终处理器
func (hp *HeaderProcessor) Process(req *RequestContext, resp *ResponseContext) *ProcessResult {
	// 使用数字标识进行高效判断
	if req.HeaderFlag == 0 {
		hp.LogDebug("跳过头部处理: HeaderFlag=%d", req.HeaderFlag)
		return Success()
	}

	// 处理响应头部
	hp.processResponseHeaders(req, resp)

	// 处理缓存相关头部
	hp.processCacheHeaders(req, resp)

	// 处理调试头部
	hp.processDebugHeaders(req, resp)

	return Success()
}

// ShouldProcess 判断是否应该处理
func (hp *HeaderProcessor) ShouldProcess(req *RequestContext) bool {
	return true // 头部处理器总是执行
}

// processResponseHeaders 处理响应头部配置
func (hp *HeaderProcessor) processResponseHeaders(req *RequestContext, resp *ResponseContext) {
	// 多级配置优先级：路由级 → 站点级 → 全局级

	// 1. 首先应用全局配置（最低优先级）
	if hp.globalHeadersConfig != nil {
		hp.applyResponseHeaderRules(hp.globalHeadersConfig.Response, req, resp, "全局")
	}

	// 2. 然后应用站点级配置（中等优先级）
	if req.Site != nil {
		hp.applyResponseHeaderRules(req.Site.Headers.Response, req, resp, "站点")
	}

	// 3. 最后应用路由级配置（最高优先级）
	if req.Route != nil && req.Route.Headers != nil {
		hp.applyResponseHeaderRules(req.Route.Headers.Response, req, resp, "路由")
	}
}

// applyResponseHeaderRules 应用响应头部规则
func (hp *HeaderProcessor) applyResponseHeaderRules(rules config.HeaderRules, req *RequestContext, resp *ResponseContext, level string) {
	// 设置头部
	for key, value := range rules.Set {
		processedValue := hp.processHeaderValue(value, req, resp)
		resp.Headers.Set(key, processedValue)
		hp.LogDebug("设置%s级响应头: %s = %s", level, key, processedValue)
	}

	// 删除头部
	for _, key := range rules.Remove {
		resp.Headers.Del(key)
		hp.LogDebug("删除%s级响应头: %s", level, key)
	}

	// 忽略头部（从上游响应中移除）
	for _, key := range rules.Ignore {
		resp.Headers.Del(key)
		hp.LogDebug("忽略%s级响应头: %s", level, key)
	}
}

// processCacheHeaders 处理缓存相关头部
func (hp *HeaderProcessor) processCacheHeaders(req *RequestContext, resp *ResponseContext) {
	var cacheConfig *config.CacheHeadersConfig

	// 获取缓存头部配置（优先级：站点级 > 全局级）
	if req.Site != nil && req.Site.CacheHeaders != nil && req.Site.CacheHeaders.Enabled {
		cacheConfig = req.Site.CacheHeaders
		hp.LogDebug("使用站点级缓存头部配置")
	} else if hp.globalCacheConfig != nil && hp.globalCacheConfig.Enabled {
		cacheConfig = hp.globalCacheConfig
		hp.LogDebug("使用全局级缓存头部配置")
	}

	if cacheConfig == nil {
		hp.LogDebug("没有可用的缓存头部配置")
		return
	}

	hp.LogDebug("处理缓存头部 - CacheStatus: %s, resp.CacheStatus: %s", cacheConfig.CacheStatus, resp.CacheStatus)

	// 设置缓存状态头部
	if cacheConfig.CacheStatus != "" && resp.CacheStatus != "" {
		resp.Headers.Set(cacheConfig.CacheStatus, resp.CacheStatus)
		hp.LogDebug("设置缓存状态头部: %s = %s", cacheConfig.CacheStatus, resp.CacheStatus)
	}

	// 设置详细信息头部
	if cacheConfig.ShowDetail {
		hp.setDetailHeaders(req, resp)
	}
}

// setDetailHeaders 设置详细信息头部
func (hp *HeaderProcessor) setDetailHeaders(req *RequestContext, resp *ResponseContext) {
	// 设置后端服务器信息
	if resp.BackendIP != "" {
		resp.Headers.Set("X-Backend-Server", fmt.Sprintf("%s:%d", resp.BackendIP, resp.BackendPort))

		// 设置详细的后端信息（用于调试）
		resp.Headers.Set("X-Cache-Backend-IP", resp.BackendIP)
		resp.Headers.Set("X-Cache-Backend-Port", fmt.Sprintf("%d", resp.BackendPort))

		// 确定后端协议
		var backendProtocol string
		if req.Upstream != nil {
			switch req.Upstream.Protocol {
			case "passthrough":
				// 协议透传：根据客户端请求协议决定
				if req.OriginalRequest != nil && req.OriginalRequest.TLS != nil {
					backendProtocol = "https"
				} else {
					backendProtocol = "http"
				}
			case "https":
				backendProtocol = "https"
			case "auto":
				backendProtocol = "https" // 自动选择优先HTTPS
			default:
				backendProtocol = "http"
			}
			resp.Headers.Set("X-Cache-Backend-Protocol", backendProtocol)
		}
	}

	// 设置处理时间
	if resp.ProcessTime > 0 {
		resp.Headers.Set("X-Process-Time", resp.ProcessTime.String())
	}

	// 设置缓存键
	if req.CacheKey != "" {
		resp.Headers.Set("X-Cache-Key", req.CacheKey)
	}

	// 设置站点ID
	resp.Headers.Set("X-Site-ID", req.SiteID)

	// 设置处理链信息
	if len(resp.ProcessChain) > 0 {
		resp.Headers.Set("X-Process-Chain", strings.Join(resp.ProcessChain, " -> "))
	}

	// 设置缓存相关时间信息
	if resp.CacheStatus == "HIT" || resp.CacheStatus == "MISS" {
		resp.Headers.Set("X-Cache-Request-Time", time.Now().Format("2006-01-02 15:04:05"))
	}
}

// processDebugHeaders 处理调试头部
func (hp *HeaderProcessor) processDebugHeaders(req *RequestContext, resp *ResponseContext) {
	// 添加响应时间戳
	resp.Headers.Set("Date", time.Now().UTC().Format(time.RFC1123))

	// 如果有错误信息，添加错误头部（仅在调试模式下）
	if resp.Error != nil && resp.ErrorStage != "" {
		resp.Headers.Set("X-Error-Stage", resp.ErrorStage)
	}
}

// processHeaderValue 处理头部值中的变量替换
func (hp *HeaderProcessor) processHeaderValue(value string, req *RequestContext, resp *ResponseContext) string {
	// 定义可替换的变量
	replacements := map[string]string{
		"{{CLIENT_IP}}":      req.ClientIP,
		"{{REQUEST_METHOD}}": req.Method,
		"{{REQUEST_PATH}}":   req.URL.Path,
		"{{USER_AGENT}}":     req.UserAgent,
		"{{REFERER}}":        req.Referer,
		"{{TIMESTAMP}}":      time.Now().Format("2006-01-02 15:04:05"),
		"{{SITE_ID}}":        req.SiteID,
		"{{SITE_NAME}}":      hp.getSiteName(req),
		"{{BACKEND_IP}}":     resp.BackendIP,
		"{{STATUS_CODE}}":    fmt.Sprintf("%d", resp.StatusCode),
		"{{CONTENT_TYPE}}":   resp.ContentType,
		"{{CACHE_STATUS}}":   resp.CacheStatus,
	}
	
	// 执行替换
	result := value
	for placeholder, replacement := range replacements {
		result = strings.ReplaceAll(result, placeholder, replacement)
	}
	
	return result
}

// getSiteName 获取站点名称
func (hp *HeaderProcessor) getSiteName(req *RequestContext) string {
	if req.Site != nil {
		return req.Site.Name
	}
	return "Unknown"
}

// ProcessRequestHeaders 处理请求头部（在请求阶段调用）
func (hp *HeaderProcessor) ProcessRequestHeaders(req *RequestContext) {
	// 多级配置优先级：路由级 → 站点级 → 全局级

	// 1. 首先应用全局配置（最低优先级）
	if hp.globalHeadersConfig != nil {
		hp.applyRequestHeaderRules(hp.globalHeadersConfig.Request, req, "全局")
	}

	// 2. 然后应用站点级配置（中等优先级）
	if req.Site != nil {
		hp.applyRequestHeaderRules(req.Site.Headers.Request, req, "站点")
	}

	// 3. 最后应用路由级配置（最高优先级）
	if req.Route != nil && req.Route.Headers != nil {
		hp.applyRequestHeaderRules(req.Route.Headers.Request, req, "路由")
	}
}

// applyRequestHeaderRules 应用请求头部规则
func (hp *HeaderProcessor) applyRequestHeaderRules(rules config.HeaderRules, req *RequestContext, level string) {
	// 设置请求头部
	for key, value := range rules.Set {
		processedValue := hp.processRequestHeaderValue(value, req)
		req.Headers.Set(key, processedValue)
		hp.LogDebug("设置%s级请求头: %s = %s", level, key, processedValue)
	}

	// 删除请求头部
	for _, key := range rules.Remove {
		req.Headers.Del(key)
		hp.LogDebug("删除%s级请求头: %s", level, key)
	}

	// 忽略请求头部（不转发到上游）
	for _, key := range rules.Ignore {
		req.Headers.Del(key)
		hp.LogDebug("忽略%s级请求头: %s", level, key)
	}
}

// processRequestHeaderValue 处理请求头部值中的变量替换
func (hp *HeaderProcessor) processRequestHeaderValue(value string, req *RequestContext) string {
	// 定义可替换的变量
	replacements := map[string]string{
		"{{CLIENT_IP}}":      req.ClientIP,
		"{{REQUEST_METHOD}}": req.Method,
		"{{REQUEST_PATH}}":   req.URL.Path,
		"{{USER_AGENT}}":     req.UserAgent,
		"{{REFERER}}":        req.Referer,
		"{{TIMESTAMP}}":      time.Now().Format("2006-01-02 15:04:05"),
		"{{SITE_ID}}":        req.SiteID,
		"{{SITE_NAME}}":      hp.getSiteName(req),
	}
	
	// 执行替换
	result := value
	for placeholder, replacement := range replacements {
		result = strings.ReplaceAll(result, placeholder, replacement)
	}
	
	return result
}

// SetSecurityHeaders 设置安全相关头部
func (hp *HeaderProcessor) SetSecurityHeaders(resp *ResponseContext) {
	// 设置基本安全头部
	securityHeaders := map[string]string{
		"X-Content-Type-Options": "nosniff",
		"X-Frame-Options":        "DENY",
		"X-XSS-Protection":       "1; mode=block",
		"Referrer-Policy":        "strict-origin-when-cross-origin",
	}
	
	for key, value := range securityHeaders {
		if resp.Headers.Get(key) == "" { // 只在没有设置时添加
			resp.Headers.Set(key, value)
		}
	}
}

// SetCORSHeaders 设置CORS头部 (暂时注释掉，因为CORSConfig未定义)
/*
func (hp *HeaderProcessor) SetCORSHeaders(req *RequestContext, resp *ResponseContext, corsConfig *config.CORSConfig) {
	if corsConfig == nil || !corsConfig.Enabled {
		return
	}

	origin := req.Headers.Get("Origin")
	if origin == "" {
		return
	}

	// 检查是否允许的源
	if hp.isAllowedOrigin(origin, corsConfig.AllowedOrigins) {
		resp.Headers.Set("Access-Control-Allow-Origin", origin)
		resp.Headers.Set("Access-Control-Allow-Credentials", "true")

		if len(corsConfig.AllowedMethods) > 0 {
			resp.Headers.Set("Access-Control-Allow-Methods", strings.Join(corsConfig.AllowedMethods, ", "))
		}

		if len(corsConfig.AllowedHeaders) > 0 {
			resp.Headers.Set("Access-Control-Allow-Headers", strings.Join(corsConfig.AllowedHeaders, ", "))
		}

		if corsConfig.MaxAge > 0 {
			resp.Headers.Set("Access-Control-Max-Age", fmt.Sprintf("%d", corsConfig.MaxAge))
		}
	}
}

// isAllowedOrigin 检查是否为允许的源
func (hp *HeaderProcessor) isAllowedOrigin(origin string, allowedOrigins []string) bool {
	for _, allowed := range allowedOrigins {
		if allowed == "*" || allowed == origin {
			return true
		}
	}
	return false
}
*/

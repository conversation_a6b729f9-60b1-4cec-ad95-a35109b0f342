# 负载均衡算法优化

## 📋 概述

负载均衡算法优化通过实现多种智能分发策略，确保请求在多个上游服务器之间合理分配，提升系统整体性能和可用性。

## 🎯 优化目标

- **智能流量分发**：根据服务器性能动态调整流量
- **提升系统可用性**：避免单点过载
- **优化响应时间**：选择最优服务器处理请求
- **支持多种场景**：适应不同业务需求

## 🔧 算法实现

### 1. 平滑权重轮询 (Smooth Weighted Round Robin)

#### 特点
- **避免权重分配不均**：平滑分配高权重服务器的请求
- **动态权重调整**：支持运行时权重修改
- **健康状态感知**：自动排除不健康的服务器

#### 实现原理
```go
type SmoothWeightedRoundRobin struct {
    upstreams []*WeightedUpstream
    mutex     sync.RWMutex
}

type WeightedUpstream struct {
    Upstream      *config.UpstreamConfig
    Weight        int  // 配置权重
    CurrentWeight int  // 当前权重
    EffectiveWeight int // 有效权重
}
```

#### 算法流程
1. 每次选择时，所有服务器的当前权重加上其有效权重
2. 选择当前权重最大的服务器
3. 被选中服务器的当前权重减去所有服务器权重总和
4. 健康检查失败时降低有效权重

#### 配置示例
```json
{
  "load_balancer": {
    "algorithm": "smooth_weighted",
    "health_check_weight": true
  },
  "upstreams": [
    {"name": "server1", "weight": 5},
    {"name": "server2", "weight": 3},
    {"name": "server3", "weight": 2}
  ]
}
```

### 2. 一致性哈希 (Consistent Hash)

#### 特点
- **会话保持**：相同客户端请求路由到同一服务器
- **缓存友好**：适合有状态服务和缓存场景
- **扩展性好**：添加/删除服务器影响最小

#### 实现原理
```go
type ConsistentHash struct {
    hashRing     map[uint32]*config.UpstreamConfig
    sortedHashes []uint32
    upstreams    []*config.UpstreamConfig
    virtualNodes int
    mutex        sync.RWMutex
}
```

#### 虚拟节点
- **默认100个虚拟节点**：提高分布均匀性
- **可配置虚拟节点数**：根据服务器数量调整
- **哈希算法**：使用CRC32保证分布性

#### 哈希键策略
```json
{
  "load_balancer": {
    "algorithm": "consistent_hash",
    "hash_key": "ip",        // 基于客户端IP
    "virtual_nodes": 150
  }
}
```

支持的哈希键：
- `ip`：客户端IP地址
- `url`：请求URL路径
- `header:X-User-ID`：指定请求头

### 3. 响应时间加权 (Response Time Weighted)

#### 特点
- **性能感知**：根据实际响应时间调整权重
- **自适应调整**：自动适应服务器性能变化
- **实时优化**：持续优化请求分发

#### 实现原理
```go
type ResponseTimeWeighted struct {
    upstreams []*ResponseTimeUpstream
    mutex     sync.RWMutex
}

type ResponseTimeUpstream struct {
    Upstream     *config.UpstreamConfig
    Weight       float64
    ResponseTime time.Duration
    RequestCount int64
    LastUpdate   time.Time
}
```

#### 权重计算
```go
// 权重 = 基础权重 / (平均响应时间 + 1ms)
weight := float64(baseWeight) / float64(avgResponseTime.Milliseconds() + 1)
```

#### 配置示例
```json
{
  "load_balancer": {
    "algorithm": "response_time_weighted",
    "health_check_weight": true
  }
}
```

## ⚙️ 配置说明

### 全局负载均衡配置
```json
{
  "load_balancer": {
    "algorithm": "smooth_weighted",
    "hash_key": "ip",
    "virtual_nodes": 100,
    "health_check_weight": true
  }
}
```

### 站点级负载均衡
```json
{
  "sites": [
    {
      "name": "example",
      "load_balancer": {
        "algorithm": "consistent_hash",
        "hash_key": "header:X-Session-ID"
      },
      "upstreams": [
        {
          "name": "server1",
          "address": "************",
          "weight": 100,
          "load_balance_group": "main_group"
        }
      ]
    }
  ]
}
```

### 算法选择指南

| 场景 | 推荐算法 | 原因 |
|------|----------|------|
| 无状态服务 | smooth_weighted | 均匀分发，性能最优 |
| 有状态服务 | consistent_hash | 会话保持 |
| 缓存服务 | consistent_hash | 缓存命中率高 |
| 性能差异大 | response_time_weighted | 自适应性能 |
| 混合场景 | smooth_weighted | 通用性好 |

## 📊 监控指标

### 算法统计
通过 `/stats` API 查看：
```json
{
  "load_balancer": {
    "algorithm": "smooth_weighted",
    "total_requests": 10000,
    "upstreams": {
      "server1": {
        "weight": 100,
        "current_weight": 50,
        "request_count": 5000,
        "response_time": "25ms"
      }
    }
  }
}
```

### 关键指标
- **请求分发比例**：各服务器处理请求的比例
- **响应时间分布**：各服务器的响应时间统计
- **健康状态**：服务器健康检查状态
- **权重变化**：动态权重调整历史

## 🔍 故障排查

### 常见问题

#### 1. 负载分发不均
**现象**：某些服务器负载过高
**排查**：
```bash
# 检查权重配置
curl -u zdw:z7758521 http://localhost:8080/stats | jq '.load_balancer'

# 检查健康状态
curl -u zdw:z7758521 http://localhost:8080/stats | jq '.upstreams'
```

**解决**：
- 调整服务器权重
- 检查健康检查配置
- 验证算法选择是否合适

#### 2. 会话保持失效
**现象**：用户会话丢失
**解决**：
- 使用一致性哈希算法
- 配置正确的哈希键
- 检查虚拟节点数量

#### 3. 响应时间异常
**现象**：某些服务器响应时间突然增加
**解决**：
- 启用响应时间加权算法
- 检查服务器资源使用情况
- 调整健康检查参数

## 🚀 最佳实践

### 1. 算法选择
```json
// 推荐配置：混合使用
{
  "sites": [
    {
      "name": "api_service",
      "load_balancer": {
        "algorithm": "response_time_weighted"  // API服务用性能感知
      }
    },
    {
      "name": "static_content", 
      "load_balancer": {
        "algorithm": "consistent_hash",        // 静态内容用一致性哈希
        "hash_key": "url"
      }
    }
  ]
}
```

### 2. 权重配置
- **基于服务器性能**：CPU、内存、网络带宽
- **动态调整**：根据监控数据调整权重
- **预留容量**：为突发流量预留20-30%容量

### 3. 健康检查集成
```json
{
  "upstreams": [
    {
      "name": "server1",
      "weight": 100,
      "health_check": "/health",
      "health_interval": "10s",
      "health_timeout": "3s"
    }
  ],
  "load_balancer": {
    "health_check_weight": true  // 启用健康检查权重调整
  }
}
```

### 4. 监控告警
- **负载不均告警**：某服务器负载超过平均值50%
- **响应时间告警**：平均响应时间超过阈值
- **健康检查告警**：服务器健康检查失败

## 📈 性能测试

### 测试脚本
```bash
#!/bin/bash
# 负载均衡测试

# 测试权重分发
for i in {1..1000}; do
  curl -s http://your-proxy/ | grep "Server:"
done | sort | uniq -c

# 测试一致性哈希
for i in {1..100}; do
  curl -s -H "X-User-ID: user$i" http://your-proxy/
done
```

### 性能指标
- **分发均匀性**：各服务器请求数量方差
- **响应时间**：平均响应时间和P99延迟
- **吞吐量**：每秒处理请求数

负载均衡算法优化是提升系统性能和可用性的关键技术，选择合适的算法并正确配置可以显著改善用户体验。

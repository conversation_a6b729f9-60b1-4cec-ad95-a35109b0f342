{"rustc": 28845397767708332, "features": "", "target": 0, "profile": 0, "path": 0, "deps": [[17187612321583693167, "build_script_build", false, 4805246388144621123]], "local": [{"RerunIfChanged": {"output": "release\\build\\winapi-2e3f894ef3b15c72\\output", "paths": ["build.rs"]}}, {"RerunIfEnvChanged": {"var": "WINAPI_NO_BUNDLED_LIBRARIES", "val": null}}, {"RerunIfEnvChanged": {"var": "WINAPI_STATIC_NOBUNDLE", "val": null}}], "rustflags": [], "metadata": 0, "config": 0, "compile_kind": 0}
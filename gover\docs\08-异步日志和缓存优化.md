# 异步日志和缓存优化

## 📋 概述

异步日志和缓存优化通过非阻塞的日志写入和智能缓存管理，显著提升系统性能，减少I/O阻塞，优化内存使用，确保高并发场景下的稳定运行。

## 🎯 优化目标

- **消除I/O阻塞**：异步处理日志写入，不影响请求处理
- **提升写入性能**：批量处理，减少系统调用次数
- **优化内存使用**：智能缓存管理，防止内存泄漏
- **保证数据完整性**：确保日志不丢失，缓存数据一致性

## 🔧 技术实现

### 1. 异步日志系统

#### 核心架构设计
```go
type AsyncLogger struct {
    logChan     chan LogEntry      // 日志通道
    batchSize   int               // 批处理大小
    flushTime   time.Duration     // 刷新时间间隔
    buffer      []LogEntry        // 日志缓冲区
    writer      io.Writer         // 日志写入器
    wg          sync.WaitGroup    // 等待组
    ctx         context.Context   // 上下文
    cancel      context.CancelFunc // 取消函数
    stats       LoggerStats       // 统计信息
    dropPolicy  DropPolicy        // 丢弃策略
}

type LogEntry struct {
    Level     string    `json:"level"`
    Message   string    `json:"message"`
    Timestamp time.Time `json:"timestamp"`
    Fields    map[string]interface{} `json:"fields"`
}
```

#### 非阻塞日志写入
```go
func (al *AsyncLogger) Log(level, message string, fields map[string]interface{}) {
    entry := LogEntry{
        Level:     level,
        Message:   message,
        Timestamp: time.Now(),
        Fields:    fields,
    }
    
    select {
    case al.logChan <- entry:
        // 成功写入通道
        atomic.AddInt64(&al.stats.TotalLogs, 1)
    default:
        // 通道满，根据丢弃策略处理
        al.handleChannelFull(entry)
    }
}

func (al *AsyncLogger) handleChannelFull(entry LogEntry) {
    switch al.dropPolicy {
    case DropOldest:
        // 丢弃最旧的日志
        select {
        case <-al.logChan:
            al.logChan <- entry
            atomic.AddInt64(&al.stats.DroppedLogs, 1)
        default:
        }
    case DropNewest:
        // 丢弃最新的日志
        atomic.AddInt64(&al.stats.DroppedLogs, 1)
    case Block:
        // 阻塞等待
        al.logChan <- entry
    }
}
```

#### 批量处理机制
```go
func (al *AsyncLogger) processingLoop() {
    defer al.wg.Done()
    
    ticker := time.NewTicker(al.flushTime)
    defer ticker.Stop()
    
    for {
        select {
        case <-al.ctx.Done():
            al.flushBuffer()
            return
        case entry := <-al.logChan:
            al.buffer = append(al.buffer, entry)
            if len(al.buffer) >= al.batchSize {
                al.flushBuffer()
            }
        case <-ticker.C:
            if len(al.buffer) > 0 {
                al.flushBuffer()
            }
        }
    }
}

func (al *AsyncLogger) flushBuffer() {
    if len(al.buffer) == 0 {
        return
    }
    
    start := time.Now()
    
    // 批量写入日志
    for _, entry := range al.buffer {
        if err := al.writeEntry(entry); err != nil {
            atomic.AddInt64(&al.stats.ErrorLogs, 1)
        }
    }
    
    // 更新统计信息
    atomic.AddInt64(&al.stats.BatchWrites, 1)
    atomic.AddInt64(&al.stats.WrittenLogs, int64(len(al.buffer)))
    
    duration := time.Since(start)
    atomic.StoreInt64(&al.stats.LastFlushDuration, duration.Nanoseconds())
    
    // 清空缓冲区
    al.buffer = al.buffer[:0]
}
```

### 2. 智能缓存清理

#### 异步清理器设计
```go
type CacheCleaner struct {
    cache           Cache
    config          CleanupConfig
    cleanupChan     chan CleanupTask
    indexHeap       *IndexHeap
    ctx             context.Context
    cancel          context.CancelFunc
    wg              sync.WaitGroup
    stats           CleanupStats
    lastCleanupTime time.Time
}

type CleanupTask struct {
    Type     CleanupType
    FilePath string
    Size     int64
    ModTime  time.Time
}

type CleanupType int

const (
    CleanupExpired CleanupType = iota
    CleanupLRU
    CleanupSize
)
```

#### LRU淘汰算法
```go
type LRUCleaner struct {
    accessTimes map[string]time.Time
    mutex       sync.RWMutex
}

func (lru *LRUCleaner) UpdateAccess(key string) {
    lru.mutex.Lock()
    defer lru.mutex.Unlock()
    lru.accessTimes[key] = time.Now()
}

func (lru *LRUCleaner) GetLRUCandidates(count int) []string {
    lru.mutex.RLock()
    defer lru.mutex.RUnlock()
    
    type accessEntry struct {
        key  string
        time time.Time
    }
    
    entries := make([]accessEntry, 0, len(lru.accessTimes))
    for key, accessTime := range lru.accessTimes {
        entries = append(entries, accessEntry{key, accessTime})
    }
    
    // 按访问时间排序
    sort.Slice(entries, func(i, j int) bool {
        return entries[i].time.Before(entries[j].time)
    })
    
    result := make([]string, 0, count)
    for i := 0; i < count && i < len(entries); i++ {
        result = append(result, entries[i].key)
    }
    
    return result
}
```

#### 过期清理机制
```go
func (cc *CacheCleaner) expiredCleanupLoop() {
    defer cc.wg.Done()
    
    ticker := time.NewTicker(cc.config.ExpiredCheckInterval)
    defer ticker.Stop()
    
    for {
        select {
        case <-cc.ctx.Done():
            return
        case <-ticker.C:
            cc.cleanExpiredFiles()
        }
    }
}

func (cc *CacheCleaner) cleanExpiredFiles() {
    start := time.Now()
    cleanedCount := 0
    cleanedSize := int64(0)
    
    // 遍历缓存目录
    err := filepath.Walk(cc.config.CacheDir, func(path string, info os.FileInfo, err error) error {
        if err != nil {
            return nil
        }
        
        if info.IsDir() {
            return nil
        }
        
        // 检查文件是否过期
        if cc.isFileExpired(path, info) {
            if err := os.Remove(path); err == nil {
                cleanedCount++
                cleanedSize += info.Size()
                
                // 从索引中移除
                cc.indexHeap.Remove(path)
            }
        }
        
        return nil
    })
    
    if err == nil {
        atomic.AddInt64(&cc.stats.ExpiredCleaned, int64(cleanedCount))
        atomic.AddInt64(&cc.stats.SizeCleaned, cleanedSize)
        atomic.AddInt64(&cc.stats.TotalCleanups, 1)
        cc.lastCleanupTime = time.Now()
    }
    
    duration := time.Since(start)
    atomic.StoreInt64(&cc.stats.LastCleanupDuration, duration.Nanoseconds())
}
```

### 3. 内存管理优化

#### 对象池模式
```go
var logEntryPool = sync.Pool{
    New: func() interface{} {
        return &LogEntry{
            Fields: make(map[string]interface{}),
        }
    },
}

func getLogEntry() *LogEntry {
    return logEntryPool.Get().(*LogEntry)
}

func putLogEntry(entry *LogEntry) {
    // 清理字段
    for k := range entry.Fields {
        delete(entry.Fields, k)
    }
    entry.Level = ""
    entry.Message = ""
    entry.Timestamp = time.Time{}
    
    logEntryPool.Put(entry)
}
```

#### 缓冲区管理
```go
type BufferManager struct {
    buffers chan []byte
    size    int
}

func NewBufferManager(poolSize, bufferSize int) *BufferManager {
    bm := &BufferManager{
        buffers: make(chan []byte, poolSize),
        size:    bufferSize,
    }
    
    // 预分配缓冲区
    for i := 0; i < poolSize; i++ {
        bm.buffers <- make([]byte, 0, bufferSize)
    }
    
    return bm
}

func (bm *BufferManager) GetBuffer() []byte {
    select {
    case buf := <-bm.buffers:
        return buf[:0] // 重置长度但保留容量
    default:
        return make([]byte, 0, bm.size)
    }
}

func (bm *BufferManager) PutBuffer(buf []byte) {
    if cap(buf) != bm.size {
        return // 不是我们的缓冲区
    }
    
    select {
    case bm.buffers <- buf:
    default:
        // 池满，丢弃缓冲区
    }
}
```

## ⚙️ 配置说明

### 异步日志配置
```json
{
  "log": {
    "async": {
      "enabled": true,
      "buffer_size": 10000,
      "batch_size": 100,
      "flush_interval": "1s",
      "drop_policy": "drop_oldest",
      "memory_limit": "100MB"
    }
  }
}
```

### 缓存清理配置
```json
{
  "cache": {
    "cleanup": {
      "enabled": true,
      "async_cleanup": true,
      "cleanup_interval": "5m",
      "expired_check_interval": "1m",
      "lru_cleanup_threshold": 0.8,
      "max_cleanup_batch": 1000
    }
  }
}
```

### 参数详解

#### 异步日志参数
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `buffer_size` | int | 10000 | 日志通道缓冲区大小 |
| `batch_size` | int | 100 | 批处理大小 |
| `flush_interval` | duration | 1s | 强制刷新间隔 |
| `drop_policy` | string | drop_oldest | 丢弃策略 |
| `memory_limit` | string | 100MB | 内存使用限制 |

#### 缓存清理参数
| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `async_cleanup` | bool | true | 启用异步清理 |
| `cleanup_interval` | duration | 5m | 清理间隔 |
| `expired_check_interval` | duration | 1m | 过期检查间隔 |
| `lru_cleanup_threshold` | float | 0.8 | LRU清理阈值 |

## 📊 性能指标

### 异步日志统计
```json
{
  "async_logger": {
    "enabled": true,
    "total_logs": 1000000,
    "written_logs": 999500,
    "dropped_logs": 500,
    "error_logs": 0,
    "batch_writes": 10000,
    "avg_batch_size": 99.95,
    "last_flush_duration": "2.5ms",
    "channel_usage": 0.15,
    "memory_usage": "45MB"
  }
}
```

### 缓存清理统计
```json
{
  "cache_cleanup": {
    "async_cleanup": true,
    "cleanup_interval": 300000000000,
    "enabled": true,
    "expired_check_interval": 60000000000,
    "expired_cleaned": 1500,
    "heap_size": 50000,
    "indexed_files": 50000,
    "last_cleanup_time": "2024-01-01T12:30:00Z",
    "size_cleaned": 157286400,
    "total_cleanups": 48,
    "cleanup_efficiency": 0.95,
    "avg_cleanup_duration": "150ms"
  }
}
```

## 🔍 故障排查

### 常见问题

#### 1. 日志丢失
**现象**：部分日志未写入文件
**排查**：
```bash
# 检查异步日志统计
curl -u admin:password http://localhost:8080/stats | jq '.async_logger'

# 检查丢弃日志数量
curl -u admin:password http://localhost:8080/stats | jq '.async_logger.dropped_logs'
```

**解决**：
- 增加缓冲区大小
- 调整丢弃策略
- 优化日志写入性能

#### 2. 内存使用过高
**现象**：内存使用持续增长
**排查**：
```bash
# 检查内存使用
curl -u admin:password http://localhost:8080/stats | jq '.async_logger.memory_usage'

# 检查缓存大小
curl -u admin:password http://localhost:8080/stats | jq '.cache.total_size'
```

**解决**：
- 启用内存限制
- 调整批处理大小
- 优化清理策略

#### 3. 缓存清理效率低
**现象**：缓存清理耗时过长
**解决**：
- 减少清理批次大小
- 优化文件索引结构
- 使用并发清理

## 🚀 最佳实践

### 1. 分级日志策略
```json
{
  "log": {
    "levels": {
      "error": {
        "async": false,        // 错误日志同步写入
        "buffer_size": 0
      },
      "warn": {
        "async": true,
        "buffer_size": 1000,
        "batch_size": 50
      },
      "info": {
        "async": true,
        "buffer_size": 10000,
        "batch_size": 200
      },
      "debug": {
        "async": true,
        "buffer_size": 50000,
        "batch_size": 500,
        "drop_policy": "drop_oldest"
      }
    }
  }
}
```

### 2. 智能清理策略
```go
type SmartCleaner struct {
    systemLoad   *SystemLoadMonitor
    cleanupRules []CleanupRule
}

func (sc *SmartCleaner) ShouldCleanup() bool {
    load := sc.systemLoad.GetCurrentLoad()
    
    // 系统负载高时减少清理频率
    if load.CPU > 0.8 || load.Memory > 0.9 {
        return false
    }
    
    // 磁盘使用率高时增加清理频率
    if load.Disk > 0.85 {
        return true
    }
    
    return sc.normalCleanupCondition()
}
```

### 3. 优雅关闭
```go
func (al *AsyncLogger) Shutdown(timeout time.Duration) error {
    // 停止接收新日志
    al.cancel()
    
    // 等待处理完成或超时
    done := make(chan struct{})
    go func() {
        al.wg.Wait()
        close(done)
    }()
    
    select {
    case <-done:
        return nil
    case <-time.After(timeout):
        return fmt.Errorf("关闭超时")
    }
}
```

### 4. 监控告警
```json
{
  "alerts": {
    "log_drop_rate_high": {
      "threshold": 0.01,
      "message": "日志丢弃率过高"
    },
    "cache_cleanup_slow": {
      "threshold": "5s",
      "message": "缓存清理耗时过长"
    },
    "memory_usage_high": {
      "threshold": "500MB",
      "message": "异步日志内存使用过高"
    }
  }
}
```

## 📈 性能测试

### 日志性能测试
```bash
#!/bin/bash
# 异步日志性能测试

# 测试日志写入性能
test_log_performance() {
    echo "测试异步日志性能:"
    
    # 同步日志性能
    echo "同步日志性能:"
    time for i in {1..10000}; do
        echo "Test log message $i" >> sync.log
    done
    
    # 异步日志性能（通过API测试）
    echo "异步日志性能:"
    time for i in {1..10000}; do
        curl -s -X POST http://localhost:8080/log \
             -d '{"level":"info","message":"Test log message '$i'"}'
    done
}

# 测试内存使用
test_memory_usage() {
    echo "测试内存使用:"
    
    # 记录初始内存
    initial_mem=$(curl -s -u admin:password http://localhost:8080/stats | jq '.async_logger.memory_usage')
    echo "初始内存使用: $initial_mem"
    
    # 发送大量日志
    for i in {1..100000}; do
        curl -s -X POST http://localhost:8080/log \
             -d '{"level":"info","message":"Memory test message '$i'"}'
    done
    
    # 检查内存使用
    final_mem=$(curl -s -u admin:password http://localhost:8080/stats | jq '.async_logger.memory_usage')
    echo "最终内存使用: $final_mem"
}

test_log_performance
test_memory_usage
```

### 缓存清理测试
```bash
#!/bin/bash
# 缓存清理性能测试

# 创建测试缓存文件
create_test_cache() {
    echo "创建测试缓存文件:"
    
    for i in {1..10000}; do
        echo "Test cache content $i" > "cache/test_$i.cache"
    done
    
    echo "创建了10000个测试缓存文件"
}

# 测试清理性能
test_cleanup_performance() {
    echo "测试缓存清理性能:"
    
    start_time=$(date +%s)
    
    # 触发清理
    curl -s -X POST -u admin:password http://localhost:8080/cache/cleanup
    
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    
    echo "清理耗时: ${duration}秒"
    
    # 检查清理统计
    curl -s -u admin:password http://localhost:8080/stats | jq '.cache_cleanup'
}

create_test_cache
test_cleanup_performance
```

## 🔧 高级功能

### 1. 分布式日志聚合
```go
type DistributedLogger struct {
    localLogger  *AsyncLogger
    remoteLogger *RemoteLogger
    router       *LogRouter
}

func (dl *DistributedLogger) Log(entry LogEntry) {
    // 本地日志
    dl.localLogger.Log(entry.Level, entry.Message, entry.Fields)
    
    // 根据路由规则决定是否发送到远程
    if dl.router.ShouldSendRemote(entry) {
        dl.remoteLogger.Send(entry)
    }
}
```

### 2. 压缩日志存储
```go
type CompressedLogger struct {
    writer     *gzip.Writer
    file       *os.File
    rotateSize int64
    currentSize int64
}

func (cl *CompressedLogger) Write(data []byte) (int, error) {
    n, err := cl.writer.Write(data)
    cl.currentSize += int64(n)
    
    if cl.currentSize > cl.rotateSize {
        cl.rotate()
    }
    
    return n, err
}
```

### 3. 智能预清理
```go
type PredictiveCleaner struct {
    predictor *UsagePredictor
    cleaner   *CacheCleaner
}

func (pc *PredictiveCleaner) PredictiveCleanup() {
    prediction := pc.predictor.PredictUsage(time.Hour)
    
    if prediction.WillExceedLimit {
        // 提前清理
        pc.cleaner.CleanupByPrediction(prediction.ExcessSize)
    }
}
```

异步日志和缓存优化是高性能系统的重要组成部分，通过合理的设计和配置可以显著提升系统的并发处理能力和稳定性。

# 监控功能文档

## 概述

反向代理服务器提供了完整的状态监控功能，通过独立的HTTP API接口提供实时统计信息和健康检查。

## 功能特性

### 1. 全局统计
- **总请求数**: 服务器启动以来的总请求数量
- **错误请求数**: 4xx/5xx状态码的请求数量
- **接收流量**: 从客户端接收的总字节数
- **发送流量**: 向客户端发送的总字节数
- **上游接收流量**: 从上游服务器接收的总字节数
- **上游发送流量**: 向上游服务器发送的总字节数
- **当前连接数**: 当前活跃连接数
- **运行时间**: 服务器启动后的运行时间

### 2. 站点统计
- **每个站点的请求统计**: 包括总请求数、错误请求数、流量统计
- **站点连接数**: 每个站点的当前连接数
- **站点运行时间**: 每个站点的运行时间

### 3. 上游服务器监控
- **健康状态**: 实时健康检查结果
- **响应时间**: 健康检查的响应时间
- **成功/失败次数**: 健康检查的成功和失败统计
- **离线次数**: 服务器变为不健康的次数
- **离线时间**: 累计离线时间
- **最后检查时间**: 最后一次健康检查的时间

### 4. 带宽统计
- **入站带宽**: 最近1分钟的平均入站速率 (bytes/sec)
- **出站带宽**: 最近1分钟的平均出站速率 (bytes/sec)
- **上游入站带宽**: 最近1分钟的平均上游入站速率
- **上游出站带宽**: 最近1分钟的平均上游出站速率

## 配置

在配置文件中添加 `monitor` 部分：

```json
{
  "monitor": {
    "enabled": true,
    "port": 8080,
    "username": "admin",
    "password": "admin123"
  }
}
```

### 配置参数

- **enabled**: 是否启用监控功能 (true/false)
- **port**: 监控API服务器监听端口
- **username**: 认证用户名
- **password**: 认证密码

## API接口

### 1. 监控面板首页
```
GET http://localhost:8080/
```
提供完整的Web监控界面，包含所有统计信息的可视化展示。

### 2. 统计信息API
```
GET http://localhost:8080/stats
```
返回JSON格式的完整统计信息。

### 3. 健康检查API
```
GET http://localhost:8080/health
```
返回监控服务自身的健康状态。

### 4. 上游服务器状态API
```
GET http://localhost:8080/upstreams
```
返回所有上游服务器的详细状态信息。

### 5. 站点统计API
```
GET http://localhost:8080/sites
```
返回所有站点的统计信息。

## 认证

所有API接口都需要认证，支持以下认证方式：

### 1. Basic认证
```
Authorization: Basic YWRtaW46YWRtaW4xMjM=
```

### 2. URL参数认证
```
GET http://localhost:8080/stats?auth=admin:admin123
```

### 3. 简单token认证
```
Authorization: admin:admin123
```

## 健康检查

### 上游服务器健康检查
- **检查间隔**: 30秒
- **超时时间**: 10秒
- **检查URL**: 默认 `/health`，可在配置中自定义
- **失败阈值**: 连续失败3次后标记为不健康
- **恢复机制**: 成功响应后立即恢复健康状态

### 健康检查URL配置
在站点配置的上游服务器中设置：

```json
{
  "name": "primary",
  "address": "*************",
  "port": 80,
  "health_check": "http://*************/health"
}
```

## 使用示例

### 1. 启动带监控的服务器
```bash
go run main.go -config config-monitor.json
```

### 2. 访问监控面板
打开浏览器访问：`http://localhost:8080/`

### 3. 获取统计信息
```bash
curl -u admin:admin123 http://localhost:8080/stats
```

### 4. 监控特定站点
```bash
curl -u admin:admin123 http://localhost:8080/sites
```

## 监控数据说明

### 请求统计
- **总请求数**: 包括所有HTTP请求（GET、POST等）
- **错误请求数**: 状态码 >= 400 的请求
- **流量统计**: 包含请求体和响应体的字节数

### 连接统计
- **当前连接数**: 实时活跃的TCP连接数
- **连接跟踪**: 请求开始时增加，结束时减少

### 带宽计算
- **计算周期**: 每分钟计算一次
- **计算方式**: (当前字节数 - 上次字节数) / 时间间隔
- **单位**: bytes/sec

## 注意事项

1. **性能影响**: 监控功能会轻微影响性能，建议在生产环境中合理配置
2. **内存使用**: 统计数据会占用一定内存，长时间运行建议定期清理
3. **安全考虑**: 监控接口包含敏感信息，请使用强密码并限制访问IP
4. **健康检查**: 确保上游服务器提供 `/health` 端点或自定义健康检查URL
5. **日志记录**: 监控相关的错误和状态变化会记录到应用日志中

## 故障排除

### 监控服务无法启动
- 检查端口是否被占用
- 确认配置文件中的监控配置正确
- 查看应用日志中的错误信息

### 健康检查失败
- 确认上游服务器可访问
- 检查健康检查URL是否正确
- 验证网络连接和防火墙设置

### 统计数据不准确
- 确认监控器已正确注入到代理服务器
- 检查请求处理流程中的监控调用
- 验证并发访问时的数据一致性 
# 异步日志系统

## 概述

异步日志系统通过将日志写入操作从HTTP请求处理流程中分离出来，显著提升反向代理的性能。特别是在高并发场景下，可以避免因日志I/O阻塞导致的响应延迟。

## 性能优势

| 场景 | 同步日志 | 异步日志 | 性能提升 |
|------|----------|----------|----------|
| 本地文件写入 | 1-5ms | 0.01ms | 100-500x |
| 网络syslog | 10-50ms | 0.01ms | 1000-5000x |
| 高并发请求 | 线性下降 | 基本恒定 | 显著提升 |

## 配置说明

### 基本配置

```json
{
  "log": {
    "async": {
      "enabled": true,
      "channel_size": 10000,
      "batch_size": 100,
      "flush_interval": "500ms",
      "max_memory_mb": 50,
      "drop_policy": "drop_oldest"
    }
  }
}
```

### 配置参数详解

#### `enabled` (bool)
- **默认值**: `false`
- **说明**: 是否启用异步日志
- **建议**: 生产环境建议启用

#### `channel_size` (int)
- **默认值**: `1000`
- **说明**: 日志通道的缓冲区大小
- **建议**: 
  - 低并发: 1000-5000
  - 中并发: 5000-10000
  - 高并发: 10000-50000

#### `batch_size` (int)
- **默认值**: `100`
- **说明**: 批量写入的日志条数
- **建议**:
  - 文件日志: 50-200
  - 网络日志: 20-100
  - 平衡性能和实时性

#### `flush_interval` (duration)
- **默认值**: `500ms`
- **说明**: 强制刷新的时间间隔
- **建议**:
  - 实时性要求高: 100ms-500ms
  - 性能优先: 1s-5s

#### `max_memory_mb` (int)
- **默认值**: `50`
- **说明**: 异步日志系统最大内存使用量(MB)
- **建议**:
  - 小型服务: 10-50MB
  - 中型服务: 50-200MB
  - 大型服务: 200-500MB

#### `drop_policy` (string)
- **默认值**: `drop_oldest`
- **可选值**:
  - `drop_oldest`: 丢弃最旧的日志
  - `drop_newest`: 丢弃最新的日志
  - `block`: 阻塞等待（降级为同步）
- **建议**: 通常使用 `drop_oldest`

## 使用场景建议

### 高并发Web服务
```json
{
  "async": {
    "enabled": true,
    "channel_size": 20000,
    "batch_size": 200,
    "flush_interval": "1s",
    "max_memory_mb": 100,
    "drop_policy": "drop_oldest"
  }
}
```

### 实时监控场景
```json
{
  "async": {
    "enabled": true,
    "channel_size": 5000,
    "batch_size": 50,
    "flush_interval": "200ms",
    "max_memory_mb": 30,
    "drop_policy": "block"
  }
}
```

### 资源受限环境
```json
{
  "async": {
    "enabled": true,
    "channel_size": 1000,
    "batch_size": 50,
    "flush_interval": "2s",
    "max_memory_mb": 20,
    "drop_policy": "drop_newest"
  }
}
```

## 监控指标

异步日志系统提供以下监控指标：

- `total_logs`: 总日志数量
- `dropped_logs`: 丢弃的日志数量
- `memory_usage`: 当前内存使用量
- `channel_len`: 通道中待处理日志数
- `buffer_len`: 缓冲区中日志数

### 获取监控数据

通过监控API可以获取异步日志的统计信息：

```bash
curl -u admin:password http://localhost:8080/stats
```

## 最佳实践

### 1. 容量规划
- 根据QPS估算通道大小: `channel_size >= QPS * flush_interval(秒)`
- 内存使用估算: 平均每条日志约500字节

### 2. 性能调优
- 批量大小与刷新间隔成反比
- 网络日志建议较小的批量大小
- 文件日志可以使用较大的批量大小

### 3. 可靠性保证
- 重要日志使用 `block` 策略
- 监控丢弃日志数量
- 定期检查磁盘空间

### 4. 故障处理
- 异步日志失败时自动降级为同步
- 程序关闭时确保缓冲区日志写入完成
- 网络异常时重试机制

## 注意事项

1. **内存使用**: 异步日志会占用额外内存，需要合理配置
2. **日志丢失**: 在极端情况下可能丢失少量日志
3. **延迟写入**: 日志写入有一定延迟，不适合需要立即写入的场景
4. **程序崩溃**: 缓冲区中的日志可能丢失

## 故障排查

### 日志丢失过多
- 检查 `max_memory_mb` 是否过小
- 检查 `channel_size` 是否不足
- 考虑调整 `drop_policy`

### 内存使用过高
- 减少 `channel_size` 或 `max_memory_mb`
- 减少 `flush_interval`
- 检查日志写入是否有阻塞

### 响应延迟
- 检查是否频繁降级为同步写入
- 检查网络日志连接是否正常
- 考虑增加 `channel_size`

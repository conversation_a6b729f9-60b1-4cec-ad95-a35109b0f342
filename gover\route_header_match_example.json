{"sites": [{"name": "wordpress_site", "domains": ["example.com", "www.example.com"], "upstreams": [{"name": "wordpress_backend", "address": "127.0.0.1", "port": 8080, "weight": 1}], "routes": [{"pattern": "\\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$", "upstream": "wordpress_backend", "cache": true, "match_conditions": {"headers": [{"name": "Accept", "pattern": "text/css|application/javascript|image/.*", "description": "静态资源请求"}]}}, {"pattern": "/api/v1/", "upstream": "wordpress_backend", "cache": true, "match_conditions": {"headers": [{"name": "X-API-Version", "value": "1.0", "description": "API版本1.0"}, {"name": "Accept", "value": "application/json", "description": "JSON请求"}], "methods": ["GET", "POST"]}}, {"pattern": "/api/v2/", "upstream": "wordpress_backend", "cache": true, "match_conditions": {"headers": [{"name": "X-API-Version", "value": "2.0", "description": "API版本2.0"}]}}, {"pattern": "/mobile/", "upstream": "wordpress_backend", "cache": true, "match_conditions": {"headers": [{"name": "User-Agent", "pattern": ".*(Mobile|Android|iPhone|iPad).*", "description": "移动设备"}], "user_agents": [".*(Mobile|Android|iPhone|iPad).*"]}}, {"pattern": "/ajax/", "upstream": "wordpress_backend", "cache": true, "match_conditions": {"headers": [{"name": "X-Requested-With", "value": "XMLHttpRequest", "description": "AJAX请求"}]}}, {"pattern": "/admin/", "upstream": "wordpress_backend", "cache": false, "match_conditions": {"headers": [{"name": "Authorization", "exists": true, "description": "需要认证"}]}}, {"pattern": "/wp-admin/", "upstream": "wordpress_backend", "cache": false, "match_conditions": {"cookies": [{"name": "wordpress_logged_in", "exists": true, "description": "WordPress登录状态"}]}}, {"pattern": "/wp-admin/", "upstream": "wordpress_backend", "cache": false, "match_conditions": null, "description": "WordPress管理后台（兜底路由）"}, {"pattern": "/feed/", "upstream": "wordpress_backend", "cache": true, "match_conditions": {"headers": [{"name": "Accept", "pattern": ".*(xml|rss|atom).*", "description": "RSS/Atom feed"}], "cookies": [{"name": "wordpress_logged_in", "exists": false, "description": "未登录用户"}]}}, {"pattern": "/search/", "upstream": "wordpress_backend", "cache": true, "match_conditions": {"query_params": [{"name": "q", "exists": true, "description": "有搜索关键词"}], "headers": [{"name": "Accept", "pattern": "text/html", "description": "HTML页面"}], "cookies": [{"name": "wordpress_logged_in", "exists": false, "description": "未登录用户"}]}}, {"pattern": "/download/", "upstream": "wordpress_backend", "cache": true, "match_conditions": {"headers": [{"name": "Range", "exists": false, "description": "完整下载（非断点续传）"}, {"name": "Accept", "pattern": "application/.*", "description": "二进制文件"}]}}, {"pattern": "/locale/zh-cn/", "upstream": "wordpress_backend", "cache": true, "match_conditions": {"headers": [{"name": "Accept-Language", "pattern": "zh-CN|zh", "description": "中文用户"}]}}, {"pattern": "/locale/en-us/", "upstream": "wordpress_backend", "cache": true, "match_conditions": {"headers": [{"name": "Accept-Language", "pattern": "en-US|en", "description": "英文用户"}]}}, {"pattern": "/compress/", "upstream": "wordpress_backend", "cache": true, "match_conditions": {"headers": [{"name": "Accept-Encoding", "pattern": ".*(gzip|deflate|br).*", "description": "支持压缩"}]}}, {"pattern": "/preview/", "upstream": "wordpress_backend", "cache": false, "match_conditions": {"cookies": [{"name": "preview_token", "exists": true, "description": "预览令牌"}], "query_params": [{"name": "preview", "value": "true", "description": "预览模式"}]}}, {"pattern": "^/", "upstream": "wordpress_backend", "cache": false, "match_conditions": {"cookies": [{"name": "wordpress_logged_in", "pattern": "wordpress_[a-f0-9]+", "description": "WordPress登录用户"}]}, "description": "已登录用户的页面不缓存"}, {"pattern": "^/", "upstream": "wordpress_backend", "cache": false, "match_conditions": {"methods": ["POST", "PUT", "DELETE", "PATCH"]}, "description": "非GET请求不缓存"}, {"pattern": "^/", "upstream": "wordpress_backend", "cache": false, "match_conditions": {"query_params": [{"name": "*", "exists": true, "description": "有查询参数"}]}, "description": "有查询参数的请求不缓存"}, {"pattern": "^/", "upstream": "wordpress_backend", "cache": true, "match_conditions": {"methods": ["GET", "HEAD"], "headers": [{"name": "Accept", "pattern": "text/html", "description": "HTML页面"}], "cookies": [{"name": "wordpress_logged_in", "exists": false, "description": "未登录用户"}]}, "description": "未登录用户的HTML页面缓存"}, {"pattern": "^/", "upstream": "wordpress_backend", "cache": false, "description": "兜底路由，不缓存"}]}]}
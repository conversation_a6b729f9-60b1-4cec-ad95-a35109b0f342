# 📋 内存映射缓存配置指南

## 🎯 配置选项说明

内存映射缓存现在支持完全通过配置文件进行路径设置，提供了多种灵活的配置方式。

### **配置结构**

```json
"mmap_cache": {
  "enabled": true,
  "max_size": "1GB",
  "base_path": "cache/mmap",
  "auto_platform_path": false,
  "windows_path": "",
  "linux_path": "",
  "macos_path": "",
  "freebsd_path": "",
  "fallback_path": ""
}
```

### **配置字段详解**

| 字段 | 类型 | 说明 | 示例 |
|------|------|------|------|
| `enabled` | bool | 是否启用内存映射缓存 | `true` |
| `max_size` | string | 最大缓存大小 | `"1GB"`, `"512MB"` |
| `base_path` | string | 基础路径（所有平台通用） | `"cache/mmap"` |
| `auto_platform_path` | bool | 是否自动选择平台路径 | `true` |
| `windows_path` | string | Windows专用路径 | `"C:\\cache\\mmap"` |
| `linux_path` | string | Linux专用路径 | `"/dev/shm/proxy_mmap"` |
| `macos_path` | string | macOS专用路径 | `"/tmp/proxy_mmap"` |
| `freebsd_path` | string | FreeBSD专用路径 | `"/tmp/proxy_mmap"` |
| `fallback_path` | string | 降级路径 | `"./cache/mmap"` |

## 🔧 配置方式

### **方式1：简单配置（推荐）**

使用基础路径，系统自动处理跨平台兼容性：

```json
"mmap_cache": {
  "enabled": true,
  "max_size": "1GB",
  "base_path": "cache/mmap",
  "auto_platform_path": false,
  "windows_path": "",
  "linux_path": "",
  "macos_path": "",
  "fallback_path": ""
}
```

**效果**：
- Windows: `cache\mmap`
- Linux: `cache/mmap`
- macOS: `cache/mmap`

### **方式2：自动平台路径**

启用自动平台路径选择，系统自动选择最佳路径：

```json
"mmap_cache": {
  "enabled": true,
  "max_size": "1GB",
  "base_path": "proxy_mmap",
  "auto_platform_path": true,
  "windows_path": "",
  "linux_path": "",
  "macos_path": "",
  "fallback_path": ""
}
```

**效果**：
- Windows: `C:\Users\<USER>\AppData\Local\Temp\proxy_mmap`
- Linux: `/dev/shm/proxy_mmap` (如果存在) 或 `/tmp/proxy_mmap`
- macOS: `/tmp/proxy_mmap`
- FreeBSD: `/tmp/proxy_mmap`

### **方式3：平台特定路径**

为不同平台指定专用路径：

```json
"mmap_cache": {
  "enabled": true,
  "max_size": "1GB",
  "base_path": "cache/mmap",
  "auto_platform_path": false,
  "windows_path": "D:\\cache\\proxy_mmap",
  "linux_path": "/dev/shm/proxy_mmap",
  "macos_path": "/tmp/proxy_mmap",
  "freebsd_path": "/tmp/proxy_mmap",
  "fallback_path": "./cache/mmap"
}
```

**效果**：
- Windows: `D:\cache\proxy_mmap`
- Linux: `/dev/shm/proxy_mmap`
- macOS: `/tmp/proxy_mmap`
- FreeBSD: `/tmp/proxy_mmap`

### **方式4：高性能配置**

针对高性能场景的优化配置：

```json
"mmap_cache": {
  "enabled": true,
  "max_size": "2GB",
  "base_path": "",
  "auto_platform_path": false,
  "windows_path": "%TEMP%\\proxy_mmap",
  "linux_path": "/dev/shm/proxy_mmap",
  "macos_path": "/tmp/proxy_mmap",
  "fallback_path": "./cache/mmap"
}
```

## 📊 路径选择优先级

系统按以下优先级选择缓存路径：

1. **自动平台路径** (`auto_platform_path: true`)
2. **平台特定路径** (`windows_path`, `linux_path`, `macos_path`, `freebsd_path`)
3. **基础路径** (`base_path`)
4. **降级路径** (`fallback_path`)
5. **系统默认路径**

## 🌟 特殊功能

### **环境变量支持**

路径中可以使用环境变量：

```json
"windows_path": "%USERPROFILE%\\cache\\proxy_mmap",
"linux_path": "$HOME/cache/proxy_mmap",
"macos_path": "$HOME/cache/proxy_mmap"
```

### **用户目录支持**

支持 `~` 符号表示用户目录：

```json
"base_path": "~/cache/proxy_mmap"
```

### **相对路径支持**

支持相对于程序运行目录的路径：

```json
"base_path": "./cache/mmap",
"fallback_path": "../shared_cache/mmap"
```

## 🎯 推荐配置

### **开发环境**
```json
"mmap_cache": {
  "enabled": true,
  "max_size": "512MB",
  "base_path": "cache/mmap",
  "auto_platform_path": false,
  "windows_path": "",
  "linux_path": "",
  "macos_path": "",
  "fallback_path": ""
}
```

### **生产环境（跨平台）**
```json
"mmap_cache": {
  "enabled": true,
  "max_size": "2GB",
  "base_path": "proxy_mmap",
  "auto_platform_path": true,
  "windows_path": "",
  "linux_path": "",
  "macos_path": "",
  "fallback_path": "./cache/mmap"
}
```

### **生产环境（高性能）**
```json
"mmap_cache": {
  "enabled": true,
  "max_size": "4GB",
  "base_path": "",
  "auto_platform_path": false,
  "windows_path": "%TEMP%\\proxy_mmap",
  "linux_path": "/dev/shm/proxy_mmap",
  "macos_path": "/tmp/proxy_mmap",
  "fallback_path": "./cache/mmap"
}
```

## 🔍 日志输出

启动时会显示实际使用的缓存路径：

```
time="..." level=info msg="内存映射缓存已启用, 路径: /dev/shm/proxy_mmap (平台: linux)"
time="..." level=info msg="内存映射缓存已启用, 路径: C:\Users\<USER>\AppData\Local\Temp\proxy_mmap (平台: windows)"
time="..." level=info msg="内存映射缓存已启用, 路径: /tmp/proxy_mmap (平台: darwin)"
```

## ⚠️ 注意事项

1. **权限问题**：确保指定的路径有读写权限
2. **磁盘空间**：确保有足够的磁盘空间存储缓存
3. **性能考虑**：
   - Linux: `/dev/shm` 最快（内存文件系统）
   - Windows: SSD上的临时目录较快
   - 避免使用网络驱动器
4. **路径格式**：
   - Windows: 使用反斜杠 `\` 或正斜杠 `/`
   - Linux/macOS: 使用正斜杠 `/`
5. **环境变量**：确保使用的环境变量在运行环境中存在

## 🚀 部署建议

1. **测试配置**：在测试环境验证路径配置
2. **监控日志**：观察启动日志确认路径正确
3. **性能测试**：验证缓存性能是否符合预期
4. **备份配置**：保存工作正常的配置作为备份

现在你可以完全通过配置文件来控制内存映射缓存的路径，无需修改代码！

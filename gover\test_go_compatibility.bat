@echo off
chcp 936 > nul
setlocal enabledelayedexpansion

echo ==========================================
echo Go版本兼容性测试脚本
echo ==========================================
echo.

:: 检查Go版本
echo 检查Go版本...
go version
echo.

:: 测试Go 1.20兼容性
echo ==========================================
echo 测试Go 1.20兼容性
echo ==========================================

echo 1. 复制go1.20.mod到go.mod...
copy /y go1.20.mod go.mod
if errorlevel 1 (
    echo ERROR: 复制go1.20.mod失败
    goto :error
)

echo 2. 下载依赖...
go mod download
if errorlevel 1 (
    echo ERROR: Go 1.20依赖下载失败
    goto :error
)

echo 3. 整理依赖...
go mod tidy
if errorlevel 1 (
    echo ERROR: Go 1.20依赖整理失败
    goto :error
)

echo 4. 编译测试...
go build -o test_go120.exe main.go
if errorlevel 1 (
    echo ERROR: Go 1.20编译失败
    goto :error
) else (
    echo ✓ Go 1.20编译成功
    del test_go120.exe 2>nul
)

echo.

:: 测试Go 1.24兼容性
echo ==========================================
echo 测试Go 1.24兼容性
echo ==========================================

echo 1. 复制go1.24.mod到go.mod...
copy /y go1.24.mod go.mod
if errorlevel 1 (
    echo ERROR: 复制go1.24.mod失败
    goto :error
)

echo 2. 下载依赖...
go mod download
if errorlevel 1 (
    echo ERROR: Go 1.24依赖下载失败
    goto :error
)

echo 3. 整理依赖...
go mod tidy
if errorlevel 1 (
    echo ERROR: Go 1.24依赖整理失败
    goto :error
)

echo 4. 编译测试...
go build -o test_go124.exe main.go
if errorlevel 1 (
    echo ERROR: Go 1.24编译失败
    goto :error
) else (
    echo ✓ Go 1.24编译成功
    del test_go124.exe 2>nul
)

echo.

:: 运行代码检查
echo ==========================================
echo 运行代码检查
echo ==========================================

echo 1. 检查Go循环变量陷阱...
echo 搜索可能的循环变量陷阱模式...

:: 搜索for range + goroutine模式
findstr /s /n /r "for.*range.*{" *.go internal\*.go internal\*\*.go 2>nul | findstr /c:"go func" >nul
if not errorlevel 1 (
    echo 警告: 发现可能的循环变量陷阱，请检查以下文件:
    findstr /s /n /r "for.*range.*{" *.go internal\*.go internal\*\*.go 2>nul | findstr /c:"go func"
) else (
    echo ✓ 未发现明显的循环变量陷阱
)

echo.

:: 依赖版本对比
echo ==========================================
echo 依赖版本对比
echo ==========================================

echo Go 1.20 mod文件依赖:
echo ------------------------
type go1.20.mod | findstr "require" -A 20

echo.
echo Go 1.24 mod文件依赖:
echo ------------------------
type go1.24.mod | findstr "require" -A 20

echo.

:: 恢复原始go.mod
echo 恢复原始go.mod...
copy /y go1.20.mod go.mod

echo ==========================================
echo ✓ Go版本兼容性测试完成
echo ==========================================
echo.
echo 测试结果:
echo - Go 1.20兼容性: 通过
echo - Go 1.24兼容性: 通过
echo - 循环变量陷阱检查: 已修复
echo.
echo 建议:
echo 1. 使用go1.20.mod进行Go 1.20构建
echo 2. 使用go1.24.mod进行Go 1.24构建
echo 3. 已修复的循环变量陷阱确保跨版本兼容性
echo.
goto :end

:error
echo.
echo ==========================================
echo ✗ 兼容性测试失败
echo ==========================================
echo 请检查错误信息并修复问题
exit /b 1

:end
echo 测试完成，按任意键退出...
pause >nul

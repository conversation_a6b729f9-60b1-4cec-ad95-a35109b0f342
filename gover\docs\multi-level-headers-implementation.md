# 多级Headers配置实现文档

## 概述

本文档描述了反向代理系统中多级Headers配置功能的实现，该功能支持全局、站点、路由三个级别的头部配置，并按照优先级进行覆盖。

## 功能特性

### 1. 多级配置层次
- **全局级别**: 在配置文件根级别的 `headers` 配置
- **站点级别**: 在每个站点配置中的 `headers` 配置  
- **路由级别**: 在每个路由配置中的 `headers` 配置

### 2. 优先级规则
配置优先级从高到低：
1. 路由级别（最高优先级）
2. 站点级别（中等优先级）
3. 全局级别（最低优先级）

高优先级的配置会覆盖低优先级的同名头部设置。

### 3. 头部操作类型
每个级别都支持以下操作：
- `set`: 设置头部值
- `remove`: 删除头部
- `ignore`: 忽略头部（不转发到上游或不返回给客户端）

## 配置格式

### 全局级别配置
```json
{
  "headers": {
    "request": {
      "set": {
        "X-Global-Header": "global-value"
      },
      "remove": ["X-Unwanted-Header"],
      "ignore": ["X-Internal-Header"]
    },
    "response": {
      "set": {
        "Server": "Global-CDN-Server/1.0"
      },
      "remove": [],
      "ignore": []
    }
  }
}
```

### 站点级别配置
```json
{
  "sites": [
    {
      "site_id": "example-site",
      "headers": {
        "request": {
          "set": {
            "X-Site-Header": "site-value"
          }
        },
        "response": {
          "set": {
            "Server": "Site-Server/1.0"
          }
        }
      }
    }
  ]
}
```

### 路由级别配置
```json
{
  "routes": [
    {
      "pattern": "^/api/",
      "headers": {
        "response": {
          "set": {
            "Server": "Route-API-Server/1.0",
            "Cache-Control": "no-cache"
          }
        }
      }
    }
  ]
}
```

## 实现细节

### 1. 配置结构更新
- 在 `Config` 结构体中添加了 `Headers HeadersConfig` 字段
- 在 `RouteConfig` 结构体中添加了 `Headers *HeadersConfig` 字段
- 在 `RouteInfo` 结构体中添加了 `Headers *HeadersConfig` 字段

### 2. HeaderProcessor增强
- 构造函数现在接收全局headers配置参数
- 实现了多级配置的应用逻辑
- 支持请求头和响应头的分别处理

### 3. 处理流程
1. **请求阶段**: 按优先级应用请求头配置（全局→站点→路由）
2. **响应阶段**: 按优先级应用响应头配置（全局→站点→路由）

### 4. 核心方法
- `applyRequestHeaderRules()`: 应用请求头规则
- `applyResponseHeaderRules()`: 应用响应头规则
- `processRequestHeaders()`: 处理请求头（多级配置）
- `processResponseHeaders()`: 处理响应头（多级配置）

## 测试配置示例

为了验证多级配置的优先级，我们设置了不同的Server头部值：

1. **全局级别**: `"Server": "Global-CDN-Server/1.0"`
2. **站点级别**: `"Server": "Site-TingTao-Server/1.0"`
3. **路由级别**: `"Server": "Route-Static-Server/1.0"`

根据优先级规则，最终的Server头部值应该是：
- 有路由级配置的请求: `Route-Static-Server/1.0`
- 只有站点级配置的请求: `Site-TingTao-Server/1.0`
- 只有全局级配置的请求: `Global-CDN-Server/1.0`

## 兼容性说明

- 该功能完全向后兼容现有配置
- 如果某个级别没有配置headers，会自动跳过该级别
- 现有的站点级headers配置继续正常工作

## 日志输出

HeaderProcessor会输出详细的调试日志，显示每个级别的头部设置操作：
```
设置全局级响应头: Server = Global-CDN-Server/1.0
设置站点级响应头: Server = Site-TingTao-Server/1.0
设置路由级响应头: Server = Route-Static-Server/1.0
```

## 性能考虑

- 多级配置按需应用，没有配置的级别会被跳过
- 使用高效的map操作进行头部设置
- 避免了不必要的字符串处理和内存分配

## 总结

多级Headers配置功能成功实现了用户要求的"删除硬编码，由配置文件决定，这个配置是多级的，不是只有全局设置"的需求。该功能提供了灵活的头部管理能力，支持从全局到路由的精细化控制，同时保持了良好的性能和向后兼容性。

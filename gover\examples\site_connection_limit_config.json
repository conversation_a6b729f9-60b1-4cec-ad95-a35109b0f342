{"server": {"http_port": 80, "https_port": 443, "max_connections": 10000, "connection_timeout": "30s", "keep_alive_timeout": "60s"}, "rate_limit": {"enabled": true, "global_rps": 10000, "ip_rps": 100, "site_rps": 1000, "burst": 50}, "sites": [{"name": "high_priority_api", "domains": ["api.example.com"], "max_connections": 3000, "comment": "高优先级API服务 - 分配最多连接数", "upstreams": [{"name": "api_server_1", "address": "127.0.0.1", "port": 8001, "protocol": "http", "load_balance_group": "api_group"}, {"name": "api_server_2", "address": "127.0.0.1", "port": 8002, "protocol": "http", "load_balance_group": "api_group"}], "routes": [{"pattern": "^/api/v1/", "upstream": "api_group", "cache": false, "comment": "核心API接口"}, {"pattern": "^/api/", "upstream": "api_group", "cache": true, "comment": "其他API接口"}]}, {"name": "main_website", "domains": ["www.example.com", "example.com"], "max_connections": 2000, "comment": "主网站 - 中等连接数配额", "upstreams": [{"name": "web_server_1", "address": "127.0.0.1", "port": 8003, "protocol": "http", "load_balance_group": "web_group"}, {"name": "web_server_2", "address": "127.0.0.1", "port": 8004, "protocol": "http", "load_balance_group": "web_group"}], "routes": [{"pattern": "^/static/", "static_dir": "/var/www/static", "comment": "静态资源"}, {"pattern": "^/", "upstream": "web_group", "cache": true, "comment": "动态页面"}]}, {"name": "user_uploads", "domains": ["uploads.example.com"], "max_connections": 1500, "comment": "用户上传服务 - 中等连接数，处理文件上传", "upstreams": [{"name": "upload_server", "address": "127.0.0.1", "port": 8005, "protocol": "http", "load_balance_group": "upload_group"}], "routes": [{"pattern": "^/upload/", "upstream": "upload_group", "cache": false, "comment": "文件上传接口"}, {"pattern": "^/files/", "upstream": "upload_group", "cache": true, "comment": "文件下载接口"}]}, {"name": "internal_services", "domains": ["internal.example.com"], "max_connections": 800, "comment": "内部服务 - 较低连接数，内网使用", "upstreams": [{"name": "internal_server", "address": "**********", "port": 8006, "protocol": "http", "load_balance_group": "internal_group"}], "routes": [{"pattern": "^/internal/", "upstream": "internal_group", "cache": false, "comment": "内部API"}]}, {"name": "admin_panel", "domains": ["admin.example.com"], "max_connections": 200, "comment": "管理后台 - 最低连接数，管理员专用", "upstreams": [{"name": "admin_server", "address": "127.0.0.1", "port": 8007, "protocol": "http", "load_balance_group": "admin_group"}], "routes": [{"pattern": "^/admin/", "upstream": "admin_group", "cache": false, "comment": "管理界面"}]}, {"name": "experimental_features", "domains": ["beta.example.com"], "max_connections": 100, "comment": "实验功能 - 最低连接数，测试环境", "upstreams": [{"name": "beta_server", "address": "127.0.0.1", "port": 8008, "protocol": "http", "load_balance_group": "beta_group"}], "routes": [{"pattern": "^/", "upstream": "beta_group", "cache": false, "comment": "实验功能"}]}, {"name": "public_cdn", "domains": ["cdn.example.com"], "max_connections": 2500, "comment": "公共CDN - 高连接数，静态资源分发", "routes": [{"pattern": "^/images/", "static_dir": "/var/www/cdn/images", "comment": "图片资源"}, {"pattern": "^/js/", "static_dir": "/var/www/cdn/js", "comment": "JavaScript文件"}, {"pattern": "^/css/", "static_dir": "/var/www/cdn/css", "comment": "CSS文件"}]}], "monitoring": {"enabled": true, "connection_stats": {"enabled": true, "alert_thresholds": {"global_usage_warning": 80, "global_usage_critical": 95, "site_usage_warning": 85, "site_usage_critical": 98, "rejection_rate_warning": 1, "rejection_rate_critical": 5}}}, "comments": {"connection_allocation": {"total_allocated": 10100, "global_limit": 10000, "note": "站点总配额略超全局限制是正常的，因为不是所有站点会同时达到最大连接数"}, "priority_allocation": {"high_priority": {"sites": ["high_priority_api", "public_cdn"], "total_connections": 5500, "percentage": "55%"}, "medium_priority": {"sites": ["main_website", "user_uploads"], "total_connections": 3500, "percentage": "35%"}, "low_priority": {"sites": ["internal_services", "admin_panel", "experimental_features"], "total_connections": 1100, "percentage": "10%"}}, "ddos_protection": {"layer1": "全局连接数限制 (10000)", "layer2": "站点连接数限制 (按业务分配)", "layer3": "IP限流 (100 RPS per IP)", "layer4": "站点限流 (1000 RPS per site)", "effectiveness": "多层防护，有效防御各种DDoS攻击"}, "scaling_guidelines": {"traffic_growth": "根据流量增长调整各站点连接数配额", "new_services": "新服务初始分配较低连接数，根据实际需求调整", "emergency_response": "紧急情况下可临时提高重要服务的连接数限制", "monitoring": "持续监控连接使用率，优化资源分配"}}}
package processor

import (
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
)

// Processor 处理器接口 - 所有处理器都必须实现此接口
type Processor interface {
	// Process 处理请求和响应
	Process(req *RequestContext, resp *ResponseContext) *ProcessResult
	
	// Name 返回处理器名称
	Name() string
	
	// Priority 返回处理器优先级（数字越小优先级越高）
	Priority() int
	
	// ShouldProcess 判断是否应该处理此请求
	ShouldProcess(req *RequestContext) bool
}

// ProcessorChain 处理器链
type ProcessorChain struct {
	processors []Processor
	logger     *logrus.Logger
}

// NewProcessorChain 创建新的处理器链
func NewProcessorChain(logger *logrus.Logger) *ProcessorChain {
	return &ProcessorChain{
		processors: make([]Processor, 0),
		logger:     logger,
	}
}

// AddProcessor 添加处理器
func (pc *ProcessorChain) AddProcessor(processor Processor) {
	pc.processors = append(pc.processors, processor)
}

// Execute 执行处理器链
func (pc *ProcessorChain) Execute(req *RequestContext, resp *ResponseContext) error {
	startTime := time.Now()
	
	for _, processor := range pc.processors {
		// 检查是否应该处理此请求
		if !processor.ShouldProcess(req) {
			pc.logger.Debugf("处理器 %s 跳过请求 %s", processor.Name(), req.URL.Path)
			continue
		}
		
		// 记录处理步骤
		stepStart := time.Now()
		req.ProcessStage = processor.Name()
		resp.AddProcessStep(processor.Name())
		
		pc.logger.Debugf("执行处理器: %s, 请求: %s", processor.Name(), req.URL.Path)
		
		// 执行处理器
		result := processor.Process(req, resp)
		
		stepDuration := time.Since(stepStart)
		pc.logger.Debugf("处理器 %s 执行完成，耗时: %v", processor.Name(), stepDuration)
		
		// 处理结果
		if result == nil {
			pc.logger.Warnf("处理器 %s 返回了 nil 结果", processor.Name())
			continue
		}
		
		// 记录调试信息
		resp.SetDebugInfo(fmt.Sprintf("%s_duration", processor.Name()), stepDuration)
		if result.Metadata != nil {
			for k, v := range result.Metadata {
				resp.SetDebugInfo(fmt.Sprintf("%s_%s", processor.Name(), k), v)
			}
		}
		
		// 处理错误
		if !result.Success {
			if result.Error != nil {
				resp.Error = result.Error
				resp.ErrorStage = processor.Name()
				pc.logger.Errorf("处理器 %s 执行失败: %v", processor.Name(), result.Error)
				return result.Error
			}
		}
		
		// 如果处理器要求停止继续执行
		if !result.Continue {
			pc.logger.Debugf("处理器 %s 要求停止处理链执行", processor.Name())
			break
		}
	}
	
	// 计算总处理时间
	resp.ProcessTime = time.Since(startTime)
	if resp.Metrics != nil {
		resp.Metrics.TotalTime = resp.ProcessTime
	}
	
	pc.logger.Debugf("处理链执行完成，总耗时: %v", resp.ProcessTime)
	return nil
}

// GetProcessors 获取所有处理器
func (pc *ProcessorChain) GetProcessors() []Processor {
	return pc.processors
}

// RemoveProcessor 移除处理器
func (pc *ProcessorChain) RemoveProcessor(name string) bool {
	for i, processor := range pc.processors {
		if processor.Name() == name {
			pc.processors = append(pc.processors[:i], pc.processors[i+1:]...)
			return true
		}
	}
	return false
}

// HasProcessor 检查是否包含指定处理器
func (pc *ProcessorChain) HasProcessor(name string) bool {
	for _, processor := range pc.processors {
		if processor.Name() == name {
			return true
		}
	}
	return false
}

// BaseProcessor 基础处理器 - 提供通用功能
type BaseProcessor struct {
	name     string
	priority int
	logger   *logrus.Logger
}

// NewBaseProcessor 创建基础处理器
func NewBaseProcessor(name string, priority int, logger *logrus.Logger) *BaseProcessor {
	return &BaseProcessor{
		name:     name,
		priority: priority,
		logger:   logger,
	}
}

// Name 返回处理器名称
func (bp *BaseProcessor) Name() string {
	return bp.name
}

// Priority 返回处理器优先级
func (bp *BaseProcessor) Priority() int {
	return bp.priority
}

// ShouldProcess 默认实现 - 总是处理
func (bp *BaseProcessor) ShouldProcess(req *RequestContext) bool {
	return true
}

// LogDebug 记录调试日志
func (bp *BaseProcessor) LogDebug(format string, args ...interface{}) {
	if bp.logger != nil {
		bp.logger.Debugf("[%s] "+format, append([]interface{}{bp.name}, args...)...)
	}
}

// LogInfo 记录信息日志
func (bp *BaseProcessor) LogInfo(format string, args ...interface{}) {
	if bp.logger != nil {
		bp.logger.Infof("[%s] "+format, append([]interface{}{bp.name}, args...)...)
	}
}

// LogWarn 记录警告日志
func (bp *BaseProcessor) LogWarn(format string, args ...interface{}) {
	if bp.logger != nil {
		bp.logger.Warnf("[%s] "+format, append([]interface{}{bp.name}, args...)...)
	}
}

// LogError 记录错误日志
func (bp *BaseProcessor) LogError(format string, args ...interface{}) {
	if bp.logger != nil {
		bp.logger.Errorf("[%s] "+format, append([]interface{}{bp.name}, args...)...)
	}
}

// Success 创建成功结果
func Success() *ProcessResult {
	return &ProcessResult{
		Success:  true,
		Continue: true,
	}
}

// SuccessStop 创建成功但停止继续执行的结果
func SuccessStop() *ProcessResult {
	return &ProcessResult{
		Success:  true,
		Continue: false,
	}
}

// SuccessWithMetadata 创建带元数据的成功结果
func SuccessWithMetadata(metadata map[string]interface{}) *ProcessResult {
	return &ProcessResult{
		Success:  true,
		Continue: true,
		Metadata: metadata,
	}
}

// Error 创建错误结果
func Error(err error) *ProcessResult {
	return &ProcessResult{
		Success: false,
		Error:   err,
	}
}

// ErrorWithMessage 创建带消息的错误结果
func ErrorWithMessage(err error, message string) *ProcessResult {
	return &ProcessResult{
		Success: false,
		Error:   err,
		Message: message,
	}
}

// ProcessorBuilder 处理器构建器
type ProcessorBuilder struct {
	chain  *ProcessorChain
	logger *logrus.Logger
}

// NewProcessorBuilder 创建处理器构建器
func NewProcessorBuilder(logger *logrus.Logger) *ProcessorBuilder {
	return &ProcessorBuilder{
		chain:  NewProcessorChain(logger),
		logger: logger,
	}
}

// Add 添加处理器
func (pb *ProcessorBuilder) Add(processor Processor) *ProcessorBuilder {
	pb.chain.AddProcessor(processor)
	return pb
}

// Build 构建处理器链
func (pb *ProcessorBuilder) Build() *ProcessorChain {
	return pb.chain
}

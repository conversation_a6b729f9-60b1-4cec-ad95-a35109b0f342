# 🔧 配置热重载问题诊断指南

## 📋 问题总结

你遇到的3个热重载问题：
1. **站点ACL的deny没有生效**
2. **站点ACL的deny_file无效**  
3. **缓存规则没变动**

## 🛠️ 已添加的调试功能

我已经在代码中添加了详细的调试日志，现在可以清楚地看到热重载的每个步骤：

### 1. **热重载触发日志**
```
[INFO] ========== 开始重载配置 ==========
[INFO] 配置文件站点数量: 1
[INFO] 站点 0: test_site, 域名: [localhost, 127.0.0.1]
[INFO]   ACL允许: []
[INFO]   ACL拒绝: [*************]
[INFO]   ACL允许文件: 
[INFO]   ACL拒绝文件: acl/test_deny.txt
[INFO]   路由 0: ^/api/, 缓存: false
[INFO]   路由 1: ^/static/, 缓存: true
```

### 2. **站点重建日志**
```
[INFO] 重新创建站点: test_site
[DEBUG] 站点ACL配置 - 允许: [], 拒绝: [*************], 允许文件: , 拒绝文件: acl/test_deny.txt
[INFO] 创建站点ACL - 允许列表: 0 个IP, 拒绝列表: 1 个IP, 允许文件: , 拒绝文件: acl/test_deny.txt
[DEBUG] 域名映射: localhost -> test_site
[DEBUG] 域名映射: 127.0.0.1 -> test_site
```

### 3. **站点验证日志**
```
[INFO] 站点配置重载完成，旧站点: 2 个域名，新站点: 2 个域名
[INFO] 验证域名映射: localhost -> 站点: test_site
[DEBUG]   站点ACL已配置
[DEBUG]   路由数量: 3
```

### 4. **ACL检查日志**
```
[DEBUG] 检查站点ACL: IP=*************, 站点=test_site
[WARN] 客户端IP被站点ACL拒绝: IP=*************, 站点=test_site
```

### 5. **缓存检查日志**
```
[DEBUG] 缓存检查: 路由=^/api/, 缓存启用=true, 方法=GET
[DEBUG] 缓存Key: http:GET:localhost:/api/test
```

## 🧪 **测试步骤**

### 步骤1: 启动服务并观察初始日志
```bash
# 设置日志级别为debug
./reverse-proxy -config test_hotreload_config.json

# 观察启动日志，确认站点配置
```

### 步骤2: 测试站点ACL deny
```bash
# 修改配置文件，添加deny IP
jq '.sites[0].acl.deny = ["*************"]' test_hotreload_config.json > temp.json
mv temp.json test_hotreload_config.json

# 观察热重载日志
tail -f logs/proxy_*.log | grep -E "(开始重载|站点ACL|重载完成)"

# 测试被拒绝的IP
curl -H "X-Real-IP: *************" http://localhost/
# 应该返回403

# 测试允许的IP
curl -H "X-Real-IP: 127.0.0.1" http://localhost/
# 应该正常访问
```

### 步骤3: 测试站点ACL deny_file
```bash
# 创建ACL文件
mkdir -p acl
echo "*************" > acl/test_deny.txt

# 修改配置文件
jq '.sites[0].acl.deny_file = "acl/test_deny.txt"' test_hotreload_config.json > temp.json
mv temp.json test_hotreload_config.json

# 观察热重载和文件监控日志
tail -f logs/proxy_*.log | grep -E "(文件监控|文件重载|ACL)"

# 测试文件中的IP
curl -H "X-Real-IP: *************" http://localhost/
# 应该返回403
```

### 步骤4: 测试缓存规则变更
```bash
# 修改路由缓存设置
jq '.sites[0].routes[0].cache = true' test_hotreload_config.json > temp.json
mv temp.json test_hotreload_config.json

# 观察热重载日志
tail -f logs/proxy_*.log | grep -E "(缓存检查|路由.*缓存)"

# 测试缓存功能
curl http://localhost/api/test
# 观察缓存相关日志
```

## 🔍 **问题诊断清单**

### 1. **热重载是否被触发？**
查找日志中的：
```
[INFO] ========== 开始重载配置 ==========
```
- ✅ 如果有：热重载被触发
- ❌ 如果没有：配置文件监控有问题

### 2. **站点是否被重建？**
查找日志中的：
```
[INFO] 重新创建站点: [站点名]
[INFO] 站点配置重载完成
```
- ✅ 如果有：站点重建成功
- ❌ 如果没有：站点重建失败

### 3. **ACL配置是否正确？**
查找日志中的：
```
[INFO] 创建站点ACL - 允许列表: X 个IP, 拒绝列表: Y 个IP
```
- ✅ 如果IP数量正确：ACL配置正确
- ❌ 如果IP数量不对：配置解析有问题

### 4. **ACL检查是否执行？**
查找日志中的：
```
[DEBUG] 检查站点ACL: IP=X.X.X.X, 站点=XXX
```
- ✅ 如果有：ACL检查正常执行
- ❌ 如果没有：请求没有到达ACL检查

### 5. **缓存配置是否生效？**
查找日志中的：
```
[DEBUG] 缓存检查: 路由=XXX, 缓存启用=true/false
```
- ✅ 如果缓存启用状态正确：缓存配置生效
- ❌ 如果状态不对：路由配置有问题

## 🚨 **常见问题和解决方案**

### 问题1: 热重载没有触发
**可能原因**：
- 配置文件监控器没有启动
- 文件权限问题
- 配置文件路径错误

**解决方案**：
```bash
# 检查配置文件监控器日志
grep "配置监控器" logs/proxy_*.log

# 检查文件权限
ls -la config.json

# 手动触发重载（修改文件时间戳）
touch config.json
```

### 问题2: 站点ACL配置不生效
**可能原因**：
- 配置文件格式错误
- IP格式不正确
- 站点映射问题

**解决方案**：
```bash
# 验证JSON格式
jq . config.json

# 检查IP格式
echo "*************" | grep -E '^[0-9.]+$'

# 检查站点映射
grep "域名映射" logs/proxy_*.log
```

### 问题3: ACL文件监控不工作
**可能原因**：
- 文件路径不存在
- 文件权限不足
- 文件监控器创建失败

**解决方案**：
```bash
# 检查文件是否存在
ls -la acl/

# 检查文件监控日志
grep "文件监控" logs/proxy_*.log

# 检查权限
chmod 644 acl/*.txt
```

### 问题4: 缓存规则不生效
**可能原因**：
- 路由匹配问题
- 缓存配置解析错误
- 请求方法不是GET

**解决方案**：
```bash
# 检查路由匹配
grep "路由匹配" logs/proxy_*.log

# 检查缓存配置
grep "缓存检查" logs/proxy_*.log

# 确认请求方法
curl -X GET http://localhost/api/test
```

## 📝 **使用测试脚本**

我已经创建了自动化测试脚本：

```bash
# 给脚本执行权限
chmod +x test_hotreload.sh

# 运行测试
./test_hotreload.sh
```

测试脚本会：
1. 检查服务是否运行
2. 逐步修改配置
3. 验证每个功能
4. 提供详细的测试结果

## 🎯 **预期结果**

如果热重载功能正常，你应该看到：

1. **配置变更时**：立即看到重载日志
2. **ACL变更时**：新的IP立即被拒绝/允许
3. **缓存变更时**：缓存行为立即改变
4. **文件变更时**：文件监控立即触发重载

现在请按照这个指南进行测试，并告诉我具体看到了什么日志输出！🔍

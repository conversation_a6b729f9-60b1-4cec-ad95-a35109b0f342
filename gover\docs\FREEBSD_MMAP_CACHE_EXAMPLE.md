# 📋 FreeBSD内存映射缓存配置示例

## 🎯 FreeBSD平台支持

反向代理服务器现在完全支持FreeBSD平台的内存映射缓存配置。FreeBSD作为一个高性能的服务器操作系统，具有优秀的内存管理和文件系统性能。

## 🔧 FreeBSD配置示例

### **基础配置**

```json
{
  "performance": {
    "mmap_cache": {
      "enabled": true,
      "max_size": "1GB",
      "base_path": "cache/mmap",
      "auto_platform_path": false,
      "windows_path": "",
      "linux_path": "",
      "macos_path": "",
      "freebsd_path": "/tmp/proxy_mmap",
      "fallback_path": ""
    }
  }
}
```

### **高性能配置**

```json
{
  "performance": {
    "mmap_cache": {
      "enabled": true,
      "max_size": "4GB",
      "base_path": "",
      "auto_platform_path": false,
      "windows_path": "",
      "linux_path": "",
      "macos_path": "",
      "freebsd_path": "/var/cache/proxy_mmap",
      "fallback_path": "./cache/mmap"
    }
  }
}
```

### **自动平台路径配置**

```json
{
  "performance": {
    "mmap_cache": {
      "enabled": true,
      "max_size": "2GB",
      "base_path": "proxy_mmap",
      "auto_platform_path": true,
      "windows_path": "",
      "linux_path": "",
      "macos_path": "",
      "freebsd_path": "",
      "fallback_path": ""
    }
  }
}
```

## 📊 FreeBSD路径选择逻辑

### **优先级顺序**

1. **自动平台路径** (`auto_platform_path: true`) → `/tmp/proxy_mmap`
2. **FreeBSD专用路径** (`freebsd_path`) → 用户指定路径
3. **基础路径** (`base_path`) → 相对或绝对路径
4. **降级路径** (`fallback_path`) → 备用路径
5. **系统默认路径** → `/tmp/proxy_mmap`

### **路径选择示例**

| 配置情况 | 最终路径 | 说明 |
|----------|----------|------|
| `auto_platform_path: true` | `/tmp/proxy_mmap` | 自动选择FreeBSD最佳路径 |
| `freebsd_path: "/var/cache/proxy_mmap"` | `/var/cache/proxy_mmap` | 使用FreeBSD专用路径 |
| `base_path: "cache/mmap"` | `./cache/mmap` | 使用基础路径 |
| 无配置 | `/tmp/proxy_mmap` | 使用系统默认路径 |

## 🌟 FreeBSD特性支持

### **tmpfs支持**

FreeBSD支持tmpfs文件系统，可以将内存映射缓存放在内存中以获得最佳性能：

```bash
# 创建tmpfs挂载点
sudo mkdir -p /mnt/tmpfs
sudo mount -t tmpfs tmpfs /mnt/tmpfs

# 配置中使用tmpfs路径
"freebsd_path": "/mnt/tmpfs/proxy_mmap"
```

### **ZFS支持**

FreeBSD的ZFS文件系统提供了优秀的缓存性能：

```json
{
  "freebsd_path": "/zpool/cache/proxy_mmap"
}
```

### **环境变量支持**

支持FreeBSD环境变量：

```json
{
  "freebsd_path": "$HOME/cache/proxy_mmap"
}
```

## 🚀 性能优化建议

### **1. 使用tmpfs（推荐）**

```json
{
  "freebsd_path": "/mnt/tmpfs/proxy_mmap",
  "max_size": "2GB"
}
```

**优点**：
- 完全在内存中运行
- 最快的读写速度
- 重启后自动清理

### **2. 使用ZFS缓存**

```json
{
  "freebsd_path": "/zpool/cache/proxy_mmap",
  "max_size": "4GB"
}
```

**优点**：
- ZFS的ARC缓存加速
- 数据压缩和去重
- 持久化存储

### **3. 使用UFS + SSD**

```json
{
  "freebsd_path": "/var/cache/proxy_mmap",
  "max_size": "8GB"
}
```

**优点**：
- 大容量缓存
- SSD加速访问
- 系统重启后保持

## 📋 部署检查清单

### **权限检查**

```bash
# 确保缓存目录可写
sudo mkdir -p /var/cache
sudo chown www:www /var/cache
sudo chmod 755 /var/cache
```

### **磁盘空间检查**

```bash
# 检查可用空间
df -h /tmp
df -h /var/cache
```

### **性能测试**

```bash
# 测试磁盘I/O性能
dd if=/dev/zero of=/tmp/test bs=1M count=1000
dd if=/dev/zero of=/var/cache/test bs=1M count=1000
```

## 🔍 故障排除

### **常见问题**

1. **权限不足**
   ```
   解决：sudo chown -R proxy_user:proxy_group /cache/path
   ```

2. **磁盘空间不足**
   ```
   解决：增加max_size限制或清理磁盘空间
   ```

3. **tmpfs未挂载**
   ```
   解决：sudo mount -t tmpfs tmpfs /mnt/tmpfs
   ```

### **日志检查**

```bash
# 查看缓存相关日志
tail -f /var/log/proxy.log | grep "内存映射缓存"
```

## 🎯 推荐配置

### **小型部署（< 1GB内存）**

```json
{
  "freebsd_path": "/tmp/proxy_mmap",
  "max_size": "256MB"
}
```

### **中型部署（2-8GB内存）**

```json
{
  "freebsd_path": "/mnt/tmpfs/proxy_mmap",
  "max_size": "1GB"
}
```

### **大型部署（> 8GB内存）**

```json
{
  "freebsd_path": "/mnt/tmpfs/proxy_mmap",
  "max_size": "4GB"
}
```

FreeBSD平台现在完全支持内存映射缓存，可以充分利用FreeBSD的高性能特性！

# 请求限流和熔断

## 📋 概述

请求限流和熔断机制是保护系统免受过载和级联故障的重要防护手段，通过控制请求频率和自动故障隔离，确保系统在高负载和异常情况下的稳定性。

## 🎯 优化目标

- **防止系统过载**：控制请求频率，保护后端服务
- **提升系统稳定性**：避免级联故障
- **保障服务质量**：确保核心功能可用
- **快速故障恢复**：自动检测和恢复机制

## 🔧 技术实现

### 1. 令牌桶限流算法

#### 实现原理
```go
type RateLimitManager struct {
    globalLimiter *rate.Limiter           // 全局限流器
    ipLimiters    map[string]*rate.Limiter // IP级限流器
    siteLimiters  map[string]*rate.Limiter // 站点级限流器
    config        RateLimitConfig
    mu            sync.RWMutex
}
```

#### 令牌桶特性
- **平滑限流**：允许突发流量，平滑处理请求
- **精确控制**：精确控制每秒请求数(RPS)
- **多级限流**：支持全局、IP、站点三级限流

#### 限流层级
1. **全局限流**：整个代理服务器的总请求限制
2. **IP限流**：单个客户端IP的请求限制
3. **站点限流**：单个站点的请求限制

### 2. 熔断器模式

#### 状态机设计
```go
type CircuitBreakerState int

const (
    StateClosed     CircuitBreakerState = iota // 关闭状态（正常）
    StateOpen                                  // 开启状态（熔断）
    StateHalfOpen                             // 半开状态（探测）
)

type CircuitBreaker struct {
    state           CircuitBreakerState
    failureCount    int64
    successCount    int64
    lastFailureTime time.Time
    config          CircuitBreakerConfig
    mutex           sync.RWMutex
}
```

#### 状态转换
- **Closed → Open**：失败次数达到阈值
- **Open → HalfOpen**：超过重置时间
- **HalfOpen → Closed**：探测请求成功
- **HalfOpen → Open**：探测请求失败

### 3. 多维度限流

#### IP级限流
```go
func (rlm *RateLimitManager) checkIPLimit(clientIP string) error {
    rlm.mu.RLock()
    limiter, exists := rlm.ipLimiters[clientIP]
    rlm.mu.RUnlock()
    
    if !exists {
        rlm.mu.Lock()
        limiter = rate.NewLimiter(rate.Limit(rlm.config.IPRPS), rlm.config.Burst)
        rlm.ipLimiters[clientIP] = limiter
        rlm.mu.Unlock()
    }
    
    if !limiter.Allow() {
        return fmt.Errorf("IP限流: %s", clientIP)
    }
    return nil
}
```

#### 站点级限流
```go
func (rlm *RateLimitManager) checkSiteLimit(siteName string) error {
    rlm.mu.RLock()
    limiter, exists := rlm.siteLimiters[siteName]
    rlm.mu.RUnlock()
    
    if !exists {
        rlm.mu.Lock()
        limiter = rate.NewLimiter(rate.Limit(rlm.config.SiteRPS), rlm.config.Burst)
        rlm.siteLimiters[siteName] = limiter
        rlm.mu.Unlock()
    }
    
    if !limiter.Allow() {
        return fmt.Errorf("站点限流: %s", siteName)
    }
    return nil
}
```

## ⚙️ 配置说明

### 限流配置
```json
{
  "rate_limit": {
    "enabled": true,
    "global_rps": 10000,    // 全局每秒请求数
    "ip_rps": 100,          // 单IP每秒请求数
    "site_rps": 1000,       // 单站点每秒请求数
    "burst": 50             // 突发请求容量
  }
}
```

### 熔断器配置
```json
{
  "circuit_breaker": {
    "enabled": true,
    "max_failures": 5,           // 最大失败次数
    "reset_timeout": "30s",      // 重置超时时间
    "half_open_requests": 3      // 半开状态探测请求数
  }
}
```

### 站点级配置
```json
{
  "sites": [
    {
      "name": "api_service",
      "rate_limiter": {
        "enabled": true,
        "rps": 500,
        "burst": 20
      },
      "circuit_breaker": {
        "enabled": true,
        "max_failures": 3,
        "reset_timeout": "60s"
      }
    }
  ]
}
```

### 参数调优指南

#### 限流参数
| 参数 | 推荐值 | 说明 |
|------|--------|------|
| `global_rps` | 服务器容量的80% | 预留20%缓冲 |
| `ip_rps` | 10-1000 | 根据业务特点调整 |
| `site_rps` | 站点容量的90% | 站点级精确控制 |
| `burst` | RPS的10-50% | 允许短时突发 |

#### 熔断器参数
| 参数 | 推荐值 | 说明 |
|------|--------|------|
| `max_failures` | 3-10 | 快速检测故障 |
| `reset_timeout` | 30s-300s | 给系统恢复时间 |
| `half_open_requests` | 1-5 | 谨慎探测恢复 |

## 📊 监控指标

### 限流统计
```json
{
  "rate_limit": {
    "global": {
      "total_requests": 100000,
      "rejected_requests": 1000,
      "rejection_rate": 0.01
    },
    "ip_limits": {
      "*************": {
        "requests": 500,
        "rejected": 50,
        "last_rejection": "2024-01-01T12:00:00Z"
      }
    },
    "site_limits": {
      "api_service": {
        "requests": 10000,
        "rejected": 100,
        "current_rps": 450
      }
    }
  }
}
```

### 熔断器统计
```json
{
  "circuit_breakers": {
    "api_service": {
      "state": "closed",
      "failure_count": 2,
      "success_count": 1000,
      "last_failure_time": "2024-01-01T11:30:00Z",
      "state_changes": 5
    }
  }
}
```

## 🔍 故障排查

### 常见问题

#### 1. 误限流
**现象**：正常请求被限流
**排查**：
```bash
# 检查限流统计
curl -u zdw:z7758521 http://localhost:8080/stats | jq '.rate_limit'

# 检查IP限流
curl -u zdw:z7758521 http://localhost:8080/stats | jq '.rate_limit.ip_limits'
```

**解决**：
- 调整RPS限制
- 增加burst容量
- 检查客户端行为

#### 2. 熔断器误触发
**现象**：服务正常但熔断器开启
**排查**：
```bash
# 检查熔断器状态
curl -u zdw:z7758521 http://localhost:8080/stats | jq '.circuit_breakers'

# 检查上游服务健康状态
curl -u zdw:z7758521 http://localhost:8080/stats | jq '.upstreams'
```

**解决**：
- 调整失败阈值
- 检查健康检查配置
- 优化上游服务性能

#### 3. 性能影响
**现象**：限流导致响应时间增加
**解决**：
- 优化限流器实现
- 使用更高效的数据结构
- 减少锁竞争

## 🚀 最佳实践

### 1. 分层限流策略
```json
{
  "rate_limit": {
    "global_rps": 10000,     // 总体保护
    "ip_rps": 100,           // 防止单点攻击
    "site_rps": 1000         // 业务隔离
  }
}
```

### 2. 动态调整
```go
// 根据系统负载动态调整限流参数
func (rlm *RateLimitManager) AdjustLimits(cpuUsage, memUsage float64) {
    if cpuUsage > 0.8 {
        // 降低限流阈值
        rlm.globalLimiter.SetLimit(rate.Limit(rlm.config.GlobalRPS * 0.8))
    }
}
```

### 3. 白名单机制
```json
{
  "rate_limit": {
    "whitelist": [
      "***********/24",      // 内网IP
      "10.0.0.0/8"           // 管理网段
    ]
  }
}
```

### 4. 优雅降级
```go
func (p *Proxy) handleRateLimit(w http.ResponseWriter, r *http.Request) {
    // 返回友好的限流响应
    w.Header().Set("Retry-After", "60")
    w.Header().Set("X-RateLimit-Limit", "100")
    w.Header().Set("X-RateLimit-Remaining", "0")
    http.Error(w, "请求频率超限，请稍后重试", http.StatusTooManyRequests)
}
```

## 📈 性能测试

### 限流测试
```bash
#!/bin/bash
# 限流压力测试

# 测试全局限流
ab -n 20000 -c 200 http://your-proxy/

# 测试IP限流
for i in {1..200}; do
  curl -H "X-Forwarded-For: 192.168.1.$i" http://your-proxy/ &
done
wait

# 测试站点限流
ab -n 10000 -c 100 http://your-proxy/api/
```

### 熔断器测试
```bash
#!/bin/bash
# 熔断器测试

# 模拟上游服务故障
# 1. 停止上游服务
# 2. 发送请求触发熔断
# 3. 恢复上游服务
# 4. 观察熔断器恢复过程

for i in {1..20}; do
  curl -w "%{http_code}\n" http://your-proxy/
  sleep 1
done
```

### 性能指标
- **限流准确性**：实际RPS与配置RPS的偏差
- **响应时间影响**：限流对正常请求的延迟影响
- **内存使用**：限流器的内存占用
- **CPU开销**：限流检查的CPU消耗

## 🔧 高级功能

### 1. 分布式限流
```go
// 使用Redis实现分布式限流
type DistributedRateLimiter struct {
    redis  *redis.Client
    script string  // Lua脚本
}
```

### 2. 自适应限流
```go
// 根据系统指标自动调整限流参数
type AdaptiveRateLimiter struct {
    baseLimiter *rate.Limiter
    cpuMonitor  *CPUMonitor
    memMonitor  *MemoryMonitor
}
```

### 3. 限流统计和分析
```go
// 限流数据收集和分析
type RateLimitAnalyzer struct {
    collector *MetricsCollector
    analyzer  *TrendAnalyzer
}
```

请求限流和熔断是系统稳定性的重要保障，合理配置和监控可以有效防止系统过载和级联故障。

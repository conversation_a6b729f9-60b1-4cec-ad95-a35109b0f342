package memorycache

import (
	"container/heap"
	"fmt"
	"mime"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"reverse-proxy/internal/config"
	"reverse-proxy/internal/logger"
)

// MemoryCacheManager 内存缓存管理器
type MemoryCacheManager struct {
	globalLimit  int64
	currentUsage int64
	siteManagers map[string]*SiteMemoryCache
	hotFiles     *HotFileTracker
	config       config.MemoryCacheConfig
	mu           sync.RWMutex
	stats        *MemoryCacheStats
}

// SiteMemoryCache 站点内存缓存
type SiteMemoryCache struct {
	siteID       string
	memoryLimit  int64
	currentUsage int64
	files        map[string]*CachedFile
	priority     config.Priority
	mu           sync.RWMutex
}

// CachedFile 缓存文件
type CachedFile struct {
	Path        string
	Content     []byte
	ContentType string
	Size        int64
	Score       *HotFileScore
	LoadTime    time.Time
	LastAccess  time.Time
	AccessCount int64
}

// HotFileScore 热点文件评分
type HotFileScore struct {
	AccessCount    int64     `json:"access_count"`
	LastAccessTime time.Time `json:"last_access_time"`
	FileSize       int64     `json:"file_size"`
	AccessFreq     float64   `json:"access_freq"`
	Score          float64   `json:"score"`
	LoadTime       time.Time `json:"load_time"`
}

// HotFileTracker 热点文件追踪器
type HotFileTracker struct {
	accessStats map[string]*HotFileScore
	scoreHeap   *ScoreHeap
	mu          sync.RWMutex
}

// ScoreHeap 评分堆（最小堆）
type ScoreHeap []*ScoreItem

type ScoreItem struct {
	Path  string
	Score float64
	Index int
}

// MemoryCacheStats 内存缓存统计
type MemoryCacheStats struct {
	GlobalUsage   int64                 `json:"global_usage"`
	GlobalLimit   int64                 `json:"global_limit"`
	UsagePercent  float64               `json:"usage_percent"`
	TotalFiles    int                   `json:"total_files"`
	HitCount      int64                 `json:"hit_count"`
	MissCount     int64                 `json:"miss_count"`
	HitRate       float64               `json:"hit_rate"`
	EvictionCount int64                 `json:"eviction_count"`
	SiteStats     map[string]*SiteStats `json:"site_stats"`
	TopHotFiles   []*HotFileInfo        `json:"top_hot_files"`
}

// SiteStats 站点统计
type SiteStats struct {
	MemoryUsage int64   `json:"memory_usage"`
	MemoryLimit int64   `json:"memory_limit"`
	FileCount   int     `json:"file_count"`
	HitCount    int64   `json:"hit_count"`
	MissCount   int64   `json:"miss_count"`
	HitRate     float64 `json:"hit_rate"`
	Priority    string  `json:"priority"`
}

// HotFileInfo 热点文件信息
type HotFileInfo struct {
	Path        string  `json:"path"`
	Score       float64 `json:"score"`
	AccessCount int64   `json:"access_count"`
	Size        int64   `json:"size"`
	InMemory    bool    `json:"in_memory"`
}

// NewMemoryCacheManager 创建内存缓存管理器
func NewMemoryCacheManager(cfg config.MemoryCacheConfig) (*MemoryCacheManager, error) {
	if !cfg.Enabled {
		return nil, nil
	}

	globalLimit, err := parseSize(cfg.GlobalMemoryLimit)
	if err != nil {
		return nil, fmt.Errorf("解析全局内存限制失败: %w", err)
	}

	manager := &MemoryCacheManager{
		globalLimit:  globalLimit,
		siteManagers: make(map[string]*SiteMemoryCache),
		hotFiles:     NewHotFileTracker(),
		config:       cfg,
		stats: &MemoryCacheStats{
			GlobalLimit: globalLimit,
			SiteStats:   make(map[string]*SiteStats),
		},
	}

	// 启动清理协程
	go manager.cleanupLoop()

	return manager, nil
}

// NewHotFileTracker 创建热点文件追踪器
func NewHotFileTracker() *HotFileTracker {
	return &HotFileTracker{
		accessStats: make(map[string]*HotFileScore),
		scoreHeap:   &ScoreHeap{},
	}
}

// CalculateScore 计算热点评分
func (h *HotFileScore) CalculateScore() float64 {
	now := time.Now()

	// 时间衰减因子（24小时内访问权重更高）
	timeFactor := 1.0 / (1.0 + now.Sub(h.LastAccessTime).Hours()/24)

	// 大小惩罚因子（文件越大权重越低）
	sizeFactor := 1.0 / (1.0 + float64(h.FileSize)/1024/1024)

	// 频率奖励因子
	if h.LoadTime.IsZero() {
		h.LoadTime = now
	}
	duration := now.Sub(h.LoadTime).Hours()
	if duration < 0.1 {
		duration = 0.1 // 避免除零
	}
	freqFactor := float64(h.AccessCount) / duration

	h.Score = freqFactor * timeFactor * sizeFactor * 100
	return h.Score
}

// RecordAccess 记录文件访问
func (h *HotFileTracker) RecordAccess(filePath string, size int64) {
	h.mu.Lock()
	defer h.mu.Unlock()

	now := time.Now()
	if stats, exists := h.accessStats[filePath]; exists {
		stats.AccessCount++
		stats.LastAccessTime = now
		stats.CalculateScore()
	} else {
		stats = &HotFileScore{
			AccessCount:    1,
			LastAccessTime: now,
			FileSize:       size,
			LoadTime:       now,
		}
		stats.CalculateScore()
		h.accessStats[filePath] = stats
	}

	// 更新堆
	h.updateHeap(filePath, h.accessStats[filePath].Score)
}

// updateHeap 更新评分堆
func (h *HotFileTracker) updateHeap(path string, score float64) {
	// 查找是否已存在
	for i, item := range *h.scoreHeap {
		if item.Path == path {
			item.Score = score
			heap.Fix(h.scoreHeap, i)
			return
		}
	}

	// 新增项目
	item := &ScoreItem{
		Path:  path,
		Score: score,
	}
	heap.Push(h.scoreHeap, item)
}

// GetStats 获取文件统计
func (h *HotFileTracker) GetStats(filePath string) *HotFileScore {
	h.mu.RLock()
	defer h.mu.RUnlock()

	if stats, exists := h.accessStats[filePath]; exists {
		return stats
	}

	return &HotFileScore{}
}

// GetTopFiles 获取热点文件列表
func (h *HotFileTracker) GetTopFiles(count int) []*HotFileInfo {
	h.mu.RLock()
	defer h.mu.RUnlock()

	var files []*HotFileInfo
	for path, stats := range h.accessStats {
		files = append(files, &HotFileInfo{
			Path:        path,
			Score:       stats.Score,
			AccessCount: stats.AccessCount,
			Size:        stats.FileSize,
		})
	}

	// 按评分排序
	for i := 0; i < len(files)-1; i++ {
		for j := i + 1; j < len(files); j++ {
			if files[i].Score < files[j].Score {
				files[i], files[j] = files[j], files[i]
			}
		}
	}

	if count > len(files) {
		count = len(files)
	}
	return files[:count]
}

// parseSize 解析大小字符串
func parseSize(sizeStr string) (int64, error) {
	if sizeStr == "" {
		return 0, fmt.Errorf("大小字符串为空")
	}

	sizeStr = strings.ToUpper(strings.TrimSpace(sizeStr))

	var multiplier int64 = 1
	var numStr string

	if strings.HasSuffix(sizeStr, "KB") {
		multiplier = 1024
		numStr = sizeStr[:len(sizeStr)-2]
	} else if strings.HasSuffix(sizeStr, "MB") {
		multiplier = 1024 * 1024
		numStr = sizeStr[:len(sizeStr)-2]
	} else if strings.HasSuffix(sizeStr, "GB") {
		multiplier = 1024 * 1024 * 1024
		numStr = sizeStr[:len(sizeStr)-2]
	} else if strings.HasSuffix(sizeStr, "B") {
		numStr = sizeStr[:len(sizeStr)-1]
	} else {
		numStr = sizeStr
	}

	num, err := strconv.ParseInt(strings.TrimSpace(numStr), 10, 64)
	if err != nil {
		return 0, fmt.Errorf("解析数字失败: %w", err)
	}

	return num * multiplier, nil
}

// 实现heap.Interface
func (h ScoreHeap) Len() int           { return len(h) }
func (h ScoreHeap) Less(i, j int) bool { return h[i].Score < h[j].Score } // 最小堆
func (h ScoreHeap) Swap(i, j int) {
	h[i], h[j] = h[j], h[i]
	h[i].Index = i
	h[j].Index = j
}

func (h *ScoreHeap) Push(x interface{}) {
	n := len(*h)
	item := x.(*ScoreItem)
	item.Index = n
	*h = append(*h, item)
}

func (h *ScoreHeap) Pop() interface{} {
	old := *h
	n := len(old)
	item := old[n-1]
	old[n-1] = nil
	item.Index = -1
	*h = old[0 : n-1]
	return item
}

// GetOrCreateSiteCache 获取或创建站点缓存
func (m *MemoryCacheManager) GetOrCreateSiteCache(siteID string) *SiteMemoryCache {
	m.mu.Lock()
	defer m.mu.Unlock()

	if cache, exists := m.siteManagers[siteID]; exists {
		return cache
	}

	// 获取站点配置
	var siteConfig *config.SiteCacheConfig
	if m.config.Sites != nil {
		siteConfig = m.config.Sites[siteID]
	}

	// 设置默认值
	memoryLimit, _ := parseSize(m.config.DefaultSiteLimit)
	priority := config.PriorityNormal

	if siteConfig != nil {
		if siteConfig.MemoryLimit != "" {
			if limit, err := parseSize(siteConfig.MemoryLimit); err == nil {
				memoryLimit = limit
			}
		}
		priority = config.ParsePriority(siteConfig.Priority)
	}

	cache := &SiteMemoryCache{
		siteID:      siteID,
		memoryLimit: memoryLimit,
		files:       make(map[string]*CachedFile),
		priority:    priority,
	}

	m.siteManagers[siteID] = cache
	m.stats.SiteStats[siteID] = &SiteStats{
		MemoryLimit: memoryLimit,
		Priority:    priority.String(),
	}

	return cache
}

// ShouldLoadToMemory 判断是否应该加载到内存
func (m *MemoryCacheManager) ShouldLoadToMemory(filePath string, size int64, contentType string) bool {
	// 1. 检查文件类型
	if !m.isAllowedType(contentType) {
		logger.Debugf("文件类型不允许: %s, 类型: %s", filePath, contentType)
		return false
	}

	// 2. 检查文件大小
	maxSize, _ := parseSize(m.config.MaxFileSize)
	if size > maxSize {
		logger.Debugf("文件过大: %s, 大小: %d, 限制: %d", filePath, size, maxSize)
		return false
	}

	// 3. 检查访问次数和评分
	stats := m.hotFiles.GetStats(filePath)
	if stats.AccessCount < m.config.MinAccessCount {
		logger.Debugf("访问次数不足: %s, 访问: %d, 最小: %d", filePath, stats.AccessCount, m.config.MinAccessCount)
		return false
	}

	if stats.Score < m.config.ScoreThreshold {
		logger.Debugf("评分不足: %s, 评分: %.2f, 阈值: %.2f", filePath, stats.Score, m.config.ScoreThreshold)
		return false
	}

	logger.Debugf("文件符合缓存条件: %s, 访问: %d, 评分: %.2f", filePath, stats.AccessCount, stats.Score)
	return true
}

// isAllowedType 检查文件类型是否允许
func (m *MemoryCacheManager) isAllowedType(contentType string) bool {
	// 检查黑名单
	for _, blocked := range m.config.BlockedTypes {
		if m.matchContentType(contentType, blocked) {
			return false
		}
	}

	// 检查白名单
	if len(m.config.AllowedTypes) == 0 {
		return true // 没有白名单限制
	}

	for _, allowed := range m.config.AllowedTypes {
		if m.matchContentType(contentType, allowed) {
			return true
		}
	}

	return false
}

// matchContentType 匹配内容类型
func (m *MemoryCacheManager) matchContentType(contentType, pattern string) bool {
	if pattern == "*" {
		return true
	}

	// 提取主要的内容类型，忽略字符集等参数
	mainType := contentType
	if idx := strings.Index(contentType, ";"); idx != -1 {
		mainType = strings.TrimSpace(contentType[:idx])
	}

	if strings.HasSuffix(pattern, "/*") {
		prefix := pattern[:len(pattern)-2]
		return strings.HasPrefix(mainType, prefix)
	}

	return mainType == pattern
}

// LoadToMemory 加载文件到内存
func (m *MemoryCacheManager) LoadToMemory(siteID, filePath string, content []byte, contentType string) error {
	if !m.config.Enabled {
		return nil
	}

	size := int64(len(content))

	// 检查是否应该加载
	if !m.ShouldLoadToMemory(filePath, size, contentType) {
		return nil
	}

	// 获取站点缓存
	siteCache := m.GetOrCreateSiteCache(siteID)

	// 检查内存限制
	if err := m.ensureMemoryAvailable(siteCache, size); err != nil {
		return err
	}

	// 加载到内存
	siteCache.mu.Lock()
	defer siteCache.mu.Unlock()

	cachedFile := &CachedFile{
		Path:        filePath,
		Content:     make([]byte, len(content)),
		ContentType: contentType,
		Size:        size,
		LoadTime:    time.Now(),
		LastAccess:  time.Now(),
		AccessCount: 1,
	}
	copy(cachedFile.Content, content)

	// 如果文件已存在，先释放旧的内存
	if oldFile, exists := siteCache.files[filePath]; exists {
		atomic.AddInt64(&siteCache.currentUsage, -oldFile.Size)
		atomic.AddInt64(&m.currentUsage, -oldFile.Size)
	}

	siteCache.files[filePath] = cachedFile
	atomic.AddInt64(&siteCache.currentUsage, size)
	atomic.AddInt64(&m.currentUsage, size)

	// 更新统计
	m.updateStats()

	return nil
}

// GetFromMemory 从内存获取文件
func (m *MemoryCacheManager) GetFromMemory(siteID, filePath string) ([]byte, string, bool) {
	if !m.config.Enabled {
		return nil, "", false
	}

	m.mu.RLock()
	siteCache, exists := m.siteManagers[siteID]
	m.mu.RUnlock()

	if !exists {
		atomic.AddInt64(&m.stats.MissCount, 1)
		return nil, "", false
	}

	siteCache.mu.RLock()
	cachedFile, exists := siteCache.files[filePath]
	siteCache.mu.RUnlock()

	if !exists {
		atomic.AddInt64(&m.stats.MissCount, 1)
		return nil, "", false
	}

	// 更新访问信息
	cachedFile.LastAccess = time.Now()
	atomic.AddInt64(&cachedFile.AccessCount, 1)
	atomic.AddInt64(&m.stats.HitCount, 1)

	// 记录访问到热点追踪器
	m.hotFiles.RecordAccess(filePath, cachedFile.Size)

	return cachedFile.Content, cachedFile.ContentType, true
}

// ensureMemoryAvailable 确保有足够的内存空间
func (m *MemoryCacheManager) ensureMemoryAvailable(siteCache *SiteMemoryCache, requiredSize int64) error {
	// 检查全局内存限制
	if atomic.LoadInt64(&m.currentUsage)+requiredSize > m.globalLimit {
		if err := m.handleGlobalMemoryPressure(requiredSize); err != nil {
			return err
		}
	}

	// 检查站点内存限制
	if atomic.LoadInt64(&siteCache.currentUsage)+requiredSize > siteCache.memoryLimit {
		if err := m.handleSiteMemoryPressure(siteCache, requiredSize); err != nil {
			return err
		}
	}

	return nil
}

// handleGlobalMemoryPressure 处理全局内存压力
func (m *MemoryCacheManager) handleGlobalMemoryPressure(requiredSize int64) error {
	freedSize := int64(0)

	switch m.config.EvictionStrategy {
	case "size_based":
		freedSize = m.evictBySize(requiredSize)
	case "lru":
		freedSize = m.evictByLRU(requiredSize)
	default: // "score_based"
		freedSize = m.evictByScore(requiredSize)
	}

	if freedSize < requiredSize {
		return fmt.Errorf("无法释放足够的内存空间，需要 %d bytes，只释放了 %d bytes", requiredSize, freedSize)
	}

	return nil
}

// handleSiteMemoryPressure 处理站点内存压力
func (m *MemoryCacheManager) handleSiteMemoryPressure(siteCache *SiteMemoryCache, requiredSize int64) error {
	freedSize := m.evictFromSite(siteCache, requiredSize)

	if freedSize < requiredSize {
		return fmt.Errorf("站点 %s 无法释放足够的内存空间", siteCache.siteID)
	}

	return nil
}

// evictByScore 按评分淘汰文件
func (m *MemoryCacheManager) evictByScore(requiredSize int64) int64 {
	freedSize := int64(0)

	// 收集所有文件及其评分
	type fileScore struct {
		siteID   string
		filePath string
		score    float64
		size     int64
	}

	var files []fileScore

	m.mu.RLock()
	for siteID, siteCache := range m.siteManagers {
		siteCache.mu.RLock()
		for filePath, cachedFile := range siteCache.files {
			stats := m.hotFiles.GetStats(filePath)
			files = append(files, fileScore{
				siteID:   siteID,
				filePath: filePath,
				score:    stats.Score,
				size:     cachedFile.Size,
			})
		}
		siteCache.mu.RUnlock()
	}
	m.mu.RUnlock()

	// 按评分排序（评分低的先淘汰）
	for i := 0; i < len(files)-1; i++ {
		for j := i + 1; j < len(files); j++ {
			if files[i].score > files[j].score {
				files[i], files[j] = files[j], files[i]
			}
		}
	}

	// 淘汰评分最低的文件
	for _, file := range files {
		if freedSize >= requiredSize {
			break
		}

		if m.removeFromMemory(file.siteID, file.filePath) {
			freedSize += file.size
			atomic.AddInt64(&m.stats.EvictionCount, 1)
		}
	}

	return freedSize
}

// evictBySize 按大小淘汰文件（大文件优先）
func (m *MemoryCacheManager) evictBySize(requiredSize int64) int64 {
	freedSize := int64(0)

	type fileSize struct {
		siteID   string
		filePath string
		size     int64
	}

	var files []fileSize

	m.mu.RLock()
	for siteID, siteCache := range m.siteManagers {
		siteCache.mu.RLock()
		for filePath, cachedFile := range siteCache.files {
			files = append(files, fileSize{
				siteID:   siteID,
				filePath: filePath,
				size:     cachedFile.Size,
			})
		}
		siteCache.mu.RUnlock()
	}
	m.mu.RUnlock()

	// 按大小排序（大文件先淘汰）
	for i := 0; i < len(files)-1; i++ {
		for j := i + 1; j < len(files); j++ {
			if files[i].size < files[j].size {
				files[i], files[j] = files[j], files[i]
			}
		}
	}

	// 淘汰最大的文件
	for _, file := range files {
		if freedSize >= requiredSize {
			break
		}

		if m.removeFromMemory(file.siteID, file.filePath) {
			freedSize += file.size
			atomic.AddInt64(&m.stats.EvictionCount, 1)
		}
	}

	return freedSize
}

// evictByLRU 按LRU淘汰文件
func (m *MemoryCacheManager) evictByLRU(requiredSize int64) int64 {
	freedSize := int64(0)

	type fileLRU struct {
		siteID     string
		filePath   string
		lastAccess time.Time
		size       int64
	}

	var files []fileLRU

	m.mu.RLock()
	for siteID, siteCache := range m.siteManagers {
		siteCache.mu.RLock()
		for filePath, cachedFile := range siteCache.files {
			files = append(files, fileLRU{
				siteID:     siteID,
				filePath:   filePath,
				lastAccess: cachedFile.LastAccess,
				size:       cachedFile.Size,
			})
		}
		siteCache.mu.RUnlock()
	}
	m.mu.RUnlock()

	// 按最后访问时间排序（最久未访问的先淘汰）
	for i := 0; i < len(files)-1; i++ {
		for j := i + 1; j < len(files); j++ {
			if files[i].lastAccess.After(files[j].lastAccess) {
				files[i], files[j] = files[j], files[i]
			}
		}
	}

	// 淘汰最久未访问的文件
	for _, file := range files {
		if freedSize >= requiredSize {
			break
		}

		if m.removeFromMemory(file.siteID, file.filePath) {
			freedSize += file.size
			atomic.AddInt64(&m.stats.EvictionCount, 1)
		}
	}

	return freedSize
}

// evictFromSite 从指定站点淘汰文件
func (m *MemoryCacheManager) evictFromSite(siteCache *SiteMemoryCache, requiredSize int64) int64 {
	freedSize := int64(0)

	type fileLRU struct {
		filePath   string
		lastAccess time.Time
		size       int64
	}

	var files []fileLRU

	siteCache.mu.RLock()
	for filePath, cachedFile := range siteCache.files {
		files = append(files, fileLRU{
			filePath:   filePath,
			lastAccess: cachedFile.LastAccess,
			size:       cachedFile.Size,
		})
	}
	siteCache.mu.RUnlock()

	// 按最后访问时间排序
	for i := 0; i < len(files)-1; i++ {
		for j := i + 1; j < len(files); j++ {
			if files[i].lastAccess.After(files[j].lastAccess) {
				files[i], files[j] = files[j], files[i]
			}
		}
	}

	// 淘汰文件
	for _, file := range files {
		if freedSize >= requiredSize {
			break
		}

		if m.removeFromMemory(siteCache.siteID, file.filePath) {
			freedSize += file.size
		}
	}

	return freedSize
}

// removeFromMemory 从内存中移除文件
func (m *MemoryCacheManager) removeFromMemory(siteID, filePath string) bool {
	m.mu.RLock()
	siteCache, exists := m.siteManagers[siteID]
	m.mu.RUnlock()

	if !exists {
		return false
	}

	siteCache.mu.Lock()
	defer siteCache.mu.Unlock()

	cachedFile, exists := siteCache.files[filePath]
	if !exists {
		return false
	}

	delete(siteCache.files, filePath)
	atomic.AddInt64(&siteCache.currentUsage, -cachedFile.Size)
	atomic.AddInt64(&m.currentUsage, -cachedFile.Size)

	return true
}

// cleanupLoop 清理循环
func (m *MemoryCacheManager) cleanupLoop() {
	if m.config.CleanupInterval <= 0 {
		m.config.CleanupInterval = 5 * time.Minute
	}

	ticker := time.NewTicker(m.config.CleanupInterval)
	defer ticker.Stop()

	for range ticker.C {
		m.cleanup()
	}
}

// cleanup 执行清理
func (m *MemoryCacheManager) cleanup() {
	// 清理过期的热点统计
	m.hotFiles.cleanup()

	// 更新统计信息
	m.updateStats()
}

// cleanup 清理热点文件追踪器中的过期数据
func (h *HotFileTracker) cleanup() {
	h.mu.Lock()
	defer h.mu.Unlock()

	now := time.Now()
	expireTime := 24 * time.Hour // 24小时未访问的文件清理统计

	for path, stats := range h.accessStats {
		if now.Sub(stats.LastAccessTime) > expireTime {
			delete(h.accessStats, path)
		}
	}

	// 重建堆
	*h.scoreHeap = (*h.scoreHeap)[:0]
	for path, stats := range h.accessStats {
		heap.Push(h.scoreHeap, &ScoreItem{
			Path:  path,
			Score: stats.Score,
		})
	}
}

// updateStats 更新统计信息
func (m *MemoryCacheManager) updateStats() {
	m.mu.RLock()
	defer m.mu.RUnlock()

	// 更新全局统计
	currentUsage := atomic.LoadInt64(&m.currentUsage)
	m.stats.GlobalUsage = currentUsage
	m.stats.UsagePercent = float64(currentUsage) / float64(m.globalLimit) * 100

	totalFiles := 0

	// 更新站点统计
	for siteID, siteCache := range m.siteManagers {
		siteCache.mu.RLock()
		fileCount := len(siteCache.files)
		totalFiles += fileCount

		siteStats := m.stats.SiteStats[siteID]
		if siteStats == nil {
			siteStats = &SiteStats{}
			m.stats.SiteStats[siteID] = siteStats
		}

		siteStats.MemoryUsage = atomic.LoadInt64(&siteCache.currentUsage)
		siteStats.MemoryLimit = siteCache.memoryLimit
		siteStats.FileCount = fileCount
		siteStats.Priority = siteCache.priority.String()

		// 计算命中率
		totalRequests := siteStats.HitCount + siteStats.MissCount
		if totalRequests > 0 {
			siteStats.HitRate = float64(siteStats.HitCount) / float64(totalRequests)
		}

		siteCache.mu.RUnlock()
	}

	m.stats.TotalFiles = totalFiles

	// 计算全局命中率
	totalRequests := m.stats.HitCount + m.stats.MissCount
	if totalRequests > 0 {
		m.stats.HitRate = float64(m.stats.HitCount) / float64(totalRequests)
	}

	// 更新热点文件列表
	m.stats.TopHotFiles = m.hotFiles.GetTopFiles(10)

	// 标记哪些文件在内存中
	for _, hotFile := range m.stats.TopHotFiles {
		hotFile.InMemory = m.isFileInMemory(hotFile.Path)
	}
}

// isFileInMemory 检查文件是否在内存中
func (m *MemoryCacheManager) isFileInMemory(filePath string) bool {
	for _, siteCache := range m.siteManagers {
		siteCache.mu.RLock()
		_, exists := siteCache.files[filePath]
		siteCache.mu.RUnlock()
		if exists {
			return true
		}
	}
	return false
}

// GetStats 获取统计信息
func (m *MemoryCacheManager) GetStats() *MemoryCacheStats {
	if !m.config.Enabled {
		return &MemoryCacheStats{
			GlobalUsage: 0,
			GlobalLimit: 0,
		}
	}

	m.updateStats()
	return m.stats
}

// RecordAccess 记录文件访问（外部调用）
func (m *MemoryCacheManager) RecordAccess(filePath string, size int64) {
	if !m.config.Enabled {
		return
	}

	m.hotFiles.RecordAccess(filePath, size)
}

// GetContentType 根据文件路径获取内容类型
func GetContentType(filePath string) string {
	ext := filepath.Ext(filePath)
	contentType := mime.TypeByExtension(ext)
	if contentType == "" {
		contentType = "application/octet-stream"
	}
	return contentType
}

// WarmupCache 缓存预热
func (m *MemoryCacheManager) WarmupCache() {
	if !m.config.Enabled {
		return
	}

	// 获取热点文件列表
	hotFiles := m.hotFiles.GetTopFiles(100)

	for _, hotFile := range hotFiles {
		// 这里需要实际的文件加载逻辑
		// 在实际使用中，这个方法会被代理服务器调用
		// 传入实际的文件内容
		_ = hotFile
	}
}

// Shutdown 关闭内存缓存管理器
func (m *MemoryCacheManager) Shutdown() {
	if !m.config.Enabled {
		return
	}

	m.mu.Lock()
	defer m.mu.Unlock()

	// 清空所有缓存
	for _, siteCache := range m.siteManagers {
		siteCache.mu.Lock()
		siteCache.files = make(map[string]*CachedFile)
		siteCache.currentUsage = 0
		siteCache.mu.Unlock()
	}

	m.currentUsage = 0
	m.siteManagers = make(map[string]*SiteMemoryCache)
}

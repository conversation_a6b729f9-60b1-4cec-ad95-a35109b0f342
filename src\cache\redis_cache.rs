#[cfg(feature = "redis-cache")]
use anyhow::{Context, Result};
#[cfg(feature = "redis-cache")]
use async_trait::async_trait;
#[cfg(feature = "redis-cache")]
use bytes::Bytes;
#[cfg(feature = "redis-cache")]
use redis::{AsyncCommands, Client, ConnectionManager};
#[cfg(feature = "redis-cache")]
use serde::{Deserialize, Serialize};
#[cfg(feature = "redis-cache")]
use std::sync::Arc;
#[cfg(feature = "redis-cache")]
use std::time::{Duration, SystemTime, UNIX_EPOCH};
#[cfg(feature = "redis-cache")]
use tokio::sync::RwLock;
#[cfg(feature = "redis-cache")]
use tracing::{debug, error, warn};

#[cfg(feature = "redis-cache")]
use super::{CacheBackend, CacheEntry, CacheStats, RedisConfig};

#[cfg(feature = "redis-cache")]
/// Redis缓存条目（用于序列化）
#[derive(Debug, Clone, Serialize, Deserialize)]
struct RedisCacheEntry {
    data: Vec<u8>,
    content_type: String,
    status_code: u16,
    headers: Vec<(String, String)>,
    created_at: u64,
    expires_at: u64,
    access_count: u64,
    last_accessed: u64,
    size: usize,
    etag: Option<String>,
    last_modified: Option<String>,
}

#[cfg(feature = "redis-cache")]
impl From<&CacheEntry> for RedisCacheEntry {
    fn from(entry: &CacheEntry) -> Self {
        Self {
            data: entry.data.to_vec(),
            content_type: entry.content_type.clone(),
            status_code: entry.status_code,
            headers: entry.headers.clone(),
            created_at: entry.created_at.duration_since(UNIX_EPOCH).unwrap_or_default().as_secs(),
            expires_at: entry.expires_at.duration_since(UNIX_EPOCH).unwrap_or_default().as_secs(),
            access_count: entry.access_count,
            last_accessed: entry.last_accessed.duration_since(UNIX_EPOCH).unwrap_or_default().as_secs(),
            size: entry.size,
            etag: entry.etag.clone(),
            last_modified: entry.last_modified.clone(),
        }
    }
}

#[cfg(feature = "redis-cache")]
impl RedisCacheEntry {
    fn to_cache_entry(&self) -> CacheEntry {
        CacheEntry {
            data: Bytes::from(self.data.clone()),
            content_type: self.content_type.clone(),
            status_code: self.status_code,
            headers: self.headers.clone(),
            created_at: UNIX_EPOCH + Duration::from_secs(self.created_at),
            expires_at: UNIX_EPOCH + Duration::from_secs(self.expires_at),
            access_count: self.access_count,
            last_accessed: UNIX_EPOCH + Duration::from_secs(self.last_accessed),
            size: self.size,
            etag: self.etag.clone(),
            last_modified: self.last_modified.clone(),
        }
    }
    
    fn is_expired(&self) -> bool {
        let now = SystemTime::now().duration_since(UNIX_EPOCH).unwrap_or_default().as_secs();
        now > self.expires_at
    }
}

#[cfg(feature = "redis-cache")]
/// Redis缓存实现
pub struct RedisCache {
    connection_manager: ConnectionManager,
    config: RedisConfig,
    stats: Arc<RwLock<CacheStats>>,
}

#[cfg(feature = "redis-cache")]
impl RedisCache {
    /// 创建新的Redis缓存
    pub async fn new(config: RedisConfig) -> Result<Self> {
        let client = Client::open(config.url.clone())
            .with_context(|| format!("Failed to create Redis client for URL: {}", config.url))?;
        
        let connection_manager = ConnectionManager::new(client).await
            .context("Failed to create Redis connection manager")?;
        
        // 测试连接
        let mut conn = connection_manager.clone();
        let _: String = conn.ping().await
            .context("Failed to ping Redis server")?;
        
        debug!("Connected to Redis server: {}", config.url);
        
        Ok(Self {
            connection_manager,
            config,
            stats: Arc::new(RwLock::new(CacheStats::default())),
        })
    }
    
    /// 获取带前缀的键
    fn prefixed_key(&self, key: &str) -> String {
        format!("{}{}", self.config.prefix, key)
    }
    
    /// 获取统计键
    fn stats_key(&self) -> String {
        format!("{}stats", self.config.prefix)
    }
    
    /// 获取键集合的键
    fn keys_set_key(&self) -> String {
        format!("{}keys", self.config.prefix)
    }
    
    /// 计算TTL（秒）
    fn calculate_ttl(&self, entry: &CacheEntry) -> i64 {
        let now = SystemTime::now().duration_since(UNIX_EPOCH).unwrap_or_default().as_secs();
        let expires_at = entry.expires_at.duration_since(UNIX_EPOCH).unwrap_or_default().as_secs();
        
        if expires_at > now {
            (expires_at - now) as i64
        } else {
            0 // 已过期
        }
    }
    
    /// 更新统计信息
    async fn update_stats<F>(&self, update_fn: F) -> Result<()>
    where
        F: FnOnce(&mut CacheStats),
    {
        let mut stats = self.stats.write().await;
        update_fn(&mut stats);
        
        // 同时更新Redis中的统计信息
        let mut conn = self.connection_manager.clone();
        let stats_data = serde_json::to_string(&*stats)?;
        let _: () = conn.set(self.stats_key(), stats_data).await?;
        
        Ok(())
    }
    
    /// 从Redis加载统计信息
    async fn load_stats(&self) -> Result<()> {
        let mut conn = self.connection_manager.clone();
        
        match conn.get::<_, Option<String>>(self.stats_key()).await {
            Ok(Some(stats_data)) => {
                match serde_json::from_str::<CacheStats>(&stats_data) {
                    Ok(stats) => {
                        let mut current_stats = self.stats.write().await;
                        *current_stats = stats;
                        debug!("Loaded cache stats from Redis");
                    }
                    Err(e) => {
                        warn!("Failed to parse cache stats from Redis: {}", e);
                    }
                }
            }
            Ok(None) => {
                debug!("No existing cache stats found in Redis");
            }
            Err(e) => {
                warn!("Failed to load cache stats from Redis: {}", e);
            }
        }
        
        Ok(())
    }
}

#[cfg(feature = "redis-cache")]
#[async_trait]
impl CacheBackend for RedisCache {
    async fn get(&self, key: &str) -> Result<Option<CacheEntry>> {
        let mut conn = self.connection_manager.clone();
        let prefixed_key = self.prefixed_key(key);
        
        match conn.get::<_, Option<String>>(&prefixed_key).await {
            Ok(Some(data)) => {
                match serde_json::from_str::<RedisCacheEntry>(&data) {
                    Ok(redis_entry) => {
                        if redis_entry.is_expired() {
                            // 删除过期条目
                            let _ = self.delete(key).await;
                            Ok(None)
                        } else {
                            let mut entry = redis_entry.to_cache_entry();
                            entry.update_access();
                            
                            // 更新访问信息
                            let updated_redis_entry = RedisCacheEntry::from(&entry);
                            let updated_data = serde_json::to_string(&updated_redis_entry)?;
                            let ttl = self.calculate_ttl(&entry);
                            
                            if ttl > 0 {
                                let _: () = conn.set_ex(&prefixed_key, updated_data, ttl as u64).await?;
                            }
                            
                            Ok(Some(entry))
                        }
                    }
                    Err(e) => {
                        error!("Failed to deserialize cache entry from Redis: {}", e);
                        Ok(None)
                    }
                }
            }
            Ok(None) => Ok(None),
            Err(e) => {
                error!("Redis get error for key {}: {}", key, e);
                Err(e.into())
            }
        }
    }
    
    async fn set(&self, key: &str, entry: CacheEntry) -> Result<()> {
        let mut conn = self.connection_manager.clone();
        let prefixed_key = self.prefixed_key(key);
        
        let redis_entry = RedisCacheEntry::from(&entry);
        let data = serde_json::to_string(&redis_entry)?;
        let ttl = self.calculate_ttl(&entry);
        
        if ttl <= 0 {
            return Err(anyhow::anyhow!("Cannot set expired cache entry"));
        }
        
        // 设置缓存条目
        let _: () = conn.set_ex(&prefixed_key, data, ttl as u64).await?;
        
        // 添加到键集合
        let _: () = conn.sadd(self.keys_set_key(), key).await?;
        
        // 更新统计信息
        self.update_stats(|stats| {
            stats.record_write(entry.size);
        }).await?;
        
        Ok(())
    }
    
    async fn delete(&self, key: &str) -> Result<bool> {
        let mut conn = self.connection_manager.clone();
        let prefixed_key = self.prefixed_key(key);
        
        // 获取条目大小用于统计
        let size = if let Ok(Some(data)) = conn.get::<_, Option<String>>(&prefixed_key).await {
            serde_json::from_str::<RedisCacheEntry>(&data)
                .map(|entry| entry.size)
                .unwrap_or(0)
        } else {
            0
        };
        
        // 删除缓存条目
        let deleted: i32 = conn.del(&prefixed_key).await?;
        
        if deleted > 0 {
            // 从键集合中移除
            let _: () = conn.srem(self.keys_set_key(), key).await?;
            
            // 更新统计信息
            self.update_stats(|stats| {
                stats.record_delete(size);
            }).await?;
            
            Ok(true)
        } else {
            Ok(false)
        }
    }
    
    async fn exists(&self, key: &str) -> Result<bool> {
        let mut conn = self.connection_manager.clone();
        let prefixed_key = self.prefixed_key(key);
        
        let exists: bool = conn.exists(&prefixed_key).await?;
        Ok(exists)
    }
    
    async fn clear(&self) -> Result<()> {
        let mut conn = self.connection_manager.clone();
        
        // 获取所有键
        let keys: Vec<String> = conn.smembers(self.keys_set_key()).await?;
        
        if !keys.is_empty() {
            // 删除所有缓存条目
            let prefixed_keys: Vec<String> = keys.iter()
                .map(|k| self.prefixed_key(k))
                .collect();
            
            let _: () = conn.del(&prefixed_keys).await?;
        }
        
        // 清空键集合
        let _: () = conn.del(self.keys_set_key()).await?;
        
        // 重置统计信息
        let mut stats = self.stats.write().await;
        *stats = CacheStats::default();
        let stats_data = serde_json::to_string(&*stats)?;
        let _: () = conn.set(self.stats_key(), stats_data).await?;
        
        Ok(())
    }
    
    async fn stats(&self) -> Result<CacheStats> {
        // 先尝试从Redis加载最新统计信息
        let _ = self.load_stats().await;
        
        let stats = self.stats.read().await.clone();
        Ok(stats)
    }
    
    async fn cleanup_expired(&self) -> Result<u64> {
        let mut conn = self.connection_manager.clone();
        let keys: Vec<String> = conn.smembers(self.keys_set_key()).await?;
        
        let mut removed_count = 0u64;
        let mut expired_keys = Vec::new();
        
        for key in keys {
            let prefixed_key = self.prefixed_key(&key);
            
            match conn.get::<_, Option<String>>(&prefixed_key).await {
                Ok(Some(data)) => {
                    if let Ok(redis_entry) = serde_json::from_str::<RedisCacheEntry>(&data) {
                        if redis_entry.is_expired() {
                            expired_keys.push(key);
                        }
                    }
                }
                Ok(None) => {
                    // 键在集合中但不存在，清理集合
                    expired_keys.push(key);
                }
                Err(_) => {
                    // 忽略错误，继续处理其他键
                }
            }
        }
        
        // 删除过期键
        for key in expired_keys {
            if self.delete(&key).await.unwrap_or(false) {
                removed_count += 1;
            }
        }
        
        if removed_count > 0 {
            self.update_stats(|stats| {
                stats.cleanup_count += 1;
            }).await?;
        }
        
        Ok(removed_count)
    }
    
    async fn keys(&self) -> Result<Vec<String>> {
        let mut conn = self.connection_manager.clone();
        let keys: Vec<String> = conn.smembers(self.keys_set_key()).await?;
        Ok(keys)
    }
    
    async fn size(&self) -> Result<u64> {
        let stats = self.stats().await?;
        Ok(stats.size_bytes)
    }
}

// 当redis-cache特性未启用时的占位符实现
#[cfg(not(feature = "redis-cache"))]
pub struct RedisCache;

#[cfg(not(feature = "redis-cache"))]
impl RedisCache {
    pub async fn new(_config: super::RedisConfig) -> Result<Self> {
        Err(anyhow::anyhow!("Redis cache not enabled. Enable 'redis-cache' feature"))
    }
}

#[cfg(all(test, feature = "redis-cache"))]
mod tests {
    use super::*;
    use bytes::Bytes;
    
    // 注意：这些测试需要运行Redis服务器
    async fn create_test_cache() -> RedisCache {
        let config = RedisConfig {
            url: "redis://127.0.0.1:6379".to_string(),
            pool_size: 10,
            connection_timeout: Duration::from_secs(5),
            command_timeout: Duration::from_secs(3),
            database: 15, // 使用测试数据库
            prefix: "test_cache:".to_string(),
        };
        
        RedisCache::new(config).await.unwrap()
    }
    
    #[tokio::test]
    #[ignore] // 需要Redis服务器
    async fn test_redis_cache_basic_operations() {
        let cache = create_test_cache().await;
        
        // 清空测试数据
        cache.clear().await.unwrap();
        
        let entry = CacheEntry::new(
            Bytes::from("test data"),
            "text/plain".to_string(),
            200,
            vec![("X-Test".to_string(), "value".to_string())],
            Duration::from_secs(60),
        );
        
        // 测试设置
        cache.set("test_key", entry.clone()).await.unwrap();
        
        // 测试获取
        let retrieved = cache.get("test_key").await.unwrap().unwrap();
        assert_eq!(retrieved.data, entry.data);
        assert_eq!(retrieved.content_type, entry.content_type);
        
        // 测试存在性检查
        assert!(cache.exists("test_key").await.unwrap());
        assert!(!cache.exists("nonexistent_key").await.unwrap());
        
        // 测试删除
        assert!(cache.delete("test_key").await.unwrap());
        assert!(!cache.exists("test_key").await.unwrap());
        
        // 清理
        cache.clear().await.unwrap();
    }
    
    #[tokio::test]
    #[ignore] // 需要Redis服务器
    async fn test_redis_cache_expiration() {
        let cache = create_test_cache().await;
        cache.clear().await.unwrap();
        
        let entry = CacheEntry::new(
            Bytes::from("expiring data"),
            "text/plain".to_string(),
            200,
            vec![],
            Duration::from_secs(1), // 1秒TTL
        );
        
        cache.set("expiring_key", entry).await.unwrap();
        
        // 立即获取应该成功
        assert!(cache.get("expiring_key").await.unwrap().is_some());
        
        // 等待过期
        tokio::time::sleep(Duration::from_secs(2)).await;
        
        // 再次获取应该失败
        assert!(cache.get("expiring_key").await.unwrap().is_none());
        
        cache.clear().await.unwrap();
    }
}

use anyhow::{Context, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::time::Duration;

pub mod validation;
pub mod watcher;

/// 主配置结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    pub server: ServerConfig,
    pub log: LogConfig,
    pub cache: CacheConfig,
    pub rate_limit: RateLimitConfig,
    pub circuit_breaker: CircuitBreakerConfig,
    pub compression: CompressionConfig,
    pub hot_reload: HotReloadConfig,
    pub memory_cache: MemoryCacheConfig,
    pub performance: PerformanceConfig,
    pub headers: HeadersConfig,
    pub error_pages: Vec<ErrorPageConfig>,
    pub acl: AclConfig,
    pub monitor: MonitorConfig,
    pub sites: Vec<SiteConfig>,
}

/// 服务器配置
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ServerConfig {
    pub http_port: u16,
    pub https_port: u16,
    #[serde(with = "humantime_serde")]
    pub read_timeout: Duration,
    #[serde(with = "humantime_serde")]
    pub write_timeout: Duration,
    #[serde(with = "humantime_serde")]
    pub idle_timeout: Duration,
    pub max_connections: usize,
    pub connection_pool: ConnectionPoolConfig,
}

/// 连接池配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConnectionPoolConfig {
    pub max_idle_conns: usize,
    pub max_idle_conns_per_host: usize,
    #[serde(with = "humantime_serde")]
    pub idle_conn_timeout: Duration,
    #[serde(with = "humantime_serde")]
    pub dial_timeout: Duration,
    #[serde(with = "humantime_serde")]
    pub keep_alive: Duration,
    pub max_conns_per_host: usize,
    pub disable_keep_alives: bool,
}

/// 日志配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogConfig {
    pub level: String,
    pub file: Option<String>,
    pub format: String,
    pub formats: HashMap<String, String>,
    pub max_size: u64,
    pub max_backups: u32,
    pub max_age: u32,
    pub targets: Vec<LogTargetConfig>,
    pub r#async: AsyncLogConfig,
}

/// 日志目标配置
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum LogTargetConfig {
    #[serde(rename = "file")]
    File {
        filename: String,
        format: String,
    },
    #[serde(rename = "syslog")]
    Syslog {
        network: String,
        address: String,
        format: String,
    },
}

/// 异步日志配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AsyncLogConfig {
    pub enabled: bool,
    pub channel_size: usize,
    pub batch_size: usize,
    #[serde(with = "humantime_serde")]
    pub flush_interval: Duration,
    pub max_memory_mb: usize,
    pub drop_policy: String,
}

/// 缓存配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheConfig {
    pub enabled: bool,
    pub r#type: CacheType,
    pub path: Option<PathBuf>,
    pub max_size: String,
    #[serde(with = "humantime_serde")]
    pub ttl: Duration,
    pub status_ttl: HashMap<String, String>,
    pub cache_headers: Option<CacheHeadersConfig>,
    #[serde(with = "humantime_serde")]
    pub cleanup_interval: Duration,
    #[serde(with = "humantime_serde")]
    pub expired_check_interval: Duration,
    pub enable_async_cleanup: bool,
    pub rules: Vec<CacheRule>,
    pub redis: Option<RedisConfig>,
}

/// 缓存类型
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum CacheType {
    File,
    Memory,
    Redis,
    Hybrid,  // 内存+文件
}

/// Redis配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RedisConfig {
    pub url: String,
    pub pool_size: usize,
    #[serde(with = "humantime_serde")]
    pub connection_timeout: Duration,
    #[serde(with = "humantime_serde")]
    pub command_timeout: Duration,
    pub database: u8,
    pub prefix: String,
}

/// 缓存规则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheRule {
    pub pattern: String,
    #[serde(with = "humantime_serde")]
    pub ttl: Duration,
    pub enabled: bool,
}

/// 缓存头部配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheHeadersConfig {
    pub enabled: bool,
    pub cache_status: String,
    pub show_detail: bool,
}

impl Config {
    /// 从文件加载配置
    pub async fn load<P: AsRef<Path>>(path: P) -> Result<Self> {
        let content = tokio::fs::read_to_string(&path)
            .await
            .with_context(|| format!("Failed to read config file: {}", path.as_ref().display()))?;
        
        let config: Config = serde_json::from_str(&content)
            .with_context(|| format!("Failed to parse config file: {}", path.as_ref().display()))?;
        
        // 验证配置
        config.validate()?;
        
        Ok(config)
    }
    
    /// 验证配置
    pub fn validate(&self) -> Result<()> {
        validation::validate_config(self)
    }
    
    /// 获取站点的缓存目录
    pub fn get_site_cache_dir(&self, site_name: &str) -> PathBuf {
        if let Some(base_path) = &self.cache.path {
            base_path.join(site_name)
        } else {
            PathBuf::from("cache").join(site_name)
        }
    }
}

impl Default for Config {
    fn default() -> Self {
        Self {
            server: ServerConfig {
                http_port: 80,
                https_port: 443,
                read_timeout: std::time::Duration::from_secs(30),
                write_timeout: std::time::Duration::from_secs(30),
                idle_timeout: std::time::Duration::from_secs(60),
                max_connections: 1000,
                connection_pool: ConnectionPoolConfig {
                    max_idle_conns: 20,
                    max_idle_conns_per_host: 2,
                    idle_conn_timeout: std::time::Duration::from_secs(90),
                    dial_timeout: std::time::Duration::from_secs(30),
                    keep_alive: std::time::Duration::from_secs(30),
                    max_conns_per_host: 0,
                    disable_keep_alives: false,
                },
            },
            log: LogConfig {
                level: "info".to_string(),
                file: None,
                format: "combined".to_string(),
                formats: {
                    let mut formats = std::collections::HashMap::new();
                    formats.insert("combined".to_string(), "{remote_addr} - - [{time}] \"{request}\" {status} {body_bytes_sent} \"{http_referer}\" \"{http_user_agent}\" {request_time} {site}".to_string());
                    formats
                },
                max_size: 100,
                max_backups: 10,
                max_age: 30,
                targets: vec![],
                r#async: AsyncLogConfig {
                    enabled: true,
                    channel_size: 2000,
                    batch_size: 50,
                    flush_interval: std::time::Duration::from_secs(2),
                    max_memory_mb: 2,
                    drop_policy: "drop_oldest".to_string(),
                },
            },
            cache: CacheConfig {
                enabled: true,
                r#type: CacheType::Memory,
                path: Some(PathBuf::from("cache")),
                max_size: "1GB".to_string(),
                ttl: std::time::Duration::from_secs(3600),
                status_ttl: std::collections::HashMap::new(),
                cache_headers: None,
                cleanup_interval: std::time::Duration::from_secs(300),
                expired_check_interval: std::time::Duration::from_secs(60),
                enable_async_cleanup: true,
                rules: vec![],
                redis: None,
            },
            rate_limit: RateLimitConfig {
                enabled: false,
                global_rps: 10000,
                ip_rps: 100,
                site_rps: 1000,
                burst: 50,
            },
            circuit_breaker: CircuitBreakerConfig {
                enabled: false,
                max_failures: 5,
                reset_timeout: std::time::Duration::from_secs(30),
                half_open_requests: 3,
            },
            compression: CompressionConfig {
                enabled: true,
                types: vec!["text/html".to_string(), "text/css".to_string(), "application/json".to_string()],
                min_size: "1KB".to_string(),
                max_size: "2MB".to_string(),
                level: 6,
                algorithms: vec!["gzip".to_string()],
                brotli_quality: 6,
                zstd_level: 3,
            },
            hot_reload: HotReloadConfig {
                enabled: true,
                check_interval: std::time::Duration::from_secs(5),
            },
            memory_cache: MemoryCacheConfig {
                enabled: true,
                global_memory_limit: "32MB".to_string(),
                default_site_limit: "6MB".to_string(),
                max_file_size: "2MB".to_string(),
                min_access_count: 3,
                score_threshold: 10.0,
                cleanup_interval: std::time::Duration::from_secs(300),
                allowed_types: vec!["text/html".to_string()],
                blocked_types: vec!["image/*".to_string()],
                eviction_strategy: "score_based".to_string(),
                sites: std::collections::HashMap::new(),
            },
            performance: PerformanceConfig {
                enabled: false,
                enable_zero_copy: false,
                buffer_size: "128KB".to_string(),
                max_connections: "1M".to_string(),
                connection_timeout: std::time::Duration::from_secs(5),
                keep_alive_timeout: std::time::Duration::from_secs(60),
                enable_cpu_affinity: true,
                worker_threads: 0,
            },
            headers: HeadersConfig {
                request: HeaderOperations {
                    set: std::collections::HashMap::new(),
                    remove: vec![],
                    ignore: vec![],
                },
                response: HeaderOperations {
                    set: {
                        let mut headers = std::collections::HashMap::new();
                        headers.insert("Server".to_string(), "RustProxy/1.0".to_string());
                        headers
                    },
                    remove: vec![],
                    ignore: vec![],
                },
            },
            error_pages: vec![],
            acl: AclConfig {
                enabled: false,
                global_allow: vec![],
                global_deny: vec![],
                allow_file: None,
                deny_file: None,
                reload_interval: std::time::Duration::from_secs(300),
            },
            monitor: MonitorConfig {
                enabled: false,
                port: 8080,
                username: "admin".to_string(),
                password: "password".to_string(),
                api_key: "api-key".to_string(),
                acl: MonitorAclConfig {
                    allowed_ips: vec!["127.0.0.1".to_string()],
                    denied_ips: vec![],
                },
            },
            sites: vec![],
        }
    }
}

// 其他配置结构的占位符，稍后实现
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitConfig {
    pub enabled: bool,
    pub global_rps: u32,
    pub ip_rps: u32,
    pub site_rps: u32,
    pub burst: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CircuitBreakerConfig {
    pub enabled: bool,
    pub max_failures: u32,
    #[serde(with = "humantime_serde")]
    pub reset_timeout: Duration,
    pub half_open_requests: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompressionConfig {
    pub enabled: bool,
    pub types: Vec<String>,
    pub min_size: String,
    pub max_size: String,
    pub level: u8,
    pub algorithms: Vec<String>,
    pub brotli_quality: u8,
    pub zstd_level: i32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HotReloadConfig {
    pub enabled: bool,
    #[serde(with = "humantime_serde")]
    pub check_interval: Duration,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MemoryCacheConfig {
    pub enabled: bool,
    pub global_memory_limit: String,
    pub default_site_limit: String,
    pub max_file_size: String,
    pub min_access_count: u32,
    pub score_threshold: f64,
    #[serde(with = "humantime_serde")]
    pub cleanup_interval: Duration,
    pub allowed_types: Vec<String>,
    pub blocked_types: Vec<String>,
    pub eviction_strategy: String,
    pub sites: HashMap<String, SiteMemoryCacheConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SiteMemoryCacheConfig {
    pub memory_limit: String,
    pub priority: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceConfig {
    pub enabled: bool,
    pub enable_zero_copy: bool,
    pub buffer_size: String,
    pub max_connections: String,
    #[serde(with = "humantime_serde")]
    pub connection_timeout: Duration,
    #[serde(with = "humantime_serde")]
    pub keep_alive_timeout: Duration,
    pub enable_cpu_affinity: bool,
    pub worker_threads: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HeadersConfig {
    pub request: HeaderOperations,
    pub response: HeaderOperations,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HeaderOperations {
    pub set: HashMap<String, String>,
    pub remove: Vec<String>,
    pub ignore: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorPageConfig {
    pub code: u16,
    pub file: Option<String>,
    pub content: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AclConfig {
    pub enabled: bool,
    pub global_allow: Vec<String>,
    pub global_deny: Vec<String>,
    pub allow_file: Option<String>,
    pub deny_file: Option<String>,
    #[serde(with = "humantime_serde")]
    pub reload_interval: Duration,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitorConfig {
    pub enabled: bool,
    pub port: u16,
    pub username: String,
    pub password: String,
    pub api_key: String,
    pub acl: MonitorAclConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitorAclConfig {
    pub allowed_ips: Vec<String>,
    pub denied_ips: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SiteConfig {
    pub site_id: String,
    pub name: String,
    pub domains: Vec<String>,
    pub default_site: Option<bool>,
    pub max_connections: Option<usize>,
    pub http_port: Option<u16>,
    pub https_port: Option<u16>,
    pub debug_mode: Option<bool>,
    pub ssl: Option<SslConfig>,
    pub upstreams: Vec<UpstreamConfig>,
    pub cache_headers: Option<CacheHeadersConfig>,
    pub routes: Vec<RouteConfig>,
    pub log_format: Option<String>,
    pub log_targets: Vec<LogTargetConfig>,
    pub acl: Option<SiteAclConfig>,
    pub headers: Option<HeadersConfig>,
    pub rules: Vec<CacheRule>,
    pub error_pages: Vec<ErrorPageConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SslConfig {
    pub enabled: bool,
    pub cert_file: String,
    pub key_file: String,
    pub protocols: String,
    pub min_version: Option<String>,
    pub max_version: Option<String>,
    pub prefer_server_ciphers: Option<bool>,
    pub session_cache: Option<usize>,
    pub session_tickets: Option<bool>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpstreamConfig {
    pub name: String,
    pub address: String,
    pub port: u16,
    pub protocol: String,
    pub https_port: Option<u16>,
    pub weight: u32,
    pub max_fails: u32,
    #[serde(with = "humantime_serde")]
    pub fail_timeout: Duration,
    pub backup: bool,
    pub load_balance_group: String,
    pub health_check: Option<String>,
    pub health_host: Option<String>,
    #[serde(with = "humantime_serde")]
    pub health_interval: Duration,
    #[serde(with = "humantime_serde")]
    pub health_timeout: Duration,
    pub health_path: Option<String>,
}

/// 负载均衡方法
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum LoadBalanceMethod {
    RoundRobin,
    LeastConn,
    WeightedRoundRobin,
    IpHash,
    Random,
}

impl Default for LoadBalanceMethod {
    fn default() -> Self {
        LoadBalanceMethod::RoundRobin
    }
}

/// 上游服务器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpstreamServerConfig {
    pub url: String,
    pub weight: u32,
    pub max_fails: u32,
    #[serde(with = "humantime_serde")]
    pub fail_timeout: Duration,
}

/// 上游服务器组配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpstreamGroupConfig {
    pub name: String,
    pub load_balance: LoadBalanceMethod,
    pub servers: Vec<UpstreamServerConfig>,
    pub health_check: Option<HealthCheckConfig>,
}

/// 健康检查配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthCheckConfig {
    pub enabled: bool,
    pub path: String,
    #[serde(with = "humantime_serde")]
    pub interval: Duration,
    #[serde(with = "humantime_serde")]
    pub timeout: Duration,
    pub healthy_threshold: u32,
    pub unhealthy_threshold: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RouteConfig {
    pub pattern: String,
    pub upstream: Option<String>,
    pub rewrite: Option<String>,
    pub cache: bool,
    pub static_dir: Option<String>,
    pub dir_listing: Option<bool>,
    pub index_files: Option<Vec<String>>,
    pub mime_types: Option<HashMap<String, String>>,
    pub hidden_in_listing: Option<Vec<String>>,
    pub match_conditions: Option<MatchConditions>,
    pub description: Option<String>,
    pub headers: Option<HeadersConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MatchConditions {
    pub methods: Option<Vec<String>>,
    pub query_params: Option<Vec<QueryParamCondition>>,
    pub cookies: Option<Vec<CookieCondition>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueryParamCondition {
    pub name: String,
    pub pattern: String,
    pub description: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CookieCondition {
    pub name: Option<String>,
    pub name_pattern: Option<String>,
    pub exists: Option<bool>,
    pub description: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SiteAclConfig {
    pub allow: Vec<String>,
    pub deny: Vec<String>,
    pub allow_file: Option<String>,
    pub deny_file: Option<String>,
}

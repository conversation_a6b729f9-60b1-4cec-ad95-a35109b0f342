# 监控和指标增强

## 📋 概述

监控和指标增强通过全面的性能监控、实时统计和智能告警，提供深入的系统洞察，帮助运维人员及时发现问题、优化性能和保障服务质量。

## 🎯 优化目标

- **全面监控**：覆盖系统各个层面的关键指标
- **实时统计**：提供实时性能数据和趋势分析
- **智能告警**：主动发现和预警潜在问题
- **性能优化**：基于数据驱动的性能调优

## 🔧 技术实现

### 1. 多维度监控体系

#### 全局监控指标
```go
type GlobalStats struct {
    TotalRequests      int64     `json:"total_requests"`
    ErrorRequests      int64     `json:"error_requests"`
    BytesReceived      int64     `json:"bytes_received"`
    BytesSent          int64     `json:"bytes_sent"`
    UpstreamReceived   int64     `json:"upstream_received"`
    UpstreamSent       int64     `json:"upstream_sent"`
    CurrentConnections int64     `json:"current_connections"`
    StartTime          time.Time `json:"start_time"`
    Uptime             int64     `json:"uptime"`
}
```

#### 站点级监控
```go
type SiteStats struct {
    Name               string    `json:"name"`
    TotalRequests      int64     `json:"total_requests"`
    ErrorRequests      int64     `json:"error_requests"`
    BytesReceived      int64     `json:"bytes_received"`
    BytesSent          int64     `json:"bytes_sent"`
    OutBytes           int64     `json:"out_bytes"`
    CurrentConnections int64     `json:"current_connections"`
    StartTime          time.Time `json:"start_time"`
    Uptime             int64     `json:"uptime"`
    ResponseTimes      *ResponseTimeStats `json:"response_times"`
}
```

#### 上游服务器监控
```go
type UpstreamStats struct {
    Name                string    `json:"name"`
    Address             string    `json:"address"`
    Port                int       `json:"port"`
    Healthy             bool      `json:"healthy"`
    OfflineCount        int64     `json:"offline_count"`
    LastOfflineTime     time.Time `json:"last_offline_time"`
    TotalOfflineTime    int64     `json:"total_offline_time"`
    ResponseTime        int64     `json:"response_time"`
    ResponseTimeMs      int64     `json:"response_time_ms"`
    LastCheckTime       time.Time `json:"last_check_time"`
    SuccessCount        int64     `json:"success_count"`
    FailCount           int64     `json:"fail_count"`
    CurrentConnections  int64     `json:"current_connections"`
    BytesReceived       int64     `json:"bytes_received"`
    BytesSent           int64     `json:"bytes_sent"`
}
```

### 2. 高级性能指标

#### 响应时间分布
```go
type ResponseTimeStats struct {
    P50  time.Duration `json:"p50"`   // 50%分位数
    P90  time.Duration `json:"p90"`   // 90%分位数
    P95  time.Duration `json:"p95"`   // 95%分位数
    P99  time.Duration `json:"p99"`   // 99%分位数
    Mean time.Duration `json:"mean"`  // 平均值
    Max  time.Duration `json:"max"`   // 最大值
    Min  time.Duration `json:"min"`   // 最小值
}

func (rts *ResponseTimeStats) Update(duration time.Duration) {
    // 使用滑动窗口或直方图算法更新分位数
    rts.histogram.Update(duration)
    rts.P50 = rts.histogram.Percentile(0.5)
    rts.P95 = rts.histogram.Percentile(0.95)
    rts.P99 = rts.histogram.Percentile(0.99)
}
```

#### QPS和吞吐量统计
```go
type ThroughputStats struct {
    CurrentQPS    float64 `json:"current_qps"`
    AverageQPS    float64 `json:"average_qps"`
    PeakQPS       float64 `json:"peak_qps"`
    TotalRequests int64   `json:"total_requests"`
    WindowSize    time.Duration `json:"window_size"`
}

func (ts *ThroughputStats) UpdateQPS() {
    now := time.Now()
    // 使用滑动窗口计算QPS
    ts.CurrentQPS = float64(ts.requestsInWindow) / ts.WindowSize.Seconds()
}
```

### 3. 缓存系统监控

#### 缓存性能指标
```go
type CacheStats struct {
    Enabled       bool    `json:"enabled"`
    Type          string  `json:"type"`
    TotalSize     int64   `json:"total_size"`
    MaxSize       int64   `json:"max_size"`
    UsagePercent  float64 `json:"usage_percent"`
    FileCount     int64   `json:"file_count"`
    HitCount      int64   `json:"hit_count"`
    MissCount     int64   `json:"miss_count"`
    HitRate       float64 `json:"hit_rate"`
    WriteCount    int64   `json:"write_count"`
    DeleteCount   int64   `json:"delete_count"`
    ErrorCount    int64   `json:"error_count"`
}
```

#### 缓存清理监控
```go
type CacheCleanupStats struct {
    AsyncCleanup         bool      `json:"async_cleanup"`
    CleanupInterval      int64     `json:"cleanup_interval"`
    Enabled              bool      `json:"enabled"`
    ExpiredCheckInterval int64     `json:"expired_check_interval"`
    ExpiredCleaned       int64     `json:"expired_cleaned"`
    HeapSize             int       `json:"heap_size"`
    IndexedFiles         int       `json:"indexed_files"`
    LastCleanupTime      time.Time `json:"last_cleanup_time"`
    SizeCleaned          int64     `json:"size_cleaned"`
    TotalCleanups        int64     `json:"total_cleanups"`
}
```

### 4. 带宽监控

#### 实时带宽统计
```go
type BandwidthStats struct {
    IncomingRate    float64 `json:"incoming_rate"`     // 入站速率 (bytes/s)
    OutgoingRate    float64 `json:"outgoing_rate"`     // 出站速率 (bytes/s)
    UpstreamInRate  float64 `json:"upstream_in_rate"`  // 上游入站速率
    UpstreamOutRate float64 `json:"upstream_out_rate"` // 上游出站速率
}

func (bs *BandwidthStats) UpdateRates() {
    now := time.Now()
    duration := now.Sub(bs.lastUpdate).Seconds()
    
    bs.IncomingRate = float64(bs.incomingBytes) / duration
    bs.OutgoingRate = float64(bs.outgoingBytes) / duration
    
    // 重置计数器
    bs.incomingBytes = 0
    bs.outgoingBytes = 0
    bs.lastUpdate = now
}
```

## ⚙️ 监控配置

### 基础监控配置
```json
{
  "monitor": {
    "enabled": true,
    "port": 8080,
    "username": "admin",
    "password": "password",
    "update_interval": "5s",
    "retention_period": "24h"
  }
}
```

### 高级监控配置
```json
{
  "monitor": {
    "enabled": true,
    "port": 8080,
    "metrics": {
      "response_time_percentiles": true,
      "qps_calculation": true,
      "bandwidth_monitoring": true,
      "cache_detailed_stats": true,
      "upstream_health_tracking": true
    },
    "alerts": {
      "enabled": true,
      "thresholds": {
        "error_rate": 0.05,
        "response_time_p99": "1s",
        "cache_hit_rate": 0.8,
        "upstream_failure_rate": 0.1
      }
    },
    "export": {
      "prometheus": {
        "enabled": true,
        "endpoint": "/metrics"
      },
      "json": {
        "enabled": true,
        "endpoint": "/stats"
      }
    }
  }
}
```

## 📊 监控API

### 统计信息API
```bash
# 获取全部统计信息
curl -u admin:password http://localhost:8080/stats

# 获取特定站点统计
curl -u admin:password http://localhost:8080/stats/sites/example.com

# 获取上游服务器状态
curl -u admin:password http://localhost:8080/stats/upstreams

# 获取缓存统计
curl -u admin:password http://localhost:8080/stats/cache
```

### 实时监控API
```bash
# 实时QPS监控
curl -u admin:password http://localhost:8080/metrics/qps

# 实时响应时间监控
curl -u admin:password http://localhost:8080/metrics/response-time

# 实时带宽监控
curl -u admin:password http://localhost:8080/metrics/bandwidth
```

### Prometheus指标导出
```bash
# Prometheus格式指标
curl http://localhost:8080/metrics

# 示例输出
# HELP reverse_proxy_requests_total Total number of requests
# TYPE reverse_proxy_requests_total counter
reverse_proxy_requests_total{site="example.com",method="GET"} 12345

# HELP reverse_proxy_response_time_seconds Response time in seconds
# TYPE reverse_proxy_response_time_seconds histogram
reverse_proxy_response_time_seconds_bucket{site="example.com",le="0.1"} 8000
reverse_proxy_response_time_seconds_bucket{site="example.com",le="0.5"} 11000
reverse_proxy_response_time_seconds_bucket{site="example.com",le="1.0"} 12000
```

## 🔍 故障排查

### 性能问题诊断

#### 1. 响应时间异常
**排查步骤**：
```bash
# 检查P99响应时间
curl -u admin:password http://localhost:8080/stats | jq '.sites.example.response_times.p99'

# 检查上游服务器响应时间
curl -u admin:password http://localhost:8080/stats | jq '.upstreams'

# 检查缓存命中率
curl -u admin:password http://localhost:8080/stats | jq '.cache.hit_rate'
```

#### 2. 错误率上升
**排查步骤**：
```bash
# 检查全局错误率
curl -u admin:password http://localhost:8080/stats | jq '.global.error_requests / .global.total_requests'

# 检查各站点错误分布
curl -u admin:password http://localhost:8080/stats | jq '.sites[] | {name: .name, error_rate: (.error_requests / .total_requests)}'

# 检查上游服务器健康状态
curl -u admin:password http://localhost:8080/stats | jq '.upstreams[] | {name: .name, healthy: .healthy, fail_count: .fail_count}'
```

#### 3. 带宽异常
**排查步骤**：
```bash
# 检查实时带宽使用
curl -u admin:password http://localhost:8080/stats | jq '.bandwidth'

# 检查各站点流量分布
curl -u admin:password http://localhost:8080/stats | jq '.sites[] | {name: .name, bytes_sent: .bytes_sent, bytes_received: .bytes_received}'
```

## 🚀 最佳实践

### 1. 监控仪表板
```json
{
  "dashboard": {
    "panels": [
      {
        "title": "QPS趋势",
        "type": "line_chart",
        "metrics": ["current_qps", "average_qps"],
        "time_range": "1h"
      },
      {
        "title": "响应时间分布",
        "type": "histogram",
        "metrics": ["p50", "p95", "p99"],
        "time_range": "1h"
      },
      {
        "title": "错误率",
        "type": "gauge",
        "metrics": ["error_rate"],
        "threshold": 0.05
      }
    ]
  }
}
```

### 2. 告警规则
```yaml
# Prometheus告警规则
groups:
  - name: reverse_proxy_alerts
    rules:
      - alert: HighErrorRate
        expr: rate(reverse_proxy_errors_total[5m]) / rate(reverse_proxy_requests_total[5m]) > 0.05
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "错误率过高"
          description: "站点 {{ $labels.site }} 错误率超过5%"
      
      - alert: SlowResponseTime
        expr: histogram_quantile(0.99, rate(reverse_proxy_response_time_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "响应时间过慢"
          description: "P99响应时间超过1秒"
```

### 3. 自动化运维
```go
type AutoOpsManager struct {
    monitor *Monitor
    config  *AutoOpsConfig
}

func (aom *AutoOpsManager) AutoScale() {
    stats := aom.monitor.GetStats()
    
    // 根据QPS自动调整上游服务器权重
    if stats.Global.CurrentQPS > aom.config.ScaleUpThreshold {
        aom.scaleUpUpstreams()
    }
    
    // 根据错误率自动切换上游服务器
    if stats.Global.ErrorRate > aom.config.FailoverThreshold {
        aom.failoverUpstreams()
    }
}
```

## 📈 性能分析

### 监控数据分析
```go
type PerformanceAnalyzer struct {
    dataCollector *DataCollector
    analyzer      *TrendAnalyzer
}

func (pa *PerformanceAnalyzer) AnalyzePerformance() *AnalysisReport {
    // 收集历史数据
    data := pa.dataCollector.GetHistoricalData(24 * time.Hour)
    
    // 分析趋势
    trends := pa.analyzer.AnalyzeTrends(data)
    
    // 生成报告
    return &AnalysisReport{
        QPSTrend:          trends.QPS,
        ResponseTimeTrend: trends.ResponseTime,
        ErrorRateTrend:    trends.ErrorRate,
        Recommendations:   pa.generateRecommendations(trends),
    }
}
```

### 容量规划
```go
func (pa *PerformanceAnalyzer) CapacityPlanning() *CapacityReport {
    currentStats := pa.monitor.GetCurrentStats()
    historicalData := pa.dataCollector.GetHistoricalData(7 * 24 * time.Hour)
    
    // 预测未来负载
    predictedLoad := pa.predictFutureLoad(historicalData)
    
    // 计算所需容量
    requiredCapacity := pa.calculateRequiredCapacity(predictedLoad)
    
    return &CapacityReport{
        CurrentCapacity:  currentStats.MaxQPS,
        PredictedLoad:    predictedLoad,
        RequiredCapacity: requiredCapacity,
        ScalingAdvice:    pa.generateScalingAdvice(requiredCapacity),
    }
}
```

## 🔧 高级功能

### 1. 分布式追踪
```go
type DistributedTracing struct {
    tracer opentracing.Tracer
}

func (dt *DistributedTracing) TraceRequest(r *http.Request) opentracing.Span {
    span := dt.tracer.StartSpan("reverse_proxy_request")
    span.SetTag("http.method", r.Method)
    span.SetTag("http.url", r.URL.String())
    return span
}
```

### 2. 自定义指标
```go
type CustomMetrics struct {
    registry *prometheus.Registry
    counters map[string]prometheus.Counter
    gauges   map[string]prometheus.Gauge
}

func (cm *CustomMetrics) RegisterCustomMetric(name, help string, metricType string) {
    switch metricType {
    case "counter":
        counter := prometheus.NewCounter(prometheus.CounterOpts{
            Name: name,
            Help: help,
        })
        cm.registry.MustRegister(counter)
        cm.counters[name] = counter
    case "gauge":
        gauge := prometheus.NewGauge(prometheus.GaugeOpts{
            Name: name,
            Help: help,
        })
        cm.registry.MustRegister(gauge)
        cm.gauges[name] = gauge
    }
}
```

### 3. 实时告警
```go
type RealTimeAlerting struct {
    rules    []AlertRule
    notifier *AlertNotifier
}

func (rta *RealTimeAlerting) CheckAlerts(stats *Stats) {
    for _, rule := range rta.rules {
        if rule.Evaluate(stats) {
            alert := &Alert{
                Rule:      rule,
                Timestamp: time.Now(),
                Value:     rule.GetCurrentValue(stats),
            }
            rta.notifier.SendAlert(alert)
        }
    }
}
```

监控和指标增强是系统运维的重要基础，全面的监控体系可以帮助及时发现问题、优化性能和保障服务质量。

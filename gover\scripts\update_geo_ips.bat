@echo off
setlocal enabledelayedexpansion

REM 地域IP列表更新脚本 (Windows版本)
REM 用于自动更新国家/地区IP段列表

set ACL_DIR=acl
set TEMP_DIR=%TEMP%\geo_ips
set LOG_FILE=logs\geo_update.log

REM 创建必要目录
if not exist "%ACL_DIR%" mkdir "%ACL_DIR%"
if not exist "%TEMP_DIR%" mkdir "%TEMP_DIR%"
if not exist "logs" mkdir "logs"

REM 日志函数
call :log "开始更新地域IP列表..."

REM 检查curl是否可用
curl --version >nul 2>&1
if errorlevel 1 (
    call :log "ERROR: curl 未安装或不可用"
    exit /b 1
)

REM 更新恶意IP列表
call :update_malicious_ips

REM 下载中国IP段（示例）
call :download_country_ips "cn" "%ACL_DIR%\china_ips.txt"

REM 下载美国IP段（示例）
call :download_country_ips "us" "%ACL_DIR%\us_ips.txt"

REM 创建合并的封锁列表
call :create_blocked_list

REM 显示统计信息
call :show_statistics

REM 清理临时文件
rmdir /s /q "%TEMP_DIR%" 2>nul

call :log "地域IP列表更新完成"
goto :eof

REM ===== 函数定义 =====

:log
echo [%date% %time%] %~1
echo [%date% %time%] %~1 >> "%LOG_FILE%"
goto :eof

:download_country_ips
set country=%~1
set output_file=%~2
set url=https://www.ipdeny.com/ipblocks/data/countries/%country%.zone

call :log "下载 %country% IP列表..."

curl -s -f "%url%" -o "%TEMP_DIR%\%country%.zone"
if errorlevel 1 (
    call :log "ERROR: 下载 %country% IP列表失败"
    goto :eof
)

REM 创建带注释的文件
(
    echo # %country% 国家IP段列表
    echo # 更新时间: %date% %time%
    echo # 数据源: ipdeny.com
    echo.
    type "%TEMP_DIR%\%country%.zone"
) > "%output_file%"

REM 统计行数
for /f %%i in ('type "%output_file%" ^| find /c /v ""') do set count=%%i
call :log "成功更新 %country% IP列表，共 !count! 行"
goto :eof

:update_malicious_ips
set output_file=%ACL_DIR%\malicious_ips.txt

call :log "更新恶意IP列表..."

(
    echo # 恶意IP列表
    echo # 更新时间: %date% %time%
    echo # 包含已知攻击者、僵尸网络、恶意扫描器等
    echo.
    echo # 已知攻击IP
    echo *******
    echo *******
    echo **********
    echo.
    echo # 恶意网段
    echo ************/24
    echo **********/24
) > "%output_file%"

call :log "恶意IP列表更新完成"
goto :eof

:create_blocked_list
set output_file=%ACL_DIR%\blocked_countries.txt

call :log "创建封锁国家IP列表..."

(
    echo # 封锁国家IP列表
    echo # 更新时间: %date% %time%
    echo # 包含需要封锁的国家/地区IP段
    echo.
    echo # 示例：如果需要封锁特定国家，取消下面的注释
    echo # 并将对应的IP段文件内容复制到这里
    echo.
) > "%output_file%"

call :log "封锁国家IP列表创建完成"
goto :eof

:show_statistics
call :log "=== IP列表统计 ==="

for %%f in ("%ACL_DIR%\*.txt") do (
    set file=%%f
    for /f %%i in ('type "!file!" ^| findstr /v "^#" ^| findstr /v "^$" ^| find /c /v ""') do (
        call :log "%%~nxf: %%i 个IP/网段"
    )
)
goto :eof

package cache

import (
	"container/heap"
	"context"
	"encoding/gob"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"sync/atomic"
	"time"

	"reverse-proxy/internal/config"
)

// CacheEntry 缓存条目信息
type CacheEntry struct {
	Path        string
	Size        int64
	ModTime     time.Time
	ExpireAt    time.Time
	AccessCount int64 // LRU计数
	LastAccess  time.Time
}

// CacheEntryHeap 缓存条目最小堆（用于LRU）
type CacheEntryHeap []*CacheEntry

func (h CacheEntryHeap) Len() int           { return len(h) }
func (h CacheEntryHeap) Less(i, j int) bool { return h[i].LastAccess.Before(h[j].LastAccess) }
func (h CacheEntryHeap) Swap(i, j int)      { h[i], h[j] = h[j], h[i] }

func (h *CacheEntryHeap) Push(x interface{}) {
	*h = append(*h, x.(*CacheEntry))
}

func (h *CacheEntryHeap) Pop() interface{} {
	old := *h
	n := len(old)
	item := old[n-1]
	*h = old[0 : n-1]
	return item
}

// CacheCleaner 缓存清理器
type CacheCleaner struct {
	config      config.CacheConfig
	cachePath   string
	maxSize     int64
	currentSize *int64 // 指向FileCache的currentSize

	// 清理策略配置
	cleanupInterval      time.Duration
	expiredCheckInterval time.Duration

	// 运行时状态
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
	mu     sync.RWMutex

	// 统计信息
	totalCleanups   int64
	expiredCleaned  int64
	sizeCleaned     int64
	lastCleanupTime time.Time

	// 缓存条目索引（用于快速查找和LRU）
	entryIndex map[string]*CacheEntry
	lruHeap    *CacheEntryHeap
}

// NewCacheCleaner 创建缓存清理器
func NewCacheCleaner(cfg config.CacheConfig, cachePath string, currentSize *int64) *CacheCleaner {
	ctx, cancel := context.WithCancel(context.Background())

	// 设置默认清理间隔
	cleanupInterval := 5 * time.Minute
	expiredCheckInterval := 1 * time.Minute

	// 如果配置中有自定义间隔，使用配置值
	if cfg.CleanupInterval > 0 {
		cleanupInterval = cfg.CleanupInterval
	}
	if cfg.ExpiredCheckInterval > 0 {
		expiredCheckInterval = cfg.ExpiredCheckInterval
	}

	lruHeap := &CacheEntryHeap{}
	heap.Init(lruHeap)

	cleaner := &CacheCleaner{
		config:               cfg,
		cachePath:            cachePath,
		maxSize:              int64(cfg.MaxSize),
		currentSize:          currentSize,
		cleanupInterval:      cleanupInterval,
		expiredCheckInterval: expiredCheckInterval,
		ctx:                  ctx,
		cancel:               cancel,
		entryIndex:           make(map[string]*CacheEntry),
		lruHeap:              lruHeap,
	}

	return cleaner
}

// Start 启动清理器
func (cc *CacheCleaner) Start() {
	// 初始扫描建立索引
	cc.buildIndex()

	// 启动定期清理协程
	cc.wg.Add(2)
	go cc.periodicCleanup()
	go cc.expiredCleanup()
}

// Stop 停止清理器
func (cc *CacheCleaner) Stop() {
	cc.cancel()
	cc.wg.Wait()
}

// buildIndex 构建缓存文件索引
func (cc *CacheCleaner) buildIndex() {
	cc.mu.Lock()
	defer cc.mu.Unlock()

	cc.entryIndex = make(map[string]*CacheEntry)
	cc.lruHeap = &CacheEntryHeap{}
	heap.Init(cc.lruHeap)

	filepath.Walk(cc.cachePath, func(path string, info os.FileInfo, err error) error {
		if err != nil || info.IsDir() {
			return nil
		}

		// 尝试读取缓存文件获取过期时间
		expireAt := cc.getFileExpireTime(path)

		entry := &CacheEntry{
			Path:        path,
			Size:        info.Size(),
			ModTime:     info.ModTime(),
			ExpireAt:    expireAt,
			LastAccess:  info.ModTime(), // 初始使用修改时间
			AccessCount: 0,
		}

		cc.entryIndex[path] = entry
		heap.Push(cc.lruHeap, entry)

		return nil
	})
}

// getFileExpireTime 获取缓存文件的过期时间
func (cc *CacheCleaner) getFileExpireTime(filePath string) time.Time {
	file, err := os.Open(filePath)
	if err != nil {
		return time.Now().Add(-time.Hour) // 默认已过期
	}
	defer file.Close()

	var response CachedResponse
	decoder := gob.NewDecoder(file)
	if err := decoder.Decode(&response); err != nil {
		return time.Now().Add(-time.Hour) // 解码失败，标记为过期
	}

	return response.ExpireAt
}

// periodicCleanup 定期清理
func (cc *CacheCleaner) periodicCleanup() {
	defer cc.wg.Done()

	ticker := time.NewTicker(cc.cleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-cc.ctx.Done():
			return
		case <-ticker.C:
			cc.performSizeBasedCleanup()
		}
	}
}

// expiredCleanup 过期文件清理
func (cc *CacheCleaner) expiredCleanup() {
	defer cc.wg.Done()

	ticker := time.NewTicker(cc.expiredCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-cc.ctx.Done():
			return
		case <-ticker.C:
			cc.cleanExpiredFiles()
		}
	}
}

// performSizeBasedCleanup 基于大小的清理
func (cc *CacheCleaner) performSizeBasedCleanup() {
	currentSize := atomic.LoadInt64(cc.currentSize)
	if currentSize <= cc.maxSize {
		return
	}

	cc.mu.Lock()
	defer cc.mu.Unlock()

	// 目标大小：保留80%空间
	targetSize := cc.maxSize * 8 / 10
	needToFree := currentSize - targetSize

	var freedSize int64
	var cleanedFiles []string

	// 使用LRU策略清理
	for cc.lruHeap.Len() > 0 && freedSize < needToFree {
		entry := heap.Pop(cc.lruHeap).(*CacheEntry)

		if err := os.Remove(entry.Path); err == nil {
			freedSize += entry.Size
			cleanedFiles = append(cleanedFiles, entry.Path)
			delete(cc.entryIndex, entry.Path)
		}
	}

	// 更新大小统计
	atomic.AddInt64(cc.currentSize, -freedSize)
	atomic.AddInt64(&cc.sizeCleaned, int64(len(cleanedFiles)))
	atomic.AddInt64(&cc.totalCleanups, 1)
	cc.lastCleanupTime = time.Now()

	if len(cleanedFiles) > 0 {
		fmt.Printf("[缓存清理] 基于大小清理了 %d 个文件，释放 %d 字节\n", len(cleanedFiles), freedSize)
	}
}

// cleanExpiredFiles 清理过期文件
func (cc *CacheCleaner) cleanExpiredFiles() {
	cc.mu.Lock()
	defer cc.mu.Unlock()

	now := time.Now()
	var expiredFiles []string
	var freedSize int64

	// 检查所有文件是否过期
	for path, entry := range cc.entryIndex {
		if now.After(entry.ExpireAt) {
			if err := os.Remove(path); err == nil {
				expiredFiles = append(expiredFiles, path)
				freedSize += entry.Size

				// 从索引中移除
				delete(cc.entryIndex, path)

				// 从堆中移除（标记为无效）
				entry.Path = "" // 标记为已删除
			}
		}
	}

	// 重建堆（移除已删除的条目）
	if len(expiredFiles) > 0 {
		cc.rebuildHeap()

		// 更新统计
		atomic.AddInt64(cc.currentSize, -freedSize)
		atomic.AddInt64(&cc.expiredCleaned, int64(len(expiredFiles)))

		fmt.Printf("[缓存清理] 清理了 %d 个过期文件，释放 %d 字节\n", len(expiredFiles), freedSize)
	}
}

// rebuildHeap 重建LRU堆
func (cc *CacheCleaner) rebuildHeap() {
	cc.lruHeap = &CacheEntryHeap{}
	heap.Init(cc.lruHeap)

	for _, entry := range cc.entryIndex {
		if entry.Path != "" { // 未被删除的条目
			heap.Push(cc.lruHeap, entry)
		}
	}
}

// UpdateAccess 更新文件访问信息
func (cc *CacheCleaner) UpdateAccess(filePath string) {
	cc.mu.Lock()
	defer cc.mu.Unlock()

	if entry, exists := cc.entryIndex[filePath]; exists {
		entry.LastAccess = time.Now()
		entry.AccessCount++

		// 重新调整堆（简单方式：重建堆）
		// 在高频访问场景下，可以考虑延迟重建
		heap.Init(cc.lruHeap)
	}
}

// AddFile 添加新文件到索引
func (cc *CacheCleaner) AddFile(filePath string, size int64, expireAt time.Time) {
	cc.mu.Lock()
	defer cc.mu.Unlock()

	entry := &CacheEntry{
		Path:        filePath,
		Size:        size,
		ModTime:     time.Now(),
		ExpireAt:    expireAt,
		LastAccess:  time.Now(),
		AccessCount: 1,
	}

	cc.entryIndex[filePath] = entry
	heap.Push(cc.lruHeap, entry)
}

// RemoveFile 从索引中移除文件
func (cc *CacheCleaner) RemoveFile(filePath string) {
	cc.mu.Lock()
	defer cc.mu.Unlock()

	if entry, exists := cc.entryIndex[filePath]; exists {
		delete(cc.entryIndex, filePath)
		entry.Path = "" // 标记为已删除
		// 注意：这里不立即重建堆，而是在下次清理时重建
	}
}

// GetStats 获取清理器统计信息
func (cc *CacheCleaner) GetStats() map[string]interface{} {
	cc.mu.RLock()
	defer cc.mu.RUnlock()

	return map[string]interface{}{
		"total_cleanups":         atomic.LoadInt64(&cc.totalCleanups),
		"expired_cleaned":        atomic.LoadInt64(&cc.expiredCleaned),
		"size_cleaned":           atomic.LoadInt64(&cc.sizeCleaned),
		"last_cleanup_time":      cc.lastCleanupTime,
		"indexed_files":          len(cc.entryIndex),
		"heap_size":              cc.lruHeap.Len(),
		"cleanup_interval":       cc.cleanupInterval,
		"expired_check_interval": cc.expiredCheckInterval,
	}
}

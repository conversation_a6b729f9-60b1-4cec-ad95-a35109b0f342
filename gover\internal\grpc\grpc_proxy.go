package grpc

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"strings"
	"sync"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/keepalive"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"reverse-proxy/internal/config"
	"reverse-proxy/internal/logger"
)

// GRPCProxy gRPC代理管理器
type GRPCProxy struct {
	connections map[string]*grpc.ClientConn
	mu          sync.RWMutex
	config      GRPCConfig
	stats       *GRPCStats
}

// GRPCConfig gRPC代理配置
type GRPCConfig struct {
	Enabled        bool          `json:"enabled"`
	MaxRecvMsgSize int           `json:"max_recv_msg_size"` // 最大接收消息大小 (兼容性保留)
	MaxSendMsgSize int           `json:"max_send_msg_size"` // 最大发送消息大小 (兼容性保留)
	Timeout        time.Duration `json:"timeout"`           // 请求超时
	KeepAlive      bool          `json:"keep_alive"`        // 保持连接
	Compression    string        `json:"compression"`       // 压缩算法
	LoadBalancing  string        `json:"load_balancing"`    // 负载均衡策略
}

// GRPCStats gRPC统计信息
type GRPCStats struct {
	TotalRequests   int64            `json:"total_requests"`
	SuccessRequests int64            `json:"success_requests"`
	ErrorRequests   int64            `json:"error_requests"`
	StreamRequests  int64            `json:"stream_requests"`
	UnaryRequests   int64            `json:"unary_requests"`
	ActiveStreams   int64            `json:"active_streams"`
	ConnectionStats map[string]int64 `json:"connection_stats"`
	mu              sync.RWMutex
}

// NewGRPCProxy 创建gRPC代理
func NewGRPCProxy(config GRPCConfig) *GRPCProxy {
	if config.MaxRecvMsgSize == 0 {
		config.MaxRecvMsgSize = 4 * 1024 * 1024 // 4MB
	}
	if config.MaxSendMsgSize == 0 {
		config.MaxSendMsgSize = 4 * 1024 * 1024 // 4MB
	}
	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second
	}

	return &GRPCProxy{
		connections: make(map[string]*grpc.ClientConn),
		config:      config,
		stats: &GRPCStats{
			ConnectionStats: make(map[string]int64),
		},
	}
}

// IsGRPCRequest 检测是否为gRPC请求
func (gp *GRPCProxy) IsGRPCRequest(r *http.Request) bool {
	// gRPC请求必须是HTTP/2
	if r.ProtoMajor != 2 {
		return false
	}

	// 检查Content-Type
	contentType := r.Header.Get("Content-Type")
	if !strings.HasPrefix(contentType, "application/grpc") {
		return false
	}

	// 检查方法
	if r.Method != "POST" {
		return false
	}

	// 检查路径格式 /service/method
	if !strings.HasPrefix(r.URL.Path, "/") || strings.Count(r.URL.Path, "/") < 2 {
		return false
	}

	return true
}

// ProxyGRPCRequest 代理gRPC请求
func (gp *GRPCProxy) ProxyGRPCRequest(w http.ResponseWriter, r *http.Request, upstream *config.UpstreamConfig) error {
	if !gp.config.Enabled {
		return fmt.Errorf("gRPC proxy is disabled")
	}

	// 更新统计
	gp.stats.mu.Lock()
	gp.stats.TotalRequests++
	gp.stats.mu.Unlock()

	// 获取连接
	conn, err := gp.getConnection(upstream)
	if err != nil {
		gp.recordError()
		return fmt.Errorf("failed to get gRPC connection: %w", err)
	}

	// 检测调用类型
	if gp.isStreamingCall(r) {
		return gp.handleStreamingCall(w, r, conn)
	} else {
		return gp.handleUnaryCall(w, r, conn)
	}
}

// getConnection 获取或创建gRPC连接
func (gp *GRPCProxy) getConnection(upstream *config.UpstreamConfig) (*grpc.ClientConn, error) {
	target := fmt.Sprintf("%s:%d", upstream.Address, upstream.Port)

	gp.mu.RLock()
	if conn, exists := gp.connections[target]; exists {
		// 检查连接状态
		if conn.GetState().String() != "SHUTDOWN" {
			gp.mu.RUnlock()
			return conn, nil
		}
	}
	gp.mu.RUnlock()

	// 创建新连接
	gp.mu.Lock()
	defer gp.mu.Unlock()

	// 双重检查
	if conn, exists := gp.connections[target]; exists {
		if conn.GetState().String() != "SHUTDOWN" {
			return conn, nil
		}
		// 关闭旧连接
		conn.Close()
	}

	// 创建连接选项
	opts := []grpc.DialOption{
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithDefaultCallOptions(
			grpc.MaxCallRecvMsgSize(gp.config.MaxRecvMsgSize),
			grpc.MaxCallSendMsgSize(gp.config.MaxSendMsgSize),
		),
	}

	// 添加保持连接选项
	if gp.config.KeepAlive {
		opts = append(opts, grpc.WithKeepaliveParams(keepalive.ClientParameters{
			Time:                30 * time.Second,
			Timeout:             5 * time.Second,
			PermitWithoutStream: true,
		}))
	}

	// 建立连接
	conn, err := grpc.Dial(target, opts...)
	if err != nil {
		return nil, err
	}

	gp.connections[target] = conn

	// 更新连接统计
	gp.stats.mu.Lock()
	gp.stats.ConnectionStats[target]++
	gp.stats.mu.Unlock()

	logger.Info("Created new gRPC connection to:", target)
	return conn, nil
}

// isStreamingCall 检测是否为流式调用
func (gp *GRPCProxy) isStreamingCall(r *http.Request) bool {
	// 简单检测：如果有grpc-encoding头，通常是流式调用
	// 更准确的检测需要解析protobuf定义
	encoding := r.Header.Get("grpc-encoding")
	return encoding != ""
}

// handleUnaryCall 处理一元调用
func (gp *GRPCProxy) handleUnaryCall(w http.ResponseWriter, r *http.Request, conn *grpc.ClientConn) error {
	gp.stats.mu.Lock()
	gp.stats.UnaryRequests++
	gp.stats.mu.Unlock()

	// 创建上下文
	ctx, cancel := context.WithTimeout(r.Context(), gp.config.Timeout)
	defer cancel()

	// 提取元数据
	md := gp.extractMetadata(r)
	ctx = metadata.NewOutgoingContext(ctx, md)

	// 读取请求体
	body, err := io.ReadAll(r.Body)
	if err != nil {
		gp.recordError()
		return fmt.Errorf("failed to read request body: %w", err)
	}

	// 调用远程方法
	method := r.URL.Path
	var response []byte
	err = conn.Invoke(ctx, method, body, &response)
	if err != nil {
		gp.recordError()
		return gp.handleGRPCError(w, err)
	}

	// 设置响应头
	w.Header().Set("Content-Type", "application/grpc")
	w.Header().Set("grpc-status", "0")

	// 写入响应
	w.WriteHeader(http.StatusOK)
	w.Write(response)

	gp.recordSuccess()
	return nil
}

// handleStreamingCall 处理流式调用
func (gp *GRPCProxy) handleStreamingCall(w http.ResponseWriter, r *http.Request, conn *grpc.ClientConn) error {
	gp.stats.mu.Lock()
	gp.stats.StreamRequests++
	gp.stats.ActiveStreams++
	gp.stats.mu.Unlock()

	defer func() {
		gp.stats.mu.Lock()
		gp.stats.ActiveStreams--
		gp.stats.mu.Unlock()
	}()

	// 创建上下文
	ctx, cancel := context.WithCancel(r.Context())
	defer cancel()

	// 提取元数据
	md := gp.extractMetadata(r)
	ctx = metadata.NewOutgoingContext(ctx, md)

	// 创建流
	method := r.URL.Path
	stream, err := conn.NewStream(ctx, &grpc.StreamDesc{
		StreamName:    method,
		ServerStreams: true,
		ClientStreams: true,
	}, method)
	if err != nil {
		gp.recordError()
		return fmt.Errorf("failed to create stream: %w", err)
	}

	// 设置响应头
	w.Header().Set("Content-Type", "application/grpc")

	// 启动双向流处理
	errCh := make(chan error, 2)

	// 客户端到服务器
	go func() {
		defer stream.CloseSend()
		// 读取请求体并发送到流
		buffer := make([]byte, 32*1024)
		for {
			n, err := r.Body.Read(buffer)
			if n > 0 {
				if sendErr := stream.SendMsg(buffer[:n]); sendErr != nil {
					errCh <- sendErr
					return
				}
			}
			if err != nil {
				if err != io.EOF {
					errCh <- err
				} else {
					errCh <- nil
				}
				return
			}
		}
	}()

	// 服务器到客户端
	go func() {
		// 从流接收数据并写入响应
		for {
			var msg []byte
			if err := stream.RecvMsg(&msg); err != nil {
				if err != io.EOF {
					errCh <- err
				} else {
					errCh <- nil
				}
				return
			}
			if _, writeErr := w.Write(msg); writeErr != nil {
				errCh <- writeErr
				return
			}
		}
	}()

	// 等待任一方向完成
	err = <-errCh
	if err != nil {
		gp.recordError()
		return fmt.Errorf("stream error: %w", err)
	}

	gp.recordSuccess()
	return nil
}

// extractMetadata 提取gRPC元数据
func (gp *GRPCProxy) extractMetadata(r *http.Request) metadata.MD {
	md := metadata.New(nil)

	for key, values := range r.Header {
		// 跳过HTTP/2伪头部
		if strings.HasPrefix(key, ":") {
			continue
		}

		// 转换为小写（gRPC元数据键必须小写）
		key = strings.ToLower(key)

		// 添加到元数据
		for _, value := range values {
			md.Append(key, value)
		}
	}

	return md
}

// handleGRPCError 处理gRPC错误
func (gp *GRPCProxy) handleGRPCError(w http.ResponseWriter, err error) error {
	st, ok := status.FromError(err)
	if !ok {
		// 非gRPC错误
		w.Header().Set("grpc-status", fmt.Sprintf("%d", codes.Internal))
		w.Header().Set("grpc-message", err.Error())
		w.WriteHeader(http.StatusInternalServerError)
		return nil
	}

	// gRPC错误
	w.Header().Set("grpc-status", fmt.Sprintf("%d", st.Code()))
	w.Header().Set("grpc-message", st.Message())

	// 根据gRPC状态码设置HTTP状态码
	httpStatus := grpcToHTTPStatus(st.Code())
	w.WriteHeader(httpStatus)

	return nil
}

// grpcToHTTPStatus 将gRPC状态码转换为HTTP状态码
func grpcToHTTPStatus(code codes.Code) int {
	switch code {
	case codes.OK:
		return http.StatusOK
	case codes.Canceled:
		return http.StatusRequestTimeout
	case codes.Unknown:
		return http.StatusInternalServerError
	case codes.InvalidArgument:
		return http.StatusBadRequest
	case codes.DeadlineExceeded:
		return http.StatusGatewayTimeout
	case codes.NotFound:
		return http.StatusNotFound
	case codes.AlreadyExists:
		return http.StatusConflict
	case codes.PermissionDenied:
		return http.StatusForbidden
	case codes.Unauthenticated:
		return http.StatusUnauthorized
	case codes.ResourceExhausted:
		return http.StatusTooManyRequests
	case codes.FailedPrecondition:
		return http.StatusBadRequest
	case codes.Aborted:
		return http.StatusConflict
	case codes.OutOfRange:
		return http.StatusBadRequest
	case codes.Unimplemented:
		return http.StatusNotImplemented
	case codes.Internal:
		return http.StatusInternalServerError
	case codes.Unavailable:
		return http.StatusServiceUnavailable
	case codes.DataLoss:
		return http.StatusInternalServerError
	default:
		return http.StatusInternalServerError
	}
}

// recordSuccess 记录成功
func (gp *GRPCProxy) recordSuccess() {
	gp.stats.mu.Lock()
	gp.stats.SuccessRequests++
	gp.stats.mu.Unlock()
}

// recordError 记录错误
func (gp *GRPCProxy) recordError() {
	gp.stats.mu.Lock()
	gp.stats.ErrorRequests++
	gp.stats.mu.Unlock()
}

// GetStats 获取统计信息
func (gp *GRPCProxy) GetStats() *GRPCStats {
	gp.stats.mu.RLock()
	defer gp.stats.mu.RUnlock()

	stats := &GRPCStats{
		TotalRequests:   gp.stats.TotalRequests,
		SuccessRequests: gp.stats.SuccessRequests,
		ErrorRequests:   gp.stats.ErrorRequests,
		StreamRequests:  gp.stats.StreamRequests,
		UnaryRequests:   gp.stats.UnaryRequests,
		ActiveStreams:   gp.stats.ActiveStreams,
		ConnectionStats: make(map[string]int64),
	}

	for k, v := range gp.stats.ConnectionStats {
		stats.ConnectionStats[k] = v
	}

	return stats
}

// Close 关闭所有连接
func (gp *GRPCProxy) Close() error {
	gp.mu.Lock()
	defer gp.mu.Unlock()

	for target, conn := range gp.connections {
		if err := conn.Close(); err != nil {
			logger.Warn("Failed to close gRPC connection to", target, ":", err)
		}
	}

	gp.connections = make(map[string]*grpc.ClientConn)
	return nil
}

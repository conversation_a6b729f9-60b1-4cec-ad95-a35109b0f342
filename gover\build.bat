@echo off
chcp 936 > nul
setlocal enabledelayedexpansion

echo ��ʼ����...

:: ����buildĿ¼
if not exist build mkdir build

:: ���ð汾��
set VERSION=1.0.0

:: ���ñ���ʱ��
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "BUILD_TIME=%dt:~0,8% %dt:~8,6%"

:: ���ñ������
set LDFLAGS=-s -w -X main.Version=%VERSION% -X main.BuildTime=%BUILD_TIME%


:: Windows amd64 win7 (Go 1.20 - 包含循环变量陷阱修复)
echo ���� Windows amd64 win7...
set GOOS=windows
set GOARCH=amd64
copy /y go1.20.mod go.mod
go1.20.14 mod download
go1.20.14 mod tidy
go1.20.14 build -o build\CacheServer_windows_amd64_win7.exe main.go
if errorlevel 1 (
    echo Windows amd64 ����ʧ�ܣ�
    goto :error
)


:: Windows amd64 (Go 1.24 - 包含循环变量陷阱修复)
echo ���� Windows amd64...
set GOOS=windows
set GOARCH=amd64

copy /y go1.24.mod go.mod
go mod download
go mod tidy
go build -o build\CacheServer_windows_amd64.exe main.go

if errorlevel 1 (
    echo Windows amd64 ����ʧ�ܣ�
    goto :error
)

:: Linux amd64
echo ���� Linux amd64...
set GOOS=linux
set GOARCH=amd64
go build -o build\CacheServer_linux_amd64 main.go
if errorlevel 1 (
    echo Linux amd64 ����ʧ�ܣ�
    goto :error
)

:: FreeBSD amd64
echo ���� FreeBSD amd64...
set GOOS=freebsd
set GOARCH=amd64
go build -o build\CacheServer_freebsd_amd64 main.go
if errorlevel 1 (
    echo FreeBSD amd64 ����ʧ�ܣ�
    goto :error
)

:: ���������ļ���buildĿ¼
echo ���������ļ�...
copy config.json build\
if errorlevel 1 (
    echo ���������ļ�ʧ�ܣ�
    goto :error
)


echo ������ɣ�
echo ������λ�� build Ŀ¼
goto :end

:error
echo ��������г��ִ���
exit /b 1

:end
pause 
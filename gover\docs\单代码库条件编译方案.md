# 单代码库条件编译方案

## 🎯 **设计理念**

### **核心原则**
- **单一代码库**：所有代码在同一个仓库中维护
- **条件编译**：使用Go build tags控制功能包含
- **接口隔离**：Pro版功能通过接口隔离，Core版看不到实现
- **配置驱动**：通过配置控制功能启用/禁用

### **优势**
- ✅ **代码重用**：核心功能只需维护一份
- ✅ **Bug修复一致**：修复一次，两个版本都受益
- ✅ **测试统一**：共享测试用例和测试基础设施
- ✅ **文档一致**：单一文档源，自动生成不同版本文档
- ✅ **发布同步**：确保两个版本功能同步

## 🏗️ **技术架构设计**

### **目录结构**
```
reverse-proxy/
├── cmd/
│   ├── core/               # Core版主程序
│   │   └── main.go
│   └── pro/                # Pro版主程序
│       └── main.go
├── internal/
│   ├── core/               # 核心功能 (两版本共享)
│   │   ├── proxy/          # 基础代理
│   │   ├── config/         # 配置管理
│   │   ├── cache/          # 基础缓存
│   │   ├── loadbalancer/   # 基础负载均衡
│   │   ├── monitor/        # 基础监控
│   │   └── logger/         # 日志系统
│   ├── pro/                # Pro版独有功能
│   │   ├── performance/    # 高性能引擎
│   │   ├── protocols/      # 高级协议
│   │   ├── smartlb/        # 智能负载均衡
│   │   ├── security/       # 企业级安全
│   │   ├── monitoring/     # 高级监控
│   │   └── license/        # 许可证验证
│   └── shared/             # 共享接口和类型
│       ├── interfaces/     # 接口定义
│       ├── types/          # 类型定义
│       └── utils/          # 工具函数
├── pkg/
│   └── api/                # 公共API
├── configs/
│   ├── core.json           # Core版配置示例
│   └── pro.json            # Pro版配置示例
├── build/
│   ├── core.mk             # Core版构建脚本
│   ├── pro.mk              # Pro版构建脚本
│   └── common.mk           # 共享构建脚本
└── docs/                   # 文档
```

## 🔧 **条件编译实现**

### **构建标签策略**
```go
// 核心功能文件 (两版本都包含)
// +build core pro

// Pro版独有功能文件
// +build pro

// Core版特定文件 (如果需要)
// +build core

// 许可证相关文件
// +build pro,!core
```

### **接口隔离设计**
```go
// internal/shared/interfaces/performance.go
// +build core pro

package interfaces

// PerformanceEngine 性能引擎接口
type PerformanceEngine interface {
    HandleRequest(ctx *RequestContext) error
    GetStats() *PerformanceStats
    IsEnabled() bool
}

// 在Core版中提供空实现
// internal/core/performance/stub.go
// +build core

package performance

import "reverse-proxy/internal/shared/interfaces"

type StubPerformanceEngine struct{}

func NewPerformanceEngine() interfaces.PerformanceEngine {
    return &StubPerformanceEngine{}
}

func (s *StubPerformanceEngine) HandleRequest(ctx *interfaces.RequestContext) error {
    return nil // 不处理，直接返回
}

func (s *StubPerformanceEngine) GetStats() *interfaces.PerformanceStats {
    return nil // Core版不提供性能统计
}

func (s *StubPerformanceEngine) IsEnabled() bool {
    return false // Core版中性能引擎未启用
}

// 在Pro版中提供真实实现
// internal/pro/performance/engine.go
// +build pro

package performance

import "reverse-proxy/internal/shared/interfaces"

type HighPerformanceEngine struct {
    zeroCopy    *ZeroCopyEngine
    objectPools *ObjectPools
    // ... 其他高性能组件
}

func NewPerformanceEngine() interfaces.PerformanceEngine {
    return &HighPerformanceEngine{
        zeroCopy:    NewZeroCopyEngine(),
        objectPools: NewObjectPools(),
    }
}

func (h *HighPerformanceEngine) HandleRequest(ctx *interfaces.RequestContext) error {
    // 真实的高性能处理逻辑
    return h.zeroCopy.Handle(ctx)
}

func (h *HighPerformanceEngine) GetStats() *interfaces.PerformanceStats {
    // 返回详细的性能统计
    return h.collectStats()
}

func (h *HighPerformanceEngine) IsEnabled() bool {
    return true
}
```

### **配置驱动功能控制**
```go
// internal/shared/types/config.go
// +build core pro

package types

type Config struct {
    // 核心配置 (两版本共享)
    Server    ServerConfig    `json:"server"`
    Sites     []SiteConfig    `json:"sites"`
    Log       LogConfig       `json:"log"`
    
    // 条件配置 (根据版本决定是否可用)
    Performance *PerformanceConfig `json:"performance,omitempty"`
    HTTP3       *HTTP3Config       `json:"http3,omitempty"`
    GRPC        *GRPCConfig        `json:"grpc,omitempty"`
    License     *LicenseConfig     `json:"license,omitempty"`
}

// 配置验证
func (c *Config) Validate() error {
    // 核心配置验证
    if err := c.validateCore(); err != nil {
        return err
    }
    
    // Pro版功能配置验证
    return c.validateProFeatures()
}

// internal/core/config/validation.go
// +build core

func (c *Config) validateProFeatures() error {
    // Core版中，如果配置了Pro功能，返回警告但不报错
    if c.Performance != nil {
        log.Warn("Performance features are not available in Core edition")
        c.Performance = nil
    }
    if c.HTTP3 != nil {
        log.Warn("HTTP/3 is not available in Core edition")
        c.HTTP3 = nil
    }
    return nil
}

// internal/pro/config/validation.go
// +build pro

func (c *Config) validateProFeatures() error {
    // Pro版中，验证Pro功能配置
    if c.Performance != nil {
        if err := c.Performance.Validate(); err != nil {
            return fmt.Errorf("performance config error: %w", err)
        }
    }
    if c.License == nil {
        return errors.New("license configuration is required for Pro edition")
    }
    return c.License.Validate()
}
```

### **主程序差异化**
```go
// cmd/core/main.go
// +build core

package main

import (
    "reverse-proxy/internal/core/proxy"
    "reverse-proxy/internal/core/performance" // Stub实现
)

var (
    Version = "1.0.0"
    Edition = "Core"
)

func main() {
    fmt.Printf("反向代理服务器 %s %s\n", Edition, Version)
    
    // 创建Core版代理
    perfEngine := performance.NewPerformanceEngine() // 返回Stub
    proxy := proxy.NewProxy(config, perfEngine)
    
    proxy.Start()
}

// cmd/pro/main.go
// +build pro

package main

import (
    "reverse-proxy/internal/core/proxy"
    "reverse-proxy/internal/pro/performance" // 真实实现
    "reverse-proxy/internal/pro/license"
)

var (
    Version = "1.0.0"
    Edition = "Pro"
)

func main() {
    fmt.Printf("反向代理服务器 %s %s\n", Edition, Version)
    
    // 许可证验证
    if err := license.Validate(); err != nil {
        log.Fatal("License validation failed:", err)
    }
    
    // 创建Pro版代理
    perfEngine := performance.NewPerformanceEngine() // 返回真实实现
    proxy := proxy.NewProxy(config, perfEngine)
    
    proxy.Start()
}
```

## 📦 **构建系统设计**

### **Makefile实现**
```makefile
# Makefile
include build/common.mk

# 版本信息
VERSION ?= 1.0.0
BUILD_TIME := $(shell date -u '+%Y-%m-%d_%H:%M:%S')
GIT_COMMIT := $(shell git rev-parse --short HEAD)

# 通用构建标志
LDFLAGS_COMMON := -s -w \
	-X main.Version=$(VERSION) \
	-X main.BuildTime=$(BUILD_TIME) \
	-X main.GitCommit=$(GIT_COMMIT)

.PHONY: build-core build-pro build-all

# 构建Core版
build-core:
	@echo "🔨 构建Core版..."
	go build -tags="core" \
		-ldflags="$(LDFLAGS_COMMON) -X main.Edition=Core" \
		-o bin/reverse-proxy-core \
		cmd/core/main.go

# 构建Pro版
build-pro:
	@echo "🚀 构建Pro版..."
	go build -tags="pro" \
		-ldflags="$(LDFLAGS_COMMON) -X main.Edition=Pro" \
		-o bin/reverse-proxy-pro \
		cmd/pro/main.go

# 构建所有版本
build-all: build-core build-pro

# 测试
test-core:
	go test -tags="core" ./...

test-pro:
	go test -tags="pro" ./...

test-all: test-core test-pro

# 功能验证
verify-separation:
	@echo "🔍 验证版本分离..."
	@echo "Core版大小: $$(ls -lh bin/reverse-proxy-core | awk '{print $$5}')"
	@echo "Pro版大小: $$(ls -lh bin/reverse-proxy-pro | awk '{print $$5}')"
	@echo "检查Core版是否包含Pro功能..."
	@if strings bin/reverse-proxy-core | grep -q "high.performance\|zero.copy\|grpc"; then \
		echo "❌ Core版意外包含Pro功能"; \
		exit 1; \
	else \
		echo "✅ Core版功能分离正确"; \
	fi
```

## 🔒 **Pro版功能保护**

### **接口隐藏策略**
```go
// internal/shared/interfaces/registry.go
// +build core pro

package interfaces

// FeatureRegistry 功能注册表
type FeatureRegistry interface {
    RegisterFeature(name string, feature interface{}) error
    GetFeature(name string) (interface{}, bool)
    ListFeatures() []string
}

// internal/core/registry/registry.go
// +build core

package registry

type CoreRegistry struct {
    features map[string]interface{}
}

func NewRegistry() interfaces.FeatureRegistry {
    return &CoreRegistry{
        features: make(map[string]interface{}),
    }
}

func (r *CoreRegistry) RegisterFeature(name string, feature interface{}) error {
    // Core版只允许注册基础功能
    allowedFeatures := []string{"basic-proxy", "file-cache", "round-robin"}
    for _, allowed := range allowedFeatures {
        if name == allowed {
            r.features[name] = feature
            return nil
        }
    }
    return fmt.Errorf("feature %s not available in Core edition", name)
}

func (r *CoreRegistry) ListFeatures() []string {
    // Core版只返回基础功能列表
    return []string{"basic-proxy", "file-cache", "round-robin"}
}

// internal/pro/registry/registry.go
// +build pro

package registry

type ProRegistry struct {
    features map[string]interface{}
}

func NewRegistry() interfaces.FeatureRegistry {
    return &ProRegistry{
        features: make(map[string]interface{}),
    }
}

func (r *ProRegistry) RegisterFeature(name string, feature interface{}) error {
    // Pro版允许注册所有功能
    r.features[name] = feature
    return nil
}

func (r *ProRegistry) ListFeatures() []string {
    // Pro版返回所有功能列表
    var features []string
    for name := range r.features {
        features = append(features, name)
    }
    return features
}
```

### **运行时功能检查**
```go
// internal/shared/utils/edition.go
// +build core pro

package utils

// IsProEdition 检查是否为Pro版
func IsProEdition() bool {
    return hasProFeatures()
}

// GetEditionInfo 获取版本信息
func GetEditionInfo() EditionInfo {
    return EditionInfo{
        Name:     getEditionName(),
        Features: getAvailableFeatures(),
        Limits:   getEditionLimits(),
    }
}

// internal/core/utils/edition.go
// +build core

func hasProFeatures() bool {
    return false
}

func getEditionName() string {
    return "Core"
}

func getAvailableFeatures() []string {
    return []string{"http-proxy", "file-cache", "basic-lb"}
}

func getEditionLimits() EditionLimits {
    return EditionLimits{
        MaxConnections: 10000,
        MaxUpstreams:   10,
        MaxSites:       5,
    }
}

// internal/pro/utils/edition.go
// +build pro

func hasProFeatures() bool {
    return true
}

func getEditionName() string {
    return "Pro"
}

func getAvailableFeatures() []string {
    return []string{
        "http-proxy", "http3", "grpc", 
        "zero-copy", "object-pools", "cpu-affinity",
        "consistent-hash", "geo-routing",
        "ocsp-stapling", "ddos-protection",
    }
}

func getEditionLimits() EditionLimits {
    return EditionLimits{
        MaxConnections: -1, // 无限制
        MaxUpstreams:   -1,
        MaxSites:       -1,
    }
}
```

## 📊 **优势对比**

| 方面 | 双代码库方案 | 单代码库+条件编译 |
|------|-------------|------------------|
| **代码维护** | 困难 (需要同步) | 简单 (单一源码) |
| **Bug修复** | 需要两处修复 | 修复一次即可 |
| **功能同步** | 容易不一致 | 自动同步 |
| **测试成本** | 高 (两套测试) | 低 (共享测试) |
| **文档维护** | 复杂 | 简单 |
| **发布管理** | 复杂 | 简单 |
| **代码重用** | 低 | 高 |
| **功能隔离** | 完全隔离 | 接口隔离 |

## 🎯 **实施建议**

### **第一步：重构现有代码**
1. 将现有功能按Core/Pro分类
2. 提取共享接口和类型
3. 实现条件编译标签
4. 创建Stub实现

### **第二步：构建系统改造**
1. 修改Makefile支持条件编译
2. 设置CI/CD流程
3. 添加版本分离验证
4. 自动化测试两个版本

### **第三步：功能验证**
1. 确保Core版不包含Pro功能
2. 验证Pro版功能完整性
3. 测试配置兼容性
4. 性能基准测试

这个方案既保护了Pro版的商业价值，又最大化了代码重用，大大降低了维护成本！

package performance

import (
	"bufio"
	"fmt"
	"io"
	"net"
	"net/http"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"reverse-proxy/internal/config"
	"reverse-proxy/internal/logger"
)

// HighPerformanceProxy 高性能代理
type HighPerformanceProxy struct {
	// 对象池
	requestPool  sync.Pool
	responsePool sync.Pool
	bufferPool   sync.Pool

	// 连接池
	connPools   map[string]*ConnectionPool
	connPoolsMu sync.RWMutex

	// 性能统计
	stats *PerformanceStats

	// 配置
	config PerformanceConfig
}

// PerformanceConfig 性能配置
type PerformanceConfig struct {
	EnableZeroCopy    bool          `json:"enable_zero_copy"`
	BufferSize        int           `json:"buffer_size"`
	MaxConnections    int           `json:"max_connections"`
	ConnectionTimeout time.Duration `json:"connection_timeout"`
	KeepAliveTimeout  time.Duration `json:"keep_alive_timeout"`
	EnableCPUAffinity bool          `json:"enable_cpu_affinity"`
	WorkerThreads     int           `json:"worker_threads"`
}

// PerformanceStats 性能统计
type PerformanceStats struct {
	TotalRequests     int64 `json:"total_requests"`
	ActiveConnections int64 `json:"active_connections"`
	PoolHits          int64 `json:"pool_hits"`
	PoolMisses        int64 `json:"pool_misses"`
	ZeroCopyBytes     int64 `json:"zero_copy_bytes"`
	AvgResponseTime   int64 `json:"avg_response_time_ns"`
}

// ConnectionPool 高性能连接池
type ConnectionPool struct {
	conns       chan *PooledConnection
	factory     func() (net.Conn, error)
	maxConns    int
	activeConns int64
	target      string
	mu          sync.Mutex
}

// PooledConnection 池化连接
type PooledConnection struct {
	net.Conn
	pool     *ConnectionPool
	lastUsed time.Time
	inUse    bool
}

// NewHighPerformanceProxy 创建高性能代理
func NewHighPerformanceProxy(config PerformanceConfig) *HighPerformanceProxy {
	if config.BufferSize == 0 {
		config.BufferSize = 64 * 1024 // 64KB
	}
	if config.MaxConnections == 0 {
		config.MaxConnections = 1000
	}
	if config.ConnectionTimeout == 0 {
		config.ConnectionTimeout = 5 * time.Second
	}
	if config.KeepAliveTimeout == 0 {
		config.KeepAliveTimeout = 60 * time.Second
	}

	hpp := &HighPerformanceProxy{
		connPools: make(map[string]*ConnectionPool),
		config:    config,
		stats:     &PerformanceStats{},
	}

	hpp.initPools()
	return hpp
}

// initPools 初始化对象池
func (hpp *HighPerformanceProxy) initPools() {
	hpp.bufferPool = sync.Pool{
		New: func() interface{} {
			return make([]byte, hpp.config.BufferSize)
		},
	}

	hpp.requestPool = sync.Pool{
		New: func() interface{} {
			return &http.Request{}
		},
	}

	hpp.responsePool = sync.Pool{
		New: func() interface{} {
			return &http.Response{}
		},
	}
}

// GetConnection 获取连接
func (hpp *HighPerformanceProxy) GetConnection(target string) (*PooledConnection, error) {
	hpp.connPoolsMu.RLock()
	pool, exists := hpp.connPools[target]
	hpp.connPoolsMu.RUnlock()

	if !exists {
		hpp.connPoolsMu.Lock()
		// 双重检查
		if pool, exists = hpp.connPools[target]; !exists {
			pool = &ConnectionPool{
				conns:    make(chan *PooledConnection, hpp.config.MaxConnections),
				maxConns: hpp.config.MaxConnections,
				target:   target,
				factory: func() (net.Conn, error) {
					return net.DialTimeout("tcp", target, hpp.config.ConnectionTimeout)
				},
			}
			hpp.connPools[target] = pool
		}
		hpp.connPoolsMu.Unlock()
	}

	return pool.Get()
}

// Get 从连接池获取连接
func (cp *ConnectionPool) Get() (*PooledConnection, error) {
	select {
	case conn := <-cp.conns:
		// 检查连接是否仍然有效
		if time.Since(conn.lastUsed) > 30*time.Second || !cp.isConnAlive(conn.Conn) {
			conn.Close()
			atomic.AddInt64(&cp.activeConns, -1)
			return cp.createNew()
		}
		conn.inUse = true
		conn.lastUsed = time.Now()
		return conn, nil
	default:
		return cp.createNew()
	}
}

// isConnAlive 检查连接是否仍然活跃
func (cp *ConnectionPool) isConnAlive(conn net.Conn) bool {
	// 设置一个很短的读取超时来检查连接状态
	conn.SetReadDeadline(time.Now().Add(1 * time.Millisecond))
	defer conn.SetReadDeadline(time.Time{}) // 清除超时设置

	// 尝试读取一个字节，如果连接已关闭会立即返回错误
	one := make([]byte, 1)
	_, err := conn.Read(one)

	// 如果是超时错误，说明连接是活跃的
	if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
		return true
	}

	// 其他错误说明连接已关闭
	return false
}

// createNew 创建新连接
func (cp *ConnectionPool) createNew() (*PooledConnection, error) {
	if atomic.LoadInt64(&cp.activeConns) >= int64(cp.maxConns) {
		// 等待可用连接，但设置超时避免无限阻塞
		select {
		case conn := <-cp.conns:
			return conn, nil
		case <-time.After(5 * time.Second):
			return nil, fmt.Errorf("连接池已满，等待超时")
		}
	}

	rawConn, err := cp.factory()
	if err != nil {
		return nil, err
	}

	atomic.AddInt64(&cp.activeConns, 1)

	conn := &PooledConnection{
		Conn:     rawConn,
		pool:     cp,
		lastUsed: time.Now(),
		inUse:    true,
	}

	return conn, nil
}

// Return 归还连接到池
func (pc *PooledConnection) Return() {
	if !pc.inUse {
		return
	}

	pc.inUse = false
	pc.lastUsed = time.Now()

	// 检查连接是否仍然有效
	if !pc.pool.isConnAlive(pc.Conn) {
		pc.Close()
		atomic.AddInt64(&pc.pool.activeConns, -1)
		return
	}

	select {
	case pc.pool.conns <- pc:
		// 成功归还到池
	default:
		// 池已满，关闭连接
		pc.Close()
		atomic.AddInt64(&pc.pool.activeConns, -1)
	}
}

// ForceClose 强制关闭连接（用于错误情况）
func (pc *PooledConnection) ForceClose() {
	pc.inUse = false
	pc.Close()
	atomic.AddInt64(&pc.pool.activeConns, -1)
}

// FastProxy 高性能代理处理
func (hpp *HighPerformanceProxy) FastProxy(w http.ResponseWriter, r *http.Request, upstream interface{}) error {
	logger.Debug("[DEBUG] FastProxy开始: %s %s\n", r.Method, r.URL.Path)
	atomic.AddInt64(&hpp.stats.TotalRequests, 1)
	atomic.AddInt64(&hpp.stats.ActiveConnections, 1)
	defer atomic.AddInt64(&hpp.stats.ActiveConnections, -1)

	// 获取缓冲区
	buffer := hpp.bufferPool.Get().([]byte)
	defer hpp.bufferPool.Put(buffer)

	// 解析上游配置
	var address string
	var port int

	logger.Debug("[DEBUG] 解析上游配置，类型: %T\n", upstream)
	if configUpstream, ok := upstream.(*config.UpstreamConfig); ok {
		address = configUpstream.Address
		port = configUpstream.Port
		logger.Debug("[DEBUG] 上游配置解析成功: %s:%d\n", address, port)
	} else if testUpstream, ok := upstream.(interface {
		GetAddress() string
		GetPort() int
	}); ok {
		address = testUpstream.GetAddress()
		port = testUpstream.GetPort()
		logger.Debug("[DEBUG] 测试上游配置解析成功: %s:%d\n", address, port)
	} else {
		logger.Debug("[DEBUG] 不支持的上游配置类型: %T\n", upstream)
		return fmt.Errorf("unsupported upstream config type")
	}

	// 获取连接
	target := fmt.Sprintf("%s:%d", address, port)
	logger.Debug("[DEBUG] 获取连接到: %s\n", target)
	conn, err := hpp.GetConnection(target)
	if err != nil {
		logger.Debug("[DEBUG] 获取连接失败: %v\n", err)
		return err
	}
	logger.Debug("[DEBUG] 连接获取成功\n")

	// 使用defer确保连接被正确处理
	var connReturned bool
	defer func() {
		if !connReturned {
			conn.ForceClose() // 如果出错，强制关闭连接
		}
	}()

	// 如果支持零拷贝且是TCP连接
	logger.Debug("[DEBUG] 检查零拷贝: enabled=%v\n", hpp.config.EnableZeroCopy)
	if hpp.config.EnableZeroCopy {
		if tcpConn, ok := conn.Conn.(*net.TCPConn); ok {
			logger.Debug("[DEBUG] 使用零拷贝代理\n")
			err := hpp.zeroCopyProxy(w, r, tcpConn)
			logger.Debug("[DEBUG] 零拷贝代理完成，错误: %v\n", err)
			if err == nil {
				conn.Return()
				connReturned = true
			}
			return err
		}
	}

	// 标准代理处理
	logger.Debug("[DEBUG] 使用标准代理处理\n")
	err = hpp.standardProxy(w, r, conn, buffer)
	logger.Debug("[DEBUG] 标准代理处理完成，错误: %v\n", err)
	if err == nil {
		conn.Return()
		connReturned = true
	}
	return err
}

// zeroCopyProxy 零拷贝代理
func (hpp *HighPerformanceProxy) zeroCopyProxy(w http.ResponseWriter, r *http.Request, upstreamConn *net.TCPConn) error {
	logger.Debug("[DEBUG] zeroCopyProxy开始\n")

	// 检查是否支持连接劫持
	hijacker, ok := w.(http.Hijacker)
	if !ok {
		logger.Debug("[DEBUG] 响应写入器不支持连接劫持，回退到标准代理\n")
		// 如果不支持连接劫持，回退到标准代理
		return fmt.Errorf("response writer does not support hijacking, falling back to standard proxy")
	}
	logger.Debug("[DEBUG] 响应写入器支持连接劫持\n")

	// 劫持客户端连接
	logger.Debug("[DEBUG] 劫持客户端连接...\n")
	clientConn, clientBuf, err := hijacker.Hijack()
	if err != nil {
		logger.Debug("[DEBUG] 劫持客户端连接失败: %v\n", err)
		return fmt.Errorf("failed to hijack client connection: %w", err)
	}
	defer clientConn.Close()
	logger.Debug("[DEBUG] 客户端连接劫持成功\n")

	// 确保上游连接在函数结束时关闭
	defer upstreamConn.Close()

	// 构建并发送HTTP请求到上游服务器
	logger.Debug("[DEBUG] 发送HTTP请求到上游服务器...\n")
	if err := hpp.sendHTTPRequest(upstreamConn, r); err != nil {
		logger.Debug("[DEBUG] 发送请求到上游失败: %v\n", err)
		return fmt.Errorf("failed to send request to upstream: %w", err)
	}
	logger.Debug("[DEBUG] 请求发送到上游成功\n")

	// 使用零拷贝进行双向数据传输
	logger.Debug("[DEBUG] 启动双向数据转发...\n")
	err = hpp.bidirectionalCopy(clientConn, upstreamConn, clientBuf)
	logger.Debug("[DEBUG] 双向数据转发完成，错误: %v\n", err)
	return err
}

// copyData 高效数据拷贝
func (hpp *HighPerformanceProxy) copyData(dst, src net.Conn) {
	buffer := hpp.bufferPool.Get().([]byte)
	defer hpp.bufferPool.Put(buffer)

	for {
		n, err := src.Read(buffer)
		if err != nil {
			break
		}

		_, err = dst.Write(buffer[:n])
		if err != nil {
			break
		}

		atomic.AddInt64(&hpp.stats.ZeroCopyBytes, int64(n))
	}
}

// standardProxy 标准代理处理
func (hpp *HighPerformanceProxy) standardProxy(w http.ResponseWriter, r *http.Request, conn *PooledConnection, buffer []byte) error {
	// 设置写入超时
	conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
	defer conn.SetWriteDeadline(time.Time{}) // 清除超时设置

	// 构建HTTP请求
	reqLine := fmt.Sprintf("%s %s HTTP/1.1\r\n", r.Method, r.URL.RequestURI())

	// 写入请求行
	if _, err := conn.Write([]byte(reqLine)); err != nil {
		// 连接可能已关闭，标记为不可重用
		conn.Close()
		return fmt.Errorf("failed to write request line: %w", err)
	}

	// 写入请求头
	if err := r.Header.Write(conn); err != nil {
		conn.Close()
		return fmt.Errorf("failed to write headers: %w", err)
	}

	// 写入空行分隔符
	if _, err := conn.Write([]byte("\r\n")); err != nil {
		conn.Close()
		return fmt.Errorf("failed to write header separator: %w", err)
	}

	// 如果有请求体，写入请求体
	if r.Body != nil {
		defer r.Body.Close()
		if _, err := hpp.copyWithBuffer(conn, r.Body, buffer); err != nil {
			return fmt.Errorf("failed to write request body: %w", err)
		}
	}

	// 读取响应
	return hpp.readAndWriteResponse(w, conn, buffer)
}

// copyWithBuffer 使用缓冲区进行高效拷贝
func (hpp *HighPerformanceProxy) copyWithBuffer(dst io.Writer, src io.Reader, buffer []byte) (int64, error) {
	var written int64
	for {
		nr, er := src.Read(buffer)
		if nr > 0 {
			nw, ew := dst.Write(buffer[0:nr])
			if nw < 0 || nr < nw {
				nw = 0
				if ew == nil {
					ew = fmt.Errorf("invalid write result")
				}
			}
			written += int64(nw)
			if ew != nil {
				return written, ew
			}
			if nr != nw {
				return written, fmt.Errorf("short write")
			}
		}
		if er != nil {
			if er != io.EOF {
				return written, er
			}
			break
		}
	}
	return written, nil
}

// readAndWriteResponse 读取并写入响应
func (hpp *HighPerformanceProxy) readAndWriteResponse(w http.ResponseWriter, conn net.Conn, buffer []byte) error {
	// 设置读取超时
	if tcpConn, ok := conn.(*net.TCPConn); ok {
		tcpConn.SetReadDeadline(time.Now().Add(30 * time.Second))
	}

	// 读取响应状态行
	reader := bufio.NewReader(conn)
	statusLine, _, err := reader.ReadLine()
	if err != nil {
		return fmt.Errorf("failed to read status line: %w", err)
	}

	// 解析状态行
	parts := strings.SplitN(string(statusLine), " ", 3)
	if len(parts) < 2 {
		return fmt.Errorf("invalid status line: %s", statusLine)
	}

	statusCode, err := strconv.Atoi(parts[1])
	if err != nil {
		return fmt.Errorf("invalid status code: %s", parts[1])
	}

	// 读取响应头
	headers := make(http.Header)
	for {
		line, _, err := reader.ReadLine()
		if err != nil {
			return fmt.Errorf("failed to read header: %w", err)
		}

		if len(line) == 0 {
			// 空行表示头部结束
			break
		}

		// 解析头部
		headerParts := strings.SplitN(string(line), ":", 2)
		if len(headerParts) == 2 {
			key := strings.TrimSpace(headerParts[0])
			value := strings.TrimSpace(headerParts[1])
			headers.Add(key, value)
		}
	}

	// 复制响应头到输出
	for key, values := range headers {
		for _, value := range values {
			w.Header().Add(key, value)
		}
	}

	// 写入状态码
	w.WriteHeader(statusCode)

	// 复制响应体
	_, err = hpp.copyWithBuffer(w, reader, buffer)
	return err
}

// sendHTTPRequest 发送HTTP请求到上游服务器
func (hpp *HighPerformanceProxy) sendHTTPRequest(upstreamConn net.Conn, r *http.Request) error {
	// 构建HTTP请求
	reqLine := fmt.Sprintf("%s %s HTTP/1.1\r\n", r.Method, r.URL.RequestURI())

	// 写入请求行
	if _, err := upstreamConn.Write([]byte(reqLine)); err != nil {
		return fmt.Errorf("failed to write request line: %w", err)
	}

	// 确保Host头部存在（HTTP/1.1要求）
	if r.Header.Get("Host") == "" {
		r.Header.Set("Host", r.Host)
	}

	// 写入请求头
	if err := r.Header.Write(upstreamConn); err != nil {
		return fmt.Errorf("failed to write headers: %w", err)
	}

	// 写入空行分隔符
	if _, err := upstreamConn.Write([]byte("\r\n")); err != nil {
		return fmt.Errorf("failed to write header separator: %w", err)
	}

	// 如果有请求体，写入请求体
	if r.Body != nil {
		defer r.Body.Close()
		buffer := hpp.bufferPool.Get().([]byte)
		defer hpp.bufferPool.Put(buffer)

		if _, err := hpp.copyWithBuffer(upstreamConn, r.Body, buffer); err != nil {
			return fmt.Errorf("failed to write request body: %w", err)
		}
	}

	return nil
}

// bidirectionalCopy 双向零拷贝数据传输
func (hpp *HighPerformanceProxy) bidirectionalCopy(clientConn, upstreamConn net.Conn, clientBuf *bufio.ReadWriter) error {
	// 创建错误通道
	errChan := make(chan error, 2)

	// 从客户端到上游服务器的数据传输
	go func() {
		buffer := hpp.bufferPool.Get().([]byte)
		defer hpp.bufferPool.Put(buffer)

		// 首先处理可能存在的缓冲数据
		if clientBuf.Reader.Buffered() > 0 {
			bufferedData := make([]byte, clientBuf.Reader.Buffered())
			if _, err := clientBuf.Reader.Read(bufferedData); err == nil {
				upstreamConn.Write(bufferedData)
			}
		}

		// 继续传输数据
		_, err := hpp.copyWithBuffer(upstreamConn, clientConn, buffer)
		if err != nil && err != io.EOF {
			errChan <- fmt.Errorf("client to upstream copy error: %w", err)
		} else {
			errChan <- nil
		}
	}()

	// 从上游服务器到客户端的数据传输
	go func() {
		buffer := hpp.bufferPool.Get().([]byte)
		defer hpp.bufferPool.Put(buffer)

		_, err := hpp.copyWithBuffer(clientConn, upstreamConn, buffer)
		if err != nil && err != io.EOF {
			errChan <- fmt.Errorf("upstream to client copy error: %w", err)
		} else {
			errChan <- nil
		}
	}()

	// 等待任一方向的传输完成或出错
	for i := 0; i < 2; i++ {
		if err := <-errChan; err != nil {
			return err
		}
	}

	return nil
}

// GetStats 获取性能统计
func (hpp *HighPerformanceProxy) GetStats() *PerformanceStats {
	return &PerformanceStats{
		TotalRequests:     atomic.LoadInt64(&hpp.stats.TotalRequests),
		ActiveConnections: atomic.LoadInt64(&hpp.stats.ActiveConnections),
		PoolHits:          atomic.LoadInt64(&hpp.stats.PoolHits),
		PoolMisses:        atomic.LoadInt64(&hpp.stats.PoolMisses),
		ZeroCopyBytes:     atomic.LoadInt64(&hpp.stats.ZeroCopyBytes),
		AvgResponseTime:   atomic.LoadInt64(&hpp.stats.AvgResponseTime),
	}
}

// OptimizeForHighConcurrency 高并发优化
func (hpp *HighPerformanceProxy) OptimizeForHighConcurrency() {
	// 设置GOMAXPROCS
	if hpp.config.WorkerThreads > 0 {
		runtime.GOMAXPROCS(hpp.config.WorkerThreads)
	}

	// 启用CPU亲和性
	if hpp.config.EnableCPUAffinity {
		hpp.enableCPUAffinity()
	}

	// 预热连接池
	hpp.warmupConnectionPools()
}

// enableCPUAffinity 启用CPU亲和性
func (hpp *HighPerformanceProxy) enableCPUAffinity() {
	// 这里需要根据操作系统实现CPU亲和性设置
	logger.Info("CPU affinity optimization enabled")
}

// warmupConnectionPools 预热连接池
func (hpp *HighPerformanceProxy) warmupConnectionPools() {
	// 暂时禁用预热功能，避免启动时的连接问题
	logger.Info("Connection pool warmup disabled for stability")
	return

	hpp.connPoolsMu.RLock()
	defer hpp.connPoolsMu.RUnlock()

	for target, pool := range hpp.connPools {
		// 修复Go 1.20循环变量陷阱：创建局部副本
		target := target
		pool := pool
		go func(t string, p *ConnectionPool) {
			// 预创建一些连接，但限制尝试次数和时间
			successCount := 0
			for i := 0; i < 10 && successCount < 3; i++ {
				// 设置超时上下文避免长时间阻塞
				if conn, err := p.createNew(); err == nil {
					conn.Return()
					successCount++
				} else {
					// 连接失败时等待一小段时间再重试
					time.Sleep(100 * time.Millisecond)
				}
			}
			logger.Info("Warmed up connection pool for:", t, "success:", successCount)
		}(target, pool)
	}
}

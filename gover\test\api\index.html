<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API服务器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .api-info {
            background-color: #d1ecf1;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .endpoint {
            background-color: #e2e3e5;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
        }
        .timestamp {
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 API服务器</h1>
        
        <div class="api-info">
            <h3>API服务器信息</h3>
            <p><strong>服务器名称:</strong> API Server</p>
            <p><strong>IP地址:</strong> ************</p>
            <p><strong>端口:</strong> 9000</p>
            <p><strong>状态:</strong> <span style="color: green;">运行中</span></p>
        </div>
        
        <div class="api-info">
            <h3>可用端点</h3>
            <div class="endpoint">GET /api/users</div>
            <div class="endpoint">POST /api/users</div>
            <div class="endpoint">GET /api/health</div>
            <div class="endpoint">GET /api/status</div>
        </div>
        
        <div class="api-info">
            <h3>API测试</h3>
            <p>这是API服务器的响应页面。</p>
            <p>如果您看到这个页面，说明反向代理成功将API请求路由到了API服务器。</p>
        </div>
        
        <div class="timestamp">
            <p>页面生成时间: <span id="timestamp"></span></p>
        </div>
    </div>
    
    <script>
        document.getElementById('timestamp').textContent = new Date().toLocaleString('zh-CN');
    </script>
</body>
</html> 
# 内部 Minify 实现文档

## 概述

由于外部 minify 库 `github.com/tdewolff/minify/v2` 与 Go 1.20 存在兼容性问题，我们实现了一个内部版本的 minify 功能。这个实现基于 tdewolff/minify 的核心思想，但简化了实现并确保与 Go 1.20 的完全兼容性。

## 实现特性

### 支持的格式
- **HTML/XHTML**: `text/html`, `application/xhtml+xml`
- **CSS**: `text/css`
- **JavaScript**: `application/javascript`, `text/javascript`

### 压缩效果
根据测试结果：
- **HTML**: ~40% 压缩率
- **CSS**: ~39% 压缩率  
- **JavaScript**: ~30% 压缩率

## 文件结构

### 新增文件
- `internal/minify/internal_minify.go` - 内部 minify 实现

### 修改文件
- `internal/minify/minify.go` - 更新为使用内部实现

## 核心功能

### HTML Minify
- 移除 HTML 注释（可选保留条件注释）
- 压缩空白字符
- 移除不必要的引号
- 移除默认属性值（如 `type="text/javascript"`）
- 标签间空白优化

### CSS Minify
- 移除注释（保留 `/*! */` 许可证注释）
- 压缩空白字符
- 移除末尾分号
- 简化颜色值（`#ffffff` → `#fff`）
- 移除零值单位（`0px` → `0`）
- 数字精度控制

### JavaScript Minify
- 移除单行和多行注释
- 压缩空白字符
- 布尔值简化（`true` → `!0`, `false` → `!1`）
- 数字精度控制
- 操作符周围空白优化

## 配置选项

### HTML 配置
```go
type MinifyHTMLConfig struct {
    KeepConditionalComments bool // 保留条件注释
    KeepWhitespace          bool // 保留空白字符
    KeepQuotes              bool // 保留引号
    KeepDefaultAttrVals     bool // 保留默认属性值
}
```

### CSS 配置
```go
type MinifyCSSConfig struct {
    Precision int // 数字精度
}
```

### JavaScript 配置
```go
type MinifyJSConfig struct {
    Precision int // 数字精度
}
```

## 使用方法

### 基本使用
```go
// 创建 minifier
minifier := minify.NewMinifier()

// 执行 minify
result, err := minifier.Minify(data, contentType, config)
```

### 便利函数
```go
// 直接使用内部实现
result, err := minify.MinifyResponseInternal(data, contentType, config)
```

## 兼容性

### Go 版本支持
- ✅ Go 1.20.14
- ✅ Go 1.24+

### 正则表达式限制
由于 Go 的正则表达式引擎不支持某些 Perl 语法，我们避免使用：
- 负向前瞻 `(?!)`
- 负向后瞻 `(?<!)`
- 反向引用 `\1`, `\2`

### 替代方案
- 使用 `FindAllString` + `ReplaceAllString` 组合
- 使用 `ReplaceAllStringFunc` 进行复杂替换
- 使用简单的字符串替换处理常见情况

## 性能特点

### 优势
- 无外部依赖
- 编译速度快
- 运行时性能良好
- 内存使用效率高

### 限制
- 功能相比专业库较简化
- 某些高级优化未实现
- 正则表达式功能受限

## 测试结果

### 测试用例
```
HTML (436 字节) → 259 字节 (40.60% 压缩)
CSS (196 字节) → 119 字节 (39.29% 压缩)
JavaScript (186 字节) → 129 字节 (30.65% 压缩)
```

### 编译测试
- ✅ Go 1.20.14 编译成功
- ✅ 所有平台构建成功
- ✅ 运行时测试通过

## 未来改进

### 可能的增强
1. **更智能的空白处理** - 考虑 HTML 语义
2. **更多 CSS 优化** - 属性合并、选择器优化
3. **JavaScript 变量重命名** - 局部作用域变量名缩短
4. **更好的数字处理** - 科学计数法优化
5. **源码映射支持** - 调试友好

### 架构改进
1. **插件化设计** - 支持自定义处理器
2. **流式处理** - 大文件支持
3. **并发处理** - 多文件并行压缩
4. **缓存机制** - 避免重复处理

## 总结

内部 minify 实现成功解决了外部库兼容性问题，提供了：
- 稳定的 Go 1.20 支持
- 良好的压缩效果
- 简洁的代码结构
- 完全的控制权

虽然功能相比专业库有所简化，但对于反向代理的使用场景已经足够，并且为未来的定制化需求提供了良好的基础。

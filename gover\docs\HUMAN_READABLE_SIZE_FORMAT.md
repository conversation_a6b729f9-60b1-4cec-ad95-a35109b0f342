# 人性化大小格式支持

## 🎯 功能概述

为了让配置更加直观和易用，压缩配置中的 `min_size` 和 `max_size` 现在支持人性化的大小格式，如 `2M`、`1.5GB`、`512KiB` 等。

## 📋 支持的格式

### 基本格式
```json
{
  "compression": {
    "min_size": "1KB",     // 1千字节
    "max_size": "10MB"     // 10兆字节
  }
}
```

### 完整支持列表

#### 1. **纯数字**（字节）
```json
{
  "min_size": 1024,      // 1024字节
  "max_size": 10485760   // 10485760字节
}
```

#### 2. **十进制单位**（1000进制）
```json
{
  "min_size": "1KB",     // 1,000字节
  "max_size": "10MB",    // 10,000,000字节
  "other": "5GB"         // 5,000,000,000字节
}
```

#### 3. **二进制单位**（1024进制）
```json
{
  "min_size": "1KiB",    // 1,024字节
  "max_size": "10MiB",   // 10,485,760字节
  "other": "5GiB"        // 5,368,709,120字节
}
```

#### 4. **简化格式**
```json
{
  "min_size": "1k",      // 1,000字节
  "max_size": "10m",     // 10,000,000字节
  "other": "5g"          // 5,000,000,000字节
}
```

#### 5. **大小写不敏感**
```json
{
  "min_size": "2M",      // 等同于 "2m", "2MB", "2mb"
  "max_size": "1G",      // 等同于 "1g", "1GB", "1gb"
  "other": "512K"        // 等同于 "512k", "512KB", "512kb"
}
```

#### 6. **小数支持**
```json
{
  "min_size": "1.5MB",   // 1,500,000字节
  "max_size": "2.5GiB",  // 2,684,354,560字节
  "other": "0.5KB"       // 500字节
}
```

## 🔧 单位对照表

### 十进制单位（SI标准）
| 单位 | 全称 | 倍数 | 字节数 | 示例 |
|------|------|------|--------|------|
| B | Byte | 1 | 1 | `1024` |
| KB | Kilobyte | 1,000 | 1,000 | `1KB` |
| MB | Megabyte | 1,000² | 1,000,000 | `10MB` |
| GB | Gigabyte | 1,000³ | 1,000,000,000 | `5GB` |
| TB | Terabyte | 1,000⁴ | 1,000,000,000,000 | `1TB` |

### 二进制单位（IEC标准）
| 单位 | 全称 | 倍数 | 字节数 | 示例 |
|------|------|------|--------|------|
| B | Byte | 1 | 1 | `1024` |
| KiB | Kibibyte | 1,024 | 1,024 | `1KiB` |
| MiB | Mebibyte | 1,024² | 1,048,576 | `10MiB` |
| GiB | Gibibyte | 1,024³ | 1,073,741,824 | `5GiB` |
| TiB | Tebibyte | 1,024⁴ | 1,099,511,627,776 | `1TiB` |

### 简化格式
| 格式 | 等同于 | 说明 |
|------|--------|------|
| `k`, `K` | `KB` | 千字节（十进制） |
| `m`, `M` | `MB` | 兆字节（十进制） |
| `g`, `G` | `GB` | 吉字节（十进制） |
| `t`, `T` | `TB` | 太字节（十进制） |

## 📊 实际配置示例

### 示例1: 高性能Web服务
```json
{
  "compression": {
    "enabled": true,
    "min_size": "1KB",     // 1千字节
    "max_size": "5MB",     // 5兆字节
    "algorithms": ["gzip", "br"]
  }
}
```

### 示例2: 内容分发网络
```json
{
  "compression": {
    "enabled": true,
    "min_size": "512",     // 512字节
    "max_size": "20MiB",   // 20兆字节（二进制）
    "algorithms": ["br", "gzip", "deflate"]
  }
}
```

### 示例3: 资源受限环境
```json
{
  "compression": {
    "enabled": true,
    "min_size": "2k",      // 2千字节
    "max_size": "1m",      // 1兆字节
    "algorithms": ["gzip"]
  }
}
```

### 示例4: 企业级部署
```json
{
  "compression": {
    "enabled": true,
    "min_size": "1.5KB",   // 1.5千字节
    "max_size": "50MB",    // 50兆字节
    "enable_recompression": true,
    "algorithms": ["br", "gzip", "deflate"]
  }
}
```

## 🔍 格式解析规则

### 解析优先级
1. **纯数字**: 直接作为字节数
2. **带单位**: 解析数字和单位
3. **大小写**: 不敏感处理
4. **空格**: 自动忽略

### 解析示例
```
输入: "2M"
解析: 数字=2, 单位=M
结果: 2 × 1,000,000 = 2,000,000字节

输入: "1.5 GiB"
解析: 数字=1.5, 单位=GiB
结果: 1.5 × 1,073,741,824 = 1,610,612,736字节

输入: "512"
解析: 纯数字
结果: 512字节
```

## ⚠️ 注意事项

### 1. **十进制 vs 二进制**
```json
{
  "size_decimal": "1MB",    // 1,000,000字节
  "size_binary": "1MiB"     // 1,048,576字节
}
```

### 2. **小数精度**
```json
{
  "valid": "1.5MB",         // ✅ 支持
  "valid": "2.25GiB",       // ✅ 支持
  "invalid": "1.5.5MB"      // ❌ 无效格式
}
```

### 3. **单位大小写**
```json
{
  "all_valid": [
    "1MB", "1mb", "1Mb", "1mB",  // 都有效
    "2GiB", "2gib", "2GIB"       // 都有效
  ]
}
```

### 4. **错误处理**
```json
{
  "invalid_formats": [
    "1XB",          // 未知单位
    "abc",          // 非数字
    "1.2.3MB",      // 无效数字格式
    ""              // 空字符串（默认为0）
  ]
}
```

## 🚀 使用建议

### 1. **推荐格式**
```json
{
  "compression": {
    "min_size": "1KB",     // 清晰明确
    "max_size": "10MB"     // 易于理解
  }
}
```

### 2. **避免混用**
```json
{
  "good": {
    "min_size": "1KB",     // 十进制
    "max_size": "10MB"     // 十进制
  },
  "confusing": {
    "min_size": "1KB",     // 十进制
    "max_size": "10MiB"    // 二进制（容易混淆）
  }
}
```

### 3. **场景选择**
- **Web应用**: 使用十进制单位（KB, MB, GB）
- **系统级**: 使用二进制单位（KiB, MiB, GiB）
- **简单配置**: 使用简化格式（k, m, g）

## 🎯 优势总结

### 1. **可读性提升**
```
旧格式: "max_size": 10485760
新格式: "max_size": "10MB"
```

### 2. **配置简化**
```
旧格式: 需要计算字节数
新格式: 直观的大小表示
```

### 3. **错误减少**
```
旧格式: 容易写错位数
新格式: 直观不易出错
```

### 4. **兼容性保持**
```
数字格式: 仍然支持
字符串格式: 新增支持
```

这个功能让配置文件更加人性化，大大提升了使用体验！🚀

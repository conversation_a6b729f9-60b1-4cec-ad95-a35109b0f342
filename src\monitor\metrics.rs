use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tokio::sync::RwLock;
use tokio::time::{interval, Instant};
use tracing::{debug, error, info, warn};

use crate::config::MonitorConfig;
use crate::monitor::stats::{Counter, Gauge, Histogram, MovingAverage};
use crate::processor::RequestContext;

/// 指标收集器
pub struct MetricsCollector {
    config: MonitorConfig,
    metrics: Arc<RwLock<MetricsRegistry>>,
    start_time: SystemTime,
}

impl MetricsCollector {
    /// 创建新的指标收集器
    pub async fn new(config: &MonitorConfig) -> Result<Self> {
        let metrics = Arc::new(RwLock::new(MetricsRegistry::new()));

        info!("Metrics collector initialized successfully");

        Ok(Self {
            config: config.clone(),
            metrics,
            start_time: SystemTime::now(),
        })
    }

    /// 记录请求指标
    pub async fn record_request(&self, ctx: &RequestContext) -> Result<()> {
        let mut metrics = self.metrics.write().await;
        
        // 请求计数
        metrics.request_total.increment();
        
        // 按状态码计数
        let status_key = format!("http_requests_total_status_{}", ctx.response.status_code);
        metrics.counters.entry(status_key).or_insert_with(Counter::new).increment();
        
        // 按方法计数
        let method_key = format!("http_requests_total_method_{}", ctx.method.to_lowercase());
        metrics.counters.entry(method_key).or_insert_with(Counter::new).increment();
        
        // 响应时间
        let response_time = ctx.start_time.elapsed().as_millis() as f64;
        metrics.response_time_histogram.observe(response_time);
        metrics.response_time_avg.add_sample(response_time);
        
        // 响应大小
        if let Some(content_length) = ctx.response.headers.get("content-length") {
            if let Ok(size) = content_length.parse::<f64>() {
                metrics.response_size_histogram.observe(size);
                metrics.response_size_total.add(size as u64);
            }
        }
        
        // 按站点统计
        if let Some(site) = &ctx.site {
            let site_key = format!("site_requests_total_{}", site.site_id);
            metrics.counters.entry(site_key).or_insert_with(Counter::new).increment();
        }
        
        // 活跃连接数（简化处理）
        let current_connections = metrics.active_connections.value();
        metrics.active_connections.set(current_connections + 1.0);
        
        debug!("Recorded metrics for request {}", ctx.request_id);
        Ok(())
    }

    /// 启动指标收集
    pub async fn start_collection(&self) -> Result<()> {
        let mut interval = interval(Duration::from_secs(60)); // 每分钟收集一次系统指标

        info!("Starting metrics collection...");

        loop {
            interval.tick().await;
            
            if let Err(e) = self.collect_system_metrics().await {
                error!("Failed to collect system metrics: {}", e);
            }
        }
    }

    /// 收集系统指标
    async fn collect_system_metrics(&self) -> Result<()> {
        let mut metrics = self.metrics.write().await;
        
        // CPU使用率
        let cpu_usage = self.get_cpu_usage().await?;
        metrics.cpu_usage.set(cpu_usage);
        
        // 内存使用率
        let memory_usage = self.get_memory_usage().await?;
        metrics.memory_usage.set(memory_usage);
        
        // 磁盘使用率
        let disk_usage = self.get_disk_usage().await?;
        metrics.disk_usage.set(disk_usage);
        
        // 网络I/O
        let (network_in, network_out) = self.get_network_io().await?;
        metrics.network_bytes_in.add(network_in);
        metrics.network_bytes_out.add(network_out);
        
        // 运行时间
        let uptime = self.start_time.elapsed().unwrap_or_default().as_secs() as f64;
        metrics.uptime_seconds.set(uptime);
        
        debug!("Collected system metrics");
        Ok(())
    }

    /// 获取所有指标
    pub async fn get_metrics(&self) -> Result<HashMap<String, f64>> {
        let metrics = self.metrics.read().await;
        let mut result = HashMap::new();
        
        // 基础指标
        result.insert("http_requests_total".to_string(), metrics.request_total.value() as f64);
        result.insert("http_response_time_avg".to_string(), metrics.response_time_avg.average());
        result.insert("http_response_time_p50".to_string(), metrics.response_time_histogram.quantile(0.5));
        result.insert("http_response_time_p95".to_string(), metrics.response_time_histogram.quantile(0.95));
        result.insert("http_response_time_p99".to_string(), metrics.response_time_histogram.quantile(0.99));
        result.insert("http_response_size_total".to_string(), metrics.response_size_total.value() as f64);
        
        // 系统指标
        result.insert("cpu_usage_percent".to_string(), metrics.cpu_usage.value());
        result.insert("memory_usage_percent".to_string(), metrics.memory_usage.value());
        result.insert("disk_usage_percent".to_string(), metrics.disk_usage.value());
        result.insert("network_bytes_in_total".to_string(), metrics.network_bytes_in.value() as f64);
        result.insert("network_bytes_out_total".to_string(), metrics.network_bytes_out.value() as f64);
        result.insert("uptime_seconds".to_string(), metrics.uptime_seconds.value());
        result.insert("active_connections".to_string(), metrics.active_connections.value());
        
        // 计数器指标
        for (name, counter) in &metrics.counters {
            result.insert(name.clone(), counter.value() as f64);
        }
        
        // 仪表盘指标
        for (name, gauge) in &metrics.gauges {
            result.insert(name.clone(), gauge.value());
        }
        
        Ok(result)
    }

    /// 获取Prometheus格式的指标
    pub async fn get_prometheus_metrics(&self) -> Result<String> {
        let metrics = self.get_metrics().await?;
        let mut output = String::new();
        
        for (name, value) in metrics {
            output.push_str(&format!("# TYPE {} gauge\n", name));
            output.push_str(&format!("{} {}\n", name, value));
        }
        
        Ok(output)
    }

    /// 获取CPU使用率
    async fn get_cpu_usage(&self) -> Result<f64> {
        // 简化实现，实际应该读取系统CPU信息
        Ok(25.0) // 返回25%的CPU使用率
    }

    /// 获取内存使用率
    async fn get_memory_usage(&self) -> Result<f64> {
        // 简化实现，实际应该读取系统内存信息
        Ok(45.0) // 返回45%的内存使用率
    }

    /// 获取磁盘使用率
    async fn get_disk_usage(&self) -> Result<f64> {
        // 简化实现，实际应该检查磁盘空间
        Ok(60.0) // 返回60%的磁盘使用率
    }

    /// 获取网络I/O
    async fn get_network_io(&self) -> Result<(u64, u64)> {
        // 简化实现，实际应该读取网络统计信息
        Ok((1024 * 1024, 2048 * 1024)) // 返回1MB入站，2MB出站
    }
}

/// 指标注册表
#[derive(Debug)]
pub struct MetricsRegistry {
    // 基础HTTP指标
    pub request_total: Counter,
    pub response_time_histogram: Histogram,
    pub response_time_avg: MovingAverage,
    pub response_size_histogram: Histogram,
    pub response_size_total: Counter,
    pub active_connections: Gauge,
    
    // 系统指标
    pub cpu_usage: Gauge,
    pub memory_usage: Gauge,
    pub disk_usage: Gauge,
    pub network_bytes_in: Counter,
    pub network_bytes_out: Counter,
    pub uptime_seconds: Gauge,
    
    // 动态指标
    pub counters: HashMap<String, Counter>,
    pub gauges: HashMap<String, Gauge>,
    pub histograms: HashMap<String, Histogram>,
}

impl MetricsRegistry {
    pub fn new() -> Self {
        Self {
            request_total: Counter::new(),
            response_time_histogram: Histogram::response_time_histogram(),
            response_time_avg: MovingAverage::new(1000),
            response_size_histogram: Histogram::new(vec![
                100.0, 1000.0, 10000.0, 100000.0, 1000000.0, 10000000.0
            ]),
            response_size_total: Counter::new(),
            active_connections: Gauge::new(),
            cpu_usage: Gauge::new(),
            memory_usage: Gauge::new(),
            disk_usage: Gauge::new(),
            network_bytes_in: Counter::new(),
            network_bytes_out: Counter::new(),
            uptime_seconds: Gauge::new(),
            counters: HashMap::new(),
            gauges: HashMap::new(),
            histograms: HashMap::new(),
        }
    }
}

/// 指标导出器
pub struct MetricsExporter {
    collector: Arc<MetricsCollector>,
}

impl MetricsExporter {
    pub fn new(collector: Arc<MetricsCollector>) -> Self {
        Self { collector }
    }

    /// 导出JSON格式的指标
    pub async fn export_json(&self) -> Result<String> {
        let metrics = self.collector.get_metrics().await?;
        Ok(serde_json::to_string_pretty(&metrics)?)
    }

    /// 导出Prometheus格式的指标
    pub async fn export_prometheus(&self) -> Result<String> {
        self.collector.get_prometheus_metrics().await
    }

    /// 导出CSV格式的指标
    pub async fn export_csv(&self) -> Result<String> {
        let metrics = self.collector.get_metrics().await?;
        let mut csv = String::from("metric_name,value\n");
        
        for (name, value) in metrics {
            csv.push_str(&format!("{},{}\n", name, value));
        }
        
        Ok(csv)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::net::SocketAddr;

    #[tokio::test]
    async fn test_metrics_collector_creation() {
        let config = MonitorConfig {
            enabled: true,
            port: 8080,
            username: "admin".to_string(),
            password: "password".to_string(),
            api_key: "test-key".to_string(),
            acl: crate::config::MonitorAclConfig {
                allowed_ips: vec!["127.0.0.1".to_string()],
                denied_ips: vec![],
            },
        };

        let collector = MetricsCollector::new(&config).await;
        assert!(collector.is_ok());
    }

    #[tokio::test]
    async fn test_metrics_collection() {
        let config = MonitorConfig {
            enabled: true,
            port: 8080,
            username: "admin".to_string(),
            password: "password".to_string(),
            api_key: "test-key".to_string(),
            acl: crate::config::MonitorAclConfig {
                allowed_ips: vec!["127.0.0.1".to_string()],
                denied_ips: vec![],
            },
        };

        let collector = MetricsCollector::new(&config).await.unwrap();
        let metrics = collector.get_metrics().await;
        
        assert!(metrics.is_ok());
        let metrics = metrics.unwrap();
        assert!(metrics.contains_key("http_requests_total"));
        assert!(metrics.contains_key("cpu_usage_percent"));
    }

    #[test]
    fn test_metrics_registry() {
        let mut registry = MetricsRegistry::new();
        
        registry.request_total.increment();
        assert_eq!(registry.request_total.value(), 1);
        
        registry.cpu_usage.set(50.0);
        assert_eq!(registry.cpu_usage.value(), 50.0);
        
        registry.response_time_histogram.observe(123.45);
        assert_eq!(registry.response_time_histogram.count(), 1);
    }
}

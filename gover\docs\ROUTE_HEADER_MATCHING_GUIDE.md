# 🚀 路由HTTP头部匹配功能指南

## 📋 **功能概述**

我已经为你实现了路由级别的HTTP头部匹配功能。现在路由不仅可以基于URL路径匹配，还可以基于HTTP头部、Cookie、查询参数等信息进行精确匹配。

## 🔧 **新增的配置选项**

### **RouteConfig 增强**
```json
{
  "pattern": "/api/",
  "upstream": "backend",
  "cache": true,
  "match_conditions": {
    "headers": [
      {
        "name": "X-API-Version",
        "value": "1.0",
        "description": "API版本1.0"
      },
      {
        "name": "Accept",
        "pattern": "application/json",
        "description": "JSON请求"
      }
    ],
    "cookies": [
      {
        "name": "session_id",
        "exists": true,
        "description": "有会话"
      }
    ],
    "query_params": [
      {
        "name": "format",
        "value": "json",
        "description": "JSON格式"
      }
    ],
    "user_agents": [".*(Mobile|Android|iPhone).*"],
    "methods": ["GET", "POST"],
    "content_type": ["application/json"]
  }
}
```

## 🎯 **路由匹配逻辑**

### **匹配顺序**
1. **URL模式匹配**：首先检查URL是否匹配路由的pattern
2. **匹配条件检查**：如果有match_conditions，检查所有条件
3. **条件满足**：所有条件都满足才使用该路由
4. **继续匹配**：条件不满足则继续检查下一个路由

### **匹配条件类型**

#### **1. HTTP头部匹配**
```json
"headers": [
  {
    "name": "Accept",
    "pattern": "application/json",
    "description": "JSON请求"
  },
  {
    "name": "Authorization",
    "exists": true,
    "description": "需要认证"
  },
  {
    "name": "X-API-Version",
    "value": "1.0",
    "description": "精确版本匹配"
  }
]
```

#### **2. Cookie匹配**
```json
"cookies": [
  {
    "name": "wordpress_logged_in",
    "pattern": "wordpress_[a-f0-9]+",
    "description": "WordPress登录状态"
  },
  {
    "name": "session_id",
    "exists": true,
    "description": "有会话"
  }
]
```

#### **3. 查询参数匹配**
```json
"query_params": [
  {
    "name": "preview",
    "value": "true",
    "description": "预览模式"
  },
  {
    "name": "q",
    "exists": true,
    "description": "有搜索关键词"
  }
]
```

## 📊 **实际应用场景**

### **场景1：WordPress登录状态路由**
```json
[
  {
    "pattern": "^/",
    "upstream": "wordpress_backend",
    "cache": false,
    "match_conditions": {
      "cookies": [
        {
          "name": "wordpress_logged_in",
          "pattern": "wordpress_[a-f0-9]+",
          "description": "已登录用户"
        }
      ]
    },
    "description": "已登录用户不缓存"
  },
  {
    "pattern": "^/",
    "upstream": "wordpress_backend", 
    "cache": true,
    "match_conditions": {
      "cookies": [
        {
          "name": "wordpress_logged_in",
          "exists": false,
          "description": "未登录用户"
        }
      ]
    },
    "description": "未登录用户缓存"
  }
]
```

### **场景2：API版本路由**
```json
[
  {
    "pattern": "/api/",
    "upstream": "api_v1_backend",
    "cache": true,
    "match_conditions": {
      "headers": [
        {
          "name": "X-API-Version",
          "value": "1.0"
        }
      ]
    }
  },
  {
    "pattern": "/api/",
    "upstream": "api_v2_backend",
    "cache": true,
    "match_conditions": {
      "headers": [
        {
          "name": "X-API-Version", 
          "value": "2.0"
        }
      ]
    }
  }
]
```

### **场景3：移动设备路由**
```json
[
  {
    "pattern": "^/",
    "upstream": "mobile_backend",
    "cache": true,
    "match_conditions": {
      "headers": [
        {
          "name": "User-Agent",
          "pattern": ".*(Mobile|Android|iPhone|iPad).*"
        }
      ]
    },
    "description": "移动设备专用后端"
  },
  {
    "pattern": "^/",
    "upstream": "desktop_backend",
    "cache": true,
    "description": "桌面设备后端"
  }
]
```

### **场景4：多语言路由**
```json
[
  {
    "pattern": "^/",
    "upstream": "chinese_backend",
    "cache": true,
    "match_conditions": {
      "headers": [
        {
          "name": "Accept-Language",
          "pattern": "zh-CN|zh"
        }
      ]
    }
  },
  {
    "pattern": "^/",
    "upstream": "english_backend",
    "cache": true,
    "match_conditions": {
      "headers": [
        {
          "name": "Accept-Language",
          "pattern": "en-US|en"
        }
      ]
    }
  }
]
```

### **场景5：AJAX请求路由**
```json
{
  "pattern": "/ajax/",
  "upstream": "ajax_backend",
  "cache": true,
  "match_conditions": {
    "headers": [
      {
        "name": "X-Requested-With",
        "value": "XMLHttpRequest"
      }
    ]
  }
}
```

## 🔄 **WordPress缓存解决方案**

使用路由匹配可以完美解决WordPress登录后仍然缓存的问题：

```json
{
  "routes": [
    {
      "pattern": "\\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$",
      "upstream": "wordpress_backend",
      "cache": true,
      "description": "静态资源始终缓存"
    },
    {
      "pattern": "^/",
      "upstream": "wordpress_backend",
      "cache": false,
      "match_conditions": {
        "cookies": [
          {
            "name": "wordpress_logged_in",
            "pattern": "wordpress_[a-f0-9]+",
            "description": "WordPress登录用户"
          }
        ]
      },
      "description": "已登录用户不缓存"
    },
    {
      "pattern": "^/",
      "upstream": "wordpress_backend",
      "cache": false,
      "match_conditions": {
        "methods": ["POST", "PUT", "DELETE", "PATCH"]
      },
      "description": "非GET请求不缓存"
    },
    {
      "pattern": "^/",
      "upstream": "wordpress_backend",
      "cache": false,
      "match_conditions": {
        "query_params": [
          {
            "name": "*",
            "exists": true,
            "description": "有查询参数"
          }
        ]
      },
      "description": "有查询参数不缓存"
    },
    {
      "pattern": "^/",
      "upstream": "wordpress_backend",
      "cache": true,
      "description": "默认缓存（未登录用户的GET请求）"
    }
  ]
}
```

## 💡 **配置建议**

### **1. 路由顺序很重要**
- 将最具体的匹配条件放在前面
- 将兜底路由放在最后
- 相同pattern的路由按匹配条件的严格程度排序

### **2. 性能优化**
- 避免过于复杂的正则表达式
- 将最常匹配的条件放在前面
- 使用精确匹配而非正则匹配（当可能时）

### **3. 调试建议**
- 使用description字段记录路由用途
- 启用调试日志查看路由匹配结果
- 测试不同条件的匹配效果

## 🚀 **部署建议**

1. **从简单开始**：先配置基本的URL匹配
2. **逐步添加条件**：根据需求添加头部匹配条件
3. **测试验证**：确保路由匹配符合预期
4. **监控效果**：观察缓存命中率和路由分发效果

## 📝 **注意事项**

1. **匹配条件是AND关系**：所有条件都必须满足
2. **路由顺序敏感**：第一个匹配的路由会被使用
3. **兜底路由必要**：确保有兜底路由处理未匹配的请求
4. **性能影响**：复杂的匹配条件会增加处理时间

现在你可以基于HTTP头部信息实现精确的路由控制，完美解决WordPress等动态网站的缓存问题！

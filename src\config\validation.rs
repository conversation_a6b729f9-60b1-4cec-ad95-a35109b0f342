use anyhow::{anyhow, Result};
use regex::Regex;
use std::collections::HashSet;
use std::net::{IpAddr, SocketAddr};
use std::path::Path;

use super::Config;

/// 验证完整配置
pub fn validate_config(config: &Config) -> Result<()> {
    validate_server_config(&config.server)?;
    validate_log_config(&config.log)?;
    validate_cache_config(&config.cache)?;
    validate_compression_config(&config.compression)?;
    validate_sites_config(&config.sites)?;
    validate_monitor_config(&config.monitor)?;
    
    Ok(())
}

/// 验证服务器配置
fn validate_server_config(config: &super::ServerConfig) -> Result<()> {
    if config.http_port == 0 && config.https_port == 0 {
        return Err(anyhow!("At least one of http_port or https_port must be non-zero"));
    }
    
    if config.http_port == config.https_port && config.http_port != 0 {
        return Err(anyhow!("HTTP and HTTPS ports cannot be the same"));
    }
    
    if config.max_connections == 0 {
        return Err(anyhow!("max_connections must be greater than 0"));
    }
    
    // 验证连接池配置
    let pool = &config.connection_pool;
    if pool.max_idle_conns == 0 {
        return Err(anyhow!("connection_pool.max_idle_conns must be greater than 0"));
    }
    
    if pool.max_idle_conns_per_host > pool.max_idle_conns {
        return Err(anyhow!("connection_pool.max_idle_conns_per_host cannot exceed max_idle_conns"));
    }
    
    Ok(())
}

/// 验证日志配置
fn validate_log_config(config: &super::LogConfig) -> Result<()> {
    // 验证日志级别
    let valid_levels = ["trace", "debug", "info", "warn", "error"];
    if !valid_levels.contains(&config.level.as_str()) {
        return Err(anyhow!("Invalid log level: {}. Must be one of: {:?}", config.level, valid_levels));
    }
    
    // 验证日志格式
    if !config.formats.contains_key(&config.format) {
        return Err(anyhow!("Log format '{}' not found in formats map", config.format));
    }
    
    // 验证日志目标
    for target in &config.targets {
        match target {
            super::LogTargetConfig::File { filename, format } => {
                if filename.is_empty() {
                    return Err(anyhow!("Log file target filename cannot be empty"));
                }
                if !config.formats.contains_key(format) {
                    return Err(anyhow!("Log target format '{}' not found in formats map", format));
                }
            }
            super::LogTargetConfig::Syslog { network, address, format } => {
                if !["tcp", "udp"].contains(&network.as_str()) {
                    return Err(anyhow!("Syslog network must be 'tcp' or 'udp', got: {}", network));
                }
                
                // 验证地址格式
                if address.parse::<SocketAddr>().is_err() {
                    return Err(anyhow!("Invalid syslog address format: {}", address));
                }
                
                if !config.formats.contains_key(format) {
                    return Err(anyhow!("Syslog target format '{}' not found in formats map", format));
                }
            }
        }
    }
    
    // 验证异步日志配置
    let async_config = &config.r#async;
    if async_config.enabled {
        if async_config.channel_size == 0 {
            return Err(anyhow!("async.channel_size must be greater than 0"));
        }
        if async_config.batch_size == 0 {
            return Err(anyhow!("async.batch_size must be greater than 0"));
        }
        if async_config.max_memory_mb == 0 {
            return Err(anyhow!("async.max_memory_mb must be greater than 0"));
        }
        
        let valid_policies = ["drop_oldest", "drop_newest", "block"];
        if !valid_policies.contains(&async_config.drop_policy.as_str()) {
            return Err(anyhow!("Invalid async.drop_policy: {}. Must be one of: {:?}", 
                async_config.drop_policy, valid_policies));
        }
    }
    
    Ok(())
}

/// 验证缓存配置
fn validate_cache_config(config: &super::CacheConfig) -> Result<()> {
    if !config.enabled {
        return Ok(());
    }
    
    // 验证缓存类型
    match config.r#type {
        super::CacheType::File => {
            if config.path.is_none() {
                return Err(anyhow!("Cache path is required for file cache type"));
            }
        }
        super::CacheType::Redis => {
            if config.redis.is_none() {
                return Err(anyhow!("Redis configuration is required for redis cache type"));
            }
        }
        super::CacheType::Hybrid => {
            if config.path.is_none() {
                return Err(anyhow!("Cache path is required for hybrid cache type"));
            }
        }
        super::CacheType::Memory => {
            // 内存缓存不需要额外验证
        }
    }
    
    // 验证Redis配置
    if let Some(redis_config) = &config.redis {
        if redis_config.url.is_empty() {
            return Err(anyhow!("Redis URL cannot be empty"));
        }
        if redis_config.pool_size == 0 {
            return Err(anyhow!("Redis pool_size must be greater than 0"));
        }
        if redis_config.database > 15 {
            return Err(anyhow!("Redis database index must be between 0 and 15"));
        }
    }
    
    // 验证缓存规则
    for rule in &config.rules {
        if rule.pattern.is_empty() {
            return Err(anyhow!("Cache rule pattern cannot be empty"));
        }
        
        // 验证正则表达式
        if let Err(e) = Regex::new(&rule.pattern) {
            return Err(anyhow!("Invalid cache rule pattern '{}': {}", rule.pattern, e));
        }
    }
    
    // 验证大小格式
    validate_size_format(&config.max_size, "cache.max_size")?;
    
    Ok(())
}

/// 验证压缩配置
fn validate_compression_config(config: &super::CompressionConfig) -> Result<()> {
    if !config.enabled {
        return Ok(());
    }
    
    // 验证压缩算法
    let valid_algorithms = ["gzip", "deflate", "br", "zstd"];
    for algorithm in &config.algorithms {
        if !valid_algorithms.contains(&algorithm.as_str()) {
            return Err(anyhow!("Invalid compression algorithm: {}. Must be one of: {:?}", 
                algorithm, valid_algorithms));
        }
    }
    
    // 验证压缩级别
    if config.level > 9 {
        return Err(anyhow!("Compression level must be between 0 and 9"));
    }
    
    if config.brotli_quality > 11 {
        return Err(anyhow!("Brotli quality must be between 0 and 11"));
    }
    
    if config.zstd_level < -7 || config.zstd_level > 22 {
        return Err(anyhow!("Zstd level must be between -7 and 22"));
    }
    
    // 验证大小格式
    validate_size_format(&config.min_size, "compression.min_size")?;
    validate_size_format(&config.max_size, "compression.max_size")?;
    
    Ok(())
}

/// 验证站点配置
fn validate_sites_config(sites: &[super::SiteConfig]) -> Result<()> {
    if sites.is_empty() {
        return Err(anyhow!("At least one site must be configured"));
    }
    
    let mut site_ids = HashSet::new();
    let mut domains = HashSet::new();
    let mut default_site_count = 0;
    
    for site in sites {
        // 验证站点ID唯一性
        if !site_ids.insert(&site.site_id) {
            return Err(anyhow!("Duplicate site_id: {}", site.site_id));
        }
        
        // 验证域名唯一性
        for domain in &site.domains {
            if !domains.insert(domain) {
                return Err(anyhow!("Duplicate domain: {}", domain));
            }
        }
        
        // 统计默认站点
        if site.default_site.unwrap_or(false) {
            default_site_count += 1;
        }
        
        // 验证上游服务器
        validate_upstreams(&site.upstreams)?;
        
        // 验证路由
        validate_routes(&site.routes)?;
        
        // 验证SSL配置
        if let Some(ssl_config) = &site.ssl {
            validate_ssl_config(ssl_config)?;
        }
    }
    
    if default_site_count > 1 {
        return Err(anyhow!("Only one site can be marked as default_site"));
    }
    
    Ok(())
}

/// 验证上游服务器配置
fn validate_upstreams(upstreams: &[super::UpstreamConfig]) -> Result<()> {
    if upstreams.is_empty() {
        return Ok(()); // 静态站点可能没有上游服务器
    }
    
    let mut names = HashSet::new();
    
    for upstream in upstreams {
        // 验证名称唯一性
        if !names.insert(&upstream.name) {
            return Err(anyhow!("Duplicate upstream name: {}", upstream.name));
        }
        
        // 验证地址格式
        if upstream.address.parse::<IpAddr>().is_err() {
            return Err(anyhow!("Invalid upstream address: {}", upstream.address));
        }
        
        // 验证端口
        if upstream.port == 0 {
            return Err(anyhow!("Upstream port cannot be 0"));
        }
        
        // 验证协议
        let valid_protocols = ["http", "https", "passthrough"];
        if !valid_protocols.contains(&upstream.protocol.as_str()) {
            return Err(anyhow!("Invalid upstream protocol: {}. Must be one of: {:?}", 
                upstream.protocol, valid_protocols));
        }
        
        // 验证权重
        if upstream.weight == 0 {
            return Err(anyhow!("Upstream weight must be greater than 0"));
        }
    }
    
    Ok(())
}

/// 验证路由配置
fn validate_routes(routes: &[super::RouteConfig]) -> Result<()> {
    for route in routes {
        // 验证正则表达式
        if let Err(e) = Regex::new(&route.pattern) {
            return Err(anyhow!("Invalid route pattern '{}': {}", route.pattern, e));
        }
        
        // 验证静态目录
        if let Some(static_dir) = &route.static_dir {
            if !Path::new(static_dir).exists() {
                return Err(anyhow!("Static directory does not exist: {}", static_dir));
            }
        }
        
        // 验证匹配条件
        if let Some(conditions) = &route.match_conditions {
            if let Some(methods) = &conditions.methods {
                let valid_methods = ["GET", "POST", "PUT", "DELETE", "HEAD", "OPTIONS", "PATCH"];
                for method in methods {
                    if !valid_methods.contains(&method.as_str()) {
                        return Err(anyhow!("Invalid HTTP method: {}", method));
                    }
                }
            }
            
            // 验证查询参数条件
            if let Some(query_params) = &conditions.query_params {
                for param in query_params {
                    if let Err(e) = Regex::new(&param.pattern) {
                        return Err(anyhow!("Invalid query param pattern '{}': {}", param.pattern, e));
                    }
                }
            }
            
            // 验证Cookie条件
            if let Some(cookies) = &conditions.cookies {
                for cookie in cookies {
                    if let Some(pattern) = &cookie.name_pattern {
                        if let Err(e) = Regex::new(pattern) {
                            return Err(anyhow!("Invalid cookie name pattern '{}': {}", pattern, e));
                        }
                    }
                }
            }
        }
    }
    
    Ok(())
}

/// 验证SSL配置
fn validate_ssl_config(config: &super::SslConfig) -> Result<()> {
    if !config.enabled {
        return Ok(());
    }
    
    // 验证证书文件存在
    if !Path::new(&config.cert_file).exists() {
        return Err(anyhow!("SSL certificate file does not exist: {}", config.cert_file));
    }
    
    if !Path::new(&config.key_file).exists() {
        return Err(anyhow!("SSL key file does not exist: {}", config.key_file));
    }
    
    // 验证协议版本
    if let Some(min_version) = &config.min_version {
        let valid_versions = ["TLS1.0", "TLS1.1", "TLS1.2", "TLS1.3"];
        if !valid_versions.contains(&min_version.as_str()) {
            return Err(anyhow!("Invalid SSL min_version: {}. Must be one of: {:?}", 
                min_version, valid_versions));
        }
    }
    
    if let Some(max_version) = &config.max_version {
        let valid_versions = ["TLS1.0", "TLS1.1", "TLS1.2", "TLS1.3"];
        if !valid_versions.contains(&max_version.as_str()) {
            return Err(anyhow!("Invalid SSL max_version: {}. Must be one of: {:?}", 
                max_version, valid_versions));
        }
    }
    
    Ok(())
}

/// 验证监控配置
fn validate_monitor_config(config: &super::MonitorConfig) -> Result<()> {
    if !config.enabled {
        return Ok(());
    }
    
    if config.port == 0 {
        return Err(anyhow!("Monitor port cannot be 0"));
    }
    
    if config.username.is_empty() {
        return Err(anyhow!("Monitor username cannot be empty"));
    }
    
    if config.password.is_empty() {
        return Err(anyhow!("Monitor password cannot be empty"));
    }
    
    if config.api_key.is_empty() {
        return Err(anyhow!("Monitor API key cannot be empty"));
    }
    
    // 验证ACL IP地址
    for ip in &config.acl.allowed_ips {
        if ip.parse::<IpAddr>().is_err() && !is_valid_cidr(ip) {
            return Err(anyhow!("Invalid monitor allowed IP: {}", ip));
        }
    }
    
    for ip in &config.acl.denied_ips {
        if ip.parse::<IpAddr>().is_err() && !is_valid_cidr(ip) {
            return Err(anyhow!("Invalid monitor denied IP: {}", ip));
        }
    }
    
    Ok(())
}

/// 验证大小格式 (如 "1KB", "2MB", "1GB")
fn validate_size_format(size: &str, field_name: &str) -> Result<()> {
    let size_regex = Regex::new(r"^(\d+(?:\.\d+)?)(B|KB|MB|GB|TB)$").unwrap();
    if !size_regex.is_match(size) {
        return Err(anyhow!("Invalid size format for {}: {}. Expected format: '1KB', '2MB', etc.", 
            field_name, size));
    }
    Ok(())
}

/// 验证CIDR格式
fn is_valid_cidr(cidr: &str) -> bool {
    if let Some((ip, prefix)) = cidr.split_once('/') {
        if let Ok(addr) = ip.parse::<IpAddr>() {
            if let Ok(prefix_len) = prefix.parse::<u8>() {
                match addr {
                    IpAddr::V4(_) => prefix_len <= 32,
                    IpAddr::V6(_) => prefix_len <= 128,
                }
            } else {
                false
            }
        } else {
            false
        }
    } else {
        false
    }
}

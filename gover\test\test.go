package main

import (
	"fmt"
	"io"
	"net/http"
	"time"
)

func main() {
	fmt.Println("开始测试反向代理服务器...")

	// 测试基本连接
	testBasicConnection()

	// 测试负载均衡
	testLoadBalancing()

	// 测试缓存
	testCaching()

	fmt.Println("测试完成")
}

func testBasicConnection() {
	fmt.Println("\n=== 测试基本连接 ===")

	resp, err := http.Get("http://localhost:8080/")
	if err != nil {
		fmt.Printf("连接失败: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("读取响应失败: %v\n", err)
		return
	}

	fmt.Printf("状态码: %d\n", resp.StatusCode)
	fmt.Printf("响应长度: %d\n", len(body))
	fmt.Printf("响应头: %v\n", resp.Header)
}

func testLoadBalancing() {
	fmt.Println("\n=== 测试负载均衡 ===")

	for i := 0; i < 10; i++ {
		resp, err := http.Get("http://localhost:8080/api/test")
		if err != nil {
			fmt.Printf("请求失败: %v\n", err)
			continue
		}
		defer resp.Body.Close()

		fmt.Printf("请求 %d: 状态码 %d\n", i+1, resp.StatusCode)
		time.Sleep(100 * time.Millisecond)
	}
}

func testCaching() {
	fmt.Println("\n=== 测试缓存 ===")

	// 第一次请求
	start := time.Now()
	resp1, err := http.Get("http://localhost:8080/static/test.css")
	if err != nil {
		fmt.Printf("第一次请求失败: %v\n", err)
		return
	}
	resp1.Body.Close()
	duration1 := time.Since(start)

	// 第二次请求（应该从缓存返回）
	start = time.Now()
	resp2, err := http.Get("http://localhost:8080/static/test.css")
	if err != nil {
		fmt.Printf("第二次请求失败: %v\n", err)
		return
	}
	resp2.Body.Close()
	duration2 := time.Since(start)

	fmt.Printf("第一次请求耗时: %v\n", duration1)
	fmt.Printf("第二次请求耗时: %v\n", duration2)
	fmt.Printf("缓存效果: %v\n", duration1-duration2)
}

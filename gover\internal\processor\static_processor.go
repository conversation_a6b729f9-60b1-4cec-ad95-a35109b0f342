package processor

import (
	"fmt"
	"io"
	"mime"
	"net/http"
	"os"
	"path/filepath"
	"strings"

	"github.com/sirupsen/logrus"
)

// StaticProcessor 静态文件处理器
type StaticProcessor struct {
	*BaseProcessor
}

// NewStaticProcessor 创建静态文件处理器
func NewStaticProcessor(logger *logrus.Logger) *StaticProcessor {
	return &StaticProcessor{
		BaseProcessor: NewBaseProcessor("StaticProcessor", 300, logger),
	}
}

// Process 处理静态文件请求
func (sp *StaticProcessor) Process(req *RequestContext, resp *ResponseContext) *ProcessResult {
	// 使用数字标识进行高效判断
	if req.StaticFlag == 0 || req.ContentSource == "cache" {
		sp.LogDebug("跳过静态文件处理: StaticFlag=%d, ContentSource=%s",
			req.StaticFlag, req.ContentSource)
		return Success() // 静态文件处理被跳过或已从缓存获取，继续处理
	}
	
	// 获取文件路径
	filePath := sp.getFilePath(req)
	if filePath == "" {
		resp.StatusCode = 404
		return ErrorWithMessage(
			fmt.Errorf("无效的文件路径: %s", req.URL.Path),
			"文件路径无效",
		)
	}
	
	// 检查文件是否存在
	fileInfo, err := os.Stat(filePath)
	if err != nil {
		if os.IsNotExist(err) {
			// 尝试查找默认首页文件
			if indexFile := sp.findIndexFile(req, filePath); indexFile != "" {
				return sp.serveFile(req, resp, indexFile)
			}
			
			// 如果是目录且启用了目录列表
			if sp.isDirListingEnabled(req, filePath) {
				return sp.serveDirListing(req, resp, filePath)
			}
			
			resp.StatusCode = 404
			return ErrorWithMessage(err, "文件未找到")
		}
		
		resp.StatusCode = 500
		return ErrorWithMessage(err, "文件访问错误")
	}
	
	// 如果是目录
	if fileInfo.IsDir() {
		// 尝试查找默认首页文件
		if indexFile := sp.findIndexFile(req, filePath); indexFile != "" {
			return sp.serveFile(req, resp, indexFile)
		}
		
		// 如果启用了目录列表
		if sp.isDirListingEnabled(req, filePath) {
			return sp.serveDirListing(req, resp, filePath)
		}
		
		resp.StatusCode = 403
		return ErrorWithMessage(
			fmt.Errorf("目录访问被拒绝: %s", filePath),
			"目录访问被拒绝",
		)
	}
	
	// 服务文件
	return sp.serveFile(req, resp, filePath)
}

// ShouldProcess 判断是否应该处理
func (sp *StaticProcessor) ShouldProcess(req *RequestContext) bool {
	return req.IsStaticRequest()
}

// getFilePath 获取文件路径
func (sp *StaticProcessor) getFilePath(req *RequestContext) string {
	if req.Route == nil || req.Route.StaticDir == "" {
		return ""
	}
	
	// 清理URL路径，防止目录遍历攻击
	urlPath := filepath.Clean(req.URL.Path)
	if strings.Contains(urlPath, "..") {
		sp.LogWarn("检测到目录遍历攻击尝试: %s", req.URL.Path)
		return ""
	}
	
	// 构建完整文件路径
	relativePath := strings.TrimPrefix(urlPath, "/")
	fullPath := filepath.Join(req.Route.StaticDir, relativePath)
	
	// 确保路径在静态目录内
	staticDir, _ := filepath.Abs(req.Route.StaticDir)
	fullPath, _ = filepath.Abs(fullPath)
	
	if !strings.HasPrefix(fullPath, staticDir) {
		sp.LogWarn("文件路径超出静态目录范围: %s", fullPath)
		return ""
	}
	
	return fullPath
}

// serveFile 服务文件
func (sp *StaticProcessor) serveFile(req *RequestContext, resp *ResponseContext, filePath string) *ProcessResult {
	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		resp.StatusCode = 500
		return ErrorWithMessage(err, "文件打开失败")
	}
	defer file.Close()
	
	// 获取文件信息
	fileInfo, err := file.Stat()
	if err != nil {
		resp.StatusCode = 500
		return ErrorWithMessage(err, "获取文件信息失败")
	}
	
	// 读取文件内容
	content, err := io.ReadAll(file)
	if err != nil {
		resp.StatusCode = 500
		return ErrorWithMessage(err, "读取文件失败")
	}
	
	// 设置响应
	resp.StatusCode = 200
	resp.Body = content
	resp.ContentLength = int64(len(content))
	
	// 设置Content-Type
	contentType := sp.getContentType(req, filePath)
	resp.ContentType = contentType
	resp.Headers.Set("Content-Type", contentType)
	
	// 设置Last-Modified
	resp.Headers.Set("Last-Modified", fileInfo.ModTime().UTC().Format(http.TimeFormat))
	
	// 设置ETag
	etag := fmt.Sprintf(`"%x-%x"`, fileInfo.ModTime().Unix(), fileInfo.Size())
	resp.Headers.Set("ETag", etag)
	
	sp.LogDebug("服务静态文件: %s (%d bytes)", filePath, len(content))

	return SuccessWithMetadata(map[string]interface{}{
		"file_path": filePath,
		"file_size": len(content),
		"content_type": contentType,
	})
}

// findIndexFile 查找默认首页文件
func (sp *StaticProcessor) findIndexFile(req *RequestContext, dirPath string) string {
	if req.Route == nil {
		return ""
	}
	
	indexFiles := req.Route.IndexFiles
	if len(indexFiles) == 0 {
		indexFiles = []string{"index.html", "index.htm", "default.html"}
	}
	
	for _, indexFile := range indexFiles {
		fullPath := filepath.Join(dirPath, indexFile)
		if _, err := os.Stat(fullPath); err == nil {
			sp.LogDebug("找到默认首页文件: %s", fullPath)
			return fullPath
		}
	}
	
	return ""
}

// isDirListingEnabled 检查是否启用目录列表
func (sp *StaticProcessor) isDirListingEnabled(req *RequestContext, dirPath string) bool {
	if req.Route == nil {
		return false
	}
	
	// 检查目录是否存在
	if _, err := os.Stat(dirPath); os.IsNotExist(err) {
		return false
	}
	
	return req.Route.DirListing
}

// serveDirListing 服务目录列表
func (sp *StaticProcessor) serveDirListing(req *RequestContext, resp *ResponseContext, dirPath string) *ProcessResult {
	// 读取目录内容
	entries, err := os.ReadDir(dirPath)
	if err != nil {
		resp.StatusCode = 500
		return ErrorWithMessage(err, "读取目录失败")
	}
	
	// 生成HTML目录列表
	html := sp.generateDirListingHTML(req.URL.Path, entries, req.Route.HiddenInListing)
	
	resp.StatusCode = 200
	resp.Body = []byte(html)
	resp.ContentLength = int64(len(html))
	resp.ContentType = "text/html; charset=utf-8"
	resp.Headers.Set("Content-Type", "text/html; charset=utf-8")
	
	sp.LogDebug("服务目录列表: %s (%d entries)", dirPath, len(entries))

	return SuccessWithMetadata(map[string]interface{}{
		"dir_path": dirPath,
		"entry_count": len(entries),
	})
}

// getContentType 获取内容类型
func (sp *StaticProcessor) getContentType(req *RequestContext, filePath string) string {
	// 优先使用路由配置的MIME类型
	if req.Route != nil && req.Route.MimeTypes != nil {
		ext := strings.ToLower(filepath.Ext(filePath))
		if mimeType, exists := req.Route.MimeTypes[ext]; exists {
			return mimeType
		}
	}
	
	// 使用标准MIME类型检测
	contentType := mime.TypeByExtension(filepath.Ext(filePath))
	if contentType == "" {
		contentType = "application/octet-stream"
	}
	
	return contentType
}

// generateDirListingHTML 生成目录列表HTML
func (sp *StaticProcessor) generateDirListingHTML(urlPath string, entries []os.DirEntry, hiddenPatterns []string) string {
	html := fmt.Sprintf(`<!DOCTYPE html>
<html>
<head>
    <title>Directory listing for %s</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        h1 { color: #333; }
        ul { list-style-type: none; padding: 0; }
        li { margin: 5px 0; }
        a { text-decoration: none; color: #0066cc; }
        a:hover { text-decoration: underline; }
        .dir { font-weight: bold; }
        .file { color: #666; }
    </style>
</head>
<body>
    <h1>Directory listing for %s</h1>
    <ul>`, urlPath, urlPath)
	
	// 添加上级目录链接
	if urlPath != "/" {
		parentPath := filepath.Dir(urlPath)
		if parentPath == "." {
			parentPath = "/"
		}
		html += fmt.Sprintf(`        <li><a href="%s" class="dir">../</a></li>`, parentPath)
	}
	
	// 添加目录和文件
	for _, entry := range entries {
		if sp.isHidden(entry.Name(), hiddenPatterns) {
			continue
		}
		
		name := entry.Name()
		href := filepath.Join(urlPath, name)
		href = strings.ReplaceAll(href, "\\", "/") // 确保使用正斜杠
		
		if entry.IsDir() {
			html += fmt.Sprintf(`        <li><a href="%s/" class="dir">%s/</a></li>`, href, name)
		} else {
			html += fmt.Sprintf(`        <li><a href="%s" class="file">%s</a></li>`, href, name)
		}
	}
	
	html += `    </ul>
</body>
</html>`
	
	return html
}

// isHidden 检查文件是否应该隐藏
func (sp *StaticProcessor) isHidden(name string, hiddenPatterns []string) bool {
	for _, pattern := range hiddenPatterns {
		if matched, _ := filepath.Match(pattern, name); matched {
			return true
		}
	}
	return false
}

// SuccessStopWithMetadata 创建成功但停止继续执行且带元数据的结果
func SuccessStopWithMetadata(metadata map[string]interface{}) *ProcessResult {
	return &ProcessResult{
		Success:  true,
		Continue: false,
		Metadata: metadata,
	}
}

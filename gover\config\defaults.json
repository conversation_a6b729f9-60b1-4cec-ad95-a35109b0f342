{"server": {"http_port": 80, "https_port": 443, "read_timeout": "30s", "write_timeout": "30s", "idle_timeout": "120s", "max_header_bytes": 1048576, "connection_pool": {"max_idle_conns": 100, "max_idle_conns_per_host": 10, "idle_conn_timeout": "90s", "dial_timeout": "30s", "keep_alive": "30s", "tls_handshake_timeout": "10s", "expect_continue_timeout": "1s"}}, "log": {"level": "info", "format": "json", "targets": [{"type": "file", "path": "logs/proxy.log", "max_size": "100MB", "max_backups": 10, "max_age": 30, "compress": true}]}, "cache": {"enabled": true, "type": "file", "dir": "cache", "max_size": "1GB", "ttl": "1h", "cleanup_interval": "10m", "compression": true, "compression_algorithm": "gzip"}, "memory_cache": {"enabled": true, "max_size": "256MB", "ttl": "30m", "cleanup_interval": "5m"}, "rate_limit": {"enabled": false, "global_rps": 1000, "ip_rps": 100, "site_rps": 500, "burst": 50}, "acl": {"global_allow": [], "global_deny": [], "allow_file": "", "deny_file": "", "reload_interval": "5m"}, "ssl": {"min_version": "TLS1.2", "max_version": "TLS1.3", "prefer_server_ciphers": true, "session_cache": 1000, "session_tickets": true, "protocols": "http1.1,http2", "hsts": {"enabled": false, "max_age": 31536000, "include_subdomains": false, "preload": false}}, "compression": {"enabled": true, "algorithm": "gzip", "level": 6, "min_length": 1024, "types": ["text/html", "text/css", "text/javascript", "application/javascript", "application/json", "text/xml", "application/xml"]}, "monitor": {"enabled": false, "port": 8080, "username": "admin", "password": "admin123"}, "grpc": {"enabled": false, "max_recv_msg_size": "4MB", "max_send_msg_size": "4MB", "timeout": "30s", "keep_alive": true, "compression": "gzip", "load_balancing": "round_robin"}, "hot_reload": {"enabled": true, "check_interval": "5s", "watch_files": []}}
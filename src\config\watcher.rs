use anyhow::{Context, Result};
use std::path::{Path, PathBuf};
use std::time::{Duration, SystemTime};
use tokio::sync::watch;
use tokio::time::interval;
use tracing::{debug, error, info, warn};

use super::Config;

/// 配置文件监听器
pub struct ConfigWatcher {
    config_path: PathBuf,
    last_modified: SystemTime,
    config_sender: watch::Sender<Config>,
    check_interval: Duration,
}

impl ConfigWatcher {
    /// 创建新的配置监听器
    pub fn new(
        config_path: PathBuf,
        initial_config: Config,
        check_interval: Duration,
    ) -> (Self, watch::Receiver<Config>) {
        let last_modified = get_file_modified_time(&config_path)
            .unwrap_or_else(|_| SystemTime::now());
        
        let (config_sender, config_receiver) = watch::channel(initial_config);
        
        let watcher = Self {
            config_path,
            last_modified,
            config_sender,
            check_interval,
        };
        
        (watcher, config_receiver)
    }
    
    /// 启动配置文件监听
    pub async fn start_watching(&mut self) -> Result<()> {
        info!("Starting config file watcher for: {}", self.config_path.display());
        
        let mut interval = interval(self.check_interval);
        
        loop {
            interval.tick().await;
            
            if let Err(e) = self.check_and_reload().await {
                error!("Error checking config file: {}", e);
            }
        }
    }
    
    /// 检查并重新加载配置
    async fn check_and_reload(&mut self) -> Result<()> {
        let current_modified = get_file_modified_time(&self.config_path)
            .context("Failed to get config file modification time")?;
        
        if current_modified > self.last_modified {
            debug!("Config file modified, reloading...");
            
            match self.reload_config().await {
                Ok(()) => {
                    self.last_modified = current_modified;
                    info!("Config file reloaded successfully");
                }
                Err(e) => {
                    error!("Failed to reload config: {}", e);
                    // 不更新 last_modified，这样下次还会尝试重新加载
                    return Err(e);
                }
            }
        }
        
        Ok(())
    }
    
    /// 重新加载配置文件
    async fn reload_config(&self) -> Result<()> {
        // 读取新配置
        let new_config = Config::load(&self.config_path).await
            .context("Failed to load new config")?;
        
        // 发送新配置
        self.config_sender.send(new_config)
            .map_err(|_| anyhow::anyhow!("Failed to send new config to receivers"))?;
        
        Ok(())
    }
    
    /// 手动触发配置重新加载
    pub async fn force_reload(&mut self) -> Result<()> {
        info!("Force reloading config file");
        self.reload_config().await?;
        
        // 更新修改时间
        self.last_modified = get_file_modified_time(&self.config_path)
            .unwrap_or_else(|_| SystemTime::now());
        
        Ok(())
    }
    
    /// 获取当前配置
    pub fn get_current_config(&self) -> Config {
        self.config_sender.borrow().clone()
    }
}

/// 配置变更通知器
pub struct ConfigChangeNotifier {
    config_receiver: watch::Receiver<Config>,
}

impl ConfigChangeNotifier {
    /// 创建新的配置变更通知器
    pub fn new(config_receiver: watch::Receiver<Config>) -> Self {
        Self { config_receiver }
    }
    
    /// 等待配置变更
    pub async fn wait_for_change(&mut self) -> Result<Config> {
        self.config_receiver.changed().await
            .context("Config sender dropped")?;
        
        Ok(self.config_receiver.borrow().clone())
    }
    
    /// 获取当前配置
    pub fn get_current_config(&self) -> Config {
        self.config_receiver.borrow().clone()
    }
    
    /// 检查是否有新的配置变更
    pub fn has_changed(&mut self) -> bool {
        self.config_receiver.has_changed().unwrap_or(false)
    }
}

/// 配置热重载管理器
pub struct HotReloadManager {
    watcher: ConfigWatcher,
    notifiers: Vec<ConfigChangeNotifier>,
}

impl HotReloadManager {
    /// 创建新的热重载管理器
    pub fn new(
        config_path: PathBuf,
        initial_config: Config,
        check_interval: Duration,
    ) -> Self {
        let (watcher, config_receiver) = ConfigWatcher::new(
            config_path,
            initial_config,
            check_interval,
        );
        
        Self {
            watcher,
            notifiers: vec![ConfigChangeNotifier::new(config_receiver)],
        }
    }
    
    /// 创建新的配置变更通知器
    pub fn create_notifier(&self) -> ConfigChangeNotifier {
        ConfigChangeNotifier::new(self.watcher.config_sender.subscribe())
    }
    
    /// 启动热重载
    pub async fn start(&mut self) -> Result<()> {
        self.watcher.start_watching().await
    }
    
    /// 手动重新加载配置
    pub async fn reload(&mut self) -> Result<()> {
        self.watcher.force_reload().await
    }
    
    /// 获取当前配置
    pub fn get_current_config(&self) -> Config {
        self.watcher.get_current_config()
    }
}

/// 配置差异检测器
pub struct ConfigDiffer;

impl ConfigDiffer {
    /// 检测配置变更
    pub fn detect_changes(old_config: &Config, new_config: &Config) -> ConfigChanges {
        let mut changes = ConfigChanges::default();
        
        // 检测服务器配置变更
        if old_config.server.http_port != new_config.server.http_port
            || old_config.server.https_port != new_config.server.https_port {
            changes.server_ports_changed = true;
        }
        
        // 检测站点配置变更
        if old_config.sites.len() != new_config.sites.len() {
            changes.sites_changed = true;
        } else {
            for (old_site, new_site) in old_config.sites.iter().zip(new_config.sites.iter()) {
                if old_site.site_id != new_site.site_id
                    || old_site.domains != new_site.domains
                    || old_site.upstreams.len() != new_site.upstreams.len()
                    || old_site.routes.len() != new_site.routes.len() {
                    changes.sites_changed = true;
                    break;
                }
            }
        }
        
        // 检测缓存配置变更
        if old_config.cache.enabled != new_config.cache.enabled
            || old_config.cache.r#type != new_config.cache.r#type
            || old_config.cache.max_size != new_config.cache.max_size {
            changes.cache_changed = true;
        }
        
        // 检测日志配置变更
        if old_config.log.level != new_config.log.level
            || old_config.log.targets.len() != new_config.log.targets.len() {
            changes.logging_changed = true;
        }
        
        // 检测压缩配置变更
        if old_config.compression.enabled != new_config.compression.enabled
            || old_config.compression.algorithms != new_config.compression.algorithms {
            changes.compression_changed = true;
        }
        
        // 检测监控配置变更
        if old_config.monitor.enabled != new_config.monitor.enabled
            || old_config.monitor.port != new_config.monitor.port {
            changes.monitor_changed = true;
        }
        
        changes
    }
}

/// 配置变更信息
#[derive(Debug, Default, Clone)]
pub struct ConfigChanges {
    pub server_ports_changed: bool,
    pub sites_changed: bool,
    pub cache_changed: bool,
    pub logging_changed: bool,
    pub compression_changed: bool,
    pub monitor_changed: bool,
}

impl ConfigChanges {
    /// 是否有任何变更
    pub fn has_changes(&self) -> bool {
        self.server_ports_changed
            || self.sites_changed
            || self.cache_changed
            || self.logging_changed
            || self.compression_changed
            || self.monitor_changed
    }
    
    /// 是否需要重启服务器
    pub fn requires_restart(&self) -> bool {
        self.server_ports_changed
    }
    
    /// 获取变更摘要
    pub fn summary(&self) -> Vec<&'static str> {
        let mut summary = Vec::new();
        
        if self.server_ports_changed {
            summary.push("server ports");
        }
        if self.sites_changed {
            summary.push("sites");
        }
        if self.cache_changed {
            summary.push("cache");
        }
        if self.logging_changed {
            summary.push("logging");
        }
        if self.compression_changed {
            summary.push("compression");
        }
        if self.monitor_changed {
            summary.push("monitor");
        }
        
        summary
    }
}

/// 获取文件修改时间
fn get_file_modified_time<P: AsRef<Path>>(path: P) -> Result<SystemTime> {
    let metadata = std::fs::metadata(path.as_ref())
        .with_context(|| format!("Failed to get metadata for: {}", path.as_ref().display()))?;
    
    metadata.modified()
        .context("Failed to get file modification time")
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::NamedTempFile;
    use tokio::time::timeout;
    
    #[tokio::test]
    async fn test_config_watcher() {
        // 创建临时配置文件
        let temp_file = NamedTempFile::new().unwrap();
        let config_path = temp_file.path().to_path_buf();
        
        // 写入初始配置
        let initial_config = r#"
        {
            "server": {
                "http_port": 80,
                "https_port": 443,
                "read_timeout": "30s",
                "write_timeout": "30s",
                "idle_timeout": "60s",
                "max_connections": 1000,
                "connection_pool": {
                    "max_idle_conns": 20,
                    "max_idle_conns_per_host": 2,
                    "idle_conn_timeout": "90s",
                    "dial_timeout": "30s",
                    "keep_alive": "30s",
                    "max_conns_per_host": 0,
                    "disable_keep_alives": false
                }
            },
            "sites": []
        }
        "#;
        
        tokio::fs::write(&config_path, initial_config).await.unwrap();
        
        // 加载配置
        let config = Config::load(&config_path).await.unwrap();
        
        // 创建监听器
        let (mut watcher, mut receiver) = ConfigWatcher::new(
            config_path.clone(),
            config,
            Duration::from_millis(100),
        );
        
        // 启动监听器
        let watcher_handle = tokio::spawn(async move {
            watcher.start_watching().await
        });
        
        // 等待一段时间确保监听器启动
        tokio::time::sleep(Duration::from_millis(200)).await;
        
        // 修改配置文件
        let updated_config = initial_config.replace("80", "8080");
        tokio::fs::write(&config_path, updated_config).await.unwrap();
        
        // 等待配置变更通知
        let result = timeout(Duration::from_secs(1), receiver.changed()).await;
        assert!(result.is_ok());
        
        // 验证配置已更新
        let new_config = receiver.borrow().clone();
        assert_eq!(new_config.server.http_port, 8080);
        
        // 清理
        watcher_handle.abort();
    }
}

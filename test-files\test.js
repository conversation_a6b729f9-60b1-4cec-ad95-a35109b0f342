// Rust反向代理服务器 - 测试JavaScript文件

console.log('🚀 Rust反向代理服务器测试开始');

// 测试功能类
class ProxyServerTest {
    constructor() {
        this.testResults = [];
        this.startTime = Date.now();
    }

    // 测试静态文件服务
    async testStaticFiles() {
        console.log('📁 测试静态文件服务...');
        
        const files = [
            '/test.txt',
            '/test.json',
            '/test.css',
            '/index.html'
        ];

        for (const file of files) {
            try {
                const response = await fetch(file);
                const result = {
                    file: file,
                    status: response.status,
                    contentType: response.headers.get('content-type'),
                    contentEncoding: response.headers.get('content-encoding'),
                    cacheControl: response.headers.get('cache-control'),
                    responseTime: response.headers.get('x-response-time'),
                    requestId: response.headers.get('x-request-id'),
                    success: response.ok
                };
                
                this.testResults.push(result);
                console.log(`✅ ${file}: ${response.status} (${result.contentType})`);
            } catch (error) {
                console.error(`❌ ${file}: ${error.message}`);
                this.testResults.push({
                    file: file,
                    error: error.message,
                    success: false
                });
            }
        }
    }

    // 测试压缩功能
    async testCompression() {
        console.log('🗜️ 测试压缩功能...');
        
        const testUrl = '/test.txt';
        
        try {
            // 测试支持压缩的请求
            const compressedResponse = await fetch(testUrl, {
                headers: {
                    'Accept-Encoding': 'gzip, deflate, br, zstd'
                }
            });
            
            const contentEncoding = compressedResponse.headers.get('content-encoding');
            const contentLength = compressedResponse.headers.get('content-length');
            
            console.log(`✅ 压缩测试: ${contentEncoding || '无压缩'} (${contentLength} bytes)`);
            
            // 测试不支持压缩的请求
            const uncompressedResponse = await fetch(testUrl, {
                headers: {
                    'Accept-Encoding': ''
                }
            });
            
            const uncompressedLength = uncompressedResponse.headers.get('content-length');
            console.log(`📊 未压缩大小: ${uncompressedLength} bytes`);
            
            if (contentEncoding && contentLength && uncompressedLength) {
                const ratio = ((uncompressedLength - contentLength) / uncompressedLength * 100).toFixed(2);
                console.log(`📈 压缩率: ${ratio}%`);
            }
            
        } catch (error) {
            console.error(`❌ 压缩测试失败: ${error.message}`);
        }
    }

    // 测试缓存功能
    async testCache() {
        console.log('💾 测试缓存功能...');
        
        const testUrl = '/test.json';
        
        try {
            // 第一次请求（缓存未命中）
            const firstResponse = await fetch(testUrl);
            const firstCacheStatus = firstResponse.headers.get('x-cache');
            console.log(`🔍 第一次请求: ${firstCacheStatus || 'MISS'}`);
            
            // 第二次请求（应该缓存命中）
            const secondResponse = await fetch(testUrl);
            const secondCacheStatus = secondResponse.headers.get('x-cache');
            console.log(`🎯 第二次请求: ${secondCacheStatus || 'MISS'}`);
            
            if (secondCacheStatus === 'HIT') {
                console.log('✅ 缓存功能正常工作');
            } else {
                console.log('⚠️ 缓存可能未启用或配置问题');
            }
            
        } catch (error) {
            console.error(`❌ 缓存测试失败: ${error.message}`);
        }
    }

    // 测试错误处理
    async testErrorHandling() {
        console.log('🚨 测试错误处理...');
        
        const errorUrls = [
            '/nonexistent.txt',  // 404
            '/forbidden',        // 可能的403
        ];

        for (const url of errorUrls) {
            try {
                const response = await fetch(url);
                const contentType = response.headers.get('content-type');
                const errorCode = response.headers.get('x-error-code');
                
                console.log(`📋 ${url}: ${response.status} (${contentType})`);
                
                if (response.status >= 400) {
                    const errorContent = await response.text();
                    if (errorContent.includes('<!DOCTYPE html>')) {
                        console.log('✅ 错误页面格式正确');
                    }
                }
                
            } catch (error) {
                console.error(`❌ 错误测试失败: ${error.message}`);
            }
        }
    }

    // 测试安全头部
    async testSecurityHeaders() {
        console.log('🔒 测试安全头部...');
        
        try {
            const response = await fetch('/index.html');
            
            const securityHeaders = {
                'x-frame-options': response.headers.get('x-frame-options'),
                'x-content-type-options': response.headers.get('x-content-type-options'),
                'x-xss-protection': response.headers.get('x-xss-protection'),
                'strict-transport-security': response.headers.get('strict-transport-security'),
                'content-security-policy': response.headers.get('content-security-policy'),
                'referrer-policy': response.headers.get('referrer-policy')
            };
            
            console.log('🛡️ 安全头部检查:');
            for (const [header, value] of Object.entries(securityHeaders)) {
                if (value) {
                    console.log(`  ✅ ${header}: ${value}`);
                } else {
                    console.log(`  ⚠️ ${header}: 未设置`);
                }
            }
            
        } catch (error) {
            console.error(`❌ 安全头部测试失败: ${error.message}`);
        }
    }

    // 运行所有测试
    async runAllTests() {
        console.log('🧪 开始运行所有测试...');
        console.log('='.repeat(50));
        
        await this.testStaticFiles();
        console.log('');
        
        await this.testCompression();
        console.log('');
        
        await this.testCache();
        console.log('');
        
        await this.testErrorHandling();
        console.log('');
        
        await this.testSecurityHeaders();
        console.log('');
        
        const endTime = Date.now();
        const totalTime = endTime - this.startTime;
        
        console.log('='.repeat(50));
        console.log(`🏁 测试完成，总耗时: ${totalTime}ms`);
        console.log(`📊 测试结果: ${this.testResults.length} 个测试用例`);
        
        const successCount = this.testResults.filter(r => r.success).length;
        const failureCount = this.testResults.length - successCount;
        
        console.log(`✅ 成功: ${successCount}`);
        console.log(`❌ 失败: ${failureCount}`);
        
        return {
            total: this.testResults.length,
            success: successCount,
            failure: failureCount,
            results: this.testResults,
            totalTime: totalTime
        };
    }
}

// 页面加载完成后自动运行测试
if (typeof window !== 'undefined') {
    window.addEventListener('DOMContentLoaded', async () => {
        const tester = new ProxyServerTest();
        const results = await tester.runAllTests();
        
        // 在页面上显示测试结果
        const resultDiv = document.getElementById('test-results');
        if (resultDiv) {
            resultDiv.innerHTML = `
                <h3>测试结果</h3>
                <p>总测试数: ${results.total}</p>
                <p>成功: ${results.success}</p>
                <p>失败: ${results.failure}</p>
                <p>总耗时: ${results.totalTime}ms</p>
            `;
        }
    });
}

// 导出测试类（用于Node.js环境）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ProxyServerTest;
}

console.log('📝 测试脚本加载完成');

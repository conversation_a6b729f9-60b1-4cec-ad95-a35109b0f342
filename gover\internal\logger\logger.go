package logger

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"regexp"
	"runtime"
	"strings"
	"time"

	"reverse-proxy/internal/config"

	"github.com/sirupsen/logrus"
	"golang.org/x/text/encoding/simplifiedchinese"
	"gopkg.in/natefinch/lumberjack.v2"
)

var (
	logger     *logrus.Logger
	formatters map[string]logrus.Formatter
	appWriters []io.Writer // 新增：应用日志独立输出目标
)

// Init 初始化日志系统
func Init(cfg config.LogConfig) error {
	logger = logrus.New()

	// 设置日志级别
	level, err := logrus.ParseLevel(cfg.Level)
	if err != nil {
		return fmt.Errorf("无效的日志级别: %s", cfg.Level)
	}
	logger.SetLevel(level)

	// 初始化格式化器
	initFormatters()

	// 设置应用日志输出（只输出到log.file/syslog/console，不输出到访问日志文件）
	if err := setupAppLogOutput(cfg); err != nil {
		return err
	}

	// 设置格式化器
	if err := setupFormatter(cfg); err != nil {
		return err
	}

	return nil
}

// initFormatters 初始化预定义的格式化器
func initFormatters() {
	formatters = make(map[string]logrus.Formatter)

	// combined 格式: $remote_addr - $remote_user [$time_local] "$request" $status $body_bytes_sent "$http_referer" "$http_user_agent"
	formatters["combined"] = &CombinedFormatter{}

	// common 格式: $remote_addr - $remote_user [$time_local] "$request" $status $body_bytes_sent
	formatters["common"] = &CommonFormatter{}

	// simple 格式: $remote_addr - $remote_user [$time_local] "$request" $status
	formatters["simple"] = &SimpleFormatter{}

	// json 格式: JSON格式输出
	formatters["json"] = &logrus.JSONFormatter{
		TimestampFormat: "2006-01-02 15:04:05",
	}

	// 动态注册自定义模板格式
	cfg := config.GlobalConfig() // 需要提供全局配置获取方法
	if cfg != nil {
		for name, tpl := range cfg.Log.Formats {
			formatters[name] = &TemplateFormatter{Template: tpl}
		}
	}
}

// setupAppLogOutput 设置应用日志输出目标（只用于Info/Error等）
func setupAppLogOutput(cfg config.LogConfig) error {
	appWriters = nil
	// 控制台输出
	appWriters = append(appWriters, os.Stdout)

	// 文件输出
	if cfg.File != "" {
		fileName := replaceLogVars(cfg.File, map[string]string{})
		if err := os.MkdirAll(filepath.Dir(fileName), 0755); err != nil {
			return fmt.Errorf("创建日志目录失败: %w", err)
		}
		fileWriter := &lumberjack.Logger{
			Filename:   fileName,
			MaxSize:    cfg.MaxSize, // MB
			MaxBackups: cfg.MaxBackups,
			MaxAge:     cfg.MaxAge, // days
			Compress:   true,
		}
		// 在Windows系统上，为文件输出包装GBK编码器
		if runtime.GOOS == "windows" {
			appWriters = append(appWriters, NewGBKFileWriter(fileWriter))
		} else {
			appWriters = append(appWriters, fileWriter)
		}
	}

	// Syslog输出（跨平台实现）
	if cfg.Syslog != "" {
		// cfg.Syslog 格式应为 address（如 127.0.0.1:514），默认UDP
		syslogWriter, err := NewSyslogWriter("udp", cfg.Syslog)
		if err == nil {
			appWriters = append(appWriters, syslogWriter)
		} else {
			fmt.Fprintf(os.Stderr, "[日志] syslog 连接失败: %v\n", err)
		}
	}

	logger.SetOutput(io.MultiWriter(appWriters...))
	return nil
}

// setupFormatter 设置格式化器
func setupFormatter(cfg config.LogConfig) error {
	// 在Windows系统上，我们需要为控制台和文件使用不同的编码
	// 控制台使用UTF-8，文件使用GBK
	if runtime.GOOS == "windows" {
		// Windows系统使用UTF-8格式化器（控制台兼容）
		logger.SetFormatter(&logrus.TextFormatter{
			TimestampFormat:  "2006-01-02 15:04:05",
			FullTimestamp:    true,
			ForceColors:      false,
			DisableColors:    true,
			DisableTimestamp: false,
			DisableQuote:     true,
			QuoteEmptyFields: false,
		})
	} else {
		// 其他系统使用标准格式化器
		logger.SetFormatter(&logrus.TextFormatter{
			TimestampFormat:  "2006-01-02 15:04:05",
			FullTimestamp:    true,
			ForceColors:      false,
			DisableColors:    true,
			DisableTimestamp: false,
			DisableQuote:     true,
			QuoteEmptyFields: false,
		})
	}
	return nil
}

// GetFormatter 获取指定格式的格式化器
func GetFormatter(format string) (logrus.Formatter, error) {
	formatter, exists := formatters[format]
	if !exists {
		return nil, fmt.Errorf("未知的日志格式: %s", format)
	}
	return formatter, nil
}

// Info 记录信息日志
func Info(args ...interface{}) {
	logger.Info(args...)
}

// Error 记录错误日志
func Error(args ...interface{}) {
	logger.Error(args...)
}

// Debug 记录调试日志
func Debug(args ...interface{}) {
	logger.Debug(args...)
}

// Debugf 记录格式化调试日志
func Debugf(format string, args ...interface{}) {
	logger.Debugf(format, args...)
}

// Infof 记录格式化信息日志
func Infof(format string, args ...interface{}) {
	logger.Infof(format, args...)
}

// Errorf 记录格式化错误日志
func Errorf(format string, args ...interface{}) {
	logger.Errorf(format, args...)
}

// Warnf 记录格式化警告日志
func Warnf(format string, args ...interface{}) {
	logger.Warnf(format, args...)
}

// Warn 记录警告日志
func Warn(args ...interface{}) {
	logger.Warn(args...)
}

// Fatal 记录致命错误日志并退出
func Fatal(args ...interface{}) {
	logger.Fatal(args...)
}

// WithFields 创建带字段的日志记录器
func WithFields(fields logrus.Fields) *logrus.Entry {
	return logger.WithFields(fields)
}

// safeString 保证日志字段为nil或空时输出'-'
func safeString(val interface{}) string {
	if val == nil {
		return "-"
	}
	s := fmt.Sprintf("%v", val)
	if s == "<nil>" || s == "" {
		return "-"
	}
	return s
}

// CombinedFormatter 组合格式格式化器
type CombinedFormatter struct{}

func (f *CombinedFormatter) Format(entry *logrus.Entry) ([]byte, error) {
	timestamp := entry.Time.Format("02/Jan/2006:15:04:05 -0700")

	remoteAddr := safeString(entry.Data["remote_addr"])
	remoteUser := safeString(entry.Data["remote_user"])
	request := safeString(entry.Data["request"])
	status := safeString(entry.Data["status"])
	bodyBytesSent := safeString(entry.Data["body_bytes_sent"])
	httpReferer := safeString(entry.Data["http_referer"])
	httpUserAgent := safeString(entry.Data["http_user_agent"])

	line := fmt.Sprintf("%s - %s [%s] \"%s\" %s %s \"%s\" \"%s\"\n",
		remoteAddr, remoteUser, timestamp, request, status, bodyBytesSent, httpReferer, httpUserAgent)

	return []byte(line), nil
}

// CommonFormatter 通用格式格式化器
type CommonFormatter struct{}

func (f *CommonFormatter) Format(entry *logrus.Entry) ([]byte, error) {
	timestamp := entry.Time.Format("02/Jan/2006:15:04:05 -0700")

	remoteAddr := safeString(entry.Data["remote_addr"])
	remoteUser := safeString(entry.Data["remote_user"])
	request := safeString(entry.Data["request"])
	status := safeString(entry.Data["status"])
	bodyBytesSent := safeString(entry.Data["body_bytes_sent"])

	line := fmt.Sprintf("%s - %s [%s] \"%s\" %s %s\n",
		remoteAddr, remoteUser, timestamp, request, status, bodyBytesSent)

	return []byte(line), nil
}

// SimpleFormatter 简单格式格式化器
type SimpleFormatter struct{}

func (f *SimpleFormatter) Format(entry *logrus.Entry) ([]byte, error) {
	timestamp := entry.Time.Format("02/Jan/2006:15:04:05 -0700")

	remoteAddr := safeString(entry.Data["remote_addr"])
	remoteUser := safeString(entry.Data["remote_user"])
	request := safeString(entry.Data["request"])
	status := safeString(entry.Data["status"])

	line := fmt.Sprintf("%s - %s [%s] \"%s\" %s\n",
		remoteAddr, remoteUser, timestamp, request, status)

	return []byte(line), nil
}

// TemplateFormatter 支持自定义模板格式化
type TemplateFormatter struct {
	Template string
}

func (f *TemplateFormatter) Format(entry *logrus.Entry) ([]byte, error) {
	result := f.Template
	// 支持${key}变量替换
	re := regexp.MustCompile(`\$\{([a-zA-Z0-9_]+)\}`)
	result = re.ReplaceAllStringFunc(result, func(s string) string {
		key := re.FindStringSubmatch(s)
		if len(key) == 2 {
			v, ok := entry.Data[key[1]]
			if !ok || v == nil || v == "" || v == "<nil>" {
				return "-"
			}
			return safeString(v)
		}
		return "-"
	})
	// 特殊处理时间
	result = strings.ReplaceAll(result, "${time}", entry.Time.Format("2006-01-02T15:04:05Z07:00"))
	if !strings.HasSuffix(result, "\n") {
		result += "\n"
	}
	return []byte(result), nil
}

// GetLogger 获取日志实例
func GetLogger() *logrus.Logger {
	return logger
}

// LogAccess 记录访问日志
func LogAccess(format string, fields logrus.Fields) {
	//fmt.Fprintf(os.Stderr, "LogAccess called with fields: %+v\n", fields)
	// 防止无效日志刷屏：仅有真实request字段时才写日志
	if fields == nil || fields["request"] == nil || fields["request"] == "-" || fields["request"] == "" {
		return
	}
	formatter, exists := formatters[format]
	if !exists {
		formatter = formatters["combined"]
	}
	entry := logrus.NewEntry(logger)
	entry.Data = fields
	entry.Time = time.Now()
	entry.Level = logrus.InfoLevel
	if formatted, err := formatter.Format(entry); err == nil {
		logger.Out.Write(formatted)
	}
}

// LoggerInterface 日志器接口
type LoggerInterface interface {
	Log(fields logrus.Fields, format string)
}

// MultiLogger 支持多目标输出（仅访问日志专用）
type MultiLogger struct {
	writers []io.Writer
	format  string
}

// NewMultiLogger 创建多目标日志器（支持异步）
func NewMultiLogger(targets []config.LogTargetConfig, vars map[string]string) interface{} {
	// 获取全局配置中的异步日志配置
	cfg := config.GlobalConfig()
	if cfg != nil && cfg.Log.Async.Enabled {
		// 使用异步日志器
		return NewAsyncMultiLogger(targets, vars, cfg.Log.Async)
	}

	// 使用同步日志器（原有逻辑）
	var writers []io.Writer
	for _, t := range targets {
		switch t.Type {
		case "file":
			fname := replaceLogVars(t.Filename, vars)
			_ = os.MkdirAll(filepath.Dir(fname), 0755)
			writers = append(writers, &lumberjack.Logger{
				Filename:   fname,
				MaxSize:    100,
				MaxBackups: 10,
				MaxAge:     30,
				Compress:   true,
			})
		case "syslog":
			// 支持UDP/TCP，默认UDP
			network := t.Network
			if network == "" {
				network = "udp"
			}
			syslogWriter, err := NewSyslogWriter(network, t.Address)
			if err == nil {
				writers = append(writers, syslogWriter)
			} else {
				fmt.Fprintf(os.Stderr, "[日志] syslog 连接失败: %v\n", err)
			}
		case "console":
			writers = append(writers, os.Stdout)
		}
	}
	return &MultiLogger{
		writers: writers,
		format:  "combined",
	}
}

func (l *MultiLogger) Log(fields logrus.Fields, format string) {
	//fmt.Fprintf(os.Stderr, "MultiLogger.Log called with fields: %+v\n", fields)
	formatter, exists := formatters[format]
	if !exists {
		formatter = formatters["combined"]
	}
	entry := logrus.NewEntry(logger)
	entry.Data = fields
	entry.Time = time.Now()
	entry.Level = logrus.InfoLevel
	if formatted, err := formatter.Format(entry); err == nil {
		for _, w := range l.writers {
			w.Write(formatted)
		}
	}
}

// replaceLogVars 日志文件名变量替换
func replaceLogVars(s string, vars map[string]string) string {
	for k, v := range vars {
		s = strings.ReplaceAll(s, "${"+k+"}", v)
	}
	// 支持${date}
	s = strings.ReplaceAll(s, "${date}", time.Now().Format("2006_01_02"))
	return s
}

// GBKFileWriter 包装文件Writer，将UTF-8转换为GBK编码
type GBKFileWriter struct {
	writer io.Writer
}

// NewGBKFileWriter 创建一个新的GBK文件Writer
func NewGBKFileWriter(w io.Writer) *GBKFileWriter {
	return &GBKFileWriter{writer: w}
}

// Write 实现io.Writer接口，将UTF-8内容转换为GBK后写入
func (w *GBKFileWriter) Write(p []byte) (n int, err error) {
	// 只在Windows系统上进行编码转换
	if runtime.GOOS == "windows" {
		// 将UTF-8转换为GBK
		encoder := simplifiedchinese.GBK.NewEncoder()
		gbkBytes, err := encoder.Bytes(p)
		if err != nil {
			// 转换失败，使用原始字节
			return w.writer.Write(p)
		}
		// 写入GBK编码的字节
		_, err = w.writer.Write(gbkBytes)
		if err != nil {
			return 0, err
		}
		// 返回原始字节长度（logrus期望的）
		return len(p), nil
	} else {
		// 非Windows系统直接写入
		return w.writer.Write(p)
	}
}

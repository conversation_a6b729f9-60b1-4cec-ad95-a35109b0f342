# SSL/TLS优化

## 📋 概述

SSL/TLS优化通过现代加密配置、证书缓存、会话复用等技术，在保证安全性的同时显著提升HTTPS性能，减少握手开销和延迟。

## 🎯 优化目标

- **提升HTTPS性能**：减少TLS握手开销
- **增强安全性**：使用现代加密算法和协议
- **优化用户体验**：减少页面加载时间
- **降低服务器负载**：减少CPU和内存使用

## 🔧 技术实现

### 1. 证书缓存机制

#### 内存缓存设计
```go
type SSLManager struct {
    certCache   map[string]*tls.Certificate  // 证书内存缓存
    certMu      sync.RWMutex                // 读写锁保护
    domainCerts map[string]config.SSLConfig // 域名证书映射
    sessionCache tls.ClientSessionCache      // 会话缓存
}
```

#### 证书加载策略
```go
func (sm *SSLManager) GetCertificate(hello *tls.ClientHelloInfo) (*tls.Certificate, error) {
    host := hello.ServerName
    
    // 1. 尝试从缓存获取
    sm.certMu.RLock()
    if cert, ok := sm.certCache[host]; ok {
        sm.certMu.RUnlock()
        return cert, nil
    }
    
    // 2. 泛域名匹配
    for pattern, cert := range sm.certCache {
        if strings.HasPrefix(pattern, "*.") {
            if strings.HasSuffix(host, pattern[1:]) {
                sm.certMu.RUnlock()
                return cert, nil
            }
        }
    }
    sm.certMu.RUnlock()
    
    // 3. 加载并缓存新证书
    return sm.loadAndCacheCertificate(host)
}
```

#### 缓存优势
- **避免重复加载**：证书文件只读取一次
- **支持泛域名**：`*.example.com` 匹配所有子域名
- **并发安全**：读写锁保护多线程访问
- **内存高效**：按需加载，智能缓存

### 2. 现代TLS配置

#### TLS版本控制
```go
func (sm *SSLManager) CreateTLSConfig(sslConfig config.SSLConfig) *tls.Config {
    tlsConfig := &tls.Config{
        GetCertificate: sm.GetCertificate,
        MinVersion:     tls.VersionTLS12,  // 最低TLS 1.2
        MaxVersion:     tls.VersionTLS13,  // 支持TLS 1.3
    }
    
    // 动态版本配置
    if sslConfig.MinVersion != "" {
        tlsConfig.MinVersion = sm.parseTLSVersion(sslConfig.MinVersion)
    }
    
    return tlsConfig
}
```

#### 安全加密套件
```go
// 推荐的现代加密套件
var SecureCipherSuites = []uint16{
    tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
    tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305,
    tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
    tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
    tls.TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305,
    tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
}
```

### 3. 会话复用优化

#### 会话缓存
```go
// 创建LRU会话缓存
sessionCache := tls.NewLRUClientSessionCache(1000)

tlsConfig := &tls.Config{
    ClientSessionCache: sessionCache,
    SessionTicketsDisabled: false,  // 启用会话票据
}
```

#### 会话票据
- **减少握手**：复用之前的会话参数
- **提升性能**：避免完整的TLS握手
- **降低延迟**：减少网络往返次数

### 4. HTTP/2支持

#### 协议协商
```go
tlsConfig := &tls.Config{
    NextProtos: []string{"h2", "http/1.1"},  // 优先HTTP/2
}
```

#### HTTP/2优势
- **多路复用**：单连接处理多个请求
- **头部压缩**：减少传输数据量
- **服务器推送**：主动推送资源

## ⚙️ 配置说明

### 基础SSL配置
```json
{
  "ssl": {
    "enabled": true,
    "cert_file": "ca/example.com/fullchain.pem",
    "key_file": "ca/example.com/privkey.pem",
    "protocols": "http1.1,http2",
    "min_version": "TLS1.2",
    "max_version": "TLS1.3",
    "prefer_server_ciphers": true,
    "session_cache": 1000,
    "session_tickets": true
  }
}
```

### 高级安全配置
```json
{
  "ssl": {
    "enabled": true,
    "cert_file": "ca/example.com/fullchain.pem", 
    "key_file": "ca/example.com/privkey.pem",
    "cipher_suites": [
      "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384",
      "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305",
      "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"
    ],
    "hsts": {
      "enabled": true,
      "max_age": 31536000,
      "include_subdomains": true,
      "preload": false
    },
    "ocsp": false
  }
}
```

### HSTS安全头配置
```json
{
  "hsts": {
    "enabled": true,
    "max_age": 31536000,        // 1年
    "include_subdomains": true,  // 包含子域名
    "preload": false            // HSTS预加载
  }
}
```

### 参数详解

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `min_version` | string | TLS1.2 | 最低TLS版本 |
| `max_version` | string | TLS1.3 | 最高TLS版本 |
| `prefer_server_ciphers` | bool | true | 服务器优先选择加密套件 |
| `session_cache` | int | 1000 | 会话缓存大小 |
| `session_tickets` | bool | true | 启用会话票据 |
| `cipher_suites` | []string | 默认安全套件 | 自定义加密套件 |

## 📊 性能指标

### SSL统计信息
```json
{
  "ssl": {
    "cached_certificates": 15,
    "configured_domains": 20,
    "session_cache_size": 1000,
    "tls_handshakes": {
      "total": 50000,
      "resumed": 35000,
      "full": 15000,
      "resume_rate": 0.70
    },
    "cipher_usage": {
      "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384": 30000,
      "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305": 15000,
      "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256": 5000
    }
  }
}
```

### 关键指标
- **证书缓存命中率**：避免重复加载
- **会话复用率**：减少完整握手
- **握手时间**：TLS握手平均耗时
- **加密套件分布**：各套件使用情况

## 🔍 故障排查

### 常见问题

#### 1. 证书加载失败
**现象**：HTTPS访问失败，证书错误
**排查**：
```bash
# 检查证书文件
openssl x509 -in cert.pem -text -noout

# 检查私钥文件
openssl rsa -in key.pem -check

# 验证证书和私钥匹配
openssl x509 -noout -modulus -in cert.pem | openssl md5
openssl rsa -noout -modulus -in key.pem | openssl md5
```

**解决**：
- 检查文件路径和权限
- 验证证书和私钥匹配
- 确认证书未过期

#### 2. TLS握手失败
**现象**：连接建立失败或缓慢
**排查**：
```bash
# 测试TLS连接
openssl s_client -connect your-domain:443 -servername your-domain

# 检查支持的协议和加密套件
nmap --script ssl-enum-ciphers -p 443 your-domain
```

**解决**：
- 调整TLS版本配置
- 更新加密套件列表
- 检查客户端兼容性

#### 3. 性能问题
**现象**：HTTPS响应缓慢
**解决**：
- 启用会话复用
- 优化证书链长度
- 使用ECDSA证书（更快）

## 🚀 最佳实践

### 1. 证书管理
```json
{
  "sites": [
    {
      "name": "main_site",
      "domains": ["example.com", "www.example.com"],
      "ssl": {
        "cert_file": "ca/example.com/fullchain.pem",
        "key_file": "ca/example.com/privkey.pem"
      }
    },
    {
      "name": "api_site", 
      "domains": ["*.api.example.com"],
      "ssl": {
        "cert_file": "ca/api.example.com/wildcard.pem",
        "key_file": "ca/api.example.com/wildcard.key"
      }
    }
  ]
}
```

### 2. 安全头部
```go
func (sm *SSLManager) AddSecurityHeaders(w http.ResponseWriter, sslConfig config.SSLConfig) {
    // HSTS头部
    if sslConfig.HSTS.Enabled {
        sm.AddHSTSHeaders(w, sslConfig.HSTS)
    }
    
    // 其他安全头部
    w.Header().Set("X-Content-Type-Options", "nosniff")
    w.Header().Set("X-Frame-Options", "DENY")
    w.Header().Set("X-XSS-Protection", "1; mode=block")
}
```

### 3. 证书自动更新
```go
type CertificateWatcher struct {
    certFiles map[string]string
    sslManager *SSLManager
}

func (cw *CertificateWatcher) WatchCertificates() {
    ticker := time.NewTicker(24 * time.Hour)
    for range ticker.C {
        cw.checkAndReloadCertificates()
    }
}
```

### 4. 监控告警
```json
{
  "alerts": {
    "certificate_expiry": {
      "threshold": "30d",
      "message": "证书即将过期"
    },
    "tls_handshake_slow": {
      "threshold": "500ms",
      "message": "TLS握手时间过长"
    },
    "session_resume_rate_low": {
      "threshold": 0.5,
      "message": "会话复用率过低"
    }
  }
}
```

## 📈 性能测试

### TLS性能测试
```bash
#!/bin/bash
# TLS性能测试

# 测试TLS握手时间
echo "Testing TLS handshake performance:"
for i in {1..10}; do
  time openssl s_client -connect your-domain:443 -servername your-domain < /dev/null
done

# 测试会话复用
echo "Testing session resumption:"
openssl s_client -connect your-domain:443 -servername your-domain -sess_out session.pem < /dev/null
openssl s_client -connect your-domain:443 -servername your-domain -sess_in session.pem < /dev/null
```

### HTTP/2性能测试
```bash
#!/bin/bash
# HTTP/2性能测试

# 检查HTTP/2支持
curl -I --http2 https://your-domain/

# 性能对比测试
echo "HTTP/1.1 performance:"
ab -n 1000 -c 10 https://your-domain/

echo "HTTP/2 performance:"
h2load -n 1000 -c 10 https://your-domain/
```

## 🔧 高级功能

### 1. 动态证书加载
```go
func (sm *SSLManager) LoadCertificateFromACME(domain string) (*tls.Certificate, error) {
    // 从Let's Encrypt等ACME服务自动获取证书
    cert, err := acme.GetCertificate(domain)
    if err != nil {
        return nil, err
    }
    
    // 缓存证书
    sm.certMu.Lock()
    sm.certCache[domain] = cert
    sm.certMu.Unlock()
    
    return cert, nil
}
```

### 2. 证书透明度日志
```go
func (sm *SSLManager) EnableCertificateTransparency(cert *x509.Certificate) {
    // 提交证书到CT日志
    scts, err := ct.SubmitCertificate(cert)
    if err == nil {
        // 在TLS握手中包含SCT
        sm.addSCTExtension(scts)
    }
}
```

### 3. OCSP装订
```go
func (sm *SSLManager) EnableOCSPStapling(cert *tls.Certificate) error {
    // 获取OCSP响应
    ocspResp, err := ocsp.GetOCSPResponse(cert)
    if err != nil {
        return err
    }
    
    // 在TLS配置中设置OCSP响应
    cert.OCSPStaple = ocspResp
    return nil
}
```

SSL/TLS优化是现代Web服务的基础，正确配置可以在保证安全性的同时显著提升HTTPS性能。

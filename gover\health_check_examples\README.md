# 健康检查协议示例

## 健康检查设计原则

### 重要原则
**健康检查使用IP地址连接，但设置正确的域名Host头**

### 原因
1. **避免DNS依赖**: 不依赖DNS解析，确保健康检查的准确性
2. **减少延迟**: 直接连接IP地址，减少DNS解析时间
3. **正确模拟**: 设置正确的Host头，模拟真实客户端请求
4. **虚拟主机支持**: 支持基于域名的虚拟主机配置

### URL构建逻辑
```
1. health_check (完整URL)
   → 解析URL获取路径部分
   → 使用upstream.Address和port构建新URL
   → 从health_check中提取域名作为Host头
   
2. health_path
   → 构建URL: {scheme}://{backend_ip}:{port}{health_path}
   → 设置Host头为health_host（如果配置）
```

## 不同协议的健康检查URL构建

### 1. HTTP协议
```json
{
  "name": "http_server",
  "address": "*************",
  "port": 80,
  "protocol": "http",
  "health_check": "http://example.com/health.php"
}
```
**生成的健康检查URL**: `http://*************:80/health.php`
**实际请求**:
- 连接: `*************:80`
- Host头: `example.com`

### 2. HTTPS协议
```json
{
  "name": "https_server",
  "address": "*************",
  "port": 80,
  "protocol": "https",
  "https_port": 443,
  "health_check": "https://example.com/health.php"
}
```
**生成的健康检查URL**: `https://*************:443/health.php`
**实际请求**:
- 连接: `*************:443`
- Host头: `example.com`

### 3. Passthrough协议（配置了HTTPS端口）
```json
{
  "name": "passthrough_server_https",
  "address": "*************",
  "port": 80,
  "protocol": "passthrough",
  "https_port": 443,
  "health_check": "https://example.com/health.php"
}
```
**生成的健康检查URL**: `https://*************:443/health.php`
**实际请求**:
- 连接: `*************:443`
- Host头: `example.com`

### 4. Passthrough协议（未配置HTTPS端口）
```json
{
  "name": "passthrough_server_http",
  "address": "*************",
  "port": 80,
  "protocol": "passthrough",
  "health_check": "http://example.com/health.php"
}
```
**生成的健康检查URL**: `http://*************:80/health.php`
**实际请求**:
- 连接: `*************:80`
- Host头: `example.com`

### 5. 使用health_path配置
```json
{
  "name": "path_server",
  "address": "*************",
  "port": 80,
  "health_host": "example.com",
  "health_path": "/health.php"
}
```
**生成的健康检查URL**: `http://*************:80/health.php`
**实际请求**:
- 连接: `*************:80`
- Host头: `example.com`

## 协议选择逻辑

### 代理请求时的协议选择
- **HTTP**: 始终使用HTTP
- **HTTPS**: 始终使用HTTPS
- **Passthrough**: 根据客户端请求协议动态选择
  - 客户端HTTP请求 → 后端HTTP
  - 客户端HTTPS请求 → 后端HTTPS
- **Auto**: 优先HTTPS，失败时降级到HTTP

### 健康检查时的协议选择
- **HTTP**: 使用HTTP
- **HTTPS**: 使用HTTPS
- **Passthrough**: 优先HTTPS（如果配置了https_port），否则使用HTTP
- **Auto**: 优先HTTPS（如果配置了https_port），否则使用HTTP

## 为什么Passthrough健康检查根据后端配置选择？

1. **健康检查没有客户端请求**: 健康检查是由程序自身发起的，无法知道客户端会用什么协议访问
2. **根据后端能力选择**: 如果后端配置了HTTPS端口，说明支持HTTPS，优先检查HTTPS
3. **确保服务可用**: 检查后端实际支持的服务，而不是猜测客户端协议
4. **避免检查失败**: 如果后端只支持HTTP，检查HTTPS会失败

## 实际应用建议

### 场景1：纯HTTP服务
```json
{
  "protocol": "http",
  "port": 80,
  "health_host": "example.com",
  "health_path": "/health.php"
}
```

### 场景2：纯HTTPS服务
```json
{
  "protocol": "https",
  "https_port": 443,
  "health_host": "example.com",
  "health_path": "/health.php"
}
```

### 场景3：HTTP+HTTPS双协议服务
```json
{
  "protocol": "passthrough",
  "port": 80,
  "https_port": 443,
  "health_host": "example.com",
  "health_path": "/health.php"
}
```

### 场景4：自动协议选择
```json
{
  "protocol": "auto",
  "port": 80,
  "https_port": 443,
  "health_host": "example.com",
  "health_path": "/health.php"
}
```

## 测试健康检查URL

可以使用以下命令测试健康检查URL：

```bash
# 测试HTTP健康检查（使用IP连接，设置Host头）
curl -v -H "Host: example.com" http://*************:80/health.php

# 测试HTTPS健康检查（使用IP连接，设置Host头）
curl -v -H "Host: example.com" https://*************:443/health.php

# 测试带域名的健康检查（使用IP连接，设置Host头）
curl -v -H "Host: example.com" http://*************/health.php

# 测试不同域名的健康检查
curl -v -H "Host: www.example.com" http://*************:80/health.php
``` 
{"log": {"level": "debug", "format": "text", "targets": [{"type": "console"}, {"type": "file", "path": "logs/debug.log", "max_size": "50MB", "max_backups": 3, "max_age": 7, "compress": false}]}, "cache": {"max_size": "100MB", "ttl": "5m", "cleanup_interval": "1m"}, "memory_cache": {"max_size": "50MB", "ttl": "5m"}, "rate_limit": {"enabled": false}, "ssl": {"session_cache": 100, "hsts": {"enabled": false}}, "compression": {"enabled": false}, "monitor": {"enabled": true, "port": 8080, "username": "dev", "password": "dev123"}, "hot_reload": {"check_interval": "1s"}}
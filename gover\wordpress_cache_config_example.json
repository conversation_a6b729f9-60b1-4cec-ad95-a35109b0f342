{"cache": {"enabled": true, "type": "file", "path": "cache", "max_size": 1073741824, "ttl": "2h", "status_ttl": {"200": "2h", "404": "2m", "default": "2m"}, "cache_headers": {"enabled": true, "cache_status": "X-<PERSON><PERSON>-Status", "upstream_status": "X-Upstream-Status"}, "cleanup_interval": "5m", "expired_check_interval": "1m", "enable_async_cleanup": true, "rules": [{"pattern": "\\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$", "ttl": "72h", "enabled": true, "skip_conditions": null}, {"pattern": "\\.(html|htm)$", "ttl": "2h", "enabled": true, "skip_conditions": {"cookies": ["wordpress_logged_in", "comment_author"], "headers": ["Authorization"], "query_params": ["preview", "customize_changeset_uuid"], "methods": ["POST", "PUT", "DELETE"], "custom_rules": [{"name": "skip_admin_users", "type": "cookie_value", "key": "wordpress_logged_in", "pattern": ".*", "description": "跳过已登录WordPress用户的缓存"}, {"name": "skip_preview_mode", "type": "query_value", "key": "preview", "value": "true", "description": "跳过预览模式的缓存"}]}}, {"pattern": "\\.php$", "ttl": "0s", "enabled": true, "skip_conditions": {"cookies": ["wordpress_logged_in", "comment_author"], "headers": ["Authorization"], "query_params": ["*"], "methods": ["POST", "PUT", "DELETE", "PATCH"]}}, {"pattern": "/wp-admin/", "ttl": "0s", "enabled": true, "skip_conditions": {"cookies": ["*"], "headers": ["Authorization"], "query_params": ["*"], "methods": ["*"]}}, {"pattern": "/wp-login\\.php", "ttl": "0s", "enabled": true, "skip_conditions": {"cookies": ["*"], "headers": ["*"], "query_params": ["*"], "methods": ["*"]}}, {"pattern": "/wp-json/", "ttl": "5m", "enabled": true, "skip_conditions": {"cookies": ["wordpress_logged_in"], "headers": ["Authorization"], "methods": ["POST", "PUT", "DELETE", "PATCH"], "custom_rules": [{"name": "skip_authenticated_api", "type": "header_value", "key": "Authorization", "pattern": "Bearer .*", "description": "跳过带认证的API请求"}]}}, {"pattern": "/feed/", "ttl": "30m", "enabled": true, "skip_conditions": {"cookies": ["wordpress_logged_in"], "query_params": ["preview"]}}, {"pattern": "/comments/feed/", "ttl": "15m", "enabled": true, "skip_conditions": {"cookies": ["wordpress_logged_in", "comment_author"]}}, {"pattern": "^/?$", "ttl": "2h", "enabled": true, "skip_conditions": {"cookies": ["wordpress_logged_in", "comment_author"], "query_params": ["preview", "customize_changeset_uuid", "s"], "custom_rules": [{"name": "skip_search_queries", "type": "query_value", "key": "s", "pattern": ".*", "description": "跳过搜索查询的缓存"}]}}, {"pattern": "^/", "ttl": "2h", "enabled": true, "skip_conditions": {"cookies": ["wordpress_logged_in", "comment_author"], "headers": ["Authorization"], "query_params": ["preview", "customize_changeset_uuid"], "user_agents": [".*bot.*", ".*crawler.*", ".*spider.*"], "methods": ["POST", "PUT", "DELETE", "PATCH"], "custom_rules": [{"name": "skip_logged_in_users", "type": "cookie_value", "key": "wordpress_logged_in", "pattern": ".*", "description": "跳过已登录用户的缓存"}, {"name": "skip_comment_authors", "type": "cookie_value", "key": "comment_author", "pattern": ".*", "description": "跳过评论作者的缓存"}, {"name": "skip_customizer", "type": "query_value", "key": "customize_changeset_uuid", "pattern": ".*", "description": "跳过WordPress定制器的缓存"}]}}]}, "sites": [{"name": "wordpress_site", "domains": ["example.com", "www.example.com"], "upstreams": [{"name": "wordpress_backend", "address": "127.0.0.1", "port": 8080, "weight": 1}], "routes": [{"pattern": "^/wp-content/uploads/", "cache": true, "static_dir": "/var/www/html/wp-content/uploads"}, {"pattern": "^/wp-admin/", "upstream": "wordpress_backend", "cache": false}, {"pattern": "^/wp-login\\.php", "upstream": "wordpress_backend", "cache": false}, {"pattern": "^/", "upstream": "wordpress_backend", "cache": true}], "rules": [{"pattern": "\\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$", "ttl": "168h", "enabled": true, "skip_conditions": null}, {"pattern": "/wp-content/uploads/", "ttl": "168h", "enabled": true, "skip_conditions": null}, {"pattern": "^/", "ttl": "1h", "enabled": true, "skip_conditions": {"cookies": ["wordpress_logged_in", "comment_author", "wp-settings-*"], "headers": ["Authorization", "<PERSON><PERSON>"], "query_params": ["preview", "customize_changeset_uuid", "s", "p", "page_id"], "methods": ["POST", "PUT", "DELETE", "PATCH"], "custom_rules": [{"name": "skip_wordpress_admin_cookies", "type": "cookie_value", "key": "wordpress_logged_in", "pattern": ".*", "description": "跳过WordPress管理员登录状态"}, {"name": "skip_comment_cookies", "type": "cookie_value", "key": "comment_author", "pattern": ".*", "description": "跳过评论作者Cookie"}, {"name": "skip_wp_settings", "type": "cookie_value", "key": "wp-settings-*", "pattern": ".*", "description": "跳过WordPress设置Cookie"}]}}]}]}
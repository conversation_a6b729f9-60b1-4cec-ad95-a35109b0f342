package config

import (
	"fmt"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/mitchellh/mapstructure"
	"github.com/spf13/viper"
)

var globalConfig *Config

// Size 大小配置，支持人性化单位格式
type Size int

// ParseSize 解析大小字符串，支持 k/K/kb/KB, m/M/mb/MB, g/G/gb/GB, mib/MiB, gib/GiB 等格式
func ParseSize(s string) (Size, error) {
	if s == "" {
		return 0, nil
	}

	// 如果是纯数字，直接返回
	if num, err := strconv.Atoi(s); err == nil {
		return Size(num), nil
	}

	// 正则表达式匹配数字和单位
	re := regexp.MustCompile(`^(\d+(?:\.\d+)?)\s*([a-zA-Z]*)$`)
	matches := re.FindStringSubmatch(strings.TrimSpace(s))
	if len(matches) != 3 {
		return 0, fmt.Errorf("invalid size format: %s", s)
	}

	// 解析数字部分
	num, err := strconv.ParseFloat(matches[1], 64)
	if err != nil {
		return 0, fmt.Errorf("invalid number in size: %s", matches[1])
	}

	// 解析单位部分
	unit := strings.ToLower(matches[2])
	var multiplier int64

	switch unit {
	case "", "b", "byte", "bytes":
		multiplier = 1
	case "k", "kb", "kilobyte", "kilobytes":
		multiplier = 1000
	case "ki", "kib", "kibibyte", "kibibytes":
		multiplier = 1024
	case "m", "mb", "megabyte", "megabytes":
		multiplier = 1000 * 1000
	case "mi", "mib", "mebibyte", "mebibytes":
		multiplier = 1024 * 1024
	case "g", "gb", "gigabyte", "gigabytes":
		multiplier = 1000 * 1000 * 1000
	case "gi", "gib", "gibibyte", "gibibytes":
		multiplier = 1024 * 1024 * 1024
	case "t", "tb", "terabyte", "terabytes":
		multiplier = 1000 * 1000 * 1000 * 1000
	case "ti", "tib", "tebibyte", "tebibytes":
		multiplier = 1024 * 1024 * 1024 * 1024
	default:
		return 0, fmt.Errorf("unknown size unit: %s", unit)
	}

	result := int64(num * float64(multiplier))
	return Size(result), nil
}

// String 返回大小的字符串表示
func (s Size) String() string {
	size := int64(s)
	if size == 0 {
		return "0"
	}

	// 选择合适的单位显示
	units := []struct {
		name string
		size int64
	}{
		{"TiB", 1024 * 1024 * 1024 * 1024},
		{"GiB", 1024 * 1024 * 1024},
		{"MiB", 1024 * 1024},
		{"KiB", 1024},
		{"TB", 1000 * 1000 * 1000 * 1000},
		{"GB", 1000 * 1000 * 1000},
		{"MB", 1000 * 1000},
		{"KB", 1000},
	}

	for _, unit := range units {
		if size >= unit.size {
			if size%unit.size == 0 {
				return fmt.Sprintf("%d%s", size/unit.size, unit.name)
			} else {
				return fmt.Sprintf("%.1f%s", float64(size)/float64(unit.size), unit.name)
			}
		}
	}

	return fmt.Sprintf("%d", size)
}

// Int 返回字节数
func (s Size) Int() int {
	return int(s)
}

// UnmarshalJSON 自定义JSON反序列化
func (s *Size) UnmarshalJSON(data []byte) error {
	// 移除引号
	str := strings.Trim(string(data), `"`)

	// 如果是数字，直接解析
	if num, err := strconv.Atoi(str); err == nil {
		*s = Size(num)
		return nil
	}

	// 解析带单位的字符串
	parsed, err := ParseSize(str)
	if err != nil {
		return err
	}
	*s = parsed
	return nil
}

// MarshalJSON 自定义JSON序列化
func (s Size) MarshalJSON() ([]byte, error) {
	return []byte(fmt.Sprintf(`"%s"`, s.String())), nil
}

// RateLimit 限速配置，支持单位
type RateLimit int64

// UnmarshalText 自定义反序列化方法
func (r *RateLimit) UnmarshalText(data []byte) error {
	*r = RateLimit(parseRateLimitString(string(data)))
	return nil
}

// UnmarshalJSON 自定义JSON反序列化方法
func (r *RateLimit) UnmarshalJSON(data []byte) error {
	// 移除JSON引号
	s := strings.Trim(string(data), `"`)
	*r = RateLimit(parseRateLimitString(s))
	return nil
}

// UnmarshalYAML 自定义YAML反序列化方法
func (r *RateLimit) UnmarshalYAML(unmarshal func(interface{}) error) error {
	var v interface{}
	if err := unmarshal(&v); err != nil {
		return err
	}
	*r = RateLimit(parseRateLimit(v))
	return nil
}

// Value 获取int64值
func (r RateLimit) Value() int64 {
	return int64(r)
}

// Config 总配置结构
type Config struct {
	Server         ServerConfig         `mapstructure:"server"`
	Log            LogConfig            `mapstructure:"log"`
	Cache          CacheConfig          `mapstructure:"cache"`
	ACL            ACLConfig            `mapstructure:"acl"`
	Sites          []SiteConfig         `mapstructure:"sites"`
	Monitor        MonitorConfig        `mapstructure:"monitor"`
	RateLimit      RateLimitConfig      `mapstructure:"rate_limit"`
	CircuitBreaker CircuitBreakerConfig `mapstructure:"circuit_breaker"`
	Compression    CompressionConfig    `mapstructure:"compression"`
	Minify         MinifyConfig         `mapstructure:"minify"`
	HotReload      HotReloadConfig      `mapstructure:"hot_reload"`
	MemoryCache    MemoryCacheConfig    `mapstructure:"memory_cache"`
	ErrorPages     []ErrorPageConfig    `mapstructure:"error_pages" json:"error_pages"` // 全局错误页面配置
	Headers        HeadersConfig        `mapstructure:"headers" json:"headers"`         // 全局头部配置
	GRPC           GRPCConfig           `mapstructure:"grpc"`
	HTTP3          HTTP3Config          `mapstructure:"http3"`
	Performance    PerformanceConfig    `mapstructure:"performance"`
}

// HotReloadConfig 热重载配置
type HotReloadConfig struct {
	Enabled       bool          `mapstructure:"enabled" json:"enabled"`
	CheckInterval time.Duration `mapstructure:"check_interval" json:"check_interval"`
}

// MemoryCacheConfig 内存缓存配置
type MemoryCacheConfig struct {
	Enabled           bool                        `mapstructure:"enabled" json:"enabled"`
	GlobalMemoryLimit string                      `mapstructure:"global_memory_limit" json:"global_memory_limit"`
	DefaultSiteLimit  string                      `mapstructure:"default_site_limit" json:"default_site_limit"`
	MaxFileSize       string                      `mapstructure:"max_file_size" json:"max_file_size"`
	MinAccessCount    int64                       `mapstructure:"min_access_count" json:"min_access_count"`
	ScoreThreshold    float64                     `mapstructure:"score_threshold" json:"score_threshold"`
	CleanupInterval   time.Duration               `mapstructure:"cleanup_interval" json:"cleanup_interval"`
	AllowedTypes      []string                    `mapstructure:"allowed_types" json:"allowed_types"`
	BlockedTypes      []string                    `mapstructure:"blocked_types" json:"blocked_types"`
	EvictionStrategy  string                      `mapstructure:"eviction_strategy" json:"eviction_strategy"`
	Sites             map[string]*SiteCacheConfig `mapstructure:"sites" json:"sites"`
}

// SiteCacheConfig 站点缓存配置
type SiteCacheConfig struct {
	MemoryLimit string `mapstructure:"memory_limit" json:"memory_limit"`
	Priority    string `mapstructure:"priority" json:"priority"`
}

// Priority 优先级枚举
type Priority int

const (
	PriorityLow Priority = iota
	PriorityNormal
	PriorityHigh
)

func (p Priority) String() string {
	switch p {
	case PriorityLow:
		return "low"
	case PriorityNormal:
		return "normal"
	case PriorityHigh:
		return "high"
	default:
		return "normal"
	}
}

func ParsePriority(s string) Priority {
	switch strings.ToLower(s) {
	case "low":
		return PriorityLow
	case "high":
		return PriorityHigh
	default:
		return PriorityNormal
	}
}

// CompressionConfig 压缩配置
type CompressionConfig struct {
	Enabled             bool     `mapstructure:"enabled" json:"enabled"`
	Types               []string `mapstructure:"types" json:"types"`
	MinSize             Size     `mapstructure:"min_size" json:"min_size"` // 最小压缩大小，支持 1k, 2M, 3GiB 等格式
	MaxSize             Size     `mapstructure:"max_size" json:"max_size"` // 最大压缩大小，支持 1k, 2M, 3GiB 等格式
	Level               int      `mapstructure:"level" json:"level"`
	Algorithms          []string `mapstructure:"algorithms" json:"algorithms"`                     // 支持的压缩算法: zstd, br, gzip, deflate
	BrotliQuality       int      `mapstructure:"brotli_quality" json:"brotli_quality"`             // Brotli质量等级 0-11
	ZstdLevel           int      `mapstructure:"zstd_level" json:"zstd_level"`                     // Zstd压缩级别 1-22
	EnableRecompression bool     `mapstructure:"enable_recompression" json:"enable_recompression"` // 启用重压缩功能
	RecompressionMode   string   `mapstructure:"recompression_mode" json:"recompression_mode"`     // 重压缩模式: auto, always, never
}

// MinifyConfig 最小化配置
type MinifyConfig struct {
	Enabled              bool             `mapstructure:"enabled" json:"enabled"`
	Types                []string         `mapstructure:"types" json:"types"`                                 // 支持的类型: html, css, js
	MinSize              Size             `mapstructure:"min_size" json:"min_size"`                           // 最小处理大小，支持 1k, 2M 等格式
	MaxSize              Size             `mapstructure:"max_size" json:"max_size"`                           // 最大处理大小，支持 1k, 2M 等格式
	CompressionAlgorithm string           `mapstructure:"compression_algorithm" json:"compression_algorithm"` // minify后使用的压缩算法: zstd, br, gzip, deflate
	Level                string           `mapstructure:"level" json:"level"`                                 // 压缩级别: conservative, safe, aggressive, maximum
	HTML                 MinifyHTMLConfig `mapstructure:"html" json:"html"`                                   // HTML特定配置
	CSS                  MinifyCSSConfig  `mapstructure:"css" json:"css"`                                     // CSS特定配置
	JS                   MinifyJSConfig   `mapstructure:"js" json:"js"`                                       // JavaScript特定配置
}

// MinifyHTMLConfig HTML最小化配置
type MinifyHTMLConfig struct {
	KeepConditionalComments bool `mapstructure:"keep_conditional_comments" json:"keep_conditional_comments"` // 保留IE条件注释
	KeepDefaultAttrVals     bool `mapstructure:"keep_default_attrvals" json:"keep_default_attrvals"`         // 保留默认属性值
	KeepDocumentTags        bool `mapstructure:"keep_document_tags" json:"keep_document_tags"`               // 保留html/head/body标签
	KeepEndTags             bool `mapstructure:"keep_end_tags" json:"keep_end_tags"`                         // 保留结束标签
	KeepWhitespace          bool `mapstructure:"keep_whitespace" json:"keep_whitespace"`                     // 保留重要空白
	KeepQuotes              bool `mapstructure:"keep_quotes" json:"keep_quotes"`                             // 保留属性引号
}

// MinifyCSSConfig CSS最小化配置
type MinifyCSSConfig struct {
	Precision int `mapstructure:"precision" json:"precision"` // 数字精度（小数位数）
}

// MinifyJSConfig JavaScript最小化配置
type MinifyJSConfig struct {
	Precision int `mapstructure:"precision" json:"precision"` // 数字精度（小数位数）
}

// ServerConfig 服务器配置
type ServerConfig struct {
	HTTPPort       int           `mapstructure:"http_port" json:"http_port"`
	HTTPSPort      int           `mapstructure:"https_port" json:"https_port"`
	ReadTimeout    time.Duration `mapstructure:"read_timeout" json:"read_timeout"`
	WriteTimeout   time.Duration `mapstructure:"write_timeout" json:"write_timeout"`
	IdleTimeout    time.Duration `mapstructure:"idle_timeout" json:"idle_timeout"`
	MaxConnections int           `mapstructure:"max_connections" json:"max_connections"`

	// HTTP客户端连接池配置
	ConnectionPool ConnectionPoolConfig `mapstructure:"connection_pool" json:"connection_pool"`
}

// ConnectionPoolConfig HTTP连接池配置
type ConnectionPoolConfig struct {
	MaxIdleConns        int           `mapstructure:"max_idle_conns" json:"max_idle_conns"`
	MaxIdleConnsPerHost int           `mapstructure:"max_idle_conns_per_host" json:"max_idle_conns_per_host"`
	IdleConnTimeout     time.Duration `mapstructure:"idle_conn_timeout" json:"idle_conn_timeout"`
	DialTimeout         time.Duration `mapstructure:"dial_timeout" json:"dial_timeout"`
	KeepAlive           time.Duration `mapstructure:"keep_alive" json:"keep_alive"`
	MaxConnsPerHost     int           `mapstructure:"max_conns_per_host" json:"max_conns_per_host"`
	DisableKeepAlives   bool          `mapstructure:"disable_keep_alives" json:"disable_keep_alives"`
}

// LogTargetConfig 日志目标配置
// 支持 type: file/syslog/console，filename支持变量
// format指定日志格式模板
// 站点级log.targets覆盖全局
type LogTargetConfig struct {
	Type     string `mapstructure:"type" json:"type"`
	Filename string `mapstructure:"filename,omitempty" json:"filename,omitempty"`
	Network  string `mapstructure:"network,omitempty" json:"network,omitempty"`
	Address  string `mapstructure:"address,omitempty" json:"address,omitempty"`
	Format   string `mapstructure:"format" json:"format"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level      string            `mapstructure:"level"`
	File       string            `mapstructure:"file"`
	Syslog     string            `mapstructure:"syslog"`
	Format     string            `mapstructure:"format"`
	Formats    map[string]string `mapstructure:"formats"` // 支持多格式
	MaxSize    int               `mapstructure:"max_size"`
	MaxBackups int               `mapstructure:"max_backups"`
	MaxAge     int               `mapstructure:"max_age"`
	Targets    []LogTargetConfig `mapstructure:"targets" json:"targets"` // 新增多目标

	// 异步日志配置
	Async AsyncLogConfig `mapstructure:"async" json:"async"`
}

// AsyncLogConfig 异步日志配置
type AsyncLogConfig struct {
	Enabled       bool          `mapstructure:"enabled" json:"enabled"`               // 是否启用异步日志
	ChannelSize   int           `mapstructure:"channel_size" json:"channel_size"`     // 通道缓冲区大小
	BatchSize     int           `mapstructure:"batch_size" json:"batch_size"`         // 批量处理大小
	FlushInterval time.Duration `mapstructure:"flush_interval" json:"flush_interval"` // 刷新间隔
	MaxMemoryMB   int           `mapstructure:"max_memory_mb" json:"max_memory_mb"`   // 最大内存使用(MB)
	DropPolicy    string        `mapstructure:"drop_policy" json:"drop_policy"`       // 丢弃策略: "drop_oldest", "drop_newest", "block"
}

// CacheHeadersConfig 缓存命中头部配置
type CacheHeadersConfig struct {
	Enabled     bool   `mapstructure:"enabled" json:"enabled"`         // 总开关
	CacheStatus string `mapstructure:"cache_status" json:"cache_status"` // 缓存状态头部名称
	ShowDetail  bool   `mapstructure:"show_detail" json:"show_detail"`   // 是否显示详细信息（后端服务器、缓存时间等）
}

// CacheConfig 缓存配置
type CacheConfig struct {
	Enabled      bool                     `mapstructure:"enabled"`
	Type         string                   `mapstructure:"type"`
	Path         string                   `mapstructure:"path"`
	MaxSize      Size                     `mapstructure:"max_size"`
	TTL          time.Duration            `mapstructure:"ttl"`
	Rules        []CacheRule              `mapstructure:"rules"`
	StatusTTL    map[string]time.Duration `mapstructure:"status_ttl" json:"status_ttl"`       // 新增：各状态码缓存时间
	CacheHeaders *CacheHeadersConfig      `mapstructure:"cache_headers" json:"cache_headers"` // 新增：缓存命中头部配置

	// 缓存清理配置
	CleanupInterval      time.Duration `mapstructure:"cleanup_interval" json:"cleanup_interval"`
	ExpiredCheckInterval time.Duration `mapstructure:"expired_check_interval" json:"expired_check_interval"`
	EnableAsyncCleanup   bool          `mapstructure:"enable_async_cleanup" json:"enable_async_cleanup"`
}

// CacheRule 缓存规则
type CacheRule struct {
	Pattern         string            `mapstructure:"pattern"`
	FileType        string            `mapstructure:"file_type"`
	MimeType        string            `mapstructure:"mime_type"`
	TTL             time.Duration     `mapstructure:"ttl"`
	Enabled         bool              `mapstructure:"enabled"`
	SkipConditions  *CacheSkipConfig  `mapstructure:"skip_conditions" json:"skip_conditions"`   // 缓存跳过条件
	MatchConditions *CacheMatchConfig `mapstructure:"match_conditions" json:"match_conditions"` // 缓存匹配条件
}

// CacheSkipConfig 缓存跳过条件配置
type CacheSkipConfig struct {
	Cookies     []string        `mapstructure:"cookies" json:"cookies"`           // 存在这些Cookie时跳过缓存
	Headers     []string        `mapstructure:"headers" json:"headers"`           // 存在这些Header时跳过缓存
	QueryParams []string        `mapstructure:"query_params" json:"query_params"` // 存在这些查询参数时跳过缓存
	UserAgents  []string        `mapstructure:"user_agents" json:"user_agents"`   // 匹配这些User-Agent时跳过缓存
	Methods     []string        `mapstructure:"methods" json:"methods"`           // 这些HTTP方法时跳过缓存
	CustomRules []CacheSkipRule `mapstructure:"custom_rules" json:"custom_rules"` // 自定义跳过规则
}

// CacheSkipRule 自定义缓存跳过规则
type CacheSkipRule struct {
	Name        string `mapstructure:"name" json:"name"`               // 规则名称
	Type        string `mapstructure:"type" json:"type"`               // 规则类型：cookie_value, header_value, query_value
	Key         string `mapstructure:"key" json:"key"`                 // Cookie名/Header名/查询参数名
	Value       string `mapstructure:"value" json:"value"`             // 匹配的值（支持正则）
	Pattern     string `mapstructure:"pattern" json:"pattern"`         // 正则表达式模式
	Description string `mapstructure:"description" json:"description"` // 规则描述
}

// CacheMatchConfig 缓存匹配条件配置
type CacheMatchConfig struct {
	Headers     []CacheHeaderMatch `mapstructure:"headers" json:"headers"`           // HTTP头部匹配条件
	Cookies     []CacheCookieMatch `mapstructure:"cookies" json:"cookies"`           // Cookie匹配条件
	QueryParams []CacheQueryMatch  `mapstructure:"query_params" json:"query_params"` // 查询参数匹配条件
	UserAgents  []string           `mapstructure:"user_agents" json:"user_agents"`   // User-Agent匹配模式
	Methods     []string           `mapstructure:"methods" json:"methods"`           // HTTP方法匹配
	ContentType []string           `mapstructure:"content_type" json:"content_type"` // Content-Type匹配
}

// CacheHeaderMatch HTTP头部匹配条件
type CacheHeaderMatch struct {
	Name        string `mapstructure:"name" json:"name"`               // 头部名称
	Value       string `mapstructure:"value" json:"value"`             // 精确匹配值
	Pattern     string `mapstructure:"pattern" json:"pattern"`         // 正则匹配模式
	Exists      bool   `mapstructure:"exists" json:"exists"`           // 仅检查是否存在
	Description string `mapstructure:"description" json:"description"` // 描述
}

// CacheCookieMatch Cookie匹配条件
type CacheCookieMatch struct {
	Name        string `mapstructure:"name" json:"name"`               // Cookie名称
	Value       string `mapstructure:"value" json:"value"`             // 精确匹配值
	Pattern     string `mapstructure:"pattern" json:"pattern"`         // 正则匹配模式
	Exists      bool   `mapstructure:"exists" json:"exists"`           // 仅检查是否存在
	Description string `mapstructure:"description" json:"description"` // 描述
}

// CacheQueryMatch 查询参数匹配条件
type CacheQueryMatch struct {
	Name        string `mapstructure:"name" json:"name"`               // 参数名称
	Value       string `mapstructure:"value" json:"value"`             // 精确匹配值
	Pattern     string `mapstructure:"pattern" json:"pattern"`         // 正则匹配模式
	Exists      bool   `mapstructure:"exists" json:"exists"`           // 仅检查是否存在
	Description string `mapstructure:"description" json:"description"` // 描述
}

// RouteMatchConfig 路由匹配条件配置
type RouteMatchConfig struct {
	Headers     []RouteHeaderMatch `mapstructure:"headers" json:"headers"`           // HTTP头部匹配条件
	Cookies     []RouteCookieMatch `mapstructure:"cookies" json:"cookies"`           // Cookie匹配条件
	QueryParams []RouteQueryMatch  `mapstructure:"query_params" json:"query_params"` // 查询参数匹配条件
	UserAgents  []string           `mapstructure:"user_agents" json:"user_agents"`   // User-Agent匹配模式
	Methods     []string           `mapstructure:"methods" json:"methods"`           // HTTP方法匹配
	ContentType []string           `mapstructure:"content_type" json:"content_type"` // Content-Type匹配
}

// RouteHeaderMatch 路由HTTP头部匹配条件
type RouteHeaderMatch struct {
	Name        string `mapstructure:"name" json:"name"`               // 头部名称
	Value       string `mapstructure:"value" json:"value"`             // 精确匹配值
	Pattern     string `mapstructure:"pattern" json:"pattern"`         // 正则匹配模式
	Exists      bool   `mapstructure:"exists" json:"exists"`           // 仅检查是否存在
	Description string `mapstructure:"description" json:"description"` // 描述
}

// RouteCookieMatch 路由Cookie匹配条件
type RouteCookieMatch struct {
	Name        string `mapstructure:"name" json:"name"`                 // Cookie名称
	NamePattern string `mapstructure:"name_pattern" json:"name_pattern"` // Cookie名称正则匹配模式
	Value       string `mapstructure:"value" json:"value"`               // 精确匹配值
	Pattern     string `mapstructure:"pattern" json:"pattern"`           // 正则匹配模式
	Exists      bool   `mapstructure:"exists" json:"exists"`             // 仅检查是否存在
	Description string `mapstructure:"description" json:"description"`   // 描述
}

// RouteQueryMatch 路由查询参数匹配条件
type RouteQueryMatch struct {
	Name        string `mapstructure:"name" json:"name"`               // 参数名称
	Value       string `mapstructure:"value" json:"value"`             // 精确匹配值
	Pattern     string `mapstructure:"pattern" json:"pattern"`         // 正则匹配模式
	Exists      bool   `mapstructure:"exists" json:"exists"`           // 仅检查是否存在
	Description string `mapstructure:"description" json:"description"` // 描述
}

// ACLConfig ACL配置
type ACLConfig struct {
	Enabled        bool          `mapstructure:"enabled"`
	GlobalAllow    []string      `mapstructure:"global_allow"`
	GlobalDeny     []string      `mapstructure:"global_deny"`
	AllowFile      string        `mapstructure:"allow_file"` // 允许列表文件
	DenyFile       string        `mapstructure:"deny_file"`  // 拒绝列表文件
	ReloadInterval time.Duration `mapstructure:"reload_interval"`
}

// ErrorPageConfig 错误页面配置
type ErrorPageConfig struct {
	Code    int    `mapstructure:"code" json:"code"`       // 错误码，如404
	Content string `mapstructure:"content" json:"content"` // 直接返回内容
	File    string `mapstructure:"file" json:"file"`       // 本地文件路径，优先级高于Content
}

// SiteConfig 站点配置
type SiteConfig struct {
	SiteID         string                   `mapstructure:"site_id" json:"site_id"` // 站点唯一标识符，创建时生成，永不变更
	Name           string                   `mapstructure:"name" json:"name"`       // 站点显示名称，可以修改
	Domains        []string                 `mapstructure:"domains" json:"domains"` // 支持多域名
	DefaultSite    bool                     `mapstructure:"defaultsite" json:"defaultsite"` // 是否为默认站点，当Host不匹配时使用
	HTTPPort       int                      `mapstructure:"http_port"`              // HTTP端口，0表示使用全局端口
	HTTPSPort      int                      `mapstructure:"https_port"`             // HTTPS端口，0表示使用全局端口
	SSL            SSLConfig                `mapstructure:"ssl"`
	Upstreams      []UpstreamConfig         `mapstructure:"upstreams"`
	Routes         []RouteConfig            `mapstructure:"routes"`
	LogFormat      string                   `mapstructure:"log_format"`
	LogTargets     []LogTargetConfig        `mapstructure:"log_targets" json:"log_targets"` // 新增站点级多目标
	ACL            SiteACLConfig            `mapstructure:"acl"`
	Headers        HeadersConfig            `mapstructure:"headers"`
	DenyTypes      []string                 `mapstructure:"deny_types" json:"deny_types"` // 新增：全站拒绝文件类型（如[".php",".exe"]）
	DenyURLs       []string                 `mapstructure:"deny_urls" json:"deny_urls"`   // 新增：全站拒绝URL正则
	ErrorPages     []ErrorPageConfig        `mapstructure:"error_pages" json:"error_pages"`
	StatusTTL      map[string]time.Duration `mapstructure:"status_ttl" json:"status_ttl"`           // 新增：各状态码缓存时间
	CacheHeaders   *CacheHeadersConfig      `mapstructure:"cache_headers" json:"cache_headers"`     // 新增：缓存命中头部配置
	BandwidthLimit RateLimit                `mapstructure:"bandwidth_limit" json:"bandwidth_limit"` // 新增：站点级带宽限制
	MaxConnections int                      `mapstructure:"max_connections" json:"max_connections"` // 新增：站点级最大连接数限制
	Rules          []CacheRule              `mapstructure:"rules" json:"rules"`                     // 新增：站点级缓存规则
	LoadBalancer   LoadBalancerConfig       `mapstructure:"load_balancer" json:"load_balancer"`     // 负载均衡配置
	RateLimiter    RateLimitConfig          `mapstructure:"rate_limiter" json:"rate_limiter"`       // 限流配置
	CircuitBreaker CircuitBreakerConfig     `mapstructure:"circuit_breaker" json:"circuit_breaker"` // 熔断配置
	DebugMode      bool                     `mapstructure:"debug_mode" json:"debug_mode"`           // 新增：调试模式，开启后在响应头中添加调试信息
	Minify         *MinifyConfig            `mapstructure:"minify" json:"minify"`                   // 站点级最小化配置
}

// RateLimitConfig 限流配置
type RateLimitConfig struct {
	Enabled   bool `mapstructure:"enabled" json:"enabled"`
	GlobalRPS int  `mapstructure:"global_rps" json:"global_rps"`
	IPRPS     int  `mapstructure:"ip_rps" json:"ip_rps"`
	SiteRPS   int  `mapstructure:"site_rps" json:"site_rps"`
	Burst     int  `mapstructure:"burst" json:"burst"`
}

// CircuitBreakerConfig 熔断器配置
type CircuitBreakerConfig struct {
	Enabled          bool          `mapstructure:"enabled" json:"enabled"`
	MaxFailures      int           `mapstructure:"max_failures" json:"max_failures"`
	ResetTimeout     time.Duration `mapstructure:"reset_timeout" json:"reset_timeout"`
	HalfOpenRequests int           `mapstructure:"half_open_requests" json:"half_open_requests"`
}

// LoadBalancerConfig 负载均衡配置
type LoadBalancerConfig struct {
	Algorithm         string `mapstructure:"algorithm" json:"algorithm"`                     // 算法: round_robin, weighted, least_connections, smooth_weighted, consistent_hash, response_time
	HashKey           string `mapstructure:"hash_key" json:"hash_key"`                       // 一致性哈希的key字段: ip, uri, header:name
	VirtualNodes      int    `mapstructure:"virtual_nodes" json:"virtual_nodes"`             // 一致性哈希虚拟节点数
	HealthCheckWeight bool   `mapstructure:"health_check_weight" json:"health_check_weight"` // 是否根据健康检查调整权重
}

// SSLConfig SSL配置
type SSLConfig struct {
	Enabled             bool       `mapstructure:"enabled"`
	CertFile            string     `mapstructure:"cert_file"`
	KeyFile             string     `mapstructure:"key_file"`
	Protocols           string     `mapstructure:"protocols"`                          // http1.1,http2,http3
	MinVersion          string     `mapstructure:"min_version" json:"min_version"`     // TLS1.2, TLS1.3
	MaxVersion          string     `mapstructure:"max_version" json:"max_version"`     // TLS1.2, TLS1.3
	CipherSuites        []string   `mapstructure:"cipher_suites" json:"cipher_suites"` // 加密套件
	PreferServerCiphers bool       `mapstructure:"prefer_server_ciphers" json:"prefer_server_ciphers"`
	OCSP                OCSPConfig `mapstructure:"ocsp" json:"ocsp"`                       // OCSP装订配置
	SessionCache        int        `mapstructure:"session_cache" json:"session_cache"`     // 会话缓存大小
	SessionTickets      bool       `mapstructure:"session_tickets" json:"session_tickets"` // 会话票据
	HSTS                HSTSConfig `mapstructure:"hsts" json:"hsts"`                       // HSTS配置
}

// HSTSConfig HSTS配置
type HSTSConfig struct {
	Enabled           bool `mapstructure:"enabled" json:"enabled"`
	MaxAge            int  `mapstructure:"max_age" json:"max_age"` // 秒
	IncludeSubDomains bool `mapstructure:"include_subdomains" json:"include_subdomains"`
	Preload           bool `mapstructure:"preload" json:"preload"`
}

// OCSPConfig OCSP装订配置
type OCSPConfig struct {
	Enabled        bool          `mapstructure:"enabled" json:"enabled"`
	ResponderURL   string        `mapstructure:"responder_url" json:"responder_url"`     // OCSP响应器URL
	CacheTime      time.Duration `mapstructure:"cache_time" json:"cache_time"`           // 缓存时间
	Timeout        time.Duration `mapstructure:"timeout" json:"timeout"`                 // 请求超时
	TrustedCert    string        `mapstructure:"trusted_cert" json:"trusted_cert"`       // 信任的证书文件
	VerifyResponse bool          `mapstructure:"verify_response" json:"verify_response"` // 验证OCSP响应
}

// UpstreamConfig 上游服务器配置
type UpstreamConfig struct {
	Name             string        `mapstructure:"name"`
	Address          string        `mapstructure:"address"`
	Port             int           `mapstructure:"port"`
	Protocol         string        `mapstructure:"protocol"`   // http, https, auto, passthrough
	HTTPSPort        int           `mapstructure:"https_port"` // HTTPS端口
	Weight           int           `mapstructure:"weight"`
	MaxFails         int           `mapstructure:"max_fails"`
	FailTimeout      time.Duration `mapstructure:"fail_timeout"`
	Backup           bool          `mapstructure:"backup"`
	LoadBalanceGroup string        `mapstructure:"load_balance_group" json:"load_balance_group"` // 新增：负载均衡组名
	HealthCheck      string        `mapstructure:"health_check"`
	HealthHost       string        `mapstructure:"health_host"`     // 健康检查Host头
	HealthInterval   time.Duration `mapstructure:"health_interval"` // 健康检查间隔
	HealthTimeout    time.Duration `mapstructure:"health_timeout"`  // 健康检查超时
	HealthPath       string        `mapstructure:"health_path"`     // 健康检查路径
	Healthy          bool          `json:"healthy"`                 // 运行时健康状态
}

// RouteConfig 路由配置
type RouteConfig struct {
	Pattern         string            `mapstructure:"pattern"`
	Upstream        string            `mapstructure:"upstream"`
	Rewrite         string            `mapstructure:"rewrite"`
	RewriteAdvanced *RewriteConfig    `mapstructure:"rewrite_advanced" json:"rewrite_advanced"` // 高级重写配置
	Cache           bool              `mapstructure:"cache"`
	StaticDir       string            `mapstructure:"static_dir" json:"static_dir"`
	MimeTypes       map[string]string `mapstructure:"mime_types" json:"mime_types"`
	DirListing      bool              `mapstructure:"dir_listing" json:"dir_listing"`
	IndexFiles      []string          `mapstructure:"index_files" json:"index_files"` // 默认首页文件列表
	RateLimit       RateLimit         `mapstructure:"rate_limit" json:"rate_limit"`
	HiddenInListing []string          `mapstructure:"hidden_in_listing" json:"hidden_in_listing"` // 目录索引隐藏规则
	MatchConditions *RouteMatchConfig `mapstructure:"match_conditions" json:"match_conditions"`   // 路由匹配条件
	ErrorPages      []ErrorPageConfig `mapstructure:"error_pages" json:"error_pages"`
	Headers         *HeadersConfig    `mapstructure:"headers" json:"headers"`       // 路由级头部配置
	Minify          *MinifyConfig     `mapstructure:"minify" json:"minify"`         // 路由级最小化配置
}

// RewriteConfig 高级重写配置
type RewriteConfig struct {
	Target      string             `mapstructure:"target" json:"target"`             // 重写目标路径，支持变量
	StripPrefix bool               `mapstructure:"strip_prefix" json:"strip_prefix"` // 是否移除匹配的前缀
	AppendPath  bool               `mapstructure:"append_path" json:"append_path"`   // 是否追加剩余路径
	Variables   map[string]string  `mapstructure:"variables" json:"variables"`       // 自定义变量
	Conditions  []RewriteCondition `mapstructure:"conditions" json:"conditions"`     // 重写条件
}

// RewriteCondition 重写条件
type RewriteCondition struct {
	Type     string `mapstructure:"type" json:"type"`         // 条件类型: header, query, path, method
	Key      string `mapstructure:"key" json:"key"`           // 条件键名
	Value    string `mapstructure:"value" json:"value"`       // 条件值（支持正则）
	Operator string `mapstructure:"operator" json:"operator"` // 操作符: equals, contains, matches, exists
	Target   string `mapstructure:"target" json:"target"`     // 满足条件时的重写目标
}

// SiteACLConfig 站点ACL配置
type SiteACLConfig struct {
	Allow     []string `mapstructure:"allow"`
	Deny      []string `mapstructure:"deny"`
	AllowFile string   `mapstructure:"allow_file"` // 站点允许列表文件
	DenyFile  string   `mapstructure:"deny_file"`  // 站点拒绝列表文件
}

// HeadersConfig 头部配置
type HeadersConfig struct {
	Request  HeaderRules `mapstructure:"request"`
	Response HeaderRules `mapstructure:"response"`
	Upstream HeaderRules `mapstructure:"upstream"`
}

// HeaderRules 头部规则
type HeaderRules struct {
	Set    map[string]string `mapstructure:"set"`
	Remove []string          `mapstructure:"remove"`
	Ignore []string          `mapstructure:"ignore"`
}

// UnmarshalText 自定义反序列化方法
func (hr *HeaderRules) UnmarshalText(data []byte) error {
	// 这里可以添加自定义的解析逻辑
	return nil
}

// GlobalConfig 获取全局配置
func GlobalConfig() *Config {
	return globalConfig
}

// Load 加载配置文件
func Load(configFile string) (*Config, error) {
	viper.SetConfigFile(configFile)

	// 根据文件扩展名自动检测配置类型
	ext := getFileExtension(configFile)
	switch ext {
	case "json":
		viper.SetConfigType("json")
	case "yaml", "yml":
		viper.SetConfigType("yaml")
	case "toml":
		viper.SetConfigType("toml")
	default:
		viper.SetConfigType("ini")
	}

	// 设置默认值
	setDefaults()

	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	var config Config

	// 特殊处理RateLimit和Size字段
	if err := viper.Unmarshal(&config, func(dc *mapstructure.DecoderConfig) {
		dc.DecodeHook = mapstructure.ComposeDecodeHookFunc(
			dc.DecodeHook,
			func(f reflect.Type, t reflect.Type, data interface{}) (interface{}, error) {
				// 如果目标类型是RateLimit，且源数据是字符串，则使用自定义解析
				if t == reflect.TypeOf(RateLimit(0)) && f.Kind() == reflect.String {
					if str, ok := data.(string); ok {
						return RateLimit(parseRateLimitString(str)), nil
					}
				}

				// 如果目标类型是Size，且源数据是字符串，则使用自定义解析
				if t == reflect.TypeOf(Size(0)) && f.Kind() == reflect.String {
					if str, ok := data.(string); ok {
						size, err := ParseSize(str)
						if err != nil {
							return nil, fmt.Errorf("解析大小格式失败 '%s': %w", str, err)
						}
						return size, nil
					}
				}

				// 如果目标类型是Size，且源数据是数字，则直接转换
				if t == reflect.TypeOf(Size(0)) && (f.Kind() == reflect.Int || f.Kind() == reflect.Int64 || f.Kind() == reflect.Float64) {
					if num, ok := data.(int); ok {
						return Size(num), nil
					}
					if num, ok := data.(int64); ok {
						return Size(num), nil
					}
					if num, ok := data.(float64); ok {
						return Size(int64(num)), nil
					}
				}

				return data, nil
			},
		)
	}); err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %w", err)
	}

	// 初始化头部配置
	initHeaders(&config)

	// 验证配置
	if err := validateConfig(&config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	// 初始化上游服务器的健康状态
	for i := range config.Sites {
		for j := range config.Sites[i].Upstreams {
			config.Sites[i].Upstreams[j].Healthy = true // 默认健康
		}
	}

	// 处理默认站点设置：确保只有一个站点设置为默认站点
	if err := processDefaultSiteSettings(&config); err != nil {
		return nil, fmt.Errorf("处理默认站点设置失败: %w", err)
	}

	globalConfig = &config
	return &config, nil
}

// getFileExtension 获取文件扩展名
func getFileExtension(filename string) string {
	for i := len(filename) - 1; i >= 0; i-- {
		if filename[i] == '.' {
			return filename[i+1:]
		}
	}
	return ""
}

// initHeaders 初始化头部配置
func initHeaders(config *Config) {
	for i := range config.Sites {
		site := &config.Sites[i]

		// 初始化请求头部
		if site.Headers.Request.Set == nil {
			site.Headers.Request.Set = make(map[string]string)
		}

		// 初始化响应头部
		if site.Headers.Response.Set == nil {
			site.Headers.Response.Set = make(map[string]string)
		}

		// 初始化上游头部
		if site.Headers.Upstream.Set == nil {
			site.Headers.Upstream.Set = make(map[string]string)
		}
	}
}

// setDefaults 设置默认值
func setDefaults() {
	viper.SetDefault("server.http_port", 80)
	viper.SetDefault("server.https_port", 443)
	viper.SetDefault("server.read_timeout", "30s")
	viper.SetDefault("server.write_timeout", "30s")
	viper.SetDefault("server.idle_timeout", "60s")
	viper.SetDefault("server.max_connections", 1000)

	viper.SetDefault("log.level", "info")
	viper.SetDefault("log.format", "combined")
	viper.SetDefault("log.max_size", 100)
	viper.SetDefault("log.max_backups", 10)
	viper.SetDefault("log.max_age", 30)

	viper.SetDefault("cache.enabled", true)
	viper.SetDefault("cache.type", "file")
	viper.SetDefault("cache.path", "./cache")
	viper.SetDefault("cache.max_size", **********) // 1GB
	viper.SetDefault("cache.ttl", "1h")

	viper.SetDefault("acl.enabled", false)
	viper.SetDefault("acl.reload_interval", "5m")

	// 健康检查默认值
	viper.SetDefault("upstream.health_interval", "10s") // 检查间隔10秒
	viper.SetDefault("upstream.health_timeout", "5s")   // 检查超时5秒
	viper.SetDefault("upstream.health_path", "/health") // 健康检查路径
	viper.SetDefault("upstream.max_fails", 3)           // 最大失败次数
	viper.SetDefault("upstream.fail_timeout", "30s")    // 失败超时时间
}

// validateConfig 验证配置
func validateConfig(config *Config) error {
	if config.Server.HTTPPort < 0 || config.Server.HTTPPort > 65535 {
		return fmt.Errorf("无效的HTTP端口号: %d", config.Server.HTTPPort)
	}
	if config.Server.HTTPSPort < 0 || config.Server.HTTPSPort > 65535 {
		return fmt.Errorf("无效的HTTPS端口号: %d", config.Server.HTTPSPort)
	}

	for i, site := range config.Sites {
		if site.Name == "" {
			return fmt.Errorf("站点 %d 缺少名称", i)
		}

		// 检查是否为纯静态文件站点
		hasStaticRoutes := false
		hasProxyRoutes := false
		for _, route := range site.Routes {
			if route.StaticDir != "" {
				hasStaticRoutes = true
			}
			if route.Upstream != "" {
				hasProxyRoutes = true
			}
		}

		// 如果有代理路由但没有上游服务器配置，则报错
		if hasProxyRoutes && len(site.Upstreams) == 0 {
			return fmt.Errorf("站点 %s (ID: %s) 有代理路由但缺少上游服务器配置", site.Name, site.SiteID)
		}

		// 如果既没有静态路由也没有代理路由，则报错
		if !hasStaticRoutes && !hasProxyRoutes {
			return fmt.Errorf("站点 %s (ID: %s) 必须配置静态文件路由或代理路由", site.Name, site.SiteID)
		}
	}

	return nil
}

// processDefaultSiteSettings 处理默认站点设置
func processDefaultSiteSettings(config *Config) error {
	var defaultSiteCount int
	var firstDefaultSiteIndex int = -1

	// 统计设置为默认站点的数量
	for i := range config.Sites {
		if config.Sites[i].DefaultSite {
			defaultSiteCount++
			if firstDefaultSiteIndex == -1 {
				firstDefaultSiteIndex = i
			}
		}
	}

	// 如果有多个站点设置为默认站点，只保留第一个
	if defaultSiteCount > 1 {
		for i := range config.Sites {
			if i != firstDefaultSiteIndex {
				config.Sites[i].DefaultSite = false
			}
		}
		// 记录警告信息
		fmt.Printf("警告: 检测到多个站点设置为默认站点，只保留第一个站点 '%s' (ID: %s) 作为默认站点\n",
			config.Sites[firstDefaultSiteIndex].Name, config.Sites[firstDefaultSiteIndex].SiteID)
	}

	return nil
}

// GetSiteByName 根据名称获取站点配置
func (c *Config) GetSiteByName(name string) *SiteConfig {
	for _, site := range c.Sites {
		if site.Name == name {
			return &site
		}
	}
	return nil
}

// GetDefaultSite 获取默认站点配置
func (c *Config) GetDefaultSite() *SiteConfig {
	for i := range c.Sites {
		if c.Sites[i].DefaultSite {
			return &c.Sites[i]
		}
	}
	return nil
}

// GetSiteByHost 根据主机名获取站点配置
func (c *Config) GetSiteByHost(host string) *SiteConfig {
	for _, site := range c.Sites {
		// 检查是否匹配任一域名
		for _, domain := range site.Domains {
			if domain == host {
				return &site
			}
		}
	}
	return nil
}

// ValidateSiteConfig 验证站点配置
func ValidateSiteConfig(siteConfig *SiteConfig) error {
	// 验证所有上游服务器都有load_balance_group
	for _, upstream := range siteConfig.Upstreams {
		if upstream.LoadBalanceGroup == "" {
			return fmt.Errorf("上游服务器 %s 缺少 load_balance_group 配置", upstream.Name)
		}
	}

	// 收集所有存在的组名
	groups := make(map[string]bool)
	for _, upstream := range siteConfig.Upstreams {
		groups[upstream.LoadBalanceGroup] = true
	}

	// 验证所有路由的upstream都指向存在的组
	for _, route := range siteConfig.Routes {
		if route.Upstream != "" && !groups[route.Upstream] {
			return fmt.Errorf("路由 %s 指向的组 %s 不存在", route.Pattern, route.Upstream)
		}
	}

	return nil
}

// MonitorConfig 监控配置
type MonitorConfig struct {
	Enabled  bool             `mapstructure:"enabled" json:"enabled"`
	Port     int              `mapstructure:"port" json:"port"`
	Username string           `mapstructure:"username" json:"username"`
	Password string           `mapstructure:"password" json:"password"`
	APIKey   string           `mapstructure:"api_key" json:"api_key"`
	ACL      MonitorACLConfig `mapstructure:"acl" json:"acl"`
}

// MonitorACLConfig 监控API访问控制配置
type MonitorACLConfig struct {
	AllowedIPs []string `mapstructure:"allowed_ips" json:"allowed_ips"`
	DeniedIPs  []string `mapstructure:"denied_ips" json:"denied_ips"`
}

// GRPCConfig gRPC代理配置
type GRPCConfig struct {
	Enabled        bool          `mapstructure:"enabled" json:"enabled"`
	MaxRecvMsgSize Size          `mapstructure:"max_recv_msg_size" json:"max_recv_msg_size"`
	MaxSendMsgSize Size          `mapstructure:"max_send_msg_size" json:"max_send_msg_size"`
	Timeout        time.Duration `mapstructure:"timeout" json:"timeout"`
	KeepAlive      bool          `mapstructure:"keep_alive" json:"keep_alive"`
	Compression    string        `mapstructure:"compression" json:"compression"`
	LoadBalancing  string        `mapstructure:"load_balancing" json:"load_balancing"`
}

// HTTP3Config HTTP/3配置
type HTTP3Config struct {
	Enabled          bool          `mapstructure:"enabled" json:"enabled"`
	Port             int           `mapstructure:"port" json:"port"`
	CertFile         string        `mapstructure:"cert_file" json:"cert_file"`
	KeyFile          string        `mapstructure:"key_file" json:"key_file"`
	MaxIdleTimeout   time.Duration `mapstructure:"max_idle_timeout" json:"max_idle_timeout"`
	MaxStreamTimeout time.Duration `mapstructure:"max_stream_timeout" json:"max_stream_timeout"`
	KeepAlive        bool          `mapstructure:"keep_alive" json:"keep_alive"`
	EnableDatagrams  bool          `mapstructure:"enable_datagrams" json:"enable_datagrams"`
}

// PerformanceConfig 性能优化配置
type PerformanceConfig struct {
	Enabled           bool                     `mapstructure:"enabled" json:"enabled"`
	EnableZeroCopy    bool                     `mapstructure:"enable_zero_copy" json:"enable_zero_copy"`
	BufferSize        Size                     `mapstructure:"buffer_size" json:"buffer_size"`
	MaxConnections    Size                     `mapstructure:"max_connections" json:"max_connections"`
	ConnectionTimeout time.Duration            `mapstructure:"connection_timeout" json:"connection_timeout"`
	KeepAliveTimeout  time.Duration            `mapstructure:"keep_alive_timeout" json:"keep_alive_timeout"`
	EnableCPUAffinity bool                     `mapstructure:"enable_cpu_affinity" json:"enable_cpu_affinity"`
	WorkerThreads     int                      `mapstructure:"worker_threads" json:"worker_threads"`
	MmapCache         MmapCacheConfig          `mapstructure:"mmap_cache" json:"mmap_cache"`
	ResponseOptimize  ResponseOptimizeConfig   `mapstructure:"response_optimize" json:"response_optimize"`
}

// ResponseOptimizeConfig 响应体优化配置
type ResponseOptimizeConfig struct {
	Enabled             bool     `mapstructure:"enabled" json:"enabled"`                           // 是否启用响应体优化
	MaxBodySize         Size     `mapstructure:"max_body_size" json:"max_body_size"`               // 最大响应体大小，超过则跳过优化
	MinBodySize         Size     `mapstructure:"min_body_size" json:"min_body_size"`               // 最小响应体大小，小于则跳过优化
	SkipContentTypes    []string `mapstructure:"skip_content_types" json:"skip_content_types"`     // 跳过优化的内容类型
	SkipEncodings       []string `mapstructure:"skip_encodings" json:"skip_encodings"`             // 跳过优化的编码类型
	SkipStatusCodes     []int    `mapstructure:"skip_status_codes" json:"skip_status_codes"`       // 跳过优化的状态码
	RequireContentLength bool    `mapstructure:"require_content_length" json:"require_content_length"` // 是否要求有Content-Length头部
}

// MmapCacheConfig 内存映射缓存配置
type MmapCacheConfig struct {
	Enabled          bool   `mapstructure:"enabled" json:"enabled"`
	MaxSize          Size   `mapstructure:"max_size" json:"max_size"`
	BasePath         string `mapstructure:"base_path" json:"base_path"`
	AutoPlatformPath bool   `mapstructure:"auto_platform_path" json:"auto_platform_path"`
	WindowsPath      string `mapstructure:"windows_path" json:"windows_path"`
	LinuxPath        string `mapstructure:"linux_path" json:"linux_path"`
	MacOSPath        string `mapstructure:"macos_path" json:"macos_path"`
	FreeBSDPath      string `mapstructure:"freebsd_path" json:"freebsd_path"`
	FallbackPath     string `mapstructure:"fallback_path" json:"fallback_path"`
}

// parseRateLimit 解析限速配置，支持单位
func parseRateLimit(value interface{}) int64 {
	switch v := value.(type) {
	case int64:
		return v
	case int:
		return int64(v)
	case float64:
		return int64(v)
	case string:
		return parseRateLimitString(v)
	default:
		return 0
	}
}

// parseRateLimitString 解析限速字符串，支持单位
func parseRateLimitString(s string) int64 {
	s = strings.TrimSpace(strings.ToLower(s))

	// 如果没有单位，直接解析数字
	if len(s) == 0 {
		return 0
	}

	// 查找数字部分
	var numStr string
	var unit string

	for i, char := range s {
		if char >= '0' && char <= '9' || char == '.' {
			numStr += string(char)
		} else {
			unit = s[i:]
			break
		}
	}

	if numStr == "" {
		return 0
	}

	// 解析数字
	num, err := strconv.ParseFloat(numStr, 64)
	if err != nil {
		return 0
	}

	// 解析单位
	switch unit {
	case "", "b", "bps":
		return int64(num)
	case "k", "kb", "kbps":
		return int64(num * 1024)
	case "m", "mb", "mbps":
		return int64(num * 1024 * 1024)
	case "g", "gb", "gbps":
		return int64(num * 1024 * 1024 * 1024)
	case "kib", "kibps":
		return int64(num * 1024)
	case "mib", "mibps":
		return int64(num * 1024 * 1024)
	case "gib", "gibps":
		return int64(num * 1024 * 1024 * 1024)
	default:
		return 0
	}
}

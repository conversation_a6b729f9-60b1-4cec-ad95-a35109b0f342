package processor

import (
	"bytes"
	"compress/gzip"
	"fmt"
	"mime"
	"strings"

	"github.com/sirupsen/logrus"
	"reverse-proxy/internal/config"
	"reverse-proxy/internal/minify"
	"reverse-proxy/internal/compression"
)

// MinifyProcessor 压缩和最小化处理器
type MinifyProcessor struct {
	*BaseProcessor
	globalConfig *config.MinifyConfig
}

// NewMinifyProcessor 创建压缩处理器
func NewMinifyProcessor(logger *logrus.Logger, globalConfig *config.MinifyConfig) *MinifyProcessor {
	return &MinifyProcessor{
		BaseProcessor: NewBaseProcessor("MinifyProcessor", 500, logger),
		globalConfig:  globalConfig,
	}
}

// Process 处理压缩和最小化
func (mp *MinifyProcessor) Process(req *RequestContext, resp *ResponseContext) *ProcessResult {
	// 添加POST请求的特殊调试
	if req.Method == "POST" {
		mp.LogDebug("[POST DEBUG] MinifyProcessor处理POST请求: path=%s, StatusCode=%d, ContentType=%s, BodySize=%d",
			req.URL.Path, resp.StatusCode, resp.ContentType, len(resp.Body))
	}

	// 跳过POST请求的minify处理，避免干扰WordPress登录重定向
	if req.Method == "POST" {
		mp.LogDebug("跳过POST请求的Minify处理: method=%s, path=%s", req.Method, req.URL.Path)
		// 如果启用了响应体优化，直接复制原始数据
		if resp.ShouldOptimize {
			resp.ResponseBody = make([]byte, len(resp.Body))
			copy(resp.ResponseBody, resp.Body)
			mp.LogDebug("响应体优化: 已复制POST响应数据到ResponseBody (%d bytes)", len(resp.ResponseBody))
		}
		return Success()
	}

	// 使用数字标识进行高效判断
	if req.MinifyFlag == 0 || resp.StatusCode < 200 || resp.StatusCode >= 300 {
		mp.LogDebug("跳过Minify处理: MinifyFlag=%d, StatusCode=%d",
			req.MinifyFlag, resp.StatusCode)
		return Success()
	}

	// 检查是否需要minify处理（包括类型和大小的完整检查）
	if !mp.shouldMinify(req, resp) {
		mp.LogDebug("跳过Minify处理: 不满足minify配置条件")
		// 如果启用了响应体优化，直接复制原始数据
		if resp.ShouldOptimize {
			resp.ResponseBody = make([]byte, len(resp.Body))
			copy(resp.ResponseBody, resp.Body)
			mp.LogDebug("响应体优化: 已复制处理后数据到ResponseBody (%d bytes)", len(resp.ResponseBody))
		}
		return Success()
	}

	// 处理已经被后端gzip压缩的内容（只有需要minify的内容才解压）
	wasGzipped := false
	if resp.Headers.Get("Content-Encoding") == "gzip" {
		if err := mp.decompressGzip(resp); err != nil {
			mp.LogWarn("解压gzip内容失败: %v", err)
			return Success()
		}
		wasGzipped = true
		mp.LogDebug("已解压后端gzip内容: %d bytes", len(resp.Body))
	}

	// 执行minify处理
	if err := mp.minifyContent(resp); err != nil {
		mp.LogWarn("内容最小化失败: %v", err)
	}

	// 重新应用压缩（使用minify配置中的压缩算法）
	if wasGzipped || mp.shouldCompress(req, resp) {
		if err := mp.compressContent(req, resp); err != nil {
			mp.LogWarn("内容压缩失败: %v", err)
		}
	}

	// 如果启用了响应体优化，将处理后的数据存储到ResponseBody
	if resp.ShouldOptimize {
		resp.ResponseBody = make([]byte, len(resp.Body))
		copy(resp.ResponseBody, resp.Body)
		mp.LogDebug("响应体优化: 已复制处理后的数据到ResponseBody (%d bytes)", len(resp.ResponseBody))
	}

	return Success()
}

// ShouldProcess 判断是否应该处理
func (mp *MinifyProcessor) ShouldProcess(req *RequestContext) bool {
	return true // 压缩处理器在响应阶段处理
}

// shouldCompress 判断是否应该压缩
func (mp *MinifyProcessor) shouldCompress(req *RequestContext, resp *ResponseContext) bool {
	// 检查响应状态码
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return false
	}

	// 获取minify配置
	var minifyConfig *config.MinifyConfig
	if req.Route != nil && req.Route.Minify != nil && req.Route.Minify.Enabled {
		minifyConfig = req.Route.Minify
	} else if req.Site != nil && req.Site.Minify != nil && req.Site.Minify.Enabled {
		minifyConfig = req.Site.Minify
	} else if mp.globalConfig != nil && mp.globalConfig.Enabled {
		minifyConfig = mp.globalConfig
	}

	if minifyConfig == nil || minifyConfig.CompressionAlgorithm == "" {
		return false
	}

	// 检查客户端是否支持配置的压缩算法
	acceptEncoding := req.Headers.Get("Accept-Encoding")
	algorithm := strings.ToLower(minifyConfig.CompressionAlgorithm)

	// 检查客户端支持
	switch algorithm {
	case "zstd":
		return strings.Contains(strings.ToLower(acceptEncoding), "zstd")
	case "br", "brotli":
		return strings.Contains(strings.ToLower(acceptEncoding), "br")
	case "gzip":
		return strings.Contains(strings.ToLower(acceptEncoding), "gzip")
	case "deflate":
		return strings.Contains(strings.ToLower(acceptEncoding), "deflate")
	default:
		return false
	}
}

// shouldMinify 判断是否应该最小化
func (mp *MinifyProcessor) shouldMinify(req *RequestContext, resp *ResponseContext) bool {
	var minifyConfig *config.MinifyConfig

	// 获取最小化配置（优先级：路由级 > 站点级 > 全局级）
	if req.Route != nil && req.Route.Minify != nil && req.Route.Minify.Enabled {
		minifyConfig = req.Route.Minify
	} else if req.Site != nil && req.Site.Minify != nil && req.Site.Minify.Enabled {
		minifyConfig = req.Site.Minify
	} else if mp.globalConfig != nil && mp.globalConfig.Enabled {
		minifyConfig = mp.globalConfig
	}

	if minifyConfig == nil {
		return false
	}

	// 检查内容大小
	contentSize := len(resp.Body)
	if minifyConfig.MinSize > 0 && contentSize < int(minifyConfig.MinSize) {
		return false
	}
	if minifyConfig.MaxSize > 0 && contentSize > int(minifyConfig.MaxSize) {
		return false
	}

	// 检查内容类型
	contentType := strings.ToLower(resp.ContentType)
	mediaType, _, _ := mime.ParseMediaType(contentType)

	for i := range minifyConfig.Types {
		allowedType := minifyConfig.Types[i] // 避免Go 1.20循环变量陷阱
		allowedTypeLower := strings.ToLower(allowedType)

		switch allowedTypeLower {
		case "js":
			if mediaType == "text/javascript" ||
			   mediaType == "application/javascript" ||
			   mediaType == "application/x-javascript" {
				return true
			}
		case "css":
			if mediaType == "text/css" {
				return true
			}
		case "html":
			if mediaType == "text/html" ||
			   mediaType == "application/xhtml+xml" {
				return true
			}
		default:
			// 对于其他类型，保持原有的字符串包含检查
			if strings.Contains(contentType, allowedTypeLower) {
				return true
			}
		}
	}

	return false
}

// shouldGzipCompress 判断是否应该Gzip压缩
func (mp *MinifyProcessor) shouldGzipCompress(req *RequestContext, resp *ResponseContext) bool {
	// 检查客户端是否支持gzip
	acceptEncoding := req.Headers.Get("Accept-Encoding")
	if !strings.Contains(strings.ToLower(acceptEncoding), "gzip") {
		return false
	}
	
	// 检查内容类型是否适合压缩
	contentType := strings.ToLower(resp.ContentType)
	compressibleTypes := []string{
		"text/",
		"application/json",
		"application/javascript",
		"application/xml",
		"application/rss+xml",
		"application/atom+xml",
		"image/svg+xml",
	}
	
	for _, compressibleType := range compressibleTypes {
		if strings.HasPrefix(contentType, compressibleType) {
			return true
		}
	}
	
	return false
}

// minifyContent 最小化内容
func (mp *MinifyProcessor) minifyContent(resp *ResponseContext) error {
	if len(resp.Body) == 0 {
		return nil
	}

	// 获取当前的 minify 配置
	var minifyConfig config.MinifyConfig
	if mp.globalConfig != nil {
		minifyConfig = *mp.globalConfig
	} else {
		// 使用默认配置
		minifyConfig = config.MinifyConfig{
			Enabled: true,
			Types:   []string{"html", "css", "js"},
		}
	}

	// 使用项目中的 minify 库
	minifier := minify.NewMinifier()

	originalSize := len(resp.Body)
	minified, err := minifier.Minify(resp.Body, resp.ContentType, minifyConfig)
	if err != nil {
		mp.LogWarn("内容最小化失败: %v", err)
		return nil // 失败时不影响正常流程
	}

	// 只有在压缩效果明显时才使用压缩版本
	if len(minified) < originalSize {
		resp.Body = minified
		resp.ContentLength = int64(len(minified))

		mp.LogDebug("内容最小化完成: %d -> %d bytes (%.1f%%)",
			originalSize, len(minified),
			float64(originalSize-len(minified))/float64(originalSize)*100)
	}

	return nil
}

// compressContent 根据minify配置压缩内容
func (mp *MinifyProcessor) compressContent(req *RequestContext, resp *ResponseContext) error {
	// 获取minify配置
	var minifyConfig *config.MinifyConfig
	if req.Route != nil && req.Route.Minify != nil && req.Route.Minify.Enabled {
		minifyConfig = req.Route.Minify
	} else if req.Site != nil && req.Site.Minify != nil && req.Site.Minify.Enabled {
		minifyConfig = req.Site.Minify
	} else if mp.globalConfig != nil && mp.globalConfig.Enabled {
		minifyConfig = mp.globalConfig
	}

	if minifyConfig == nil || minifyConfig.CompressionAlgorithm == "" {
		return nil
	}

	algorithm := strings.ToLower(minifyConfig.CompressionAlgorithm)

	// 根据配置的压缩算法进行压缩
	switch algorithm {
	case "zstd":
		return mp.compressWithZstd(resp)
	case "br", "brotli":
		return mp.compressWithBrotli(resp)
	case "gzip":
		return mp.compressWithGzip(resp)
	case "deflate":
		return mp.compressWithDeflate(resp)
	default:
		mp.LogWarn("不支持的压缩算法: %s", algorithm)
		return nil
	}
}

// compressWithZstd 使用Zstd压缩
func (mp *MinifyProcessor) compressWithZstd(resp *ResponseContext) error {
	var buf bytes.Buffer

	// 使用默认的compression配置
	compressionConfig := config.CompressionConfig{
		Enabled:   true,
		ZstdLevel: 3, // 默认级别
	}

	compressor, err := compression.CreateCompressor(&buf, compression.CompressionZstd, compressionConfig)
	if err != nil {
		return fmt.Errorf("创建Zstd压缩器失败: %w", err)
	}

	_, err = compressor.Write(resp.Body)
	if err != nil {
		return fmt.Errorf("Zstd压缩写入失败: %w", err)
	}

	err = compressor.Close()
	if err != nil {
		return fmt.Errorf("Zstd压缩关闭失败: %w", err)
	}

	compressed := buf.Bytes()
	originalSize := len(resp.Body)
	compressedSize := len(compressed)

	// 只有在压缩效果明显时才使用压缩版本
	if compressedSize < int(float64(originalSize)*0.9) { // 至少减少10%
		resp.Body = compressed
		resp.ContentLength = int64(compressedSize)
		resp.Headers.Set("Content-Encoding", "zstd")
		resp.Headers.Set("Vary", "Accept-Encoding")

		mp.LogDebug("Zstd压缩完成: %d -> %d bytes (%.1f%%)",
			originalSize, compressedSize,
			float64(originalSize-compressedSize)/float64(originalSize)*100)
	}

	return nil
}

// compressWithBrotli 使用Brotli压缩
func (mp *MinifyProcessor) compressWithBrotli(resp *ResponseContext) error {
	var buf bytes.Buffer

	compressionConfig := config.CompressionConfig{
		Enabled:       true,
		BrotliQuality: 6, // 默认质量
	}

	compressor, err := compression.CreateCompressor(&buf, compression.CompressionBrotli, compressionConfig)
	if err != nil {
		return fmt.Errorf("创建Brotli压缩器失败: %w", err)
	}

	_, err = compressor.Write(resp.Body)
	if err != nil {
		return fmt.Errorf("Brotli压缩写入失败: %w", err)
	}

	err = compressor.Close()
	if err != nil {
		return fmt.Errorf("Brotli压缩关闭失败: %w", err)
	}

	compressed := buf.Bytes()
	originalSize := len(resp.Body)
	compressedSize := len(compressed)

	if compressedSize < int(float64(originalSize)*0.9) {
		resp.Body = compressed
		resp.ContentLength = int64(compressedSize)
		resp.Headers.Set("Content-Encoding", "br")
		resp.Headers.Set("Vary", "Accept-Encoding")

		mp.LogDebug("Brotli压缩完成: %d -> %d bytes (%.1f%%)",
			originalSize, compressedSize,
			float64(originalSize-compressedSize)/float64(originalSize)*100)
	}

	return nil
}

// compressWithGzip 使用Gzip压缩
func (mp *MinifyProcessor) compressWithGzip(resp *ResponseContext) error {
	return mp.gzipCompress(resp)
}

// compressWithDeflate 使用Deflate压缩
func (mp *MinifyProcessor) compressWithDeflate(resp *ResponseContext) error {
	var buf bytes.Buffer

	compressionConfig := config.CompressionConfig{
		Enabled: true,
		Level:   6, // 默认级别
	}

	compressor, err := compression.CreateCompressor(&buf, compression.CompressionDeflate, compressionConfig)
	if err != nil {
		return fmt.Errorf("创建Deflate压缩器失败: %w", err)
	}

	_, err = compressor.Write(resp.Body)
	if err != nil {
		return fmt.Errorf("Deflate压缩写入失败: %w", err)
	}

	err = compressor.Close()
	if err != nil {
		return fmt.Errorf("Deflate压缩关闭失败: %w", err)
	}

	compressed := buf.Bytes()
	originalSize := len(resp.Body)
	compressedSize := len(compressed)

	if compressedSize < int(float64(originalSize)*0.9) {
		resp.Body = compressed
		resp.ContentLength = int64(compressedSize)
		resp.Headers.Set("Content-Encoding", "deflate")
		resp.Headers.Set("Vary", "Accept-Encoding")

		mp.LogDebug("Deflate压缩完成: %d -> %d bytes (%.1f%%)",
			originalSize, compressedSize,
			float64(originalSize-compressedSize)/float64(originalSize)*100)
	}

	return nil
}

// gzipCompress 应用Gzip压缩
func (mp *MinifyProcessor) gzipCompress(resp *ResponseContext) error {
	var buf bytes.Buffer
	gzipWriter := gzip.NewWriter(&buf)
	
	_, err := gzipWriter.Write(resp.Body)
	if err != nil {
		return fmt.Errorf("gzip写入失败: %w", err)
	}
	
	err = gzipWriter.Close()
	if err != nil {
		return fmt.Errorf("gzip关闭失败: %w", err)
	}
	
	compressed := buf.Bytes()
	originalSize := len(resp.Body)
	compressedSize := len(compressed)
	
	// 只有在压缩效果明显时才使用压缩版本
	if compressedSize < int(float64(originalSize)*0.9) { // 至少减少10%
		resp.Body = compressed
		resp.ContentLength = int64(compressedSize)
		resp.Headers.Set("Content-Encoding", "gzip")
		resp.Headers.Set("Vary", "Accept-Encoding")
		
		mp.LogDebug("Gzip压缩完成: %d -> %d bytes (%.1f%%)", 
			originalSize, compressedSize, 
			float64(originalSize-compressedSize)/float64(originalSize)*100)
	}
	
	return nil
}

// decompressGzip 解压gzip内容
func (mp *MinifyProcessor) decompressGzip(resp *ResponseContext) error {
	if len(resp.Body) == 0 {
		return nil
	}

	reader, err := gzip.NewReader(bytes.NewReader(resp.Body))
	if err != nil {
		return fmt.Errorf("创建gzip读取器失败: %w", err)
	}
	defer reader.Close()

	var buf bytes.Buffer
	_, err = buf.ReadFrom(reader)
	if err != nil {
		return fmt.Errorf("读取gzip内容失败: %w", err)
	}

	// 更新响应内容
	resp.Body = buf.Bytes()
	resp.ContentLength = int64(len(resp.Body))

	// 移除Content-Encoding头，因为内容已经解压
	resp.Headers.Del("Content-Encoding")

	return nil
}

// minifyHTML 最小化HTML
func (mp *MinifyProcessor) minifyHTML(content []byte) []byte {
	html := string(content)
	
	// 移除多余的空白字符
	html = mp.removeExtraWhitespace(html)
	
	// 移除HTML注释（保留条件注释）
	html = mp.removeHTMLComments(html)
	
	return []byte(html)
}

// minifyCSS 最小化CSS
func (mp *MinifyProcessor) minifyCSS(content []byte) []byte {
	css := string(content)
	
	// 移除CSS注释
	css = mp.removeCSSComments(css)
	
	// 移除多余的空白字符
	css = mp.removeExtraWhitespace(css)
	
	// 移除分号前的空格
	css = strings.ReplaceAll(css, " ;", ";")
	css = strings.ReplaceAll(css, " }", "}")
	css = strings.ReplaceAll(css, "{ ", "{")
	
	return []byte(css)
}

// minifyJS 最小化JavaScript
func (mp *MinifyProcessor) minifyJS(content []byte) []byte {
	js := string(content)
	
	// 移除单行注释
	js = mp.removeJSComments(js)
	
	// 移除多余的空白字符（保留字符串内的空格）
	js = mp.removeExtraWhitespace(js)
	
	return []byte(js)
}

// minifyJSON 最小化JSON
func (mp *MinifyProcessor) minifyJSON(content []byte) []byte {
	json := string(content)
	
	// 移除JSON中的多余空白字符
	json = mp.removeJSONWhitespace(json)
	
	return []byte(json)
}

// removeExtraWhitespace 移除多余的空白字符
func (mp *MinifyProcessor) removeExtraWhitespace(content string) string {
	// 将多个连续的空白字符替换为单个空格
	lines := strings.Split(content, "\n")
	var result []string
	
	for _, line := range lines {
		trimmed := strings.TrimSpace(line)
		if trimmed != "" {
			// 将多个空格替换为单个空格
			trimmed = strings.Join(strings.Fields(trimmed), " ")
			result = append(result, trimmed)
		}
	}
	
	return strings.Join(result, "")
}

// removeHTMLComments 移除HTML注释
func (mp *MinifyProcessor) removeHTMLComments(html string) string {
	// 简单的HTML注释移除（不处理条件注释）
	for {
		start := strings.Index(html, "<!--")
		if start == -1 {
			break
		}
		
		end := strings.Index(html[start:], "-->")
		if end == -1 {
			break
		}
		
		comment := html[start : start+end+3]
		
		// 保留条件注释
		if strings.Contains(comment, "[if") || strings.Contains(comment, "[endif]") {
			// 跳过这个注释
			html = html[:start] + "<!--CONDITIONAL-->" + html[start+end+3:]
			continue
		}
		
		html = html[:start] + html[start+end+3:]
	}
	
	// 恢复条件注释标记
	html = strings.ReplaceAll(html, "<!--CONDITIONAL-->", "")
	
	return html
}

// removeCSSComments 移除CSS注释
func (mp *MinifyProcessor) removeCSSComments(css string) string {
	for {
		start := strings.Index(css, "/*")
		if start == -1 {
			break
		}
		
		end := strings.Index(css[start:], "*/")
		if end == -1 {
			break
		}
		
		css = css[:start] + css[start+end+2:]
	}
	
	return css
}

// removeJSComments 移除JavaScript注释
func (mp *MinifyProcessor) removeJSComments(js string) string {
	lines := strings.Split(js, "\n")
	var result []string
	
	for _, line := range lines {
		// 移除单行注释（简单处理，不考虑字符串内的//）
		if idx := strings.Index(line, "//"); idx != -1 {
			line = line[:idx]
		}
		
		if strings.TrimSpace(line) != "" {
			result = append(result, line)
		}
	}
	
	return strings.Join(result, "\n")
}

// removeJSONWhitespace 移除JSON中的空白字符
func (mp *MinifyProcessor) removeJSONWhitespace(json string) string {
	var result strings.Builder
	inString := false
	escaped := false
	
	for _, char := range json {
		if escaped {
			result.WriteRune(char)
			escaped = false
			continue
		}
		
		if char == '\\' && inString {
			escaped = true
			result.WriteRune(char)
			continue
		}
		
		if char == '"' {
			inString = !inString
			result.WriteRune(char)
			continue
		}
		
		if inString {
			result.WriteRune(char)
			continue
		}
		
		// 在JSON结构外，移除空白字符
		if char != ' ' && char != '\t' && char != '\n' && char != '\r' {
			result.WriteRune(char)
		}
	}
	
	return result.String()
}

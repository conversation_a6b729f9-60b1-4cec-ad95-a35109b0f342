#!/bin/bash

# ACL文件监控功能测试脚本
# 用于验证文件变化时的实时重载功能

set -e

# 配置
ACL_DIR="acl"
LOG_FILE="logs/proxy_$(date +%Y%m%d).log"
TEST_IP="**************"
PROXY_URL="http://localhost"
PROXY_PORT="80"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查反向代理是否运行
check_proxy_running() {
    if ! curl -s -o /dev/null -w "%{http_code}" "$PROXY_URL:$PROXY_PORT" >/dev/null 2>&1; then
        error "反向代理服务未运行或无法访问"
        error "请先启动反向代理服务: ./reverse-proxy"
        exit 1
    fi
    success "反向代理服务运行正常"
}

# 监控日志文件
monitor_logs() {
    local pattern="$1"
    local timeout="$2"
    
    log "监控日志文件，等待模式: $pattern"
    
    if timeout "$timeout" tail -f "$LOG_FILE" | grep -q "$pattern"; then
        success "检测到预期的日志输出"
        return 0
    else
        warning "在 ${timeout} 秒内未检测到预期的日志输出"
        return 1
    fi
}

# 测试IP访问
test_ip_access() {
    local test_ip="$1"
    local expected_result="$2"  # "allowed" 或 "denied"
    
    log "测试IP访问: $test_ip (期望: $expected_result)"
    
    # 使用curl测试访问，设置Host头和客户端IP
    local response=$(curl -s -o /dev/null -w "%{http_code}" \
        -H "X-Forwarded-For: $test_ip" \
        -H "X-Real-IP: $test_ip" \
        "$PROXY_URL:$PROXY_PORT" 2>/dev/null || echo "000")
    
    if [[ "$expected_result" == "allowed" ]]; then
        if [[ "$response" != "403" ]]; then
            success "IP $test_ip 访问被允许 (HTTP $response)"
            return 0
        else
            error "IP $test_ip 访问被拒绝，但期望被允许"
            return 1
        fi
    else
        if [[ "$response" == "403" ]]; then
            success "IP $test_ip 访问被拒绝 (HTTP $response)"
            return 0
        else
            error "IP $test_ip 访问被允许，但期望被拒绝"
            return 1
        fi
    fi
}

# 测试1: 基本文件监控
test_basic_file_monitoring() {
    log "=== 测试1: 基本文件监控 ==="
    
    local test_file="$ACL_DIR/global_deny.txt"
    local test_ip="**************"
    
    # 确保测试IP不在拒绝列表中
    sed -i "/$test_ip/d" "$test_file" 2>/dev/null || true
    sleep 1
    
    # 测试IP应该被允许
    test_ip_access "$test_ip" "allowed"
    
    # 添加IP到拒绝列表
    log "添加 $test_ip 到全局拒绝列表"
    echo "$test_ip" >> "$test_file"
    
    # 等待文件重载
    sleep 2
    
    # 测试IP应该被拒绝
    test_ip_access "$test_ip" "denied"
    
    # 清理
    sed -i "/$test_ip/d" "$test_file"
    sleep 1
    
    success "基本文件监控测试完成"
}

# 测试2: 站点级ACL
test_site_acl() {
    log "=== 测试2: 站点级ACL ==="
    
    local test_file="$ACL_DIR/api_deny.txt"
    local test_ip="**************"
    
    if [[ ! -f "$test_file" ]]; then
        warning "站点ACL文件不存在: $test_file"
        return 0
    fi
    
    # 确保测试IP不在拒绝列表中
    sed -i "/$test_ip/d" "$test_file" 2>/dev/null || true
    sleep 1
    
    # 添加IP到站点拒绝列表
    log "添加 $test_ip 到API站点拒绝列表"
    echo "$test_ip" >> "$test_file"
    
    # 等待文件重载
    sleep 2
    
    # 测试API站点访问（需要配置对应的站点）
    log "测试站点级ACL访问控制"
    
    # 清理
    sed -i "/$test_ip/d" "$test_file"
    sleep 1
    
    success "站点级ACL测试完成"
}

# 测试3: 大文件性能
test_large_file_performance() {
    log "=== 测试3: 大文件性能测试 ==="
    
    local test_file="$ACL_DIR/performance_test.txt"
    local ip_count=1000
    
    log "创建包含 $ip_count 个IP的测试文件"
    
    # 创建大文件
    {
        echo "# 性能测试文件"
        echo "# 包含 $ip_count 个IP地址"
        echo ""
        
        for i in $(seq 1 $ip_count); do
            printf "10.%d.%d.%d\n" $((i/65536)) $(((i/256)%256)) $((i%256))
        done
    } > "$test_file"
    
    # 记录开始时间
    local start_time=$(date +%s.%N)
    
    # 等待文件重载
    sleep 3
    
    # 记录结束时间
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc)
    
    log "大文件重载耗时: ${duration}s"
    
    # 清理测试文件
    rm -f "$test_file"
    
    success "大文件性能测试完成"
}

# 测试4: 文件格式验证
test_file_format_validation() {
    log "=== 测试4: 文件格式验证 ==="
    
    local test_file="$ACL_DIR/format_test.txt"
    
    # 创建包含各种格式的测试文件
    {
        echo "# 格式测试文件"
        echo ""
        echo "# 有效的IP格式"
        echo "***********"
        echo "10.0.0.0/8"
        echo "**********/12"
        echo ""
        echo "# IPv6地址"
        echo "2001:db8::1"
        echo "2001:db8::/32"
        echo ""
        echo "# 注释和空行测试"
        echo "# 这是注释"
        echo ""
        echo "***********"
    } > "$test_file"
    
    # 等待文件重载
    sleep 2
    
    # 验证文件被正确处理
    if grep -q "格式测试文件" "$LOG_FILE" 2>/dev/null; then
        success "文件格式验证测试完成"
    else
        warning "未在日志中找到文件重载记录"
    fi
    
    # 清理测试文件
    rm -f "$test_file"
}

# 测试5: 并发文件修改
test_concurrent_modifications() {
    log "=== 测试5: 并发文件修改测试 ==="
    
    local test_files=("$ACL_DIR/concurrent_test1.txt" "$ACL_DIR/concurrent_test2.txt")
    
    # 并发修改多个文件
    for file in "${test_files[@]}"; do
        {
            echo "# 并发测试文件: $(basename "$file")"
            echo "192.168.200.$((RANDOM % 255))"
            echo "192.168.201.$((RANDOM % 255))"
        } > "$file" &
    done
    
    # 等待所有后台任务完成
    wait
    
    # 等待文件重载
    sleep 3
    
    # 清理测试文件
    for file in "${test_files[@]}"; do
        rm -f "$file"
    done
    
    success "并发文件修改测试完成"
}

# 显示测试结果摘要
show_test_summary() {
    log "=== 测试结果摘要 ==="
    
    if [[ -f "$LOG_FILE" ]]; then
        local reload_count=$(grep -c "文件重载成功" "$LOG_FILE" 2>/dev/null || echo "0")
        local error_count=$(grep -c "ERROR" "$LOG_FILE" 2>/dev/null || echo "0")
        
        log "文件重载次数: $reload_count"
        log "错误次数: $error_count"
        
        if [[ $error_count -eq 0 ]]; then
            success "所有测试通过，无错误"
        else
            warning "发现 $error_count 个错误，请检查日志"
        fi
    else
        warning "日志文件不存在: $LOG_FILE"
    fi
}

# 主函数
main() {
    log "开始ACL文件监控功能测试"
    
    # 检查环境
    check_proxy_running
    
    # 创建ACL目录
    mkdir -p "$ACL_DIR"
    
    # 执行测试
    case "${1:-all}" in
        "basic")
            test_basic_file_monitoring
            ;;
        "site")
            test_site_acl
            ;;
        "performance")
            test_large_file_performance
            ;;
        "format")
            test_file_format_validation
            ;;
        "concurrent")
            test_concurrent_modifications
            ;;
        "all")
            test_basic_file_monitoring
            test_site_acl
            test_large_file_performance
            test_file_format_validation
            test_concurrent_modifications
            ;;
        *)
            echo "用法: $0 [basic|site|performance|format|concurrent|all]"
            exit 1
            ;;
    esac
    
    # 显示测试摘要
    show_test_summary
    
    success "ACL文件监控功能测试完成"
}

# 执行主函数
main "$@"

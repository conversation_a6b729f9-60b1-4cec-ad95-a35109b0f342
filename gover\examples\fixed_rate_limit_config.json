{"server": {"http_port": 80, "https_port": 443, "max_connections": 10000}, "rate_limit": {"enabled": true, "global_rps": 10000, "ip_rps": 100, "site_rps": 1000, "burst": 50, "comment": "请求频率限制 - 防止DDoS攻击，控制QPS"}, "circuit_breaker": {"enabled": true, "max_failures": 5, "reset_timeout": "30s", "half_open_requests": 3}, "compression": {"enabled": true, "min_size": "1KB", "max_size": "10MB", "algorithms": ["br", "gzip", "deflate"]}, "sites": [{"name": "high_traffic_api", "domains": ["api.example.com"], "max_connections": 3000, "bandwidth_limit": "10MB", "comment": "高流量API服务 - 带宽限制10MB/s", "upstreams": [{"name": "api_server_1", "address": "127.0.0.1", "port": 8001, "protocol": "http", "load_balance_group": "api_group"}], "routes": [{"pattern": "^/api/v1/", "upstream": "api_group", "cache": false, "comment": "API接口 - 无带宽限制"}, {"pattern": "^/api/export/", "upstream": "api_group", "bandwidth_limit": "5MB", "cache": false, "comment": "数据导出 - 限制5MB/s防止占用过多带宽"}]}, {"name": "file_server", "domains": ["files.example.com"], "max_connections": 2000, "bandwidth_limit": "50MB", "comment": "文件服务器 - 总带宽限制50MB/s", "routes": [{"pattern": "^/uploads/", "static_dir": "/var/www/uploads", "bandwidth_limit": "20MB", "cache": false, "comment": "文件上传 - 限制20MB/s"}, {"pattern": "^/downloads/premium/", "static_dir": "/var/www/downloads/premium", "bandwidth_limit": "100MB", "cache": true, "comment": "高级用户下载 - 限制100MB/s"}, {"pattern": "^/downloads/", "static_dir": "/var/www/downloads", "bandwidth_limit": "10MB", "cache": true, "comment": "普通下载 - 限制10MB/s"}, {"pattern": "^/preview/", "static_dir": "/var/www/preview", "bandwidth_limit": "2MB", "cache": true, "comment": "文件预览 - 限制2MB/s"}]}, {"name": "streaming_service", "domains": ["stream.example.com"], "max_connections": 1500, "bandwidth_limit": "200MB", "comment": "流媒体服务 - 高带宽需求", "upstreams": [{"name": "stream_server", "address": "127.0.0.1", "port": 8003, "protocol": "http", "load_balance_group": "stream_group"}], "routes": [{"pattern": "^/video/hd/", "upstream": "stream_group", "bandwidth_limit": "50MB", "cache": true, "comment": "高清视频 - 限制50MB/s"}, {"pattern": "^/video/sd/", "upstream": "stream_group", "bandwidth_limit": "20MB", "cache": true, "comment": "标清视频 - 限制20MB/s"}, {"pattern": "^/audio/", "upstream": "stream_group", "bandwidth_limit": "5MB", "cache": true, "comment": "音频流 - 限制5MB/s"}]}, {"name": "admin_panel", "domains": ["admin.example.com"], "max_connections": 100, "bandwidth_limit": "5MB", "comment": "管理后台 - 低带宽需求", "upstreams": [{"name": "admin_server", "address": "127.0.0.1", "port": 8004, "protocol": "http", "load_balance_group": "admin_group"}], "routes": [{"pattern": "^/admin/logs/download/", "upstream": "admin_group", "bandwidth_limit": "10MB", "cache": false, "comment": "日志下载 - 临时提高到10MB/s"}, {"pattern": "^/admin/", "upstream": "admin_group", "cache": false, "comment": "管理界面 - 使用站点默认带宽限制"}]}, {"name": "cdn_service", "domains": ["cdn.example.com"], "max_connections": 5000, "comment": "CDN服务 - 无带宽限制，最大化传输速度", "routes": [{"pattern": "^/static/js/", "static_dir": "/var/www/cdn/js", "cache": true, "comment": "JavaScript文件 - 无带宽限制"}, {"pattern": "^/static/css/", "static_dir": "/var/www/cdn/css", "cache": true, "comment": "CSS文件 - 无带宽限制"}, {"pattern": "^/static/images/", "static_dir": "/var/www/cdn/images", "cache": true, "comment": "图片文件 - 无带宽限制"}]}], "comments": {"rate_limit_explanation": {"global_rate_limit": {"purpose": "请求频率限制，防止DDoS攻击", "scope": "全局/IP/站点级别", "unit": "请求/秒 (RPS)", "algorithm": "令牌桶算法", "config_location": "全局配置中的rate_limit字段"}, "bandwidth_limit": {"purpose": "传输速度限制，管理带宽使用", "scope": "站点/路由级别", "unit": "字节/秒 (如: 1MB, 10MB)", "algorithm": "流量整形", "config_location": "站点和路由配置中的bandwidth_limit字段"}}, "naming_fix": {"old_naming": "站点和路由级别使用rate_limit字段", "new_naming": "站点和路由级别使用bandwidth_limit字段", "reason": "避免与全局请求频率限制的rate_limit字段混淆", "migration": "将站点/路由中的rate_limit重命名为bandwidth_limit"}, "usage_scenarios": {"ddos_protection": {"use": "rate_limit配置", "example": "global_rps: 10000, ip_rps: 100"}, "bandwidth_management": {"use": "bandwidth_limit配置", "example": "bandwidth_limit: '10MB'"}, "file_downloads": {"use": "bandwidth_limit配置", "example": "downloads路由设置bandwidth_limit: '5MB'"}, "streaming_media": {"use": "bandwidth_limit配置", "example": "video路由设置bandwidth_limit: '50MB'"}}, "best_practices": {"rate_limit": ["根据服务器性能设置global_rps", "设置合理的ip_rps防止单点攻击", "根据业务需求设置site_rps", "burst值应该适中，避免过大或过小"], "bandwidth_limit": ["根据总带宽容量设置站点限制", "重要文件可设置更高的带宽限制", "考虑用户体验，避免限制过严", "监控带宽使用情况，及时调整"]}}}
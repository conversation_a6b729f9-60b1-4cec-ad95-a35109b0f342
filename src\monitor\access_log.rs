use anyhow::Result;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs::OpenOptions;
use std::io::Write;
use std::path::PathBuf;
use std::sync::Arc;
use tokio::sync::{mpsc, RwLock};
use tokio::time::{interval, Duration};
use tracing::{debug, error, info, warn};

use crate::config::{LogConfig, MonitorConfig};
use crate::processor::RequestContext;

/// 访问日志记录器
pub struct AccessLogger {
    config: LogConfig,
    sender: mpsc::UnboundedSender<LogEntry>,
    stats: Arc<RwLock<AccessLogStats>>,
}

impl AccessLogger {
    /// 创建新的访问日志记录器
    pub async fn new(monitor_config: &MonitorConfig) -> Result<Self> {
        // 从监控配置中获取日志配置（这里简化处理）
        let config = LogConfig {
            level: "info".to_string(),
            file: Some("logs/access.log".to_string()),
            format: "combined".to_string(),
            formats: {
                let mut formats = HashMap::new();
                formats.insert("combined".to_string(), 
                    "{remote_addr} - - [{time}] \"{request}\" {status} {body_bytes_sent} \"{http_referer}\" \"{http_user_agent}\" {request_time} {site}".to_string());
                formats.insert("common".to_string(),
                    "{remote_addr} - - [{time}] \"{request}\" {status} {body_bytes_sent}".to_string());
                formats.insert("json".to_string(),
                    r#"{{"remote_addr":"{remote_addr}","time":"{time}","request":"{request}","status":{status},"body_bytes_sent":{body_bytes_sent},"http_referer":"{http_referer}","http_user_agent":"{http_user_agent}","request_time":{request_time},"site":"{site}"}}"#.to_string());
                formats
            },
            max_size: 100,
            max_backups: 10,
            max_age: 30,
            targets: vec![],
            r#async: crate::config::AsyncLogConfig {
                enabled: true,
                channel_size: 2000,
                batch_size: 50,
                flush_interval: Duration::from_secs(2),
                max_memory_mb: 2,
                drop_policy: "drop_oldest".to_string(),
            },
        };

        let (sender, receiver) = mpsc::unbounded_channel();
        let stats = Arc::new(RwLock::new(AccessLogStats::new()));

        // 启动日志写入任务
        let log_writer = LogWriter::new(config.clone(), receiver, stats.clone()).await?;
        tokio::spawn(async move {
            if let Err(e) = log_writer.run().await {
                error!("Access log writer failed: {}", e);
            }
        });

        info!("Access logger initialized successfully");

        Ok(Self {
            config,
            sender,
            stats,
        })
    }

    /// 记录请求日志
    pub async fn log_request(&self, ctx: &RequestContext) -> Result<()> {
        let entry = LogEntry::from_context(ctx);
        
        if let Err(e) = self.sender.send(entry) {
            warn!("Failed to send log entry: {}", e);
            // 更新丢弃统计
            let mut stats = self.stats.write().await;
            stats.dropped_entries += 1;
        } else {
            // 更新统计
            let mut stats = self.stats.write().await;
            stats.total_entries += 1;
        }

        Ok(())
    }

    /// 刷新日志缓冲区
    pub async fn flush(&self) -> Result<()> {
        // 发送刷新信号（这里简化处理）
        debug!("Flushing access log buffer");
        Ok(())
    }

    /// 获取访问日志统计
    pub async fn get_stats(&self) -> AccessLogStats {
        self.stats.read().await.clone()
    }
}

/// 日志条目
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogEntry {
    pub timestamp: DateTime<Utc>,
    pub remote_addr: String,
    pub method: String,
    pub path: String,
    pub query: Option<String>,
    pub status: u16,
    pub body_bytes_sent: u64,
    pub referer: Option<String>,
    pub user_agent: Option<String>,
    pub request_time: f64, // 毫秒
    pub site: Option<String>,
    pub request_id: String,
    pub protocol: String,
}

impl LogEntry {
    /// 从请求上下文创建日志条目
    pub fn from_context(ctx: &RequestContext) -> Self {
        let body_bytes_sent = ctx.response.headers
            .get("content-length")
            .and_then(|v| v.parse().ok())
            .unwrap_or(0);

        Self {
            timestamp: Utc::now(),
            remote_addr: ctx.client_addr.to_string(),
            method: ctx.method.clone(),
            path: ctx.path().to_string(),
            query: ctx.query_string.clone(),
            status: ctx.response.status_code,
            body_bytes_sent,
            referer: ctx.headers.get("referer").cloned(),
            user_agent: ctx.headers.get("user-agent").cloned(),
            request_time: ctx.start_time.elapsed().as_millis() as f64,
            site: ctx.site.as_ref().map(|s| s.site_id.clone()),
            request_id: ctx.request_id.clone(),
            protocol: "HTTP/1.1".to_string(), // 简化处理
        }
    }

    /// 格式化日志条目
    pub fn format(&self, format_str: &str) -> String {
        let mut result = format_str.to_string();
        
        // 替换变量
        result = result.replace("{remote_addr}", &self.remote_addr);
        result = result.replace("{time}", &self.timestamp.format("%d/%b/%Y:%H:%M:%S %z").to_string());
        result = result.replace("{request}", &format!("{} {} {}", self.method, self.path, self.protocol));
        result = result.replace("{status}", &self.status.to_string());
        result = result.replace("{body_bytes_sent}", &self.body_bytes_sent.to_string());
        result = result.replace("{http_referer}", self.referer.as_deref().unwrap_or("-"));
        result = result.replace("{http_user_agent}", self.user_agent.as_deref().unwrap_or("-"));
        result = result.replace("{request_time}", &format!("{:.3}", self.request_time / 1000.0));
        result = result.replace("{site}", self.site.as_deref().unwrap_or("-"));
        result = result.replace("{request_id}", &self.request_id);

        result
    }
}

/// 日志写入器
struct LogWriter {
    config: LogConfig,
    receiver: mpsc::UnboundedReceiver<LogEntry>,
    stats: Arc<RwLock<AccessLogStats>>,
    buffer: Vec<LogEntry>,
    last_flush: std::time::Instant,
}

impl LogWriter {
    async fn new(
        config: LogConfig,
        receiver: mpsc::UnboundedReceiver<LogEntry>,
        stats: Arc<RwLock<AccessLogStats>>,
    ) -> Result<Self> {
        Ok(Self {
            config,
            receiver,
            stats,
            buffer: Vec::new(),
            last_flush: std::time::Instant::now(),
        })
    }

    async fn run(mut self) -> Result<()> {
        let mut flush_interval = interval(self.config.r#async.flush_interval);

        loop {
            tokio::select! {
                // 接收新的日志条目
                entry = self.receiver.recv() => {
                    match entry {
                        Some(entry) => {
                            self.buffer.push(entry);
                            
                            // 检查是否需要刷新
                            if self.buffer.len() >= self.config.r#async.batch_size {
                                self.flush_buffer().await?;
                            }
                        }
                        None => {
                            // 通道关闭，刷新剩余数据并退出
                            self.flush_buffer().await?;
                            break;
                        }
                    }
                }
                
                // 定时刷新
                _ = flush_interval.tick() => {
                    if !self.buffer.is_empty() {
                        self.flush_buffer().await?;
                    }
                }
            }
        }

        Ok(())
    }

    async fn flush_buffer(&mut self) -> Result<()> {
        if self.buffer.is_empty() {
            return Ok(());
        }

        // 写入文件
        if let Some(log_file) = &self.config.file {
            self.write_to_file(log_file).await?;
        }

        // 更新统计
        let mut stats = self.stats.write().await;
        stats.written_entries += self.buffer.len() as u64;
        stats.last_flush = std::time::SystemTime::now();

        // 清空缓冲区
        self.buffer.clear();
        self.last_flush = std::time::Instant::now();

        debug!("Flushed {} log entries", self.buffer.len());
        Ok(())
    }

    async fn write_to_file(&self, file_path: &str) -> Result<()> {
        let path = PathBuf::from(file_path);
        
        // 确保目录存在
        if let Some(parent) = path.parent() {
            tokio::fs::create_dir_all(parent).await?;
        }

        let mut file = OpenOptions::new()
            .create(true)
            .append(true)
            .open(&path)?;

        let format_str = self.config.formats
            .get(&self.config.format)
            .unwrap_or(&self.config.format);

        for entry in &self.buffer {
            let line = entry.format(format_str);
            writeln!(file, "{}", line)?;
        }

        file.flush()?;
        Ok(())
    }
}

/// 访问日志统计
#[derive(Debug, Clone)]
pub struct AccessLogStats {
    pub total_entries: u64,
    pub written_entries: u64,
    pub dropped_entries: u64,
    pub buffer_size: usize,
    pub last_flush: std::time::SystemTime,
    pub start_time: std::time::SystemTime,
}

impl AccessLogStats {
    pub fn new() -> Self {
        Self {
            total_entries: 0,
            written_entries: 0,
            dropped_entries: 0,
            buffer_size: 0,
            last_flush: std::time::SystemTime::now(),
            start_time: std::time::SystemTime::now(),
        }
    }

    /// 计算写入成功率
    pub fn write_success_rate(&self) -> f64 {
        if self.total_entries == 0 {
            0.0
        } else {
            self.written_entries as f64 / self.total_entries as f64 * 100.0
        }
    }

    /// 计算丢弃率
    pub fn drop_rate(&self) -> f64 {
        if self.total_entries == 0 {
            0.0
        } else {
            self.dropped_entries as f64 / self.total_entries as f64 * 100.0
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::net::SocketAddr;

    #[test]
    fn test_log_entry_format() {
        let entry = LogEntry {
            timestamp: DateTime::parse_from_rfc3339("2025-01-01T12:00:00Z").unwrap().with_timezone(&Utc),
            remote_addr: "192.168.1.1".to_string(),
            method: "GET".to_string(),
            path: "/test".to_string(),
            query: None,
            status: 200,
            body_bytes_sent: 1024,
            referer: Some("https://example.com".to_string()),
            user_agent: Some("Mozilla/5.0".to_string()),
            request_time: 123.456,
            site: Some("test-site".to_string()),
            request_id: "req-123".to_string(),
            protocol: "HTTP/1.1".to_string(),
        };

        let format_str = "{remote_addr} - - [{time}] \"{request}\" {status} {body_bytes_sent}";
        let formatted = entry.format(format_str);
        
        assert!(formatted.contains("192.168.1.1"));
        assert!(formatted.contains("GET /test HTTP/1.1"));
        assert!(formatted.contains("200"));
        assert!(formatted.contains("1024"));
    }

    #[test]
    fn test_access_log_stats() {
        let mut stats = AccessLogStats::new();
        assert_eq!(stats.write_success_rate(), 0.0);
        assert_eq!(stats.drop_rate(), 0.0);

        stats.total_entries = 100;
        stats.written_entries = 95;
        stats.dropped_entries = 5;

        assert_eq!(stats.write_success_rate(), 95.0);
        assert_eq!(stats.drop_rate(), 5.0);
    }
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上游服务器2</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f0f8ff;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .server-info {
            background-color: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .timestamp {
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>⚡ 上游服务器2</h1>
        
        <div class="server-info">
            <h3>服务器信息</h3>
            <p><strong>服务器名称:</strong> Web Server 2</p>
            <p><strong>IP地址:</strong> ************</p>
            <p><strong>端口:</strong> 8080</p>
            <p><strong>状态:</strong> <span style="color: green;">运行中</span></p>
        </div>
        
        <div class="server-info">
            <h3>负载均衡测试</h3>
            <p>这是来自上游服务器2的响应。</p>
            <p>如果您看到这个页面，说明负载均衡器成功将请求路由到了服务器2。</p>
        </div>
        
        <div class="timestamp">
            <p>页面生成时间: <span id="timestamp"></span></p>
        </div>
    </div>
    
    <script>
        document.getElementById('timestamp').textContent = new Date().toLocaleString('zh-CN');
    </script>
</body>
</html> 
# 日志设置与变量说明

本项目支持灵活、强大的日志系统，支持多目标、多格式、变量模板、全局与站点独立配置。
你可以将日志输出到文件、syslog、控制台等多个目标，并可自定义日志内容格式。

---

## 一、日志目标配置

### 1. 全局日志目标（log.targets）

在 `config.json` 的 `log.targets` 字段配置，适用于所有站点（除非站点单独设置）。

```json
"log": {
  "targets": [
    {
      "type": "file",
      "filename": "logs/access_${site}_${date}.log",
      "format": "combined"
    },
    {
      "type": "syslog",
      "network": "udp",
      "address": "127.0.0.1:514",
      "format": "json"
    },
    {
      "type": "console",
      "format": "short"
    }
  ]
}
```

- `type`：日志目标类型，支持 `file`（文件）、`syslog`（Syslog服务器）、`console`（控制台）。
- `filename`：文件型目标的路径，支持变量（如 `${site}`、`${date}`）。
- `network`/`address`：syslog 目标的网络协议和地址。
- `format`：日志格式模板名（见下文）。

### 2. 站点独立日志目标（log_targets）

每个站点可单独设置 `log_targets`，优先于全局设置，仅对该站点生效。

```json
"log_targets": [
  {
    "type": "file",
    "filename": "logs/tingtao_${date}.log",
    "format": "combined"
  },
  {
    "type": "syslog",
    "network": "udp",
    "address": "*************:514",
    "format": "json"
  }
]
```

- 站点级 `log_targets` 完全覆盖全局 `log.targets`。

---

## 二、日志格式模板（log.formats）

在 `log.formats` 字段定义多个格式模板，支持自定义变量。

```json
"log": {
  "formats": {
    "combined": "${remote_addr} - - [${time}] \"${request}\" ${status} ${body_bytes_sent} \"${http_referer}\" \"${http_user_agent}\" ${request_time} ${site}",
    "json": "{\"ip\":\"${remote_addr}\",\"status\":${status},\"site\":\"${site}\"}",
    "short": "${site} | ${remote_addr} | ${request} | ${status} | ${request_time}s"
  }
}
```

- 每个目标的 `format` 字段填写模板名（如 `"combined"`、`"json"`、`"short"`）。
- 可自定义任意格式，支持所有下方变量。

---

## 三、日志变量说明

日志格式模板支持以下变量（与Nginx变量高度兼容）：

| 变量名                  | 说明                         | 示例值                |
|-------------------------|------------------------------|-----------------------|
| `${remote_addr}`        | 客户端IP                     | *************         |
| `${clientRealIp}`       | 真实客户端IP（X-Forwarded-For）| ***********         |
| `${server_addr}`        | 本机监听IP:端口              | 0.0.0.0:443           |
| `${scheme}`             | 请求协议（http/https）       | https                 |
| `${server_protocol}`    | 协议版本                     | HTTP/1.1              |
| `${request_method}`     | 请求方法                     | GET                   |
| `${host}`               | Host头                       | www.example.com       |
| `${status}`             | 响应状态码                   | 200                   |
| `${sent_http_content_type}` | 响应Content-Type         | text/html             |
| `${body_bytes_sent}`    | 响应体字节数                 | 1234                  |
| `${request_uri}`        | 原始URI                      | /index.html           |
| `${http_referer}`       | Referer头                    | https://ref.com/      |
| `${http_user_agent}`    | User-Agent头                 | curl/7.68.0           |
| `${mysvrip}`            | 预留字段                     | -                     |
| `${ipgeo}`              | 预留字段                     | -                     |
| `${biaoji}`             | 预留标记                     | (((nginx)))           |
| `${request}`            | 完整请求行                   | GET /index.html HTTP/1.1 |
| `${request_time}`       | 请求耗时（秒，浮点）         | 0.123                 |
| `${site}`               | 站点名                       | tingtao_site          |
| `${webalias}`           | 站点别名（同site）           | tingtao_site          |
| `${fmt_localtime}`      | 本地时间（yyyy-MM-dd HH:mm:ss）| 2024-06-01 12:00:00 |
| `${time}`               | ISO时间（yyyy-MM-ddTHH:mm:ssZ）| 2024-06-01T12:00:00+08:00 |
| `${date}`               | 当前日期（yyyy-MM-dd，文件名用）| 2024-06-01         |

> 你可以在 `filename`、`format` 等字段中自由组合这些变量。

---

## 四、常见配置示例

### 1. 仅输出到文件，按天分割

```json
"log": {
  "targets": [
    {
      "type": "file",
      "filename": "logs/access_${site}_${date}.log",
      "format": "combined"
    }
  ]
}
```

### 2. 同时输出到文件、syslog、控制台

```json
"log": {
  "targets": [
    {
      "type": "file",
      "filename": "logs/access_${site}_${date}.log",
      "format": "combined"
    },
    {
      "type": "syslog",
      "network": "udp",
      "address": "127.0.0.1:514",
      "format": "json"
    },
    {
      "type": "console",
      "format": "short"
    }
  ]
}
```

### 3. 站点独立日志（覆盖全局）

```json
"sites": [
  {
    "name": "tingtao_site",
    ...
    "log_targets": [
      {
        "type": "file",
        "filename": "logs/tingtao_${date}.log",
        "format": "combined"
      },
      {
        "type": "syslog",
        "network": "udp",
        "address": "*************:514",
        "format": "json"
      }
    ]
  }
]
```

---

## 五、注意事项

- 站点 `log_targets` 存在时，**只会用站点自己的日志目标**，不会再输出到全局目标。
- 文件型日志会自动创建目录，`${date}` 变量可实现按天分割日志。
- syslog 目标仅在类Unix系统有效，Windows下不会报错但不会输出。
- 日志格式模板可随时扩展，变量不足可联系开发补充。

---

如需更多自定义或有特殊日志需求，请联系开发者或在 issues 中反馈。

---

## 六、静态文件服务扩展说明

本项目支持路由级静态文件服务与反向代理共存。你可以在站点的 routes 配置中为某些路径指定 static_dir 字段，实现如下效果：

- 某些URL前缀（如/static/）直接由本地目录提供文件下载。
- 其他URL仍然反向代理到后端服务器。

### 配置示例：

```json
"routes": [
  {
    "pattern": "^/static/",
    "static_dir": "./static",   // 本地静态目录
    "cache": true
  },
  {
    "pattern": "^/",
    "upstream": "primary",
    "cache": true
  }
]
```

- 访问 `/static/xxx` 时，直接返回本地 `./static/xxx` 文件，不走后端。
- 访问其他路径时，仍然走反向代理。
- 日志、ACL、缓存等功能对静态文件同样生效。

如需更复杂的静态/代理混合场景，可灵活配置多条路由规则。 
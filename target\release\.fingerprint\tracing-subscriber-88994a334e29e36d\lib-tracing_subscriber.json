{"rustc": 28845397767708332, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\"]", "target": 2945592894759063892, "profile": 9035703402906154366, "path": 12176239129103550620, "deps": [[499765152579515920, "matchers", false, 5647786003887267842], [3434590410718299011, "once_cell", false, 15563145737112676290], [4119535177386780416, "regex", false, 16301124054837658487], [4895452409045649178, "tracing", false, 9283975119526827175], [5565040350528839985, "nu_ansi_term", false, 11612727921281991236], [8011159453640554525, "sharded_slab", false, 15870824167377338439], [10539385342026833603, "smallvec", false, 16163106341869148138], [11178864870005554864, "tracing_log", false, 3864238683316841382], [12016117908302098842, "thread_local", false, 10378700768488709307], [17427259574003854778, "tracing_core", false, 15451797283302668918]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\tracing-subscriber-88994a334e29e36d\\dep-lib-tracing_subscriber"}}], "rustflags": [], "metadata": 1308049149787906361, "config": 2202906307356721367, "compile_kind": 0}
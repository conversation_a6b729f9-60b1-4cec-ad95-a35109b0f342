use anyhow::{Context, Result};
use async_trait::async_trait;
use bytes::Bytes;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tokio::fs;
use tokio::sync::RwLock;
use tracing::{debug, error, warn};

use super::{CacheBackend, CacheEntry, CacheStats};

/// 文件缓存元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
struct CacheMetadata {
    content_type: String,
    status_code: u16,
    headers: Vec<(String, String)>,
    created_at: u64, // Unix timestamp
    expires_at: u64, // Unix timestamp
    access_count: u64,
    last_accessed: u64, // Unix timestamp
    size: usize,
    etag: Option<String>,
    last_modified: Option<String>,
}

impl From<&CacheEntry> for CacheMetadata {
    fn from(entry: &CacheEntry) -> Self {
        Self {
            content_type: entry.content_type.clone(),
            status_code: entry.status_code,
            headers: entry.headers.clone(),
            created_at: entry.created_at.duration_since(UNIX_EPOCH).unwrap_or_default().as_secs(),
            expires_at: entry.expires_at.duration_since(UNIX_EPOCH).unwrap_or_default().as_secs(),
            access_count: entry.access_count,
            last_accessed: entry.last_accessed.duration_since(UNIX_EPOCH).unwrap_or_default().as_secs(),
            size: entry.size,
            etag: entry.etag.clone(),
            last_modified: entry.last_modified.clone(),
        }
    }
}

impl CacheMetadata {
    fn to_cache_entry(&self, data: Bytes) -> CacheEntry {
        CacheEntry {
            data,
            content_type: self.content_type.clone(),
            status_code: self.status_code,
            headers: self.headers.clone(),
            created_at: UNIX_EPOCH + Duration::from_secs(self.created_at),
            expires_at: UNIX_EPOCH + Duration::from_secs(self.expires_at),
            access_count: self.access_count,
            last_accessed: UNIX_EPOCH + Duration::from_secs(self.last_accessed),
            size: self.size,
            etag: self.etag.clone(),
            last_modified: self.last_modified.clone(),
        }
    }
    
    fn is_expired(&self) -> bool {
        let now = SystemTime::now().duration_since(UNIX_EPOCH).unwrap_or_default().as_secs();
        now > self.expires_at
    }
}

/// 文件缓存实现
pub struct FileCache {
    cache_dir: PathBuf,
    max_size: u64,
    stats: Arc<RwLock<CacheStats>>,
    index: Arc<RwLock<HashMap<String, CacheMetadata>>>,
}

impl FileCache {
    /// 创建新的文件缓存
    pub async fn new(cache_dir: PathBuf, max_size: u64) -> Result<Self> {
        // 确保缓存目录存在
        fs::create_dir_all(&cache_dir).await
            .with_context(|| format!("Failed to create cache directory: {}", cache_dir.display()))?;
        
        let cache = Self {
            cache_dir,
            max_size,
            stats: Arc::new(RwLock::new(CacheStats::default())),
            index: Arc::new(RwLock::new(HashMap::new())),
        };
        
        // 加载现有的缓存索引
        cache.load_index().await?;
        
        Ok(cache)
    }
    
    /// 加载缓存索引
    async fn load_index(&self) -> Result<()> {
        let index_path = self.cache_dir.join("index.json");
        
        if index_path.exists() {
            match fs::read_to_string(&index_path).await {
                Ok(content) => {
                    match serde_json::from_str::<HashMap<String, CacheMetadata>>(&content) {
                        Ok(index) => {
                            let mut current_index = self.index.write().await;
                            *current_index = index;
                            debug!("Loaded cache index with {} entries", current_index.len());
                        }
                        Err(e) => {
                            warn!("Failed to parse cache index, starting fresh: {}", e);
                        }
                    }
                }
                Err(e) => {
                    warn!("Failed to read cache index, starting fresh: {}", e);
                }
            }
        }
        
        Ok(())
    }
    
    /// 保存缓存索引
    async fn save_index(&self) -> Result<()> {
        let index_path = self.cache_dir.join("index.json");
        let index = self.index.read().await;
        let content = serde_json::to_string_pretty(&*index)?;
        
        fs::write(&index_path, content).await
            .with_context(|| format!("Failed to save cache index to: {}", index_path.display()))?;
        
        Ok(())
    }
    
    /// 获取缓存文件路径
    fn get_cache_file_path(&self, key: &str) -> PathBuf {
        // 使用键的哈希作为文件名，避免文件系统限制
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        
        let mut hasher = DefaultHasher::new();
        key.hash(&mut hasher);
        let hash = hasher.finish();
        
        // 创建两级目录结构以避免单个目录文件过多
        let dir1 = format!("{:02x}", (hash >> 56) & 0xff);
        let dir2 = format!("{:02x}", (hash >> 48) & 0xff);
        let filename = format!("{:016x}.cache", hash);
        
        self.cache_dir.join(dir1).join(dir2).join(filename)
    }
    
    /// 获取元数据文件路径
    fn get_metadata_file_path(&self, key: &str) -> PathBuf {
        let cache_path = self.get_cache_file_path(key);
        cache_path.with_extension("meta")
    }
    
    /// 确保目录存在
    async fn ensure_dir_exists(&self, path: &Path) -> Result<()> {
        if let Some(parent) = path.parent() {
            fs::create_dir_all(parent).await
                .with_context(|| format!("Failed to create directory: {}", parent.display()))?;
        }
        Ok(())
    }
    
    /// 计算当前缓存大小
    async fn calculate_current_size(&self) -> u64 {
        let index = self.index.read().await;
        index.values().map(|meta| meta.size as u64).sum()
    }
    
    /// 清理过期和超大小的条目
    async fn cleanup_if_needed(&self, new_entry_size: usize) -> Result<()> {
        let current_size = self.calculate_current_size().await;
        let needed_size = new_entry_size as u64;
        
        if current_size + needed_size <= self.max_size {
            return Ok(());
        }
        
        let mut index = self.index.write().await;
        let mut entries_to_remove = Vec::new();
        
        // 首先移除过期条目
        for (key, metadata) in index.iter() {
            if metadata.is_expired() {
                entries_to_remove.push(key.clone());
            }
        }
        
        // 如果还需要更多空间，按LRU策略移除
        if entries_to_remove.is_empty() || 
           current_size - entries_to_remove.iter()
               .map(|k| index.get(k).map(|m| m.size as u64).unwrap_or(0))
               .sum::<u64>() + needed_size > self.max_size {
            
            let mut entries: Vec<_> = index.iter()
                .filter(|(k, _)| !entries_to_remove.contains(k))
                .map(|(k, v)| (k.clone(), v.last_accessed))
                .collect();
            
            // 按最后访问时间排序（最旧的在前）
            entries.sort_by_key(|(_, last_accessed)| *last_accessed);
            
            let mut freed_size = entries_to_remove.iter()
                .map(|k| index.get(k).map(|m| m.size as u64).unwrap_or(0))
                .sum::<u64>();
            
            for (key, _) in entries {
                if current_size - freed_size + needed_size <= self.max_size {
                    break;
                }
                
                if let Some(metadata) = index.get(&key) {
                    freed_size += metadata.size as u64;
                    entries_to_remove.push(key);
                }
            }
        }
        
        // 删除选中的条目
        for key in &entries_to_remove {
            if let Some(metadata) = index.remove(key) {
                // 删除文件
                let cache_path = self.get_cache_file_path(key);
                let meta_path = self.get_metadata_file_path(key);
                
                let _ = fs::remove_file(cache_path).await;
                let _ = fs::remove_file(meta_path).await;
                
                // 更新统计
                let mut stats = self.stats.write().await;
                stats.record_delete(metadata.size);
            }
        }
        
        if !entries_to_remove.is_empty() {
            debug!("Cleaned up {} cache entries", entries_to_remove.len());
            self.save_index().await?;
        }
        
        Ok(())
    }
}

#[async_trait]
impl CacheBackend for FileCache {
    async fn get(&self, key: &str) -> Result<Option<CacheEntry>> {
        let index = self.index.read().await;
        
        if let Some(metadata) = index.get(key) {
            if metadata.is_expired() {
                drop(index);
                // 异步删除过期条目
                let _ = self.delete(key).await;
                return Ok(None);
            }
            
            let cache_path = self.get_cache_file_path(key);
            
            match fs::read(&cache_path).await {
                Ok(data) => {
                    let mut entry = metadata.to_cache_entry(Bytes::from(data));
                    entry.update_access();
                    
                    // 更新索引中的访问信息
                    drop(index);
                    let mut index = self.index.write().await;
                    if let Some(meta) = index.get_mut(key) {
                        meta.access_count = entry.access_count;
                        meta.last_accessed = entry.last_accessed.duration_since(UNIX_EPOCH)
                            .unwrap_or_default().as_secs();
                    }
                    
                    Ok(Some(entry))
                }
                Err(e) => {
                    error!("Failed to read cache file {}: {}", cache_path.display(), e);
                    Ok(None)
                }
            }
        } else {
            Ok(None)
        }
    }
    
    async fn set(&self, key: &str, entry: CacheEntry) -> Result<()> {
        // 清理空间
        self.cleanup_if_needed(entry.size).await?;
        
        let cache_path = self.get_cache_file_path(key);
        let meta_path = self.get_metadata_file_path(key);
        
        // 确保目录存在
        self.ensure_dir_exists(&cache_path).await?;
        
        // 写入数据文件
        fs::write(&cache_path, &entry.data).await
            .with_context(|| format!("Failed to write cache file: {}", cache_path.display()))?;
        
        // 写入元数据文件
        let metadata = CacheMetadata::from(&entry);
        let meta_content = serde_json::to_string(&metadata)?;
        fs::write(&meta_path, meta_content).await
            .with_context(|| format!("Failed to write metadata file: {}", meta_path.display()))?;
        
        // 更新索引
        let mut index = self.index.write().await;
        index.insert(key.to_string(), metadata);
        
        // 更新统计
        let mut stats = self.stats.write().await;
        stats.record_write(entry.size);
        
        // 保存索引
        drop(index);
        drop(stats);
        self.save_index().await?;
        
        Ok(())
    }
    
    async fn delete(&self, key: &str) -> Result<bool> {
        let mut index = self.index.write().await;
        
        if let Some(metadata) = index.remove(key) {
            let cache_path = self.get_cache_file_path(key);
            let meta_path = self.get_metadata_file_path(key);
            
            // 删除文件
            let _ = fs::remove_file(cache_path).await;
            let _ = fs::remove_file(meta_path).await;
            
            // 更新统计
            let mut stats = self.stats.write().await;
            stats.record_delete(metadata.size);
            
            drop(index);
            drop(stats);
            self.save_index().await?;
            
            Ok(true)
        } else {
            Ok(false)
        }
    }
    
    async fn exists(&self, key: &str) -> Result<bool> {
        let index = self.index.read().await;
        Ok(index.contains_key(key))
    }
    
    async fn clear(&self) -> Result<()> {
        // 删除所有缓存文件
        if self.cache_dir.exists() {
            fs::remove_dir_all(&self.cache_dir).await?;
            fs::create_dir_all(&self.cache_dir).await?;
        }
        
        // 清空索引
        let mut index = self.index.write().await;
        index.clear();
        
        // 重置统计
        let mut stats = self.stats.write().await;
        *stats = CacheStats::default();
        
        Ok(())
    }
    
    async fn stats(&self) -> Result<CacheStats> {
        let mut stats = self.stats.read().await.clone();
        let index = self.index.read().await;
        
        stats.entry_count = index.len() as u64;
        stats.size_bytes = index.values().map(|m| m.size as u64).sum();
        
        Ok(stats)
    }
    
    async fn cleanup_expired(&self) -> Result<u64> {
        let mut index = self.index.write().await;
        let mut expired_keys = Vec::new();
        
        for (key, metadata) in index.iter() {
            if metadata.is_expired() {
                expired_keys.push(key.clone());
            }
        }
        
        let removed_count = expired_keys.len() as u64;
        
        for key in expired_keys {
            if let Some(metadata) = index.remove(&key) {
                let cache_path = self.get_cache_file_path(&key);
                let meta_path = self.get_metadata_file_path(&key);
                
                let _ = fs::remove_file(cache_path).await;
                let _ = fs::remove_file(meta_path).await;
                
                let mut stats = self.stats.write().await;
                stats.record_delete(metadata.size);
            }
        }
        
        if removed_count > 0 {
            let mut stats = self.stats.write().await;
            stats.cleanup_count += 1;
            
            drop(index);
            drop(stats);
            self.save_index().await?;
        }
        
        Ok(removed_count)
    }
    
    async fn keys(&self) -> Result<Vec<String>> {
        let index = self.index.read().await;
        Ok(index.keys().cloned().collect())
    }
    
    async fn size(&self) -> Result<u64> {
        Ok(self.calculate_current_size().await)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    
    #[tokio::test]
    async fn test_file_cache_basic_operations() {
        let temp_dir = TempDir::new().unwrap();
        let cache_dir = temp_dir.path().to_path_buf();
        
        let cache = FileCache::new(cache_dir, 1024 * 1024).await.unwrap();
        
        let entry = CacheEntry::new(
            Bytes::from("test data"),
            "text/plain".to_string(),
            200,
            vec![("X-Test".to_string(), "value".to_string())],
            Duration::from_secs(60),
        );
        
        // 测试设置
        cache.set("test_key", entry.clone()).await.unwrap();
        
        // 测试获取
        let retrieved = cache.get("test_key").await.unwrap().unwrap();
        assert_eq!(retrieved.data, entry.data);
        assert_eq!(retrieved.content_type, entry.content_type);
        assert_eq!(retrieved.headers, entry.headers);
        
        // 测试存在性检查
        assert!(cache.exists("test_key").await.unwrap());
        assert!(!cache.exists("nonexistent_key").await.unwrap());
        
        // 测试删除
        assert!(cache.delete("test_key").await.unwrap());
        assert!(!cache.exists("test_key").await.unwrap());
    }
    
    #[tokio::test]
    async fn test_file_cache_persistence() {
        let temp_dir = TempDir::new().unwrap();
        let cache_dir = temp_dir.path().to_path_buf();
        
        let entry = CacheEntry::new(
            Bytes::from("persistent data"),
            "text/plain".to_string(),
            200,
            vec![],
            Duration::from_secs(3600),
        );
        
        // 创建缓存并添加条目
        {
            let cache = FileCache::new(cache_dir.clone(), 1024 * 1024).await.unwrap();
            cache.set("persistent_key", entry.clone()).await.unwrap();
        }
        
        // 重新创建缓存，应该能加载之前的数据
        {
            let cache = FileCache::new(cache_dir, 1024 * 1024).await.unwrap();
            let retrieved = cache.get("persistent_key").await.unwrap().unwrap();
            assert_eq!(retrieved.data, entry.data);
        }
    }
    
    #[tokio::test]
    async fn test_file_cache_size_limit() {
        let temp_dir = TempDir::new().unwrap();
        let cache_dir = temp_dir.path().to_path_buf();
        
        let cache = FileCache::new(cache_dir, 100).await.unwrap(); // 100字节限制
        
        // 添加一个大条目
        let large_entry = CacheEntry::new(
            Bytes::from(vec![0u8; 80]),
            "application/octet-stream".to_string(),
            200,
            vec![],
            Duration::from_secs(60),
        );
        
        cache.set("large_key", large_entry).await.unwrap();
        
        // 添加另一个条目，应该触发清理
        let small_entry = CacheEntry::new(
            Bytes::from(vec![1u8; 50]),
            "application/octet-stream".to_string(),
            200,
            vec![],
            Duration::from_secs(60),
        );
        
        cache.set("small_key", small_entry).await.unwrap();
        
        // 检查总大小不超过限制
        let size = cache.size().await.unwrap();
        assert!(size <= 100);
    }
}

use async_trait::async_trait;
use bytes::Bytes;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tracing::{debug, error, info, warn};

use crate::cache::{
    manager::CacheManager, CacheConfig, CacheType, CacheEntry, CacheResult,
    MemoryCacheConfig, utils::generate_etag
};
use super::{Processor, ProcessResult, ProcessError, RequestContext};

/// 缓存处理器
pub struct CacheProcessor {
    cache_manager: Option<Arc<CacheManager>>,
    config: CacheProcessorConfig,
}

/// 缓存处理器配置
#[derive(Debug, Clone)]
pub struct CacheProcessorConfig {
    pub enabled: bool,
    pub default_ttl: Duration,
    pub max_size: u64,
    pub cache_control_respect: bool,
    pub vary_headers: Vec<String>,
    pub cacheable_status_codes: Vec<u16>,
    pub cacheable_methods: Vec<String>,
    pub skip_cache_headers: Vec<String>,
}

impl Default for CacheProcessorConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            default_ttl: Duration::from_secs(3600), // 1小时
            max_size: 100 * 1024 * 1024, // 100MB
            cache_control_respect: true,
            vary_headers: vec!["Accept-Encoding".to_string()],
            cacheable_status_codes: vec![200, 203, 300, 301, 302, 404, 410],
            cacheable_methods: vec!["GET".to_string(), "HEAD".to_string()],
            skip_cache_headers: vec![
                "authorization".to_string(),
                "cache-control".to_string(),
                "pragma".to_string(),
            ],
        }
    }
}

impl CacheProcessor {
    /// 创建新的缓存处理器
    pub fn new() -> Self {
        Self::with_config(CacheProcessorConfig::default())
    }

    /// 使用指定配置创建缓存处理器
    pub fn with_config(config: CacheProcessorConfig) -> Self {
        let cache_manager = if config.enabled {
            Self::create_cache_manager(&config).ok()
        } else {
            None
        };

        Self {
            cache_manager,
            config,
        }
    }

    /// 创建缓存管理器
    fn create_cache_manager(config: &CacheProcessorConfig) -> anyhow::Result<Arc<CacheManager>> {
        let cache_config = CacheConfig {
            cache_type: CacheType::Memory,
            file_path: None,
            redis_config: None,
            memory_config: MemoryCacheConfig {
                max_size: config.max_size,
                max_entries: 10000,
                ttl: config.default_ttl,
                cleanup_interval: Duration::from_secs(300),
            },
            default_ttl: config.default_ttl,
            max_size: config.max_size,
            cleanup_interval: Duration::from_secs(300),
            async_cleanup: true,
        };

        // 注意：这里需要在异步上下文中调用
        // 暂时返回错误，实际使用时需要在异步环境中初始化
        Err(anyhow::anyhow!("Cache manager creation requires async context"))
    }

    /// 异步初始化缓存管理器
    pub async fn initialize_cache(&mut self) -> anyhow::Result<()> {
        if !self.config.enabled {
            return Ok(());
        }

        let cache_config = CacheConfig {
            cache_type: CacheType::Memory,
            file_path: None,
            redis_config: None,
            memory_config: MemoryCacheConfig {
                max_size: self.config.max_size,
                max_entries: 10000,
                ttl: self.config.default_ttl,
                cleanup_interval: Duration::from_secs(300),
            },
            default_ttl: self.config.default_ttl,
            max_size: self.config.max_size,
            cleanup_interval: Duration::from_secs(300),
            async_cleanup: true,
        };

        let manager = CacheManager::new(cache_config).await?;
        self.cache_manager = Some(Arc::new(manager));

        info!("Cache manager initialized successfully");
        Ok(())
    }
}

impl Default for CacheProcessor {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl Processor for CacheProcessor {
    fn name(&self) -> &'static str {
        "CacheProcessor"
    }
    
    fn priority(&self) -> u8 {
        20 // 在路由处理器之后，静态文件处理器之前执行
    }
    
    fn should_process(&self, ctx: &RequestContext) -> bool {
        // 检查是否启用缓存
        if !self.config.enabled || self.cache_manager.is_none() {
            return false;
        }

        // 检查HTTP方法是否可缓存
        if !self.config.cacheable_methods.contains(&ctx.method) {
            return false;
        }

        // 检查是否有跳过缓存的头部
        for skip_header in &self.config.skip_cache_headers {
            if ctx.headers.contains_key(skip_header) {
                debug!("Skipping cache due to header: {}", skip_header);
                return false;
            }
        }

        // 检查Cache-Control头部
        if let Some(cache_control) = ctx.get_header("cache-control") {
            if cache_control.contains("no-cache") || cache_control.contains("no-store") {
                debug!("Skipping cache due to Cache-Control: {}", cache_control);
                return false;
            }
        }

        true
    }

    async fn process(&self, ctx: &mut RequestContext) -> ProcessResult {
        debug!("CacheProcessor: Processing request {}", ctx.request_id);

        let cache_manager = match &self.cache_manager {
            Some(manager) => manager,
            None => {
                debug!("Cache manager not available, skipping cache");
                return ProcessResult::Continue;
            }
        };

        // 生成缓存键
        let cache_key = self.generate_cache_key(ctx);
        debug!("Generated cache key: {}", cache_key);

        // 检查缓存
        match cache_manager.get(&cache_key).await {
            CacheResult::Hit(entry) => {
                debug!("Cache hit for request {}", ctx.request_id);

                // 检查条件请求
                if self.handle_conditional_request(ctx, &entry) {
                    return ProcessResult::Stop;
                }

                // 设置缓存相关头部
                self.set_cache_headers(ctx, &entry);

                // 设置响应
                ctx.response.status_code = 200;
                ctx.response.set_body(entry.data);

                // 更新缓存状态
                ctx.cache_status = Some(crate::processor::context::CacheStatus::Hit);

                info!("Cache hit for request {}: served from cache", ctx.request_id);
                ProcessResult::Stop
            }
            CacheResult::Miss => {
                debug!("Cache miss for request {}", ctx.request_id);

                // 设置缓存键以便后续存储
                ctx.cache_key = Some(cache_key);
                ctx.cache_status = Some(crate::processor::context::CacheStatus::Miss);

                ProcessResult::Continue
            }
            CacheResult::Error(e) => {
                warn!("Cache error for request {}: {}", ctx.request_id, e);
                ProcessResult::Continue
            }
        }
    }
}

impl CacheProcessor {
    /// 生成缓存键
    fn generate_cache_key(&self, ctx: &RequestContext) -> String {
        let mut key_parts = vec![
            ctx.method.clone(),
            ctx.path().to_string(),
        ];

        // 添加查询参数
        if let Some(query) = &ctx.query_string {
            key_parts.push(query.clone());
        }

        // 添加Vary头部的值
        for vary_header in &self.config.vary_headers {
            if let Some(value) = ctx.get_header(vary_header) {
                key_parts.push(format!("{}:{}", vary_header, value));
            }
        }

        // 添加站点信息
        if let Some(site) = &ctx.site {
            key_parts.push(format!("site:{}", site.name));
        }

        key_parts.join("|")
    }

    /// 处理条件请求（If-None-Match, If-Modified-Since）
    fn handle_conditional_request(&self, ctx: &mut RequestContext, entry: &CacheEntry) -> bool {
        // 检查If-None-Match头部
        if let Some(if_none_match) = ctx.get_header("if-none-match") {
            if let Some(etag) = &entry.etag {
                if if_none_match == "*" || if_none_match.contains(etag) {
                    ctx.response.status_code = 304; // Not Modified
                    ctx.response.headers.insert("etag".to_string(), etag.clone());
                    return true;
                }
            }
        }

        // 检查If-Modified-Since头部
        if let Some(if_modified_since) = ctx.get_header("if-modified-since") {
            if let Ok(client_time) = httpdate::parse_http_date(&if_modified_since) {
                if entry.created_at <= client_time {
                    ctx.response.status_code = 304; // Not Modified
                    if let Some(etag) = &entry.etag {
                        ctx.response.headers.insert("etag".to_string(), etag.clone());
                    }
                    return true;
                }
            }
        }

        false
    }

    /// 设置缓存相关的响应头部
    fn set_cache_headers(&self, ctx: &mut RequestContext, entry: &CacheEntry) {
        // 设置ETag
        if let Some(etag) = &entry.etag {
            ctx.response.headers.insert("etag".to_string(), etag.clone());
        }

        // 设置Last-Modified
        let last_modified = httpdate::fmt_http_date(entry.created_at);
        ctx.response.headers.insert("last-modified".to_string(), last_modified);

        // 设置Cache-Control
        let max_age = entry.expires_at.duration_since(entry.created_at)
            .unwrap_or_default().as_secs();
        ctx.response.headers.insert(
            "cache-control".to_string(),
            format!("public, max-age={}", max_age)
        );

        // 设置Expires
        let expires = httpdate::fmt_http_date(entry.expires_at);
        ctx.response.headers.insert("expires".to_string(), expires);

        // 设置X-Cache头部表示缓存命中
        ctx.response.headers.insert("x-cache".to_string(), "HIT".to_string());
    }

    /// 存储响应到缓存（在响应处理完成后调用）
    pub async fn store_response(&self, ctx: &RequestContext) -> anyhow::Result<()> {
        let cache_manager = match &self.cache_manager {
            Some(manager) => manager,
            None => return Ok(()),
        };

        let cache_key = match &ctx.cache_key {
            Some(key) => key,
            None => return Ok(()),
        };

        // 检查响应是否可缓存
        if !self.is_response_cacheable(ctx) {
            debug!("Response not cacheable for request {}", ctx.request_id);
            return Ok(());
        }

        // 计算TTL
        let ttl = self.calculate_ttl(ctx);

        // 生成ETag
        let etag = if let Some(body) = &ctx.response.body {
            Some(generate_etag(body))
        } else {
            None
        };

        // 创建缓存条目
        let now = SystemTime::now();
        let entry = CacheEntry {
            data: ctx.response.body.clone().unwrap_or_default(),
            content_type: ctx.response.headers.get("content-type").cloned().unwrap_or_default(),
            status_code: ctx.response.status_code,
            headers: ctx.response.headers.iter().map(|(k, v)| (k.clone(), v.clone())).collect(),
            created_at: now,
            expires_at: now + ttl,
            access_count: 0,
            last_accessed: now,
            size: ctx.response.body.as_ref().map(|b| b.len()).unwrap_or(0),
            etag,
            last_modified: None,
        };

        // 存储到缓存
        match cache_manager.set(cache_key, entry).await {
            Ok(()) => {
                debug!("Stored response in cache for request {}", ctx.request_id);
            }
            Err(e) => {
                warn!("Failed to store response in cache for request {}: {}", ctx.request_id, e);
            }
        }

        Ok(())
    }

    /// 检查响应是否可缓存
    fn is_response_cacheable(&self, ctx: &RequestContext) -> bool {
        // 检查状态码
        if !self.config.cacheable_status_codes.contains(&ctx.response.status_code) {
            return false;
        }

        // 检查Cache-Control响应头
        if let Some(cache_control) = ctx.response.headers.get("cache-control") {
            if cache_control.contains("no-cache") ||
               cache_control.contains("no-store") ||
               cache_control.contains("private") {
                return false;
            }
        }

        // 检查响应大小
        if let Some(body) = &ctx.response.body {
            if body.len() > self.config.max_size as usize {
                return false;
            }
        }

        true
    }

    /// 计算缓存TTL
    fn calculate_ttl(&self, ctx: &RequestContext) -> Duration {
        // 如果配置了尊重Cache-Control头部
        if self.config.cache_control_respect {
            if let Some(cache_control) = ctx.response.headers.get("cache-control") {
                // 解析max-age
                for directive in cache_control.split(',') {
                    let directive = directive.trim();
                    if directive.starts_with("max-age=") {
                        if let Ok(max_age) = directive[8..].parse::<u64>() {
                            return Duration::from_secs(max_age);
                        }
                    }
                }
            }

            // 检查Expires头部
            if let Some(expires) = ctx.response.headers.get("expires") {
                if let Ok(expires_time) = httpdate::parse_http_date(expires) {
                    let now = SystemTime::now();
                    if expires_time > now {
                        return expires_time.duration_since(now).unwrap_or(self.config.default_ttl);
                    }
                }
            }
        }

        self.config.default_ttl
    }
}

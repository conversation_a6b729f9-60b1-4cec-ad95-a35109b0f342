package monitor

import (
	"encoding/json"
	"fmt"
	"net"
	"net/http"
	"os"
	"strings"

	"reverse-proxy/internal/config"
	"reverse-proxy/internal/logger"
)

// ConfigUpdateRequest 配置更新请求
type ConfigUpdateRequest struct {
	Path  string      `json:"path"`  // 配置路径，如 "sites.0.domains" 或 "global.server.port"
	Value interface{} `json:"value"` // 新的配置值
}

// ConfigBatchUpdateRequest 批量配置更新请求
type ConfigBatchUpdateRequest struct {
	Updates []ConfigUpdateRequest `json:"updates"`
}

// ConfigResponse 配置操作响应
type ConfigResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// checkAPIKeyAuth 检查API Key认证
func (api *APIServer) checkAPIKeyAuth(r *http.Request) bool {
	// 从Header获取API Key
	apiKey := r.Header.Get("X-API-Key")
	if apiKey == "" {
		// 从Query参数获取
		apiKey = r.URL.Query().Get("api_key")
	}

	configAPIKey := api.monitor.config.Monitor.APIKey
	logger.Debug(fmt.Sprintf("API Key check: received='%s', config='%s'", apiKey, configAPIKey))

	if apiKey == "" || configAPIKey == "" {
		logger.Debug("API Key check failed: empty key")
		return false
	}

	result := apiKey == configAPIKey
	logger.Debug(fmt.Sprintf("API Key check result: %v", result))
	return result
}

// checkACL 检查IP访问控制
func (api *APIServer) checkACL(r *http.Request) bool {
	clientIP := getClientIP(r)
	acl := api.monitor.config.Monitor.ACL

	// 检查拒绝列表
	for _, deniedIP := range acl.DeniedIPs {
		if matchIP(clientIP, deniedIP) {
			return false
		}
	}

	// 如果允许列表为空，则允许所有（除了拒绝列表中的）
	if len(acl.AllowedIPs) == 0 {
		return true
	}

	// 检查允许列表
	for _, allowedIP := range acl.AllowedIPs {
		if matchIP(clientIP, allowedIP) {
			return true
		}
	}

	return false
}

// getClientIP 获取客户端真实IP
func getClientIP(r *http.Request) string {
	// 检查X-Forwarded-For头
	xff := r.Header.Get("X-Forwarded-For")
	if xff != "" {
		ips := strings.Split(xff, ",")
		return strings.TrimSpace(ips[0])
	}

	// 检查X-Real-IP头
	xri := r.Header.Get("X-Real-IP")
	if xri != "" {
		return xri
	}

	// 使用RemoteAddr
	ip, _, err := net.SplitHostPort(r.RemoteAddr)
	if err != nil {
		return r.RemoteAddr
	}
	return ip
}

// matchIP 检查IP是否匹配（支持CIDR）
func matchIP(clientIP, pattern string) bool {
	// 如果包含/，则作为CIDR处理
	if strings.Contains(pattern, "/") {
		_, cidr, err := net.ParseCIDR(pattern)
		if err != nil {
			return false
		}
		ip := net.ParseIP(clientIP)
		if ip == nil {
			return false
		}
		return cidr.Contains(ip)
	}

	// 直接IP比较
	return clientIP == pattern
}

// configAuthMiddleware 配置API认证中间件
func (api *APIServer) configAuthMiddleware(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 首先检查基础认证
		if !api.authenticate(r.Header.Get("Authorization")) {
			w.Header().Set("WWW-Authenticate", `Basic realm="Monitor API"`)
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}

		// 检查API Key
		if !api.checkAPIKeyAuth(r) {
			api.sendConfigResponse(w, false, "Invalid or missing API key", nil, http.StatusForbidden)
			return
		}

		// 检查ACL
		if !api.checkACL(r) {
			api.sendConfigResponse(w, false, "Access denied by ACL", nil, http.StatusForbidden)
			return
		}

		next(w, r)
	}
}

// sendConfigResponse 发送配置API响应
func (api *APIServer) sendConfigResponse(w http.ResponseWriter, success bool, message string, data interface{}, statusCode int) {
	response := ConfigResponse{
		Success: success,
		Message: message,
		Data:    data,
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(response)
}

// handleGetConfig 获取配置
func (api *APIServer) handleGetConfig(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		api.sendConfigResponse(w, false, "Method not allowed", nil, http.StatusMethodNotAllowed)
		return
	}

	// 获取路径参数
	path := r.URL.Query().Get("path")

	if path == "" {
		// 返回完整配置
		api.sendConfigResponse(w, true, "Configuration retrieved successfully", api.monitor.config, http.StatusOK)
		return
	}

	// 根据路径获取特定配置
	value, err := getConfigByPath(api.monitor.config, path)
	if err != nil {
		api.sendConfigResponse(w, false, fmt.Sprintf("Failed to get config: %v", err), nil, http.StatusBadRequest)
		return
	}

	api.sendConfigResponse(w, true, "Configuration retrieved successfully", value, http.StatusOK)
}

// handleUpdateConfig 更新配置
func (api *APIServer) handleUpdateConfig(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost && r.Method != http.MethodPut {
		api.sendConfigResponse(w, false, "Method not allowed", nil, http.StatusMethodNotAllowed)
		return
	}

	var request ConfigUpdateRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		api.sendConfigResponse(w, false, "Invalid JSON format", nil, http.StatusBadRequest)
		return
	}

	// 验证和更新配置
	if err := api.updateConfigValue(request.Path, request.Value); err != nil {
		api.sendConfigResponse(w, false, fmt.Sprintf("Failed to update config: %v", err), nil, http.StatusBadRequest)
		return
	}

	api.sendConfigResponse(w, true, "Configuration updated successfully", nil, http.StatusOK)
}

// handleBatchUpdateConfig 批量更新配置
func (api *APIServer) handleBatchUpdateConfig(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		api.sendConfigResponse(w, false, "Method not allowed", nil, http.StatusMethodNotAllowed)
		return
	}

	var request ConfigBatchUpdateRequest
	if err := json.NewDecoder(r.Body).Decode(&request); err != nil {
		api.sendConfigResponse(w, false, "Invalid JSON format", nil, http.StatusBadRequest)
		return
	}

	// 批量更新配置
	var errors []string
	for _, update := range request.Updates {
		if err := api.updateConfigValue(update.Path, update.Value); err != nil {
			errors = append(errors, fmt.Sprintf("Path %s: %v", update.Path, err))
		}
	}

	if len(errors) > 0 {
		api.sendConfigResponse(w, false, "Some updates failed: "+strings.Join(errors, "; "), nil, http.StatusBadRequest)
		return
	}

	api.sendConfigResponse(w, true, "All configurations updated successfully", nil, http.StatusOK)
}

// handleReloadConfig 重载配置
func (api *APIServer) handleReloadConfig(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		api.sendConfigResponse(w, false, "Method not allowed", nil, http.StatusMethodNotAllowed)
		return
	}

	// 触发热重载
	if api.monitor.config.HotReload.Enabled {
		// 这里需要调用热重载功能
		// 假设有一个重载方法
		if err := api.triggerConfigReload(); err != nil {
			api.sendConfigResponse(w, false, fmt.Sprintf("Failed to reload config: %v", err), nil, http.StatusInternalServerError)
			return
		}

		api.sendConfigResponse(w, true, "Configuration reloaded successfully", nil, http.StatusOK)
	} else {
		api.sendConfigResponse(w, false, "Hot reload is disabled", nil, http.StatusBadRequest)
	}
}

// updateConfigValue 更新配置值并保存到文件
func (api *APIServer) updateConfigValue(path string, value interface{}) error {
	// 更新内存中的配置
	if err := setConfigByPath(api.monitor.config, path, value); err != nil {
		return err
	}

	// 保存到配置文件
	return api.saveConfigToFile()
}

// saveConfigToFile 保存配置到文件
func (api *APIServer) saveConfigToFile() error {
	// 获取配置文件路径
	configPath := api.getConfigFilePath()

	// 序列化配置
	data, err := json.MarshalIndent(api.monitor.config, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}

	// 创建备份
	backupPath := configPath + ".backup"
	if err := copyFile(configPath, backupPath); err != nil {
		logger.Warn("Failed to create config backup:", err)
	}

	// 写入新配置
	if err := os.WriteFile(configPath, data, 0644); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}

	logger.Info("Configuration saved to file:", configPath)
	return nil
}

// getConfigFilePath 获取配置文件路径
func (api *APIServer) getConfigFilePath() string {
	// 这里需要从启动参数或环境变量获取配置文件路径
	// 暂时使用默认路径
	return "config.json"
}

// copyFile 复制文件
func copyFile(src, dst string) error {
	data, err := os.ReadFile(src)
	if err != nil {
		return err
	}
	return os.WriteFile(dst, data, 0644)
}

// triggerConfigReload 触发配置重载
func (api *APIServer) triggerConfigReload() error {
	if api.monitor.configWatcher != nil {
		// 使用类型断言调用ForceReload方法
		if watcher, ok := api.monitor.configWatcher.(interface {
			ForceReload() error
		}); ok {
			logger.Info("Triggering configuration reload...")
			return watcher.ForceReload()
		}
	}

	logger.Warn("Config watcher not available, reload skipped")
	return fmt.Errorf("config watcher not available")
}

// getConfigByPath 根据路径获取配置值
func getConfigByPath(cfg *config.Config, path string) (interface{}, error) {
	parts := strings.Split(path, ".")
	var current interface{} = cfg

	for _, part := range parts {
		switch v := current.(type) {
		case *config.Config:
			current = getConfigField(v, part)
		case map[string]interface{}:
			current = v[part]
		case []interface{}:
			// 处理数组索引
			if idx, err := parseIndex(part); err == nil && idx < len(v) {
				current = v[idx]
			} else {
				return nil, fmt.Errorf("invalid array index: %s", part)
			}
		default:
			return nil, fmt.Errorf("cannot navigate to path: %s", path)
		}

		if current == nil {
			return nil, fmt.Errorf("path not found: %s", path)
		}
	}

	return current, nil
}

// setConfigByPath 根据路径设置配置值
func setConfigByPath(cfg *config.Config, path string, value interface{}) error {
	parts := strings.Split(path, ".")
	if len(parts) == 0 {
		return fmt.Errorf("empty path")
	}

	// 导航到父级对象
	var current interface{} = cfg
	for i := 0; i < len(parts)-1; i++ {
		part := parts[i]
		switch v := current.(type) {
		case *config.Config:
			current = getConfigField(v, part)
		case map[string]interface{}:
			current = v[part]
		case []interface{}:
			if idx, err := parseIndex(part); err == nil && idx < len(v) {
				current = v[idx]
			} else {
				return fmt.Errorf("invalid array index: %s", part)
			}
		default:
			return fmt.Errorf("cannot navigate to path: %s", strings.Join(parts[:i+1], "."))
		}

		if current == nil {
			return fmt.Errorf("path not found: %s", strings.Join(parts[:i+1], "."))
		}
	}

	// 设置最终值
	lastPart := parts[len(parts)-1]
	return setConfigField(current, lastPart, value)
}

// getConfigField 获取配置字段
func getConfigField(cfg *config.Config, field string) interface{} {
	switch field {
	case "server":
		return &cfg.Server
	case "log":
		return &cfg.Log
	case "cache":
		return &cfg.Cache
	case "acl":
		return &cfg.ACL
	case "sites":
		return cfg.Sites
	case "monitor":
		return &cfg.Monitor
	case "rate_limit":
		return &cfg.RateLimit
	case "circuit_breaker":
		return &cfg.CircuitBreaker
	case "compression":
		return &cfg.Compression
	case "hot_reload":
		return &cfg.HotReload
	case "memory_cache":
		return &cfg.MemoryCache
	default:
		return nil
	}
}

// setConfigField 设置配置字段
func setConfigField(obj interface{}, field string, value interface{}) error {
	switch v := obj.(type) {
	case *config.Config:
		return setConfigRootField(v, field, value)
	case *config.ServerConfig:
		return setServerConfigField(v, field, value)
	case *config.MonitorConfig:
		return setMonitorConfigField(v, field, value)
	case *config.CompressionConfig:
		return setCompressionConfigField(v, field, value)
	case *config.MemoryCacheConfig:
		return setMemoryCacheConfigField(v, field, value)
	case map[string]interface{}:
		v[field] = value
		return nil
	case []interface{}:
		if idx, err := parseIndex(field); err == nil && idx < len(v) {
			v[idx] = value
			return nil
		}
		return fmt.Errorf("invalid array index: %s", field)
	default:
		return fmt.Errorf("unsupported object type for field: %s", field)
	}
}

// setConfigRootField 设置根配置字段
func setConfigRootField(cfg *config.Config, field string, value interface{}) error {
	switch field {
	case "server":
		if v, ok := value.(map[string]interface{}); ok {
			return updateServerConfig(&cfg.Server, v)
		}
	case "monitor":
		if v, ok := value.(map[string]interface{}); ok {
			return updateMonitorConfig(&cfg.Monitor, v)
		}
	case "compression":
		if v, ok := value.(map[string]interface{}); ok {
			return updateCompressionConfig(&cfg.Compression, v)
		}
	case "memory_cache":
		if v, ok := value.(map[string]interface{}); ok {
			return updateMemoryCacheConfig(&cfg.MemoryCache, v)
		}
	}
	return fmt.Errorf("unsupported root field: %s", field)
}

// setServerConfigField 设置服务器配置字段
func setServerConfigField(cfg *config.ServerConfig, field string, value interface{}) error {
	switch field {
	case "http_port":
		if v, ok := value.(float64); ok {
			cfg.HTTPPort = int(v)
			return nil
		}
	case "https_port":
		if v, ok := value.(float64); ok {
			cfg.HTTPSPort = int(v)
			return nil
		}
	case "max_connections":
		if v, ok := value.(float64); ok {
			cfg.MaxConnections = int(v)
			return nil
		}
	}
	return fmt.Errorf("unsupported server config field: %s", field)
}

// setMonitorConfigField 设置监控配置字段
func setMonitorConfigField(cfg *config.MonitorConfig, field string, value interface{}) error {
	switch field {
	case "enabled":
		if v, ok := value.(bool); ok {
			cfg.Enabled = v
			return nil
		}
	case "port":
		if v, ok := value.(float64); ok {
			cfg.Port = int(v)
			return nil
		}
	case "username":
		if v, ok := value.(string); ok {
			cfg.Username = v
			return nil
		}
	case "password":
		if v, ok := value.(string); ok {
			cfg.Password = v
			return nil
		}
	case "api_key":
		if v, ok := value.(string); ok {
			cfg.APIKey = v
			return nil
		}
	}
	return fmt.Errorf("unsupported monitor config field: %s", field)
}

// setCompressionConfigField 设置压缩配置字段
func setCompressionConfigField(cfg *config.CompressionConfig, field string, value interface{}) error {
	switch field {
	case "enabled":
		if v, ok := value.(bool); ok {
			cfg.Enabled = v
			return nil
		}
	case "level":
		if v, ok := value.(float64); ok {
			cfg.Level = int(v)
			return nil
		}
	case "brotli_quality":
		if v, ok := value.(float64); ok {
			cfg.BrotliQuality = int(v)
			return nil
		}
	}
	return fmt.Errorf("unsupported compression config field: %s", field)
}

// setMemoryCacheConfigField 设置内存缓存配置字段
func setMemoryCacheConfigField(cfg *config.MemoryCacheConfig, field string, value interface{}) error {
	switch field {
	case "enabled":
		if v, ok := value.(bool); ok {
			cfg.Enabled = v
			return nil
		}
	case "global_memory_limit":
		if v, ok := value.(string); ok {
			cfg.GlobalMemoryLimit = v
			return nil
		}
	case "min_access_count":
		if v, ok := value.(float64); ok {
			cfg.MinAccessCount = int64(v)
			return nil
		}
	case "score_threshold":
		if v, ok := value.(float64); ok {
			cfg.ScoreThreshold = v
			return nil
		}
	}
	return fmt.Errorf("unsupported memory cache config field: %s", field)
}

// 辅助函数
func parseIndex(s string) (int, error) {
	// 简单的数字解析
	if len(s) == 0 {
		return 0, fmt.Errorf("empty index")
	}

	idx := 0
	for _, r := range s {
		if r < '0' || r > '9' {
			return 0, fmt.Errorf("invalid index: %s", s)
		}
		idx = idx*10 + int(r-'0')
	}
	return idx, nil
}

// 更新配置的辅助函数
func updateServerConfig(cfg *config.ServerConfig, data map[string]interface{}) error {
	for key, value := range data {
		if err := setServerConfigField(cfg, key, value); err != nil {
			return err
		}
	}
	return nil
}

func updateMonitorConfig(cfg *config.MonitorConfig, data map[string]interface{}) error {
	for key, value := range data {
		if err := setMonitorConfigField(cfg, key, value); err != nil {
			return err
		}
	}
	return nil
}

func updateCompressionConfig(cfg *config.CompressionConfig, data map[string]interface{}) error {
	for key, value := range data {
		if err := setCompressionConfigField(cfg, key, value); err != nil {
			return err
		}
	}
	return nil
}

func updateMemoryCacheConfig(cfg *config.MemoryCacheConfig, data map[string]interface{}) error {
	for key, value := range data {
		if err := setMemoryCacheConfigField(cfg, key, value); err != nil {
			return err
		}
	}
	return nil
}

# 🐛 ACL热重载Bug修复

## 📋 Bug描述

**问题现象：**
用户设置站点ACL文件`"allow_file": "badip.txt"`后，删除该设置并触发配置热重载，但ACL仍然显示加载了该文件，导致IP被错误拒绝。

**调试日志：**
```
站点ACL[配置允许:[], 配置拒绝:[], 文件允许:badip.txt, 文件拒绝:, 实际允许:1个, 实际拒绝:0个]
```

**问题分析：**
- 配置文件中ACL文件路径已为空
- 但实际运行中仍显示`文件允许:badip.txt`
- 配置热重载没有正确清理旧的ACL文件监控

## 🔍 Bug根源

### **问题代码（修复前）**
```go
// reloadSites 重新加载站点配置
func (p *Proxy) reloadSites(newConfig *config.Config) error {
    // ... 创建新站点 ...
    
    // 清理旧站点资源
    for domain, oldSite := range oldSites {
        if _, exists := newSites[domain]; !exists {
            // 只有当站点被移除时才清理ACL ❌
            if oldSite.acl != nil {
                oldSite.acl.Close()
            }
        }
    }
}
```

### **问题分析**
1. **只清理被移除的站点**：代码只在站点被完全移除时才关闭ACL
2. **更新的站点不清理**：当站点配置更新时，旧的ACL没有被关闭
3. **资源泄漏**：旧的文件监控器继续运行，导致配置不一致
4. **内存泄漏**：旧的ACL实例没有被释放

## 🔧 Bug修复

### **修复后代码**
```go
// 清理所有旧站点资源（包括更新的站点）
cleanedSites := make(map[string]bool)
for domain, oldSite := range oldSites {
    // 避免重复清理同一个站点实例
    siteKey := fmt.Sprintf("%p", oldSite)
    if !cleanedSites[siteKey] {
        if oldSite.acl != nil {
            p.logger.Debugf("关闭旧站点ACL: %s (站点实例: %s)", domain, oldSite.config.Name)
            if err := oldSite.acl.Close(); err != nil {
                p.logger.Warnf("关闭旧站点ACL失败: %s, 错误: %v", oldSite.config.Name, err)
            }
        }
        cleanedSites[siteKey] = true
    }
    
    if _, exists := newSites[domain]; !exists {
        p.logger.Infof("已清理移除的站点域名: %s", domain)
    } else {
        p.logger.Debugf("已清理更新的站点域名: %s", domain)
    }
}
```

### **修复要点**
1. **清理所有旧站点**：不管站点是被移除还是更新，都清理旧的ACL
2. **避免重复清理**：使用站点实例指针作为key，避免重复清理同一个站点
3. **错误处理**：添加ACL关闭失败的错误处理
4. **详细日志**：区分移除和更新的站点，便于调试

## 🎯 修复效果

### **修复前**
```
# 设置ACL文件
"allow_file": "badip.txt"

# 删除ACL文件设置后热重载
"allow_file": ""

# 但实际仍然加载旧文件 ❌
站点ACL[文件允许:badip.txt, 实际允许:1个]
```

### **修复后**
```
# 设置ACL文件
"allow_file": "badip.txt"

# 删除ACL文件设置后热重载
"allow_file": ""

# 正确清理，不再加载文件 ✅
站点ACL[文件允许:, 实际允许:0个]
```

## 📊 测试验证

### **测试步骤**
1. **设置ACL文件**：
   ```json
   {
     "sites": [{
       "name": "qiank",
       "acl": {
         "allow_file": "badip.txt"
       }
     }]
   }
   ```

2. **验证文件生效**：
   - 访问站点，观察ACL拒绝日志
   - 确认显示`文件允许:badip.txt`

3. **删除ACL设置**：
   ```json
   {
     "sites": [{
       "name": "qiank", 
       "acl": {
         "allow_file": ""
       }
     }]
   }
   ```

4. **验证热重载**：
   - 等待配置自动重载
   - 再次访问站点，观察ACL日志
   - 确认显示`文件允许:`（空）

### **预期结果**
- ✅ 旧的ACL文件监控被正确关闭
- ✅ 新的ACL配置正确生效
- ✅ IP不再被错误拒绝
- ✅ 调试日志显示正确的配置状态

## 🚨 影响范围

### **受影响功能**
- **站点ACL热重载**：所有站点的ACL配置更新
- **文件监控**：ACL文件的实时监控功能
- **内存管理**：防止ACL实例的内存泄漏

### **不受影响功能**
- **全局ACL**：全局ACL的热重载不受影响
- **新站点创建**：新站点的ACL创建不受影响
- **其他配置**：非ACL相关的配置热重载不受影响

## 🔄 部署建议

### **1. 立即部署**
这是一个严重的bug，建议立即部署修复版本：
```bash
# 停止当前服务
# 替换为新的 reverse-proxy.exe
# 重新启动服务
```

### **2. 验证修复**
部署后进行以下验证：
- 修改站点ACL配置并观察热重载效果
- 检查日志确认旧ACL被正确关闭
- 验证IP访问控制的正确性

### **3. 监控观察**
- 观察内存使用情况，确认没有内存泄漏
- 检查文件监控器的数量，确认没有累积
- 验证ACL配置的实时生效

## 📋 总结

这个bug的根本原因是**热重载时资源清理不完整**，导致：
- 旧的ACL文件监控器没有被关闭
- 配置更新后仍然使用旧的ACL规则
- 可能的内存泄漏和资源浪费

修复后，所有站点的ACL热重载都会正确工作，确保配置的实时生效和资源的正确清理。

**重要性：** 这个bug影响ACL功能的核心可靠性，建议优先修复和部署。

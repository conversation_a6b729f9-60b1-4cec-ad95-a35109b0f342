# 增强的URL重写功能

## 概述

本项目已经实现了灵活强大的URL重写功能，支持简单重写和高级重写两种模式，可以满足各种复杂的URL转换需求。

## 功能特性

### ✅ 简单重写 (rewrite)
- 支持基本的路径替换
- 支持正则捕获组变量 (`$1`, `$2`, `$3`...)
- 支持内置变量 (`$path`, `$query`, `$method`等)
- 向后兼容原有配置

### ✅ 高级重写 (rewrite_advanced)
- 条件重写：基于请求头、查询参数、路径、方法等条件
- 变量替换：自定义变量和内置变量
- 路径拼接：灵活的路径组合
- 多种操作符：equals, contains, matches, exists等

## 配置语法

### 简单重写示例
```json
{
  "pattern": "^/api/v([0-9]+)/(.*)$",
  "upstream": "api_server",
  "rewrite": "/api/version-$1/$2"
}
```

### 高级重写示例
```json
{
  "pattern": "^/api/(.*)$",
  "upstream": "api_server",
  "rewrite_advanced": {
    "target": "/v2/api/$1",
    "conditions": [
      {
        "type": "header",
        "key": "X-API-Version",
        "value": "v1",
        "operator": "equals",
        "target": "/v1/api/$1"
      }
    ],
    "variables": {
      "env": "production"
    },
    "append_path": true
  }
}
```

## 支持的变量

### 正则捕获组
- `$1`, `$2`, `$3`... - 正则表达式捕获组

### 路径变量
- `$path` - 完整请求路径
- `$remaining_path` - 匹配后的剩余路径
- `$query` - 查询字符串
- `$fragment` - URL片段

### 请求信息变量
- `$method` - HTTP方法
- `$host` - 请求主机名
- `$scheme` - 请求协议 (http/https)

### 时间变量
- `$year` - 当前年份
- `$month` - 当前月份
- `$day` - 当前日期
- `$timestamp` - Unix时间戳

## 支持的条件类型

### 条件类型
- `header` - HTTP请求头
- `query` - 查询参数
- `path` - 请求路径
- `method` - HTTP方法
- `host` - 主机名

### 操作符
- `equals`, `eq`, `=` - 完全匹配
- `not_equals`, `ne`, `!=` - 不等于
- `contains` - 包含
- `not_contains` - 不包含
- `starts_with` - 以...开始
- `ends_with` - 以...结束
- `matches`, `regex` - 正则匹配
- `exists` - 存在
- `not_exists` - 不存在
- `greater_than`, `gt` - 大于
- `less_than`, `lt` - 小于

## 实际应用场景

### 1. API版本控制
```json
{
  "pattern": "^/api/(.*)$",
  "rewrite_advanced": {
    "target": "/v2/api/$1",
    "conditions": [
      {
        "type": "header",
        "key": "X-API-Version",
        "value": "v1",
        "operator": "equals",
        "target": "/v1/api/$1"
      }
    ]
  }
}
```

### 2. 移动端适配
```json
{
  "pattern": "^/app/(.*)$",
  "rewrite_advanced": {
    "target": "/web/app/$1",
    "conditions": [
      {
        "type": "header",
        "key": "User-Agent",
        "value": "Mobile|Android|iPhone",
        "operator": "matches",
        "target": "/mobile/app/$1"
      }
    ]
  }
}
```

### 3. 文件存储路径
```json
{
  "pattern": "^/files/(.*)$",
  "rewrite_advanced": {
    "target": "/storage/${env}/files",
    "append_path": true,
    "variables": {
      "env": "production"
    }
  }
}
```

### 4. 基于方法的路由
```json
{
  "pattern": "^/data/(.*)$",
  "rewrite_advanced": {
    "target": "/api/data/$1",
    "conditions": [
      {
        "type": "method",
        "value": "POST",
        "operator": "equals",
        "target": "/api/data/create/$1"
      },
      {
        "type": "method",
        "value": "PUT",
        "operator": "equals",
        "target": "/api/data/update/$1"
      }
    ]
  }
}
```

## 优先级规则

1. 如果配置了 `rewrite_advanced`，会忽略 `rewrite` 字段
2. 条件重写按配置顺序检查，使用第一个匹配的条件
3. 如果没有条件匹配，使用默认的 `target`

## 配置验证

系统会在启动时验证重写配置：
- 检查必需字段是否存在
- 验证条件类型和操作符是否有效
- 确保配置语法正确

## 性能考虑

- 正则表达式在路由编译时预编译，运行时性能优秀
- 变量替换使用高效的字符串操作
- 条件检查按顺序进行，建议将最常匹配的条件放在前面

## 向后兼容性

- 完全兼容原有的简单 `rewrite` 配置
- 现有配置无需修改即可继续使用
- 新功能通过 `rewrite_advanced` 字段提供

## 示例配置文件

完整的配置示例请参考：`examples/advanced_rewrite_config.json`

## 相关文档

- [路由配置说明](docs/routes-configuration.md)
- [配置文件格式](README.md)

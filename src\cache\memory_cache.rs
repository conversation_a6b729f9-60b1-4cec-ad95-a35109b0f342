use anyhow::Result;
use async_trait::async_trait;
use dashmap::DashMap;
use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::Arc;
use std::time::{Duration, SystemTime};
use tokio::sync::RwLock;

use super::{CacheBackend, CacheEntry, CacheStats, MemoryCacheConfig};

/// 内存缓存实现
pub struct MemoryCache {
    cache: Arc<DashMap<String, CacheEntry>>,
    config: MemoryCacheConfig,
    stats: Arc<RwLock<CacheStats>>,
    size_counter: Arc<AtomicU64>,
}

impl MemoryCache {
    /// 创建新的内存缓存
    pub async fn new(config: MemoryCacheConfig) -> Result<Self> {
        Ok(Self {
            cache: Arc::new(DashMap::new()),
            config,
            stats: Arc::new(RwLock::new(CacheStats::default())),
            size_counter: Arc::new(AtomicU64::new(0)),
        })
    }
    
    /// 获取缓存配置
    pub fn config(&self) -> &MemoryCacheConfig {
        &self.config
    }
    
    /// 获取当前缓存条目数
    pub async fn entry_count(&self) -> u64 {
        self.cache.len() as u64
    }

    /// 获取当前缓存大小（估算）
    pub async fn weighted_size(&self) -> u64 {
        self.size_counter.load(Ordering::Relaxed)
    }

    /// 手动运行维护任务（清理过期条目）
    pub async fn run_pending_tasks(&self) {
        self.cleanup_expired_entries().await;
    }

    /// 使缓存条目无效
    pub async fn invalidate(&self, key: &str) {
        if let Some((_, entry)) = self.cache.remove(key) {
            self.size_counter.fetch_sub(entry.size as u64, Ordering::Relaxed);
        }
    }

    /// 使所有缓存条目无效
    pub async fn invalidate_all(&self) {
        self.cache.clear();
        self.size_counter.store(0, Ordering::Relaxed);
    }

    /// 清理过期条目
    async fn cleanup_expired_entries(&self) {
        let mut expired_keys = Vec::new();

        // 收集过期的键
        for entry in self.cache.iter() {
            if entry.value().is_expired() {
                expired_keys.push(entry.key().clone());
            }
        }

        // 删除过期条目
        for key in expired_keys {
            self.invalidate(&key).await;
        }
    }
    
    /// 获取缓存命中率
    pub async fn hit_rate(&self) -> f64 {
        let stats = self.stats.read().await;
        stats.hit_rate()
    }
    
    /// 获取详细的缓存统计信息
    pub async fn detailed_stats(&self) -> DetailedMemoryCacheStats {
        let stats = self.stats.read().await;
        let entry_count = self.cache.entry_count();
        let weighted_size = self.cache.weighted_size();
        let actual_size = self.size_counter.load(Ordering::Relaxed);
        
        DetailedMemoryCacheStats {
            basic_stats: stats.clone(),
            entry_count,
            weighted_size,
            actual_size,
            max_capacity: self.config.max_entries,
            max_size: self.config.max_size,
            ttl: self.config.ttl,
        }
    }
}

#[async_trait]
impl CacheBackend for MemoryCache {
    async fn get(&self, key: &str) -> Result<Option<CacheEntry>> {
        match self.cache.get(key).await {
            Some(mut entry) => {
                if entry.is_expired() {
                    // 删除过期条目
                    self.invalidate(key).await;
                    Ok(None)
                } else {
                    entry.update_access();
                    Ok(Some(entry))
                }
            }
            None => Ok(None),
        }
    }
    
    async fn set(&self, key: &str, entry: CacheEntry) -> Result<()> {
        let size = entry.size as u64;
        
        // 检查是否超过单个条目大小限制
        if size > self.config.max_size {
            return Err(anyhow::anyhow!(
                "Entry size {} exceeds max size {}",
                size,
                self.config.max_size
            ));
        }
        
        // 如果键已存在，先减去旧的大小
        if let Some(old_entry) = self.cache.get(key).await {
            self.size_counter.fetch_sub(old_entry.size as u64, Ordering::Relaxed);
        }
        
        // 插入新条目
        self.cache.insert(key.to_string(), entry).await;
        self.size_counter.fetch_add(size, Ordering::Relaxed);
        
        // 更新统计信息
        let mut stats = self.stats.write().await;
        stats.record_write(size as usize);
        
        Ok(())
    }
    
    async fn delete(&self, key: &str) -> Result<bool> {
        let existed = self.cache.get(key).await.is_some();
        if existed {
            self.invalidate(key).await;
            
            // 更新统计信息
            let mut stats = self.stats.write().await;
            stats.record_delete(0); // 大小在invalidate中已经处理
        }
        Ok(existed)
    }
    
    async fn exists(&self, key: &str) -> Result<bool> {
        Ok(self.cache.get(key).await.is_some())
    }
    
    async fn clear(&self) -> Result<()> {
        self.invalidate_all().await;
        
        // 重置统计信息
        let mut stats = self.stats.write().await;
        *stats = CacheStats::default();
        
        Ok(())
    }
    
    async fn stats(&self) -> Result<CacheStats> {
        let mut stats = self.stats.read().await.clone();
        stats.entry_count = self.cache.entry_count();
        stats.size_bytes = self.size_counter.load(Ordering::Relaxed);
        Ok(stats)
    }
    
    async fn cleanup_expired(&self) -> Result<u64> {
        // Moka会自动清理过期条目，这里手动触发维护任务
        let before_count = self.cache.entry_count();
        self.cache.run_pending_tasks().await;
        let after_count = self.cache.entry_count();
        
        let removed = before_count.saturating_sub(after_count);
        
        if removed > 0 {
            let mut stats = self.stats.write().await;
            stats.cleanup_count += 1;
        }
        
        Ok(removed)
    }
    
    async fn keys(&self) -> Result<Vec<String>> {
        // 注意：这个操作可能很昂贵，因为需要遍历所有条目
        let mut keys = Vec::new();
        
        // Moka没有直接的方法获取所有键，这里使用一个变通方法
        // 在实际应用中，可能需要维护一个单独的键集合
        tracing::warn!("Getting all keys from memory cache is expensive and not fully supported");
        
        Ok(keys)
    }
    
    async fn size(&self) -> Result<u64> {
        Ok(self.size_counter.load(Ordering::Relaxed))
    }
}

/// 详细的内存缓存统计信息
#[derive(Debug, Clone)]
pub struct DetailedMemoryCacheStats {
    pub basic_stats: CacheStats,
    pub entry_count: u64,
    pub weighted_size: u64,
    pub actual_size: u64,
    pub max_capacity: usize,
    pub max_size: u64,
    pub ttl: Duration,
}

impl DetailedMemoryCacheStats {
    /// 获取容量使用率
    pub fn capacity_usage(&self) -> f64 {
        if self.max_capacity == 0 {
            0.0
        } else {
            self.entry_count as f64 / self.max_capacity as f64
        }
    }
    
    /// 获取大小使用率
    pub fn size_usage(&self) -> f64 {
        if self.max_size == 0 {
            0.0
        } else {
            self.actual_size as f64 / self.max_size as f64
        }
    }
    
    /// 获取平均条目大小
    pub fn average_entry_size(&self) -> f64 {
        if self.entry_count == 0 {
            0.0
        } else {
            self.actual_size as f64 / self.entry_count as f64
        }
    }
}

/// 内存缓存构建器
pub struct MemoryCacheBuilder {
    config: MemoryCacheConfig,
}

impl MemoryCacheBuilder {
    /// 创建新的构建器
    pub fn new() -> Self {
        Self {
            config: MemoryCacheConfig {
                max_size: 64 * 1024 * 1024, // 64MB
                max_entries: 10000,
                ttl: Duration::from_secs(3600), // 1 hour
                cleanup_interval: Duration::from_secs(300), // 5 minutes
            },
        }
    }
    
    /// 设置最大大小
    pub fn max_size(mut self, size: u64) -> Self {
        self.config.max_size = size;
        self
    }
    
    /// 设置最大条目数
    pub fn max_entries(mut self, entries: usize) -> Self {
        self.config.max_entries = entries;
        self
    }
    
    /// 设置TTL
    pub fn ttl(mut self, ttl: Duration) -> Self {
        self.config.ttl = ttl;
        self
    }
    
    /// 设置清理间隔
    pub fn cleanup_interval(mut self, interval: Duration) -> Self {
        self.config.cleanup_interval = interval;
        self
    }
    
    /// 构建内存缓存
    pub async fn build(self) -> Result<MemoryCache> {
        MemoryCache::new(self.config).await
    }
}

impl Default for MemoryCacheBuilder {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use bytes::Bytes;
    
    #[tokio::test]
    async fn test_memory_cache_basic_operations() {
        let cache = MemoryCacheBuilder::new()
            .max_size(1024 * 1024) // 1MB
            .max_entries(100)
            .ttl(Duration::from_secs(60))
            .build()
            .await
            .unwrap();
        
        let entry = CacheEntry::new(
            Bytes::from("test data"),
            "text/plain".to_string(),
            200,
            vec![],
            Duration::from_secs(30),
        );
        
        // 测试设置
        cache.set("test_key", entry.clone()).await.unwrap();
        
        // 测试获取
        let retrieved = cache.get("test_key").await.unwrap().unwrap();
        assert_eq!(retrieved.data, entry.data);
        assert_eq!(retrieved.content_type, entry.content_type);
        
        // 测试存在性检查
        assert!(cache.exists("test_key").await.unwrap());
        assert!(!cache.exists("nonexistent_key").await.unwrap());
        
        // 测试删除
        assert!(cache.delete("test_key").await.unwrap());
        assert!(!cache.exists("test_key").await.unwrap());
        
        // 测试统计信息
        let stats = cache.stats().await.unwrap();
        assert_eq!(stats.writes, 1);
        assert_eq!(stats.deletes, 1);
    }
    
    #[tokio::test]
    async fn test_memory_cache_expiration() {
        let cache = MemoryCacheBuilder::new()
            .ttl(Duration::from_millis(100))
            .build()
            .await
            .unwrap();
        
        let entry = CacheEntry::new(
            Bytes::from("expiring data"),
            "text/plain".to_string(),
            200,
            vec![],
            Duration::from_millis(50), // 更短的TTL
        );
        
        cache.set("expiring_key", entry).await.unwrap();
        
        // 立即获取应该成功
        assert!(cache.get("expiring_key").await.unwrap().is_some());
        
        // 等待过期
        tokio::time::sleep(Duration::from_millis(100)).await;
        
        // 再次获取应该失败
        assert!(cache.get("expiring_key").await.unwrap().is_none());
    }
    
    #[tokio::test]
    async fn test_memory_cache_size_limit() {
        let cache = MemoryCacheBuilder::new()
            .max_size(100) // 很小的限制
            .build()
            .await
            .unwrap();
        
        let large_entry = CacheEntry::new(
            Bytes::from(vec![0u8; 200]), // 200字节，超过限制
            "application/octet-stream".to_string(),
            200,
            vec![],
            Duration::from_secs(60),
        );
        
        // 应该失败
        let result = cache.set("large_key", large_entry).await;
        assert!(result.is_err());
    }
    
    #[tokio::test]
    async fn test_memory_cache_clear() {
        let cache = MemoryCacheBuilder::new().build().await.unwrap();
        
        // 添加一些条目
        for i in 0..5 {
            let entry = CacheEntry::new(
                Bytes::from(format!("data_{}", i)),
                "text/plain".to_string(),
                200,
                vec![],
                Duration::from_secs(60),
            );
            cache.set(&format!("key_{}", i), entry).await.unwrap();
        }
        
        // 验证条目存在
        assert_eq!(cache.entry_count().await, 5);
        
        // 清空缓存
        cache.clear().await.unwrap();
        
        // 验证缓存已清空
        assert_eq!(cache.entry_count().await, 0);
        assert_eq!(cache.size().await.unwrap(), 0);
    }
}

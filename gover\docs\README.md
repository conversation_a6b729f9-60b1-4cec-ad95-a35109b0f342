# Go语言反向代理服务器

一个功能强大的Go语言反向代理服务器，具有nginx的核心反向代理能力。

## 功能特性

- ✅ **多站点支持**: 支持配置多个站点，每个站点可配置多个域名
- ✅ **多后端支持**: 支持主备节点、轮询、权重负载均衡
- ✅ **协议支持**: 支持HTTP/HTTPS/HTTP+HTTPS并存，HTTP1.1/2.0/3.0
- ✅ **协议路由**: 根据客户端请求协议路由到不同上游服务器
- ✅ **健康检查**: 支持自动健康检查，故障转移，可配置检查间隔
- ✅ **文件缓存**: 基于文件类型、MIME类型、URL匹配的缓存规则
- ✅ **URI路由**: 支持根据不同URI设置后端地址
- ✅ **访问日志**: 支持文件日志和syslog，格式可配置
- ✅ **ACL控制**: 支持全局和站点级别的IP访问控制
- ✅ **动态重载**: 支持ACL文件动态重载
- ✅ **灵活配置**: JSON格式配置文件，性能指标可配置
- ✅ **HTTP头控制**: 支持忽略请求头、响应头及上游头部设置

### 1. 多站点和多后端支持
- 支持多个站点配置
- 支持多个上游服务器
- 支持主备切换、轮询、负载平衡、权重等策略
- 支持HTTP/HTTPS/HTTP+HTTPS各种形式站点并存
- 支持HTTP1.1/2.0/3.0协议
- 支持根据协议路由到不同的上游服务器

### 2. 缓存功能
- 支持文件型缓存
- 可扩展的缓存存储接口
- 支持根据文件类型、MIME类型、URL匹配等进行缓存规则设置
- 可配置的缓存上限和TTL

### 3. 路由功能
- 支持根据不同的URI设置后端地址
- 支持URL重写
- 支持正则表达式匹配

### 4. 日志功能
- 支持访问日志记录到文件
- 支持syslog服务器
- 可设置的日志格式
- 预定义的日志格式（combined、common、simple、json）
- 每个站点可选用不同的日志格式

### 5. 访问控制（ACL）
- 支持全局和站点基于IP的ACL
- 兼容IP和CIDR格式，支持IPv4和IPv6
- **支持允许列表和拒绝列表分别从文件加载**
- **支持动态重载ACL文件，无需重启服务**
- 支持全局ACL和站点ACL的灵活组合

### 6. 配置管理
- 支持JSON/YAML/TOML/INI多种格式，推荐JSON
- 各部分性能指标可配置化

### 7. HTTP头部管理
- 灵活的HTTP头设置
- 支持忽略部分请求头、响应头
- 支持与上游之间的头部设置

### 3. 健康检查配置

健康检查功能支持自动监控后端服务器状态，实现故障转移：

```json
{
  "name": "primary",
  "address": "*************",
  "port": 80,
  "protocol": "http",
  "weight": 100,
  "backup": false,
  "health_check": "http://example.com/health.php",
  "health_host": "example.com",
  "health_interval": "10s",    // 检查间隔：每10秒检查一次
  "health_timeout": "5s",      // 检查超时：5秒超时
  "health_path": "/health.php", // 检查路径：/health.php
  "max_fails": 3,              // 最大失败次数：3次
  "fail_timeout": "30s"        // 失败超时：30秒后重新检查
}
```

**健康检查参数说明**：
- `health_check`: 完整的健康检查URL（优先级最高）
- `health_host`: **健康检查域名** - 用于健康检查的域名，支持多域名
- `health_interval`: **检查间隔** - 健康检查的执行频率（推荐：5-30秒）
- `health_timeout`: 检查超时 - 单次健康检查的超时时间（推荐：3-10秒）
- `health_path`: 检查路径 - 健康检查的URL路径
- `max_fails`: 最大失败次数 - 连续失败多少次后标记为故障（推荐：2-5次）
- `fail_timeout`: 失败超时 - 故障服务器多久后重新检查（推荐：15-60秒）

**域名健康检查**：
- 支持使用域名进行健康检查，特别适用于PHP-FPM等应用服务器
- 可以检查不同域名的应用状态
- 支持DNS解析验证

**PHP-FPM健康检查**：
- 提供专门的PHP健康检查页面
- 检测PHP-FPM服务状态
- 检查数据库连接、文件权限等关键组件

**推荐配置**：
- **PHP应用环境**: `health_interval: "30s"`, `health_timeout: "10s"`, `max_fails: 2`, `health_path: "/health.php"`
- **静态文件环境**: `health_interval: "10s"`, `health_timeout: "3s"`, `max_fails: 3`, `health_path: "/health.html"`
- **高可用环境**: `health_interval: "5s"`, `health_timeout: "3s"`, `max_fails: 2`, `health_path: "/health.php"`

详细说明请参考 [HEALTH_CHECK.md](HEALTH_CHECK.md)

## ACL访问控制功能详解

### 全局ACL
- **允许列表**: 配置允许访问的IP地址或CIDR
- **拒绝列表**: 配置拒绝访问的IP地址或CIDR
- **文件加载**: 支持从文件加载允许和拒绝列表
- **动态重载**: 支持定时自动重载ACL文件

### 站点ACL
- **站点级别控制**: 每个站点可以有自己的ACL规则
- **允许列表**: 站点特定的允许访问IP
- **拒绝列表**: 站点特定的拒绝访问IP
- **文件支持**: 支持从文件加载站点ACL规则

### ACL文件格式
ACL文件支持以下格式：
- 每行一个IP地址或CIDR
- 支持IPv4和IPv6地址
- 以`#`开头的行为注释
- 空行会被忽略

示例：
```
# 允许列表文件示例
***********/24
************
2001:db8::/32

# 拒绝列表文件示例
********
**********/16
```

### ACL工作原理
1. **优先级**: 站点ACL > 全局ACL
2. **检查顺序**: 
   - 先检查全局拒绝列表
   - 再检查站点拒绝列表
   - 如果有全局允许列表，检查是否在允许列表中
   - 如果有站点允许列表，检查是否在允许列表中
3. **默认行为**: 如果没有配置允许列表，默认允许访问
4. **文件重载**: 支持定时自动重载ACL文件，无需重启服务

## 安装和运行

### 前置要求
- Go 1.21或更高版本

### 安装依赖
```bash
go mod tidy
```

### 编译
```bash
go build -o reverse-proxy main.go
```

### 运行
```bash
./reverse-proxy
```

### 查看版本
```bash
./reverse-proxy -version
```

> 配置文件默认读取 `config.json`，无需指定参数。如需自定义路径可用 `-config` 参数。

## 配置文件说明

项目支持多种配置文件格式：
- **JSON** (推荐): `config.json`
- **YAML**: `config.yaml` 
- **TOML**: `config.toml`
- **INI**: `config.ini`

程序会根据文件扩展名自动检测配置格式。

### 服务器配置
```json
{
  "server": {
    "port": 8080,
    "read_timeout": "30s",
    "write_timeout": "30s",
    "idle_timeout": "60s",
    "max_connections": 1000
  }
}
```

### 日志配置
```json
{
  "log": {
    "level": "info",
    "file": "logs/proxy.log",
    "format": "combined",
    "max_size": 100,
    "max_backups": 10,
    "max_age": 30
  }
}
```

### 缓存配置
```json
{
  "cache": {
    "enabled": true,
    "type": "file",
    "path": "cache",
    "max_size": 1073741824,
    "ttl": "1h",
    "rules": [
      {
        "pattern": "\\.(css|js|png|jpg|jpeg|gif|ico|svg)$",
        "file_type": "static",
        "mime_type": "",
        "ttl": "24h",
        "enabled": true
      }
    ]
  }
}
```

### ACL配置
```json
{
  "acl": {
    "enabled": true,
    "global_allow": [
      "127.0.0.1",
      "***********/24"
    ],
    "global_deny": [
      "********",
      "**********/16"
    ],
    "allow_file": "acl/global_allow.txt",
    "deny_file": "acl/global_deny.txt",
    "reload_interval": "5m"
  }
}
```

### 站点配置
```json
{
  "sites": [
    {
      "name": "example.com",
      "host": "example.com",
      "port": 80,
      "log_format": "combined",
      "ssl": {
        "enabled": false,
        "cert_file": "",
        "key_file": "",
        "protocols": "http1.1,http2"
      },
      "acl": {
        "allow": [
          "***********/24"
        ],
        "deny": [
          "************0"
        ],
        "allow_file": "acl/site_allow.txt",
        "deny_file": "acl/site_deny.txt"
      },
      "upstreams": [
        {
          "name": "backend1",
          "address": "************",
          "port": 8080,
          "protocol": "http",
          "weight": 100,
          "max_fails": 3,
          "fail_timeout": "30s",
          "backup": false,
          "health_check": "http://************:8080/health"
        }
      ],
      "routes": [
        {
          "pattern": "^/api/",
          "upstream": "backend1",
          "rewrite": "/api/v1",
          "cache": false
        }
      ],
      "headers": {
        "request": {
          "set": {
            "X-Forwarded-Proto": "https"
          },
          "remove": [
            "X-Forwarded-For"
          ],
          "ignore": [
            "User-Agent"
          ]
        },
        "response": {
          "set": {
            "X-Powered-By": "Reverse-Proxy"
          },
          "remove": [
            "Server"
          ],
          "ignore": [
            "Set-Cookie"
          ]
        }
      }
    }
  ]
}
```

## ACL使用示例

### 创建ACL文件

```bash
# 创建目录
mkdir -p acl

# 创建全局允许列表
cat > acl/global_allow.txt << EOF
# 允许本地网络访问
***********/24
127.0.0.1
EOF

# 创建全局拒绝列表
cat > acl/global_deny.txt << EOF
# 拒绝恶意IP
********
**********/16
EOF

# 创建站点允许列表
cat > acl/site_allow.txt << EOF
# 允许访问特定站点的IP
***********/24
EOF

# 创建站点拒绝列表
cat > acl/site_deny.txt << EOF
# 拒绝访问特定站点的IP
************0
EOF
```

### 动态更新ACL

```bash
# 添加新的允许IP
echo "************" >> acl/global_allow.txt

# 添加新的拒绝IP
echo "********" >> acl/global_deny.txt
```

ACL文件会在配置的重载间隔后自动更新，无需重启服务。

## 目录结构

```
reverse-proxy/
├── main.go                    # 主程序入口
├── go.mod                     # Go模块文件
├── go.sum                     # 依赖校验文件
├── config.json                # 配置文件（JSON格式）
├── acl/                       # ACL文件目录
│   ├── global_allow.txt       # 全局允许列表
│   ├── global_deny.txt        # 全局拒绝列表
│   ├── site_allow.txt         # 站点允许列表
│   └── site_deny.txt          # 站点拒绝列表
├── logs/                      # 日志目录
├── cache/                     # 缓存目录
└── internal/                  # 内部模块
    ├── config/                # 配置管理
    ├── proxy/                 # 代理核心
    ├── loadbalancer/          # 负载均衡
    ├── cache/                 # 缓存管理
    ├── acl/                   # 访问控制
    └── logger/                # 日志管理
```

## 性能特性

- 高并发处理能力
- 内存高效的缓存管理
- 异步日志记录
- 连接池管理
- 健康检查机制

## 安全特性

- IP访问控制
- 头部过滤
- SSL/TLS支持
- 请求验证

## 监控和统计

- 访问日志记录
- 负载均衡统计
- 缓存命中率
- 错误统计

## 故障排除

### 常见问题

1. **配置文件解析错误**
   - 检查JSON格式是否正确
   - 确保所有必需的配置项都已设置

2. **上游服务器连接失败**
   - 检查上游服务器是否正常运行
   - 验证网络连接
   - 检查防火墙设置

3. **缓存不工作**
   - 检查缓存目录权限
   - 验证缓存配置
   - 检查磁盘空间

4. **ACL不生效**
   - 检查ACL文件格式
   - 验证IP地址格式
   - 检查文件权限

### 调试模式

设置日志级别为debug以获取更详细的日志信息：

```json
{
  "log": {
    "level": "debug"
  }
}
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。 
[package]
name = "rust-reverse-proxy"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "High-performance reverse proxy server written in Rust"
license = "MIT"
repository = "https://github.com/yourusername/rust-reverse-proxy"
keywords = ["proxy", "reverse-proxy", "http", "server", "load-balancer"]
categories = ["network-programming", "web-programming::http-server"]

[dependencies]
# 异步运行时
tokio = { version = "1.0", features = ["full"] }
futures = "0.3"
async-trait = "0.1"

# 配置管理
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 正则表达式和字符串处理
regex = "1.5"

# 缓存 (支持内存、文件)
dashmap = "5.5"  # 并发HashMap

# 日志
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# 错误处理
anyhow = "1.0"
thiserror = "1.0"

# 实用工具
uuid = { version = "1.10", features = ["v4", "serde"] }
bytes = "1.7"
clap = { version = "4.4", features = ["derive"] }

# 可选依赖
# HTTP服务器和客户端
hyper = { version = "0.14", features = ["full"], optional = true }
tower = { version = "0.4", features = ["full"], optional = true }
tower-http = { version = "0.4", features = ["full"], optional = true }
http = { version = "0.2", optional = true }

# 压缩算法
flate2 = { version = "1.0", optional = true }  # gzip, deflate
brotli = { version = "3.0", optional = true }  # brotli
zstd = { version = "0.11", optional = true }   # zstd

# SSL/TLS
rustls = { version = "0.20", optional = true }
rustls-pemfile = { version = "1.0", optional = true }
tokio-rustls = { version = "0.23", optional = true }

# Redis缓存
redis = { version = "0.23", features = ["tokio-comp", "connection-manager"], optional = true }

# 内存缓存
moka = { version = "0.9", features = ["future"], optional = true }

# 监控
metrics = { version = "0.20", optional = true }
metrics-exporter-prometheus = { version = "0.11", optional = true }

# syslog支持
syslog = { version = "6.0", optional = true }

# 开发依赖
[dev-dependencies]
criterion = { version = "0.5", features = ["html_reports"] }
tempfile = "3.8"
tokio-test = "0.4"

# 性能优化配置
[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"

[profile.dev]
opt-level = 0
debug = true



# 二进制目标
[[bin]]
name = "rust-reverse-proxy"
path = "src/main.rs"

# 特性标志
[features]
default = ["http-server"]
http-server = ["dep:hyper", "dep:tower", "dep:tower-http", "dep:http"]
compression = ["dep:flate2", "dep:brotli", "dep:zstd"]
ssl = ["dep:rustls", "dep:rustls-pemfile", "dep:tokio-rustls"]
metrics = ["dep:metrics", "dep:metrics-exporter-prometheus"]
redis-cache = ["dep:redis"]
memory-cache = ["dep:moka"]
syslog-logging = ["dep:syslog"]

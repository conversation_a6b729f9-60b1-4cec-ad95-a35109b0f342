[package]
name = "rust-reverse-proxy"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "High-performance reverse proxy server written in Rust"
license = "MIT"
repository = "https://github.com/yourusername/rust-reverse-proxy"
keywords = ["proxy", "reverse-proxy", "http", "server", "load-balancer"]
categories = ["network-programming", "web-programming::http-server"]

[dependencies]
# HTTP服务器和客户端
hyper = { version = "1.0", features = ["full"] }
hyper-util = { version = "0.1", features = ["full"] }
tower = { version = "0.4", features = ["full"] }
tower-http = { version = "0.5", features = ["full"] }
http = "1.0"
http-body = "1.0"
http-body-util = "0.1"

# 异步运行时
tokio = { version = "1.0", features = ["full"] }
tokio-util = { version = "0.7", features = ["full"] }
futures = "0.3"
async-trait = "0.1"

# 配置管理
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
toml = "0.8"
config = "0.14"

# 正则表达式和字符串处理
regex = "1.10"
glob = "0.3"
url = "2.5"

# 压缩
flate2 = "1.0"
brotli = "6.0"
zstd = "0.13"

# SSL/TLS
rustls = "0.23"
rustls-pemfile = "2.0"
tokio-rustls = "0.26"

# 缓存
moka = { version = "0.12", features = ["future"] }
dashmap = "5.5"

# 日志和监控
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
tracing-appender = "0.2"
metrics = "0.23"
metrics-exporter-prometheus = "0.15"

# 时间处理
chrono = { version = "0.4", features = ["serde"] }
humantime = "2.1"

# 错误处理
anyhow = "1.0"
thiserror = "1.0"

# 网络和系统
socket2 = "0.5"
libc = "0.2"

# 实用工具
uuid = { version = "1.10", features = ["v4", "serde"] }
bytes = "1.7"
mime = "0.3"
mime_guess = "2.0"
percent-encoding = "2.3"

# 开发依赖
[dev-dependencies]
criterion = { version = "0.5", features = ["html_reports"] }
tempfile = "3.8"
tokio-test = "0.4"

# 性能优化配置
[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"

[profile.dev]
opt-level = 0
debug = true

# 基准测试
[[bench]]
name = "proxy_benchmark"
harness = false

# 二进制目标
[[bin]]
name = "rust-reverse-proxy"
path = "src/main.rs"

# 特性标志
[features]
default = ["compression", "ssl", "metrics"]
compression = ["flate2", "brotli", "zstd"]
ssl = ["rustls", "rustls-pemfile", "tokio-rustls"]
metrics = ["metrics", "metrics-exporter-prometheus"]

这是一个测试文本文件！

Rust反向代理服务器功能测试
========================

这个文件用于测试以下功能：

1. 静态文件服务
   - 文件读取和响应
   - MIME类型检测
   - 文件大小处理

2. 缓存功能
   - 文件内容缓存
   - 缓存命中/未命中
   - TTL过期处理

3. 压缩功能
   - 文本文件压缩
   - 压缩算法选择
   - 压缩率统计

4. 头部处理
   - Content-Type设置
   - 缓存控制头部
   - 安全头部添加

5. 性能监控
   - 响应时间统计
   - 请求ID追踪
   - 处理器链执行

测试时间：2025-07-19
服务器：RustProxy/1.0
处理器链：7个处理器

这个文件包含足够的内容来测试压缩功能。
重复内容用于增加文件大小，以便测试压缩效果。

重复内容开始：
Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.

重复内容结束。

测试完成！

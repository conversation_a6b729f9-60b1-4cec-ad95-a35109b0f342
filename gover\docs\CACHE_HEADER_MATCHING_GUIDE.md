# 🚀 基于HTTP头部的缓存匹配规则指南

## 📋 **功能概述**

我已经为你实现了基于HTTP头部信息的缓存匹配规则功能。现在你可以根据请求的HTTP头部、<PERSON><PERSON>、查询参数等信息来精确控制缓存策略。

## 🔧 **新增的配置选项**

### **CacheRule 增强**
```json
{
  "pattern": "/api/",
  "ttl": "5m",
  "enabled": true,
  "match_conditions": {
    "headers": [
      {
        "name": "Accept",
        "pattern": "application/json",
        "description": "只缓存JSON API响应"
      },
      {
        "name": "Authorization",
        "exists": false,
        "description": "只缓存未认证的请求"
      }
    ],
    "cookies": [
      {
        "name": "session_id",
        "exists": true,
        "description": "有会话的用户"
      }
    ],
    "query_params": [
      {
        "name": "version",
        "value": "v1",
        "description": "API版本1"
      }
    ],
    "user_agents": [".*(Mobile|Android|iPhone).*"],
    "methods": ["GET", "HEAD"],
    "content_type": ["application/json"]
  },
  "skip_conditions": {
    "headers": ["Authorization"],
    "methods": ["POST", "PUT", "DELETE"]
  }
}
```

## 🎯 **匹配条件类型**

### **1. HTTP头部匹配**
```json
"headers": [
  {
    "name": "Accept",
    "pattern": "application/json",
    "description": "匹配JSON请求"
  },
  {
    "name": "Authorization",
    "exists": false,
    "description": "未认证的请求"
  },
  {
    "name": "X-API-Version",
    "value": "1.0",
    "description": "精确匹配API版本"
  }
]
```

**匹配方式**：
- `exists: true/false` - 检查头部是否存在
- `value: "exact_value"` - 精确值匹配
- `pattern: "regex_pattern"` - 正则表达式匹配

### **2. Cookie匹配**
```json
"cookies": [
  {
    "name": "session_id",
    "exists": true,
    "description": "有会话的用户"
  },
  {
    "name": "user_type",
    "value": "premium",
    "description": "高级用户"
  },
  {
    "name": "theme",
    "pattern": "dark|light",
    "description": "主题设置"
  }
]
```

### **3. 查询参数匹配**
```json
"query_params": [
  {
    "name": "format",
    "value": "json",
    "description": "JSON格式响应"
  },
  {
    "name": "cache",
    "exists": false,
    "description": "没有cache参数"
  }
]
```

### **4. User-Agent匹配**
```json
"user_agents": [
  ".*(Mobile|Android|iPhone|iPad).*",
  ".*Chrome.*",
  ".*bot.*"
]
```

### **5. HTTP方法匹配**
```json
"methods": ["GET", "HEAD", "OPTIONS"]
```

### **6. Content-Type匹配**
```json
"content_type": [
  "application/json",
  "text/html",
  "image/.*"
]
```

## 📊 **实际应用场景**

### **场景1：API版本化缓存**
```json
{
  "pattern": "/api/",
  "ttl": "5m",
  "match_conditions": {
    "headers": [
      {
        "name": "X-API-Version",
        "value": "1.0",
        "description": "API版本1.0"
      },
      {
        "name": "Accept",
        "value": "application/json"
      }
    ]
  }
}
```

### **场景2：移动设备专用缓存**
```json
{
  "pattern": "/mobile/",
  "ttl": "1h",
  "match_conditions": {
    "headers": [
      {
        "name": "User-Agent",
        "pattern": ".*(Mobile|Android|iPhone|iPad).*"
      }
    ],
    "user_agents": [".*(Mobile|Android|iPhone|iPad).*"]
  }
}
```

### **场景3：AJAX请求缓存**
```json
{
  "pattern": "/ajax/",
  "ttl": "10m",
  "match_conditions": {
    "headers": [
      {
        "name": "X-Requested-With",
        "value": "XMLHttpRequest"
      },
      {
        "name": "Accept",
        "pattern": "application/json|text/html"
      }
    ]
  }
}
```

### **场景4：CDN资源缓存**
```json
{
  "pattern": "/cdn/",
  "ttl": "720h",
  "match_conditions": {
    "headers": [
      {
        "name": "Accept-Encoding",
        "pattern": ".*gzip.*",
        "description": "支持gzip压缩"
      }
    ]
  }
}
```

### **场景5：多语言缓存**
```json
{
  "pattern": "/locale/",
  "ttl": "6h",
  "match_conditions": {
    "headers": [
      {
        "name": "Accept-Language",
        "pattern": "zh-CN|en-US|ja-JP"
      }
    ]
  }
}
```

### **场景6：认证状态缓存**
```json
{
  "pattern": "/dashboard/",
  "ttl": "15m",
  "match_conditions": {
    "headers": [
      {
        "name": "Authorization",
        "exists": true
      }
    ],
    "cookies": [
      {
        "name": "session_token",
        "exists": true
      }
    ]
  }
}
```

## 🔄 **工作原理**

### **缓存规则匹配流程**
```
请求到达
    ↓
URL模式匹配
    ↓
匹配条件检查
    ↓
所有条件都满足？
    ↓        ↓
   是       否
    ↓        ↓
应用此规则  继续下一规则
    ↓
设置TTL和缓存策略
```

### **匹配条件逻辑**
- **AND关系**：所有匹配条件都必须满足
- **OR关系**：同类型条件内部是OR关系（如多个User-Agent模式）
- **优先级**：站点级规则 > 全局规则

## 💡 **配置建议**

### **1. 精确匹配策略**
```json
{
  "cache": {
    "rules": [
      {
        "pattern": "/api/v1/",
        "ttl": "3m",
        "match_conditions": {
          "headers": [
            {"name": "X-API-Version", "value": "1.0"},
            {"name": "Accept", "value": "application/json"}
          ]
        }
      },
      {
        "pattern": "/api/v2/",
        "ttl": "1m",
        "match_conditions": {
          "headers": [
            {"name": "X-API-Version", "value": "2.0"}
          ]
        }
      }
    ]
  }
}
```

### **2. 设备类型缓存**
```json
{
  "pattern": "^/",
  "ttl": "2h",
  "match_conditions": {
    "headers": [
      {
        "name": "User-Agent",
        "pattern": ".*(Mobile|Android|iPhone).*"
      }
    ]
  }
}
```

### **3. 内容类型缓存**
```json
{
  "pattern": "/images/",
  "ttl": "720h",
  "match_conditions": {
    "headers": [
      {
        "name": "Accept",
        "pattern": "image/.*"
      }
    ]
  }
}
```

## ⚡ **性能优化**

### **1. 匹配顺序优化**
- 将最常匹配的规则放在前面
- 使用精确匹配而非正则表达式（当可能时）
- 避免过于复杂的正则表达式

### **2. 缓存键优化**
基于匹配条件的缓存键可以包含：
- URL路径
- 关键HTTP头部
- 查询参数
- User-Agent类型

### **3. 规则数量控制**
- 避免过多的匹配规则
- 合并相似的规则
- 使用通配符模式

## 🚀 **部署建议**

1. **从简单规则开始**：先配置基本的URL模式匹配
2. **逐步添加头部匹配**：根据实际需求添加头部条件
3. **监控缓存效果**：观察缓存命中率和性能
4. **调优规则**：根据访问模式优化匹配条件

## 📝 **注意事项**

1. **匹配条件是AND关系**：所有条件都必须满足
2. **大小写敏感**：HTTP头部名称大小写不敏感，但值可能敏感
3. **性能影响**：复杂的匹配条件会增加处理时间
4. **调试建议**：使用日志查看匹配结果

现在你可以基于HTTP头部信息实现精确的缓存控制，为不同类型的请求设置最适合的缓存策略！

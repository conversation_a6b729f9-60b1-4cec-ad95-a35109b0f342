# 使用说明

## 快速开始

### 1. 环境要求
- Go 1.21或更高版本
- Git

### 2. 获取代码
```bash
git clone <repository-url>
cd reverse-proxy
```

### 3. 快速启动
```bash
# 使用启动脚本（推荐）
./start.sh run

# 或者使用Makefile
make run

# 或者手动构建
go mod tidy
go build -o reverse-proxy main.go
./reverse-proxy -config config.ini
```

### 4. 使用Docker启动
```bash
# 使用启动脚本
./start.sh docker

# 或者使用docker-compose
docker-compose up --build
```

## 配置说明

### 基本配置
编辑 `config.ini` 文件来配置代理服务器：

```ini
[server]
port = 8080                    # 监听端口

[log]
level = info                   # 日志级别
file = logs/access.log         # 日志文件

[cache]
enabled = true                 # 启用缓存
path = ./cache                 # 缓存路径
```

### 站点配置
```ini
[[sites]]
name = "example.com"           # 站点名称
host = "example.com"           # 主机名

[[sites.upstreams]]            # 上游服务器
name = "web1"
address = "************"
port = 8080
weight = 1

[[sites.routes]]               # 路由规则
pattern = "^/api/"
upstream = "web1"
cache = false
```

### ACL配置
```ini
[acl]
enabled = true
global_allow = ["127.0.0.1", "***********/24"]
global_deny = ["10.0.0.0/8"]
file = acl/global.txt
```

## 测试

### 1. 启动测试环境
```bash
# 使用Docker启动完整测试环境
docker-compose up --build
```

### 2. 测试负载均衡
```bash
# 多次访问测试负载均衡
for i in {1..10}; do
  curl http://localhost:8080/
  sleep 1
done
```

### 3. 测试缓存
```bash
# 第一次请求（会缓存）
curl http://localhost:8080/static/test.css

# 第二次请求（从缓存返回）
curl http://localhost:8080/static/test.css
```

### 4. 测试ACL
```bash
# 测试允许的IP
curl -H "X-Forwarded-For: ************0" http://localhost:8080/

# 测试拒绝的IP
curl -H "X-Forwarded-For: ********" http://localhost:8080/
```

## 监控和日志

### 查看访问日志
```bash
tail -f logs/access.log
```

### 查看应用日志
```bash
# 如果使用Docker
docker-compose logs -f reverse-proxy

# 如果直接运行
# 日志会输出到控制台
```

### 监控缓存状态
```bash
# 查看缓存目录大小
du -sh cache/

# 查看缓存文件数量
find cache/ -type f | wc -l
```

## 性能调优

### 1. 调整连接池
```ini
[server]
max_connections = 1000         # 最大连接数
read_timeout = 30s             # 读取超时
write_timeout = 30s            # 写入超时
```

### 2. 优化缓存
```ini
[cache]
max_size = 1073741824         # 缓存大小（1GB）
ttl = 1h                       # 默认TTL

[[cache.rules]]
pattern = ".*\\.(css|js|png|jpg|jpeg|gif|ico)$"
ttl = 24h                      # 静态资源缓存24小时
```

### 3. 负载均衡策略
```ini
# 轮询（默认）
# 权重
# 最少连接
```

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 检查端口占用
   netstat -tlnp | grep 8080
   
   # 修改配置文件中的端口
   ```

2. **上游服务器连接失败**
   ```bash
   # 检查上游服务器状态
   curl http://************:8080/health
   
   # 检查网络连接
   ping ************
   ```

3. **缓存不工作**
   ```bash
   # 检查缓存目录权限
   ls -la cache/
   
   # 检查磁盘空间
   df -h
   ```

4. **ACL不生效**
   ```bash
   # 检查ACL文件格式
   cat acl/global.txt
   
   # 检查IP地址格式
   # 确保使用正确的IP或CIDR格式
   ```

### 调试模式
```ini
[log]
level = debug                  # 设置为debug级别
```

## 部署

### 生产环境部署

1. **编译发布版本**
   ```bash
   make release
   ```

2. **配置系统服务**
   ```bash
   # 创建systemd服务文件
   sudo cp build/reverse-proxy /usr/local/bin/
   sudo chmod +x /usr/local/bin/reverse-proxy
   ```

3. **配置防火墙**
   ```bash
   # 开放端口
   sudo ufw allow 8080
   ```

4. **配置SSL证书**
   ```ini
   [sites.ssl]
   enabled = true
   cert_file = /path/to/cert.pem
   key_file = /path/to/key.pem
   ```

### Docker部署
```bash
# 构建镜像
docker build -t reverse-proxy .

# 运行容器
docker run -d \
  --name reverse-proxy \
  -p 8080:8080 \
  -v $(pwd)/config.ini:/app/config.ini:ro \
  -v $(pwd)/logs:/app/logs \
  -v $(pwd)/cache:/app/cache \
  reverse-proxy
```

## 安全建议

1. **使用HTTPS**
   - 配置SSL证书
   - 启用HTTP/2或HTTP/3

2. **配置ACL**
   - 限制访问IP
   - 定期更新ACL列表

3. **日志安全**
   - 定期轮转日志
   - 限制日志文件大小

4. **缓存安全**
   - 设置合理的缓存大小
   - 定期清理过期缓存

## 扩展开发

### 添加新的负载均衡策略
1. 在 `internal/loadbalancer/` 中实现新的负载均衡器
2. 在 `NewLoadBalancer` 函数中添加新的策略

### 添加新的缓存后端
1. 实现 `Cache` 接口
2. 在 `NewCacheManager` 中添加新的缓存类型

### 添加新的日志格式
1. 实现 `logrus.Formatter` 接口
2. 在 `initFormatters` 中注册新格式

## 支持

如果遇到问题，请：
1. 查看日志文件
2. 检查配置文件
3. 提交Issue到项目仓库 
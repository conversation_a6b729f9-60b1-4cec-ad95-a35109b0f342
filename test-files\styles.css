/* Rust反向代理服务器 - 测试样式文件 */

/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 基础样式 */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

/* 容器样式 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 60px 0;
    text-align: center;
    border-radius: 12px;
    margin-bottom: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.header h1 {
    font-size: 3rem;
    font-weight: 300;
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header p {
    font-size: 1.3rem;
    opacity: 0.9;
}

/* 功能卡片样式 */
.features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 50px;
}

.feature-card {
    background: white;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-left: 4px solid #667eea;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.feature-card h3 {
    color: #667eea;
    font-size: 1.5rem;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.feature-card p {
    color: #666;
    line-height: 1.7;
}

/* 测试区域样式 */
.test-section {
    background: white;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    margin-bottom: 40px;
}

.test-section h2 {
    color: #333;
    font-size: 2rem;
    margin-bottom: 20px;
    text-align: center;
}

.test-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.test-item {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 8px;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.test-item:hover {
    background: #e3f2fd;
    border-color: #667eea;
    transform: translateY(-2px);
}

.test-item h4 {
    color: #667eea;
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.test-item p {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 15px;
}

.test-button {
    display: inline-block;
    background: #667eea;
    color: white;
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 500;
    transition: background 0.3s ease;
}

.test-button:hover {
    background: #5a6fd8;
    text-decoration: none;
    color: white;
}

/* 统计信息样式 */
.stats-section {
    background: white;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 8px;
    text-align: center;
    border-left: 4px solid #667eea;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
    margin-bottom: 5px;
}

.stat-label {
    color: #666;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header h1 {
        font-size: 2rem;
    }
    
    .header p {
        font-size: 1.1rem;
    }
    
    .features {
        grid-template-columns: 1fr;
    }
    
    .test-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .container {
        padding: 10px;
    }
    
    .header {
        padding: 40px 20px;
    }
    
    .feature-card,
    .test-section,
    .stats-section {
        padding: 25px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.feature-card,
.test-item,
.stat-card {
    animation: fadeInUp 0.6s ease forwards;
}

.feature-card:nth-child(2) { animation-delay: 0.1s; }
.feature-card:nth-child(3) { animation-delay: 0.2s; }
.feature-card:nth-child(4) { animation-delay: 0.3s; }
.feature-card:nth-child(5) { animation-delay: 0.4s; }
.feature-card:nth-child(6) { animation-delay: 0.5s; }

/* 加载状态 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 成功/错误状态 */
.success {
    color: #28a745;
    background-color: #d4edda;
    border-color: #c3e6cb;
    padding: 10px 15px;
    border-radius: 4px;
    border: 1px solid;
}

.error {
    color: #dc3545;
    background-color: #f8d7da;
    border-color: #f5c6cb;
    padding: 10px 15px;
    border-radius: 4px;
    border: 1px solid;
}

/* 工具提示 */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: #333;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 8px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 0.8rem;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

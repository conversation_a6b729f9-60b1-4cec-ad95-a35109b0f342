{"server": {"http_port": 80, "https_port": 443, "read_timeout": "30s", "write_timeout": "30s", "idle_timeout": "60s", "max_connections": 1000, "connection_pool": {"max_idle_conns": 20, "max_idle_conns_per_host": 2, "idle_conn_timeout": "90s", "dial_timeout": "30s", "keep_alive": "30s", "max_conns_per_host": 0, "disable_keep_alives": false}}, "log": {"level": "info", "file": "logs/server_${date}.log", "format": "combined", "formats": {"combined": "${remote_addr} - - [${time}] \"${request}\" ${status} ${body_bytes_sent} \"${http_referer}\" \"${http_user_agent}\" ${request_time} ${site}", "common": "${remote_addr} - - [${time}] \"${request}\" ${status} ${body_bytes_sent}", "json": "{\"remote_addr\":\"${remote_addr}\",\"request\":\"${request}\",\"status\":\"${status}\",\"body_bytes_sent\":\"${body_bytes_sent}\",\"http_referer\":\"${http_referer}\",\"http_user_agent\":\"${http_user_agent}\",\"request_time\":${request_time},\"site\":\"${site}\"}", "short": "${site} | ${remote_addr} | ${request} | ${status} | ${request_time}s", "mainjson": "{\"webalias\":\"${site}\",\"remote_addr\":\"${clientRealIp}\",\"server_addr\":\"${server_addr}\",\"fmt_localtime\":\"${fmt_localtime}\",\"scheme\":\"${scheme}\",\"server_protocol\":\"${server_protocol}\",\"request_method\":\"${request_method}\",\"host\":\"${host}\",\"status\":\"${status}\",\"sent_http_content_type\":\"${sent_http_content_type}\",\"body_bytes_sent\":\"${body_bytes_sent}\",\"request_uri\":\"${request_uri}\",\"http_referer\":\"${http_referer}\",\"http_user_agent\":\"${http_user_agent}\",\"biaoji\":\"(((nginx)))\"}"}, "max_size": 100, "max_backups": 10, "max_age": 30, "targets": [{"type": "file", "filename": "logs/access_${site}_${date}.log", "format": "combined"}, {"type": "syslog", "network": "udp", "address": "127.0.0.1:514", "format": "json"}], "async": {"enabled": true, "channel_size": 2000, "batch_size": 50, "flush_interval": "2000ms", "max_memory_mb": 2, "drop_policy": "drop_oldest"}}, "cache": {"enabled": true, "type": "file", "path": "cache", "max_size": "2GB", "ttl": "2h", "status_ttl": {"200": "2h", "404": "2m", "default": "2m"}, "cache_headers": {"enabled": true, "cache_status": "X-<PERSON><PERSON>-Status", "show_detail": true}, "cleanup_interval": "5m", "expired_check_interval": "1m", "enable_async_cleanup": true, "rules": [{"pattern": "\\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$", "ttl": "72h", "enabled": true}, {"pattern": "\\.(html|htm)$", "ttl": "2h", "enabled": true}, {"pattern": "\\.php$", "ttl": "0s", "enabled": true}, {"pattern": "/wp-admin/", "ttl": "0s", "enabled": true}, {"pattern": "^/?$", "ttl": "2h", "enabled": true}, {"pattern": "^/", "ttl": "2h", "enabled": true}]}, "rate_limit": {"enabled": true, "global_rps": 10000, "ip_rps": 100, "site_rps": 1000, "burst": 50}, "circuit_breaker": {"enabled": true, "max_failures": 5, "reset_timeout": "30s", "half_open_requests": 3}, "compression": {"enabled": true, "types": ["text/html", "text/css", "application/json", "application/javascript", "text/plain", "application/xml"], "min_size": "1KB", "max_size": "2MB", "level": 6, "algorithms": ["zstd", "br", "gzip", "deflate"], "brotli_quality": 6, "zstd_level": 3}, "minify": {"enabled": false, "types": ["html", "css", "js"], "min_size": "1KB", "max_size": "3MB", "compression_algorithm": "zstd", "level": "conservative", "html": {"keep_conditional_comments": true, "keep_default_attrvals": true, "keep_document_tags": true, "keep_end_tags": true, "keep_whitespace": false, "keep_quotes": true}, "css": {"precision": 10}, "js": {"precision": 10}}, "hot_reload": {"enabled": false, "check_interval": "5s"}, "memory_cache": {"enabled": true, "global_memory_limit": "32MB", "default_site_limit": "6MB", "max_file_size": "2MB", "min_access_count": 3, "score_threshold": 10.0, "cleanup_interval": "5m", "allowed_types": ["text/html", "text/css", "text/javascript", "application/json", "application/xml", "text/plain"], "blocked_types": ["image/*", "video/*", "audio/*"], "eviction_strategy": "score_based", "sites": {"tingtao-main-site": {"memory_limit": "128MB", "priority": "high"}, "qiank-main-site": {"memory_limit": "64MB", "priority": "high"}}}, "grpc": {"enabled": true, "max_recv_msg_size": "4MB", "max_send_msg_size": "4MB", "timeout": "30s", "keep_alive": true, "compression": "gzip", "load_balancing": "round_robin"}, "http3": {"enabled": false, "port": 443, "cert_file": "", "key_file": "", "max_idle_timeout": "30s", "max_stream_timeout": "10s", "keep_alive": true, "enable_datagrams": false}, "performance": {"enabled": false, "enable_zero_copy": false, "buffer_size": "128KB", "max_connections": "1M", "connection_timeout": "5s", "keep_alive_timeout": "60s", "enable_cpu_affinity": true, "worker_threads": 0, "mmap_cache": {"enabled": false, "max_size": "128M", "base_path": "cache/mmap", "auto_platform_path": false, "windows_path": "C:/Service/反代服务器/proxy_mmap", "linux_path": "/dev/shm/proxy_mmap", "macos_path": "/tmp/proxy_mmap", "freebsd_path": "/tmp/proxy_mmap", "fallback_path": ""}, "response_optimize": {"enabled": true, "max_body_size": "50MB", "min_body_size": "1KB", "skip_content_types": ["video/*", "audio/*", "application/octet-stream", "application/zip", "application/pdf"], "skip_encodings": ["gzip", "br", "deflate", "compress"], "skip_status_codes": [301, 302, 304, 404, 500, 502, 503], "require_content_length": true}}, "headers": {"request": {"set": {}, "remove": [], "ignore": []}, "response": {"set": {"Server": "CacheServer/1.0"}, "remove": [], "ignore": []}}, "error_pages": [{"code": 404, "file": "error_pages/global_404.html"}, {"code": 500, "content": "<h1>服务器内部错误</h1><p>这是全局级别的500错误页面</p><p>请稍后重试或联系管理员</p>"}, {"code": 403, "file": "error_pages/nonexistent_403.html", "content": ""}], "acl": {"enabled": true, "global_allow": [], "global_deny": [], "allow_file": "", "deny_file": "", "reload_interval": "5m"}, "monitor": {"enabled": true, "port": 8080, "username": "zdw", "password": "z7758521", "api_key": "config-api-key-2024", "acl": {"allowed_ips": ["127.0.0.1", "::1", "***********/16", "10.0.0.0/8"], "denied_ips": []}}, "sites": [{"site_id": "tingtao-main-site", "name": "tingtao_updated_test", "domains": ["tingtao.org", "www.tingtao.org"], "defaultsite": false, "max_connections": 3000, "http_port": 0, "https_port": 0, "debug_mode": false, "ssl": {"enabled": true, "cert_file": "ca/tingtao.org/fullchain.pem", "key_file": "ca/tingtao.org/privkey.pem", "protocols": "http1.1,http2", "min_version": "TLS1.2", "max_version": "TLS1.3", "prefer_server_ciphers": true, "session_cache": 1000, "session_tickets": true, "hsts": {"enabled": true, "max_age": 31536000, "include_subdomains": true, "preload": false}, "ocsp": {"enabled": true, "responder_url": "", "cache_time": "1h", "timeout": "10s", "trusted_cert": "", "verify_response": true}}, "upstreams": [{"name": "primary", "address": "*************", "port": 80, "protocol": "passthrough", "https_port": 443, "weight": 100, "max_fails": 3, "fail_timeout": "30s", "backup": false, "load_balance_group": "main_group", "health_check": "https://www.tingtao.org/about", "health_host": "www.tingtao.org", "health_interval": "10s", "health_timeout": "5s", "health_path": "/about"}, {"name": "backup", "address": "**************", "port": 80, "protocol": "passthrough", "https_port": 443, "weight": 50, "max_fails": 3, "fail_timeout": "30s", "backup": true, "load_balance_group": "main_group", "health_check": "https://www.tingtao.org/about", "health_host": "www.tingtao.org", "health_interval": "30s", "health_timeout": "5s", "health_path": "/about"}], "cache_headers": {"enabled": true, "cache_status": "X-<PERSON><PERSON>-Status", "show_detail": false}, "routes": [{"pattern": "\\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$", "upstream": "main_group", "cache": true}, {"pattern": "^/", "upstream": "main_group", "cache": false, "match_conditions": {"methods": ["POST"]}, "description": "Skip cache for POST requests"}, {"pattern": "^/", "upstream": "main_group", "cache": false, "match_conditions": {"query_params": [{"name": ".*", "pattern": ".*", "description": "Any query parameter exists"}]}, "description": "Skip cache when query string is present"}, {"pattern": "(/wp-admin/|/xmlrpc\\.php|/wp-(app|cron|login|register|mail)\\.php|wp-.*\\.php|/feed/|index\\.php|wp-comments-popup\\.php|wp-links-opml\\.php|wp-locations\\.php|sitemap(_index)?\\.xml|[a-z0-9_-]+-sitemap([0-9]+)?\\.xml)", "upstream": "main_group", "cache": false, "description": "Skip cache for WordPress admin and special files"}, {"pattern": "^/", "upstream": "main_group", "cache": false, "match_conditions": {"cookies": [{"name_pattern": "wordpress_logged_in_.*", "exists": true, "description": "WordPress logged in cookie"}]}, "description": "Skip cache for WordPress logged in users"}, {"pattern": "^/", "upstream": "main_group", "cache": false, "match_conditions": {"cookies": [{"name": "comment_author", "exists": true, "description": "Comment author cookie"}]}, "description": "Skip cache for recent commenters"}, {"pattern": "^/", "upstream": "main_group", "cache": false, "match_conditions": {"cookies": [{"name": "wp-postpass", "exists": true, "description": "WordPress post password cookie"}]}, "description": "Skip cache for password protected posts"}, {"pattern": "\\.php$", "upstream": "main_group", "cache": false}, {"pattern": "^/wp-admin/", "upstream": "main_group", "cache": false}, {"pattern": "^/zdw", "upstream": "main_group", "cache": false}, {"pattern": "^/", "upstream": "main_group", "cache": true, "match_conditions": {"methods": ["GET", "HEAD"]}, "description": "Cache GET/HEAD requests for anonymous users"}], "log_format": "<PERSON><PERSON><PERSON>", "log_targets": [{"type": "file", "filename": "logs/tingtao_${date}.log", "format": "<PERSON><PERSON><PERSON>"}], "acl": {"allow": [], "deny": ["*************", "*************"], "allow_file": "", "deny_file": ""}, "headers": {"request": {"set": {"X-Forwarded-Proto": "https"}, "remove": [], "ignore": []}, "response": {"set": {"Server": "Site-TingTao-Server/1.0"}, "remove": [], "ignore": []}, "upstream": {"setzz": {"X-Forwarded-For": "*************", "X-Real-IP": "*************", "X-Forwarded-Host": "tingtao.org", "X-Forwarded-Port": "443"}, "remove": [], "ignore": []}}, "rules": [{"pattern": "\\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$", "ttl": "72h", "enabled": true}, {"pattern": "(?i)^/archives/", "ttl": "2h", "enabled": true}]}, {"site_id": "soft.fan-site", "name": "soft.fan", "debug_mode": false, "domains": ["soft.fan", "www.soft.fan"], "max_connections": 3000, "http_port": 0, "https_port": 0, "ssl": {"enabled": true, "cert_file": "ca/soft.fan/fullchain.pem", "key_file": "ca/soft.fan/privkey.pem", "protocols": "http1.1,http2"}, "upstreams": [{"name": "primary", "address": "**************", "port": 80, "protocol": "passthrough", "https_port": 443, "weight": 100, "max_fails": 3, "fail_timeout": "30s", "backup": false, "load_balance_group": "soft.fan_group", "health_check": "https://www.soft.fan/sample-page", "health_host": "www.soft.fan", "health_interval": "30s", "health_timeout": "5s", "health_path": "/sample-page"}, {"name": "backup", "address": "**************", "port": 80, "protocol": "passthrough", "https_port": 443, "weight": 50, "max_fails": 3, "fail_timeout": "30s", "backup": true, "load_balance_group": "soft.fan_group", "health_check": "https://www.soft.fan/sample-page", "health_host": "www.soft.fan", "health_interval": "30s", "health_timeout": "5s", "health_path": "/sample-page"}], "cache_headers": {"enabled": true, "cache_status": "X-<PERSON><PERSON>-Status", "show_detail": false}, "deny_types": [".exe", ".bak"], "deny_urls": ["^/admin/", "^/private/"], "error_pages": [{"code": 404, "file": "error_pages/site_404.html"}, {"code": 403, "content": "<h1>访问被拒绝</h1><p>这是soft.fan站点级别的403错误页面</p><p>您没有权限访问此资源</p>"}], "routes": [{"pattern": "\\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$", "upstream": "soft.fan_group", "cache": true}, {"pattern": "^/", "upstream": "soft.fan_group", "cache": false, "match_conditions": {"methods": ["POST"]}, "description": "Skip cache for POST requests"}, {"pattern": "^/", "upstream": "soft.fan_group", "cache": false, "match_conditions": {"query_params": [{"name": ".*", "pattern": ".*", "description": "Any query parameter exists"}]}, "description": "Skip cache when query string is present"}, {"pattern": "(/wp-admin/|/xmlrpc\\.php|/wp-(app|cron|login|register|mail)\\.php|wp-.*\\.php|/feed/|index\\.php|wp-comments-popup\\.php|wp-links-opml\\.php|wp-locations\\.php|sitemap(_index)?\\.xml|[a-z0-9_-]+-sitemap([0-9]+)?\\.xml)", "upstream": "soft.fan_group", "cache": false, "description": "Skip cache for WordPress admin and special files"}, {"pattern": "^/", "upstream": "soft.fan_group", "cache": false, "match_conditions": {"cookies": [{"name_pattern": "wordpress_logged_in_.*", "exists": true, "description": "WordPress logged in cookie"}]}, "description": "Skip cache for WordPress logged in users"}, {"pattern": "^/", "upstream": "soft.fan_group", "cache": false, "match_conditions": {"cookies": [{"name": "comment_author", "exists": true, "description": "Comment author cookie"}]}, "description": "Skip cache for recent commenters"}, {"pattern": "^/", "upstream": "soft.fan_group", "cache": false, "match_conditions": {"cookies": [{"name": "wp-postpass", "exists": true, "description": "WordPress post password cookie"}]}, "description": "Skip cache for password protected posts"}, {"pattern": "\\.php$", "upstream": "soft.fan_group", "cache": false}, {"pattern": "^/wp-admin/", "upstream": "soft.fan_group", "cache": false}, {"pattern": "^/zdw", "upstream": "soft.fan_group", "cache": false}, {"pattern": "^/", "upstream": "soft.fan_group", "cache": true, "match_conditions": {"methods": ["GET", "HEAD"]}, "description": "Cache GET/HEAD requests for anonymous users"}], "log_format": "<PERSON><PERSON><PERSON>", "log_targets": [{"type": "file", "filename": "logs/soft.fan_${date}.log", "format": "<PERSON><PERSON><PERSON>"}, {"type": "syslog", "network": "udp", "address": "************:514", "format": "<PERSON><PERSON><PERSON>"}, {"type": "syslog", "network": "udp", "address": "**************:514", "format": "<PERSON><PERSON><PERSON>"}], "acl": {"allow": [], "deny": [], "allow_file": "", "deny_file": ""}, "headers": {"request": {"set": {"X-Forwarded-Proto": "https"}, "remove": [], "ignore": []}, "response": {"set": {"Server": "CDN Svr 1.0"}, "remove": [], "ignore": []}, "upstream": {"setzz": {"X-Forwarded-For": "*************", "X-Real-IP": "*************", "X-Forwarded-Host": "tingtao.org", "X-Forwarded-Port": "443"}, "remove": [], "ignore": []}}, "rules": [{"pattern": "(?i)^/Articles/", "ttl": "2h", "enabled": true}, {"pattern": "^/Catalog/", "ttl": "2h", "enabled": true}]}, {"site_id": "qiank-main-site", "name": "qiank_updated", "debug_mode": false, "domains": ["qiank.com", "www.qiank.com"], "max_connections": 3000, "http_port": 0, "https_port": 0, "ssl": {"enabled": true, "cert_file": "ca/qiank.com/fullchain.pem", "key_file": "ca/qiank.com/privkey.pem", "protocols": "http1.1,http2"}, "upstreams": [{"name": "primary", "address": "**************", "port": 80, "protocol": "passthrough", "https_port": 443, "weight": 100, "max_fails": 3, "fail_timeout": "30s", "backup": false, "load_balance_group": "qiank_group", "health_check": "https://qiank.com/Articles/59-286.html", "health_host": "qiank.com", "health_interval": "30s", "health_timeout": "5s", "health_path": "/Articles/59-286.html"}, {"name": "backup", "address": "**************", "port": 80, "protocol": "passthrough", "https_port": 443, "weight": 50, "max_fails": 3, "fail_timeout": "30s", "backup": true, "load_balance_group": "qiank_group", "health_check": "https://qiank.com/Articles/59-286.html", "health_host": "qiank.com", "health_interval": "30s", "health_timeout": "5s", "health_path": "/Articles/59-286.html"}], "cache_headers": {"enabled": true, "cache_status": "X-<PERSON><PERSON>-Status", "show_detail": false}, "deny_types": [".exe", ".bak"], "deny_urls": ["^/admin/", "^/private/"], "error_pages": [{"code": 404, "file": "error_pages/site_404.html"}, {"code": 403, "content": "<h1>访问被拒绝</h1><p>这是qiank站点级别的403错误页面</p><p>您没有权限访问此资源</p>"}], "routes": [{"pattern": "\\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$", "upstream": "qiank_group", "cache": true}, {"pattern": "^/", "upstream": "qiank_group", "cache": false, "match_conditions": {"methods": ["POST"]}, "description": "Skip cache for POST requests"}, {"pattern": "^/", "upstream": "qiank_group", "cache": false, "match_conditions": {"query_params": [{"name": ".*", "pattern": ".*", "description": "Any query parameter exists"}]}, "description": "Skip cache when query string is present"}, {"pattern": "(/wp-admin/|/xmlrpc\\.php|/wp-(app|cron|login|register|mail)\\.php|wp-.*\\.php|/feed/|index\\.php|wp-comments-popup\\.php|wp-links-opml\\.php|wp-locations\\.php|sitemap(_index)?\\.xml|[a-z0-9_-]+-sitemap([0-9]+)?\\.xml)", "upstream": "qiank_group", "cache": false, "description": "Skip cache for WordPress admin and special files"}, {"pattern": "^/", "upstream": "qiank_group", "cache": false, "match_conditions": {"cookies": [{"name_pattern": "wordpress_logged_in_.*", "exists": true, "description": "WordPress logged in cookie"}]}, "description": "Skip cache for WordPress logged in users"}, {"pattern": "^/", "upstream": "qiank_group", "cache": false, "match_conditions": {"cookies": [{"name": "comment_author", "exists": true, "description": "Comment author cookie"}]}, "description": "Skip cache for recent commenters"}, {"pattern": "^/", "upstream": "qiank_group", "cache": false, "match_conditions": {"cookies": [{"name": "wp-postpass", "exists": true, "description": "WordPress post password cookie"}]}, "description": "Skip cache for password protected posts"}, {"pattern": "\\.php$", "upstream": "qiank_group", "cache": false}, {"pattern": "^/wp-admin/", "upstream": "qiank_group", "cache": false}, {"pattern": "^/zdw", "upstream": "qiank_group", "cache": false}, {"pattern": "^/", "upstream": "qiank_group", "cache": true, "match_conditions": {"methods": ["GET", "HEAD"]}, "description": "Cache GET/HEAD requests for anonymous users"}], "log_format": "<PERSON><PERSON><PERSON>", "log_targets": [{"type": "file", "filename": "logs/qiank_${date}.log", "format": "<PERSON><PERSON><PERSON>"}, {"type": "syslog", "network": "udp", "address": "************:514", "format": "<PERSON><PERSON><PERSON>"}, {"type": "syslog", "network": "udp", "address": "**************:514", "format": "<PERSON><PERSON><PERSON>"}], "acl": {"allow": [], "deny": [], "allow_file": "", "deny_file": ""}, "headers": {"request": {"set": {"X-Forwarded-Proto": "https"}, "remove": [], "ignore": []}, "response": {"set": {"Server": "CacheServer/1.0"}, "remove": [], "ignore": []}, "upstream": {"setzz": {"X-Forwarded-For": "*************", "X-Real-IP": "*************", "X-Forwarded-Host": "tingtao.org", "X-Forwarded-Port": "443"}, "remove": [], "ignore": []}}, "rules": [{"pattern": "\\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$", "ttl": "72h", "enabled": true}, {"pattern": "(?i)^/Articles/", "ttl": "2h", "enabled": true}, {"pattern": "^/Catalog/", "ttl": "2h", "enabled": true}]}, {"site_id": "qianku-main-site", "name": "qianku", "debug_mode": false, "domains": ["qianku.vip", "www.qianku.vip"], "max_connections": 3000, "http_port": 0, "https_port": 0, "ssl": {"enabled": true, "cert_file": "ca/qianku.vip/fullchain.pem", "key_file": "ca/qianku.vip/privkey.pem", "protocols": "http1.1,http2"}, "upstreams": [{"name": "primary", "address": "**************", "port": 80, "protocol": "passthrough", "https_port": 443, "weight": 100, "max_fails": 3, "fail_timeout": "30s", "backup": false, "load_balance_group": "qianku_group", "health_check": "https://qianku.vip/Articles/32-26.html", "health_host": "qianku.vip", "health_interval": "30s", "health_timeout": "5s", "health_path": "/Articles/32-26.html"}, {"name": "backup", "address": "**************", "port": 80, "protocol": "passthrough", "https_port": 443, "weight": 50, "max_fails": 3, "fail_timeout": "30s", "backup": true, "load_balance_group": "qianku_group", "health_check": "https://qianku.vip/Articles/32-26.html", "health_host": "qianku.vip", "health_interval": "30s", "health_timeout": "5s", "health_path": "/Articles/32-26.html"}], "cache_headers": {"enabled": true, "cache_status": "X-<PERSON><PERSON>-Status", "show_detail": false}, "deny_types": [".exe", ".bak"], "deny_urls": ["^/admin/", "^/private/"], "error_pages": [{"code": 404, "file": "error_pages/site_404.html"}, {"code": 403, "content": "<h1>访问被拒绝</h1><p>这是qiank站点级别的403错误页面</p><p>您没有权限访问此资源</p>"}], "routes": [{"pattern": "\\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$", "upstream": "qianku_group", "cache": true}, {"pattern": "^/", "upstream": "qianku_group", "cache": false, "match_conditions": {"methods": ["POST"]}, "description": "Skip cache for POST requests"}, {"pattern": "^/", "upstream": "qianku_group", "cache": false, "match_conditions": {"query_params": [{"name": ".*", "pattern": ".*", "description": "Any query parameter exists"}]}, "description": "Skip cache when query string is present"}, {"pattern": "(/wp-admin/|/xmlrpc\\.php|/wp-(app|cron|login|register|mail)\\.php|wp-.*\\.php|/feed/|index\\.php|wp-comments-popup\\.php|wp-links-opml\\.php|wp-locations\\.php|sitemap(_index)?\\.xml|[a-z0-9_-]+-sitemap([0-9]+)?\\.xml)", "upstream": "qianku_group", "cache": false, "description": "Skip cache for WordPress admin and special files"}, {"pattern": "^/", "upstream": "qianku_group", "cache": false, "match_conditions": {"cookies": [{"name_pattern": "wordpress_logged_in_.*", "exists": true, "description": "WordPress logged in cookie"}]}, "description": "Skip cache for WordPress logged in users"}, {"pattern": "^/", "upstream": "qianku_group", "cache": false, "match_conditions": {"cookies": [{"name": "comment_author", "exists": true, "description": "Comment author cookie"}]}, "description": "Skip cache for recent commenters"}, {"pattern": "^/", "upstream": "qianku_group", "cache": false, "match_conditions": {"cookies": [{"name": "wp-postpass", "exists": true, "description": "WordPress post password cookie"}]}, "description": "Skip cache for password protected posts"}, {"pattern": "\\.php$", "upstream": "qianku_group", "cache": false}, {"pattern": "^/wp-admin/", "upstream": "qianku_group", "cache": false}, {"pattern": "^/zdw", "upstream": "qianku_group", "cache": false}, {"pattern": "^/", "upstream": "qianku_group", "cache": true, "match_conditions": {"methods": ["GET", "HEAD"]}, "description": "Cache GET/HEAD requests for anonymous users"}], "log_format": "<PERSON><PERSON><PERSON>", "log_targets": [{"type": "file", "filename": "logs/qianku_${date}.log", "format": "<PERSON><PERSON><PERSON>"}, {"type": "syslog", "network": "udp", "address": "************:514", "format": "<PERSON><PERSON><PERSON>"}, {"type": "syslog", "network": "udp", "address": "**************:514", "format": "<PERSON><PERSON><PERSON>"}], "acl": {"allow": [], "deny": [], "allow_file": "", "deny_file": ""}, "headers": {"request": {"set": {"X-Forwarded-Proto": "https"}, "remove": [], "ignore": []}, "response": {"set": {"Server": "CacheServer/1.0"}, "remove": [], "ignore": []}, "upstream": {"setzz": {"X-Forwarded-For": "*************", "X-Real-IP": "*************", "X-Forwarded-Host": "tingtao.org", "X-Forwarded-Port": "443"}, "remove": [], "ignore": []}}, "rules": [{"pattern": "\\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$", "ttl": "72h", "enabled": true}, {"pattern": "(?i)^/Articles/", "ttl": "2h", "enabled": true}, {"pattern": "^/Catalog/", "ttl": "2h", "enabled": true}]}, {"site_id": "tingtao-monitor-site", "name": "tingtao_monitor", "domains": ["monitor.tingtao.org"], "http_port": 0, "https_port": 0, "bandwidth_limit": "1MB", "ssl": {"enabled": true, "cert_file": "ca/tingtao.org/fullchain.pem", "key_file": "ca/tingtao.org/privkey.pem", "protocols": "http1.1,http2"}, "upstreams": [{"name": "primary", "address": "**************", "port": 80, "protocol": "http", "https_port": 443, "weight": 100, "max_fails": 3, "fail_timeout": "30s", "backup": false, "load_balance_group": "monitor_group", "health_check": "https://monitor.tingtao.org/login.php", "health_host": "monitor.tingtao.org", "health_interval": "30s", "health_timeout": "5s", "health_path": "/login.php"}], "cache_headers": {"enabled": true, "cache_status": "X-<PERSON><PERSON>-Status", "show_detail": false}, "deny_types": [".exe", ".bak"], "deny_urls": ["^/admin/", "^/private/"], "routes": [{"pattern": "\\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$", "upstream": "monitor_group", "cache": true}, {"pattern": "^/testfile/", "static_dir": "D:/web/files", "dir_listing": true, "cache": false, "bandwidth_limit": "1MB", "mime_types": {".txt": "text/plain"}, "hidden_in_listing": ["*.txttt", "secret*", "/private/*"]}, {"pattern": "\\.(css|js|jpg|jpeg|png|gif|webp|svg|ico|woff|woff2|ttf|eot|pdf|zip|mp4|mp3|txt|xml|json)$", "upstream": "monitor_group", "cache": true, "cache_ttl": "7d", "match_conditions": {"methods": ["GET", "HEAD"]}}, {"pattern": "^/", "upstream": "monitor_group", "rewrite": "", "cache": false}], "log_format": "<PERSON><PERSON><PERSON>", "log_targets": [{"type": "file", "filename": "logs/tingtao_monitor_${date}.log", "format": "<PERSON><PERSON><PERSON>"}], "acl": {"allow": [], "deny": [], "allow_file": "", "deny_file": ""}, "error_pages": [{"code": 404, "content": "<h1>Monitor Site 404 Error</h1><p>This is a custom 404 error page for tingtao monitor site</p><p>The requested page does not exist</p>"}, {"code": 403, "content": "<h1>Monitor Site 403 Forbidden</h1><p>Access to this resource is forbidden</p>"}, {"code": 500, "content": "<h1>Monitor Site 500 Error</h1><p>Internal server error occurred</p>"}], "headers": {"request": {"set": {"X-Forwarded-Proto": "https"}, "remove": [], "ignore": []}, "response": {"set": {"Server": "CacheServer/1.0"}, "remove": [], "ignore": []}, "upstream": {"setzz": {"X-Forwarded-For": "*************", "X-Real-IP": "*************", "X-Forwarded-Host": "tingtao.org", "X-Forwarded-Port": "443"}, "remove": [], "ignore": []}}}, {"site_id": "default-download-site", "name": "default_download", "domains": ["default.d9.fit"], "defaultsite": true, "max_connections": 1000, "http_port": 0, "https_port": 0, "debug_mode": false, "ssl": {"enabled": true, "cert_file": "ca/d9.fit/fullchain.pem", "key_file": "ca/d9.fit/privkey.pem", "protocols": "http1.1,http2", "min_version": "TLS1.2", "max_version": "TLS1.3", "prefer_server_ciphers": true, "session_cache": 1000, "session_tickets": true, "hsts": {"enabled": false, "max_age": 31536000, "include_subdomains": true, "preload": false}, "ocsp": {"enabled": false, "responder_url": "", "cache_time": "1h", "timeout": "10s", "trusted_cert": "", "verify_response": true}}, "routes": [{"pattern": "^/", "static_dir": "C:\\WEB\\defaultsite", "dir_listing": false, "index_files": ["index.html", "index.htm", "default.html", "home.html"], "cache": true, "gzip": false, "headers": {"response": {"set": {"Cache-Control": "public, max-age=3600", "X-Content-Type-Options": "nosniff", "X-Frame-Options": "DENY", "Server": "Route-Static-Server/1.0"}}}}], "error_pages": [{"code": 404, "file": "C:\\WEB\\defaultsite\\index.html"}, {"code": 403, "file": "C:\\WEB\\defaultsite\\index.html"}, {"code": 500, "file": "C:\\WEB\\defaultsite\\index.html"}], "acl": {"allow": [], "deny": [], "allow_file": "", "deny_file": ""}, "headers": {"request": {"set": {}, "remove": [], "ignore": []}, "response": {"set": {"Server": "CacheServer/1.0"}, "remove": ["X-Powered-By"], "ignore": []}}}]}
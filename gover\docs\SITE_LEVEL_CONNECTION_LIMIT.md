# 站点级连接数限制功能

## 🎯 功能概述

新增了站点级连接数限制功能，允许为每个站点单独设置最大连接数限制，提供更精细的DDoS防护和资源控制能力。

## 📋 功能特性

### 1. **多层级连接数控制**
- **全局限制**: 整个反向代理的总连接数限制
- **站点限制**: 每个站点的独立连接数限制
- **双重保护**: 全局和站点限制同时生效

### 2. **实时连接监控**
- **当前连接数**: 实时统计各站点连接数
- **历史统计**: 总连接数、拒绝连接数统计
- **拒绝时间**: 记录最后一次拒绝连接的时间

### 3. **智能限制策略**
- **优先级检查**: 先检查全局限制，再检查站点限制
- **自动释放**: 请求完成后自动释放连接计数
- **错误处理**: 连接超限时返回503状态码

## ⚙️ 配置说明

### 全局配置
```json
{
  "server": {
    "max_connections": 10000
  }
}
```

### 站点级配置
```json
{
  "sites": [
    {
      "name": "high_traffic_site",
      "domains": ["api.example.com"],
      "max_connections": 1000,
      "upstreams": [...],
      "routes": [...]
    },
    {
      "name": "low_traffic_site", 
      "domains": ["admin.example.com"],
      "max_connections": 100,
      "upstreams": [...],
      "routes": [...]
    }
  ]
}
```

### 配置参数详解

| 参数 | 级别 | 类型 | 默认值 | 说明 |
|------|------|------|--------|------|
| `server.max_connections` | 全局 | int | 1000 | 全局最大连接数 |
| `sites[].max_connections` | 站点 | int | 0 | 站点最大连接数，0表示不限制 |

## 🔧 工作原理

### 连接数检查流程
```mermaid
flowchart TD
    A[客户端请求] --> B{全局连接数检查}
    B -->|超限| C[返回503错误]
    B -->|通过| D{站点连接数检查}
    D -->|超限| E[返回503错误]
    D -->|通过| F[增加连接计数]
    F --> G[处理请求]
    G --> H[请求完成]
    H --> I[释放连接计数]
    
    C --> J[记录拒绝统计]
    E --> J
```

### 连接计数管理
```go
// 获取连接
func AcquireConnection(siteName string) error {
    // 1. 检查全局连接数限制
    if globalConnections >= globalLimit {
        return ErrGlobalConnectionLimit
    }
    
    // 2. 检查站点连接数限制
    if siteConnections >= siteLimit {
        return ErrSiteConnectionLimit
    }
    
    // 3. 增加连接计数
    atomic.AddInt64(&globalConnections, 1)
    atomic.AddInt64(&siteConnections, 1)
    
    return nil
}

// 释放连接
func ReleaseConnection(siteName string) {
    atomic.AddInt64(&globalConnections, -1)
    atomic.AddInt64(&siteConnections, -1)
}
```

## 📊 实际应用场景

### 场景1: 多租户SaaS平台
```json
{
  "server": {
    "max_connections": 50000
  },
  "sites": [
    {
      "name": "enterprise_tenant",
      "max_connections": 5000,
      "comment": "企业级租户，高连接数配额"
    },
    {
      "name": "standard_tenant",
      "max_connections": 1000,
      "comment": "标准租户，中等连接数配额"
    },
    {
      "name": "basic_tenant",
      "max_connections": 200,
      "comment": "基础租户，低连接数配额"
    }
  ]
}
```

### 场景2: API服务分级
```json
{
  "sites": [
    {
      "name": "public_api",
      "max_connections": 2000,
      "comment": "公开API，较高连接数限制"
    },
    {
      "name": "internal_api",
      "max_connections": 500,
      "comment": "内部API，中等连接数限制"
    },
    {
      "name": "admin_api",
      "max_connections": 50,
      "comment": "管理API，严格连接数限制"
    }
  ]
}
```

### 场景3: 业务优先级控制
```json
{
  "sites": [
    {
      "name": "core_business",
      "max_connections": 8000,
      "comment": "核心业务，最高优先级"
    },
    {
      "name": "secondary_business",
      "max_connections": 1500,
      "comment": "次要业务，中等优先级"
    },
    {
      "name": "experimental_features",
      "max_connections": 300,
      "comment": "实验功能，最低优先级"
    }
  ]
}
```

## 🔍 监控和统计

### 连接统计信息
```json
{
  "connection_stats": {
    "global": {
      "current_connections": 2500,
      "max_connections": 10000,
      "total_connections": 1250000,
      "rejected_connections": 1250,
      "last_reject_time": "2024-01-15T10:30:00Z"
    },
    "high_traffic_site": {
      "current_connections": 800,
      "max_connections": 1000,
      "total_connections": 500000,
      "rejected_connections": 50,
      "last_reject_time": "2024-01-15T10:25:00Z"
    },
    "low_traffic_site": {
      "current_connections": 25,
      "max_connections": 100,
      "total_connections": 75000,
      "rejected_connections": 0,
      "last_reject_time": null
    }
  }
}
```

### 监控指标
- **连接使用率**: `current_connections / max_connections`
- **拒绝率**: `rejected_connections / total_connections`
- **连接效率**: 平均连接持续时间
- **峰值时间**: 连接数最高的时间段

## 🚨 告警和处理

### 告警阈值设置
```json
{
  "alerts": {
    "connection_usage_warning": 80,    // 连接使用率80%告警
    "connection_usage_critical": 95,   // 连接使用率95%严重告警
    "rejection_rate_warning": 1,       // 拒绝率1%告警
    "rejection_rate_critical": 5       // 拒绝率5%严重告警
  }
}
```

### 自动处理策略
1. **连接数接近限制**: 记录警告日志
2. **连接数达到限制**: 拒绝新连接，返回503
3. **持续拒绝连接**: 触发告警通知
4. **连接数恢复**: 自动恢复服务

## 🎯 最佳实践

### 1. **合理设置限制**
```json
{
  "guidelines": {
    "global_limit": "设置为服务器最大承载能力的80%",
    "site_limit": "根据业务重要性和资源需求分配",
    "buffer_ratio": "预留20%缓冲空间应对突发流量"
  }
}
```

### 2. **分层防护策略**
- **第一层**: 全局连接数限制（硬限制）
- **第二层**: 站点连接数限制（业务隔离）
- **第三层**: IP限流（防止单点攻击）
- **第四层**: 熔断器（故障隔离）

### 3. **监控和调优**
- **实时监控**: 连接数使用情况
- **定期分析**: 连接模式和峰值时间
- **动态调整**: 根据业务发展调整限制
- **压力测试**: 验证限制设置的合理性

### 4. **故障处理**
- **优雅降级**: 连接超限时返回友好错误页面
- **快速恢复**: 连接释放后立即恢复服务
- **日志记录**: 详细记录连接拒绝原因
- **告警通知**: 及时通知运维人员

## 🔧 配置示例

### 完整配置示例
```json
{
  "server": {
    "http_port": 80,
    "https_port": 443,
    "max_connections": 10000
  },
  "sites": [
    {
      "name": "main_website",
      "domains": ["www.example.com"],
      "max_connections": 3000,
      "upstreams": [
        {
          "name": "web_server",
          "address": "127.0.0.1",
          "port": 8001,
          "load_balance_group": "web_group"
        }
      ],
      "routes": [
        {
          "pattern": "^/",
          "upstream": "web_group"
        }
      ]
    },
    {
      "name": "api_service",
      "domains": ["api.example.com"],
      "max_connections": 2000,
      "upstreams": [
        {
          "name": "api_server",
          "address": "127.0.0.1",
          "port": 8002,
          "load_balance_group": "api_group"
        }
      ],
      "routes": [
        {
          "pattern": "^/api/",
          "upstream": "api_group"
        }
      ]
    },
    {
      "name": "admin_panel",
      "domains": ["admin.example.com"],
      "max_connections": 100,
      "upstreams": [
        {
          "name": "admin_server",
          "address": "127.0.0.1",
          "port": 8003,
          "load_balance_group": "admin_group"
        }
      ],
      "routes": [
        {
          "pattern": "^/",
          "upstream": "admin_group"
        }
      ]
    }
  ]
}
```

## 🎉 总结

站点级连接数限制功能提供了：

### 优势
- ✅ **精细控制**: 每个站点独立的连接数限制
- ✅ **资源隔离**: 防止单个站点消耗过多连接资源
- ✅ **DDoS防护**: 有效防御连接耗尽攻击
- ✅ **业务保障**: 保护重要业务不受影响
- ✅ **实时监控**: 详细的连接统计和监控

### 适用场景
- ✅ **多租户平台**: 不同租户的资源隔离
- ✅ **API网关**: 不同API服务的连接控制
- ✅ **业务分级**: 核心业务优先保障
- ✅ **DDoS防护**: 连接层面的攻击防御

这个功能大大增强了反向代理的DDoS防护能力和资源管理能力！🛡️

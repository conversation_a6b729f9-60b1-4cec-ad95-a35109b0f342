package processor

import (
	"fmt"
	"os"
	"strings"

	"github.com/sirupsen/logrus"
	"reverse-proxy/internal/config"
)

// ErrorProcessor 错误页面处理器
type ErrorProcessor struct {
	*BaseProcessor
}

// NewErrorProcessor 创建错误页面处理器
func NewErrorProcessor(logger *logrus.Logger) *ErrorProcessor {
	return &ErrorProcessor{
		BaseProcessor: NewBaseProcessor("ErrorProcessor", 800, logger),
	}
}

// Process 处理错误页面
func (ep *ErrorProcessor) Process(req *RequestContext, resp *ResponseContext) *ProcessResult {
	// 使用数字标识进行高效判断
	if req.ErrorFlag == 0 || resp.StatusCode < 400 {
		ep.LogDebug("跳过错误处理: ErrorFlag=%d, StatusCode=%d",
			req.ErrorFlag, resp.StatusCode)
		return Success()
	}
	
	// 查找匹配的错误页面配置
	errorPage := ep.findErrorPage(req, resp.StatusCode)
	if errorPage == nil {
		return Success() // 没有自定义错误页面，使用默认处理
	}
	
	// 渲染错误页面
	if err := ep.renderErrorPage(req, resp, errorPage); err != nil {
		ep.LogError("渲染错误页面失败: %v", err)
		return Success() // 渲染失败，使用默认错误页面
	}
	
	ep.LogDebug("渲染自定义错误页面: %d -> %s", resp.StatusCode, ep.getErrorPageSource(errorPage))
	
	return SuccessWithMetadata(map[string]interface{}{
		"error_page_source": ep.getErrorPageSource(errorPage),
		"original_status":   resp.StatusCode,
	})
}

// ShouldProcess 判断是否应该处理
func (ep *ErrorProcessor) ShouldProcess(req *RequestContext) bool {
	// 错误页面处理器在响应阶段处理，这里总是返回true
	return true
}

// findErrorPage 查找错误页面配置
func (ep *ErrorProcessor) findErrorPage(req *RequestContext, statusCode int) *config.ErrorPageConfig {
	// 优先级1：路由级错误页面
	if req.Route != nil && len(req.Route.ErrorPages) > 0 {
		for _, errorPage := range req.Route.ErrorPages {
			if errorPage.Code == statusCode {
				return &errorPage
			}
		}
	}
	
	// 优先级2：站点级错误页面
	if req.Site != nil && len(req.Site.ErrorPages) > 0 {
		for _, errorPage := range req.Site.ErrorPages {
			if errorPage.Code == statusCode {
				return &errorPage
			}
		}
	}
	
	// 优先级3：全局级错误页面
	if globalConfig := config.GlobalConfig(); globalConfig != nil && len(globalConfig.ErrorPages) > 0 {
		for _, errorPage := range globalConfig.ErrorPages {
			if errorPage.Code == statusCode {
				return &errorPage
			}
		}
	}
	
	return nil
}

// renderErrorPage 渲染错误页面
func (ep *ErrorProcessor) renderErrorPage(req *RequestContext, resp *ResponseContext, errorPage *config.ErrorPageConfig) error {
	// 优先使用文件内容
	if errorPage.File != "" {
		content, err := os.ReadFile(errorPage.File)
		if err != nil {
			ep.LogWarn("读取错误页面文件失败: %s, %v", errorPage.File, err)
			// 文件读取失败，尝试使用内容
		} else {
			// 处理内容替换
			processedContent := ep.processErrorPageContent(string(content), req, resp)
			
			resp.Body = []byte(processedContent)
			resp.ContentLength = int64(len(processedContent))
			resp.ContentType = "text/html; charset=utf-8"
			resp.Headers.Set("Content-Type", "text/html; charset=utf-8")
			
			return nil
		}
	}
	
	// 使用配置的内容
	if errorPage.Content != "" {
		processedContent := ep.processErrorPageContent(errorPage.Content, req, resp)
		
		resp.Body = []byte(processedContent)
		resp.ContentLength = int64(len(processedContent))
		resp.ContentType = "text/plain; charset=utf-8"
		resp.Headers.Set("Content-Type", "text/plain; charset=utf-8")
		
		return nil
	}
	
	return fmt.Errorf("错误页面配置无效：既没有文件也没有内容")
}

// processErrorPageContent 处理错误页面内容中的变量替换
func (ep *ErrorProcessor) processErrorPageContent(content string, req *RequestContext, resp *ResponseContext) string {
	// 定义可替换的变量
	replacements := map[string]string{
		"{{STATUS_CODE}}":    fmt.Sprintf("%d", resp.StatusCode),
		"{{STATUS_TEXT}}":    ep.getStatusText(resp.StatusCode),
		"{{REQUEST_PATH}}":   req.URL.Path,
		"{{REQUEST_METHOD}}": req.Method,
		"{{CLIENT_IP}}":      req.ClientIP,
		"{{USER_AGENT}}":     req.UserAgent,
		"{{REFERER}}":        req.Referer,
		"{{TIMESTAMP}}":      req.StartTime.Format("2006-01-02 15:04:05"),
		"{{SITE_NAME}}":      ep.getSiteName(req),
		"{{ERROR_MESSAGE}}":  ep.getErrorMessage(resp),
	}
	
	// 执行替换
	result := content
	for placeholder, value := range replacements {
		result = strings.ReplaceAll(result, placeholder, value)
	}
	
	return result
}

// getStatusText 获取状态码对应的文本
func (ep *ErrorProcessor) getStatusText(statusCode int) string {
	statusTexts := map[int]string{
		400: "Bad Request",
		401: "Unauthorized",
		403: "Forbidden",
		404: "Not Found",
		405: "Method Not Allowed",
		408: "Request Timeout",
		429: "Too Many Requests",
		500: "Internal Server Error",
		501: "Not Implemented",
		502: "Bad Gateway",
		503: "Service Unavailable",
		504: "Gateway Timeout",
	}
	
	if text, exists := statusTexts[statusCode]; exists {
		return text
	}
	
	return fmt.Sprintf("HTTP %d", statusCode)
}

// getSiteName 获取站点名称
func (ep *ErrorProcessor) getSiteName(req *RequestContext) string {
	if req.Site != nil {
		return req.Site.Name
	}
	return "Unknown Site"
}

// getErrorMessage 获取错误消息
func (ep *ErrorProcessor) getErrorMessage(resp *ResponseContext) string {
	if resp.Error != nil {
		return resp.Error.Error()
	}
	return ""
}

// getErrorPageSource 获取错误页面来源描述
func (ep *ErrorProcessor) getErrorPageSource(errorPage *config.ErrorPageConfig) string {
	if errorPage.File != "" {
		return fmt.Sprintf("file:%s", errorPage.File)
	}
	if errorPage.Content != "" {
		return "content"
	}
	return "unknown"
}

// GenerateDefaultErrorPage 生成默认错误页面
func (ep *ErrorProcessor) GenerateDefaultErrorPage(req *RequestContext, resp *ResponseContext) string {
	template := `<!DOCTYPE html>
<html>
<head>
    <title>{{STATUS_CODE}} {{STATUS_TEXT}}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 40px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #d32f2f;
            margin-bottom: 20px;
        }
        .error-code {
            font-size: 72px;
            font-weight: bold;
            color: #d32f2f;
            margin-bottom: 10px;
        }
        .error-message {
            font-size: 24px;
            color: #666;
            margin-bottom: 30px;
        }
        .details {
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 4px;
            margin-top: 20px;
        }
        .details h3 {
            margin-top: 0;
            color: #333;
        }
        .details p {
            margin: 5px 0;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-code">{{STATUS_CODE}}</div>
        <div class="error-message">{{STATUS_TEXT}}</div>
        <p>抱歉，您请求的页面遇到了问题。</p>
        
        <div class="details">
            <h3>请求详情</h3>
            <p><strong>路径:</strong> {{REQUEST_PATH}}</p>
            <p><strong>方法:</strong> {{REQUEST_METHOD}}</p>
            <p><strong>时间:</strong> {{TIMESTAMP}}</p>
            <p><strong>站点:</strong> {{SITE_NAME}}</p>
        </div>
    </div>
</body>
</html>`
	
	return ep.processErrorPageContent(template, req, resp)
}

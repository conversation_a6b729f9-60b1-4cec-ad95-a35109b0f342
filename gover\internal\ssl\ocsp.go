package ssl

import (
	"bytes"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"io"
	"net/http"
	"os"
	"sync"
	"time"

	"golang.org/x/crypto/ocsp"
	"reverse-proxy/internal/config"
	"reverse-proxy/internal/logger"
)

// OCSPStapler OCSP装订管理器
type OCSPStapler struct {
	config    config.OCSPConfig
	cache     map[string]*OCSPResponse
	cacheMu   sync.RWMutex
	client    *http.Client
	issuerMap map[string]*x509.Certificate // 证书颁发者映射
}

// OCSPResponse OCSP响应缓存
type OCSPResponse struct {
	Response   []byte
	NextUpdate time.Time
	CreatedAt  time.Time
}

// NewOCSPStapler 创建OCSP装订管理器
func NewOCSPStapler(config config.OCSPConfig) *OCSPStapler {
	if config.CacheTime == 0 {
		config.CacheTime = 1 * time.Hour // 默认缓存1小时
	}
	if config.Timeout == 0 {
		config.Timeout = 10 * time.Second // 默认超时10秒
	}

	return &OCSPStapler{
		config: config,
		cache:  make(map[string]*OCSPResponse),
		client: &http.Client{
			Timeout: config.Timeout,
		},
		issuerMap: make(map[string]*x509.Certificate),
	}
}

// GetOCSPResponse 获取OCSP响应
func (os *OCSPStapler) GetOCSPResponse(certFile string) ([]byte, error) {
	if !os.config.Enabled {
		return nil, nil
	}

	// 检查缓存
	os.cacheMu.RLock()
	if cached, exists := os.cache[certFile]; exists {
		if time.Now().Before(cached.NextUpdate) {
			os.cacheMu.RUnlock()
			return cached.Response, nil
		}
	}
	os.cacheMu.RUnlock()

	// 加载证书
	cert, err := os.loadCertificate(certFile)
	if err != nil {
		return nil, fmt.Errorf("failed to load certificate: %w", err)
	}

	// 获取颁发者证书
	issuer, err := os.getIssuerCertificate(cert)
	if err != nil {
		return nil, fmt.Errorf("failed to get issuer certificate: %w", err)
	}

	// 创建OCSP请求
	ocspReq, err := ocsp.CreateRequest(cert, issuer, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create OCSP request: %w", err)
	}

	// 获取OCSP响应器URL
	responderURL := os.getResponderURL(cert)
	if responderURL == "" {
		return nil, fmt.Errorf("no OCSP responder URL found")
	}

	// 发送OCSP请求
	resp, err := os.sendOCSPRequest(responderURL, ocspReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send OCSP request: %w", err)
	}

	// 验证OCSP响应
	if os.config.VerifyResponse {
		if err := os.verifyOCSPResponse(resp, cert, issuer); err != nil {
			return nil, fmt.Errorf("OCSP response verification failed: %w", err)
		}
	}

	// 解析响应以获取下次更新时间
	ocspResp, err := ocsp.ParseResponse(resp, issuer)
	if err != nil {
		logger.Warn("Failed to parse OCSP response for cache timing:", err)
	}

	// 缓存响应
	nextUpdate := time.Now().Add(os.config.CacheTime)
	if ocspResp != nil && !ocspResp.NextUpdate.IsZero() {
		nextUpdate = ocspResp.NextUpdate
	}

	os.cacheMu.Lock()
	os.cache[certFile] = &OCSPResponse{
		Response:   resp,
		NextUpdate: nextUpdate,
		CreatedAt:  time.Now(),
	}
	os.cacheMu.Unlock()

	logger.Info("OCSP response cached for certificate:", certFile)
	return resp, nil
}

// loadCertificate 加载证书文件
func (os *OCSPStapler) loadCertificate(certFile string) (*x509.Certificate, error) {
	certPEM, err := os.readFile(certFile)
	if err != nil {
		return nil, err
	}

	block, _ := pem.Decode(certPEM)
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM certificate")
	}

	cert, err := x509.ParseCertificate(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse certificate: %w", err)
	}

	return cert, nil
}

// getIssuerCertificate 获取颁发者证书
func (os *OCSPStapler) getIssuerCertificate(cert *x509.Certificate) (*x509.Certificate, error) {
	// 首先检查缓存
	issuerKey := string(cert.AuthorityKeyId)
	if issuer, exists := os.issuerMap[issuerKey]; exists {
		return issuer, nil
	}

	// 如果配置了信任证书文件，从中加载
	if os.config.TrustedCert != "" {
		issuer, err := os.loadCertificate(os.config.TrustedCert)
		if err == nil {
			os.issuerMap[issuerKey] = issuer
			return issuer, nil
		}
		logger.Warn("Failed to load trusted certificate:", err)
	}

	// 尝试从证书的颁发者URL下载
	if len(cert.IssuingCertificateURL) > 0 {
		for _, url := range cert.IssuingCertificateURL {
			issuer, err := os.downloadIssuerCertificate(url)
			if err == nil {
				os.issuerMap[issuerKey] = issuer
				return issuer, nil
			}
			logger.Warn("Failed to download issuer certificate from", url, ":", err)
		}
	}

	return nil, fmt.Errorf("unable to find issuer certificate")
}

// downloadIssuerCertificate 下载颁发者证书
func (os *OCSPStapler) downloadIssuerCertificate(url string) (*x509.Certificate, error) {
	resp, err := os.client.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP %d", resp.StatusCode)
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// 尝试解析为DER格式
	cert, err := x509.ParseCertificate(data)
	if err == nil {
		return cert, nil
	}

	// 尝试解析为PEM格式
	block, _ := pem.Decode(data)
	if block != nil {
		return x509.ParseCertificate(block.Bytes)
	}

	return nil, fmt.Errorf("unable to parse certificate")
}

// getResponderURL 获取OCSP响应器URL
func (os *OCSPStapler) getResponderURL(cert *x509.Certificate) string {
	// 优先使用配置的URL
	if os.config.ResponderURL != "" {
		return os.config.ResponderURL
	}

	// 使用证书中的OCSP服务器URL
	if len(cert.OCSPServer) > 0 {
		return cert.OCSPServer[0]
	}

	return ""
}

// sendOCSPRequest 发送OCSP请求
func (os *OCSPStapler) sendOCSPRequest(url string, req []byte) ([]byte, error) {
	httpReq, err := http.NewRequest("POST", url, bytes.NewReader(req))
	if err != nil {
		return nil, err
	}

	httpReq.Header.Set("Content-Type", "application/ocsp-request")
	httpReq.Header.Set("Accept", "application/ocsp-response")

	resp, err := os.client.Do(httpReq)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("OCSP server returned HTTP %d", resp.StatusCode)
	}

	return io.ReadAll(resp.Body)
}

// verifyOCSPResponse 验证OCSP响应
func (os *OCSPStapler) verifyOCSPResponse(respData []byte, cert, issuer *x509.Certificate) error {
	resp, err := ocsp.ParseResponse(respData, issuer)
	if err != nil {
		return fmt.Errorf("failed to parse OCSP response: %w", err)
	}

	// 检查证书序列号
	if resp.SerialNumber.Cmp(cert.SerialNumber) != 0 {
		return fmt.Errorf("OCSP response serial number mismatch")
	}

	// 检查响应状态
	switch resp.Status {
	case ocsp.Good:
		logger.Debug("OCSP status: Good")
	case ocsp.Revoked:
		return fmt.Errorf("certificate is revoked")
	case ocsp.Unknown:
		return fmt.Errorf("certificate status unknown")
	default:
		return fmt.Errorf("invalid OCSP status: %d", resp.Status)
	}

	// 检查响应时间
	now := time.Now()
	if resp.ThisUpdate.After(now) {
		return fmt.Errorf("OCSP response not yet valid")
	}

	if !resp.NextUpdate.IsZero() && resp.NextUpdate.Before(now) {
		return fmt.Errorf("OCSP response expired")
	}

	return nil
}

// readFile 读取文件内容
func (os *OCSPStapler) readFile(filename string) ([]byte, error) {
	return os.readFileContent(filename)
}

// readFileContent 实际的文件读取实现
func (stapler *OCSPStapler) readFileContent(filename string) ([]byte, error) {
	return os.ReadFile(filename)
}

// GetCacheStats 获取缓存统计
func (os *OCSPStapler) GetCacheStats() map[string]interface{} {
	os.cacheMu.RLock()
	defer os.cacheMu.RUnlock()

	stats := map[string]interface{}{
		"enabled":     os.config.Enabled,
		"cache_size":  len(os.cache),
		"cache_time":  os.config.CacheTime.String(),
		"timeout":     os.config.Timeout.String(),
		"verify_resp": os.config.VerifyResponse,
	}

	// 添加每个证书的缓存状态
	cacheDetails := make(map[string]interface{})
	for cert, resp := range os.cache {
		cacheDetails[cert] = map[string]interface{}{
			"created_at":  resp.CreatedAt,
			"next_update": resp.NextUpdate,
			"valid":       time.Now().Before(resp.NextUpdate),
		}
	}
	stats["cache_details"] = cacheDetails

	return stats
}

// CleanExpiredCache 清理过期缓存
func (os *OCSPStapler) CleanExpiredCache() {
	os.cacheMu.Lock()
	defer os.cacheMu.Unlock()

	now := time.Now()
	for cert, resp := range os.cache {
		if now.After(resp.NextUpdate) {
			delete(os.cache, cert)
			logger.Debug("Cleaned expired OCSP cache for:", cert)
		}
	}
}

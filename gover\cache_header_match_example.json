{"cache": {"enabled": true, "type": "file", "path": "cache", "max_size": 1073741824, "ttl": "2h", "status_ttl": {"200": "2h", "404": "2m", "default": "2m"}, "rules": [{"pattern": "\\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$", "ttl": "168h", "enabled": true, "match_conditions": {"headers": [{"name": "Accept", "pattern": "image/.*|text/css|application/javascript", "description": "匹配静态资源请求"}]}, "skip_conditions": null}, {"pattern": "/api/", "ttl": "5m", "enabled": true, "match_conditions": {"headers": [{"name": "Accept", "pattern": "application/json", "description": "只缓存JSON API响应"}, {"name": "Authorization", "exists": false, "description": "只缓存未认证的API请求"}], "methods": ["GET"], "content_type": ["application/json"]}, "skip_conditions": {"headers": ["Authorization"], "methods": ["POST", "PUT", "DELETE", "PATCH"]}}, {"pattern": "/mobile/", "ttl": "1h", "enabled": true, "match_conditions": {"headers": [{"name": "User-Agent", "pattern": ".*(Mobile|Android|iPhone|iPad).*", "description": "移动设备专用缓存"}], "user_agents": [".*(Mobile|Android|iPhone|iPad).*"]}}, {"pattern": "/ajax/", "ttl": "10m", "enabled": true, "match_conditions": {"headers": [{"name": "X-Requested-With", "value": "XMLHttpRequest", "description": "AJAX请求缓存"}, {"name": "Accept", "pattern": "application/json|text/html", "description": "接受JSON或HTML响应"}]}}, {"pattern": "/cdn/", "ttl": "720h", "enabled": true, "match_conditions": {"headers": [{"name": "Accept-Encoding", "pattern": ".*gzip.*", "description": "支持gzip压缩的CDN资源"}]}}, {"pattern": "/feed/", "ttl": "30m", "enabled": true, "match_conditions": {"headers": [{"name": "Accept", "pattern": ".*(xml|rss|atom).*", "description": "RSS/Atom feed缓存"}]}, "skip_conditions": {"cookies": ["wordpress_logged_in"]}}, {"pattern": "/search/", "ttl": "15m", "enabled": true, "match_conditions": {"query_params": [{"name": "q", "exists": true, "description": "有搜索关键词的请求"}], "headers": [{"name": "Accept", "pattern": "text/html", "description": "HTML搜索结果页面"}]}, "skip_conditions": {"cookies": ["wordpress_logged_in"], "query_params": ["preview"]}}, {"pattern": "/admin/", "ttl": "0s", "enabled": true, "match_conditions": {"headers": [{"name": "Authorization", "exists": true, "description": "需要认证的管理页面"}]}}, {"pattern": "/download/", "ttl": "24h", "enabled": true, "match_conditions": {"headers": [{"name": "Range", "exists": false, "description": "完整文件下载（非断点续传）"}, {"name": "Accept", "pattern": "application/octet-stream|application/.*", "description": "二进制文件下载"}]}}, {"pattern": "/preview/", "ttl": "5m", "enabled": true, "match_conditions": {"cookies": [{"name": "preview_token", "exists": true, "description": "有预览令牌的请求"}], "query_params": [{"name": "preview", "value": "true", "description": "预览模式"}]}}, {"pattern": "/locale/", "ttl": "6h", "enabled": true, "match_conditions": {"headers": [{"name": "Accept-Language", "pattern": "zh-CN|en-US|ja-JP", "description": "支持的语言版本"}]}}, {"pattern": "/compress/", "ttl": "12h", "enabled": true, "match_conditions": {"headers": [{"name": "Accept-Encoding", "pattern": ".*(gzip|deflate|br).*", "description": "支持压缩的请求"}]}}, {"pattern": "^/", "ttl": "2h", "enabled": true, "match_conditions": {"headers": [{"name": "Accept", "pattern": "text/html", "description": "HTML页面请求"}], "methods": ["GET", "HEAD"]}, "skip_conditions": {"cookies": ["wordpress_logged_in", "comment_author"], "headers": ["Authorization"], "query_params": ["preview", "customize_changeset_uuid"], "methods": ["POST", "PUT", "DELETE", "PATCH"]}}]}, "sites": [{"name": "example_site", "domains": ["example.com", "www.example.com"], "upstreams": [{"name": "backend", "address": "127.0.0.1", "port": 8080, "weight": 1}], "routes": [{"pattern": "^/", "upstream": "backend", "cache": true}], "rules": [{"pattern": "/api/v1/", "ttl": "3m", "enabled": true, "match_conditions": {"headers": [{"name": "X-API-Version", "value": "1.0", "description": "API版本1.0的请求"}, {"name": "Accept", "value": "application/json", "description": "JSON API请求"}]}, "skip_conditions": {"headers": ["Authorization"], "methods": ["POST", "PUT", "DELETE"]}}, {"pattern": "/api/v2/", "ttl": "1m", "enabled": true, "match_conditions": {"headers": [{"name": "X-API-Version", "value": "2.0", "description": "API版本2.0的请求"}]}}, {"pattern": "/images/", "ttl": "720h", "enabled": true, "match_conditions": {"headers": [{"name": "Accept", "pattern": "image/.*", "description": "图片资源请求"}]}}]}]}
use anyhow::Result;
use bytes::Bytes;
use http::{Request, Response, StatusCode, HeaderMap, HeaderValue};
use http_body_util::{BodyExt, Full};
use hyper::body::Incoming;
use hyper::service::service_fn;
use hyper_util::rt::TokioIo;
use std::collections::HashMap;
use std::convert::Infallible;
use std::net::SocketAddr;
use std::sync::Arc;
use tokio::net::TcpListener;
// 移除未使用的导入
use tracing::{debug, error, info, warn};

use crate::config::Config;
use crate::processor::{
    RequestContext,
    chain::ProcessorChain,
    route::RouteProcessor,
    static_file::StaticFileProcessor,
    proxy::ProxyProcessor,
    cache::CacheProcessor,
    compression::CompressionProcessor,
    header::HeaderProcessor,
    error::ErrorProcessor,
};

/// HTTP服务器
pub struct HttpServer {
    config: Config,
    processor_chain: Arc<ProcessorChain>,
    cache_processor: Arc<CacheProcessor>,
}

impl HttpServer {
    /// 创建新的HTTP服务器
    pub async fn new(config: Config) -> Result<Self> {
        // 创建处理器链和缓存处理器
        let (processor_chain, cache_processor) = Self::create_processor_chain(&config).await?;

        Ok(Self {
            config,
            processor_chain: Arc::new(processor_chain),
            cache_processor,
        })
    }
    
    /// 创建处理器链
    async fn create_processor_chain(config: &Config) -> Result<(ProcessorChain, Arc<CacheProcessor>)> {
        let mut chain = ProcessorChain::new("main");

        // 添加路由处理器
        let route_processor = Arc::new(RouteProcessor::new(Arc::new(config.clone())).await?);
        chain = chain.add_processor(route_processor);

        // 添加缓存处理器
        let mut cache_processor = CacheProcessor::new();
        // 初始化缓存管理器
        if let Err(e) = cache_processor.initialize_cache().await {
            warn!("Failed to initialize cache manager: {}", e);
        }
        let cache_processor = Arc::new(cache_processor);
        chain = chain.add_processor(cache_processor.clone());

        // 添加静态文件处理器
        let static_processor = Arc::new(StaticFileProcessor::new());
        chain = chain.add_processor(static_processor);

        // 添加代理处理器
        let proxy_processor = Arc::new(ProxyProcessor::new());
        chain = chain.add_processor(proxy_processor);

        // 添加压缩处理器
        let compression_processor = Arc::new(CompressionProcessor::new());
        chain = chain.add_processor(compression_processor);

        // 添加头部处理器
        let header_processor = Arc::new(HeaderProcessor::new());
        chain = chain.add_processor(header_processor);

        // 添加错误处理器
        let error_processor = Arc::new(ErrorProcessor::new());
        chain = chain.add_processor(error_processor);

        // 按优先级排序
        chain = chain.sort_by_priority();

        // 初始化处理器链
        chain.initialize().await?;

        info!("Processor chain created with {} processors", chain.len());
        Ok((chain, cache_processor))
    }
    
    /// 启动HTTP服务器
    pub async fn start(&self, addr: SocketAddr) -> Result<()> {
        let listener = TcpListener::bind(addr).await?;
        info!("HTTP server listening on {}", addr);

        // 克隆Arc引用用于服务处理
        let processor_chain = self.processor_chain.clone();
        let cache_processor = self.cache_processor.clone();

        loop {
            // 接受新连接
            match listener.accept().await {
                Ok((stream, remote_addr)) => {
                    let processor_chain = processor_chain.clone();
                    let cache_processor = cache_processor.clone();

                    // 为每个连接启动一个任务
                    tokio::spawn(async move {
                        let io = TokioIo::new(stream);

                        let service = service_fn(move |req| {
                            let processor_chain = processor_chain.clone();
                            let cache_processor = cache_processor.clone();
                            async move {
                                Self::handle_request(req, remote_addr, processor_chain, cache_processor).await
                            }
                        });

                        if let Err(err) = hyper::server::conn::http1::Builder::new()
                            .serve_connection(io, service)
                            .await
                        {
                            error!("Error serving connection: {}", err);
                        }
                    });
                }
                Err(e) => {
                    error!("Failed to accept connection: {}", e);
                    break;
                }
            }
        }

        Ok(())
    }
    
    /// 处理HTTP请求
    async fn handle_request(
        req: Request<Incoming>,
        remote_addr: SocketAddr,
        processor_chain: Arc<ProcessorChain>,
        cache_processor: Arc<CacheProcessor>,
    ) -> Result<Response<Full<Bytes>>, Infallible> {
        let start_time = std::time::Instant::now();
        
        // 提取请求信息
        let method = req.method().to_string();
        let uri = req.uri().to_string();
        let headers = Self::extract_headers(req.headers());
        
        debug!("Incoming request: {} {} from {}", method, uri, remote_addr);
        
        // 创建请求上下文
        let mut ctx = RequestContext::new(method, uri, headers, remote_addr);
        
        // 读取请求体
        if let Ok(body_bytes) = req.into_body().collect().await {
            let body = body_bytes.to_bytes();
            if !body.is_empty() {
                ctx.body = Some(body);
            }
        }
        
        // 执行处理器链
        if let Err(e) = processor_chain.execute(&mut ctx).await {
            error!("Error executing processor chain: {}", e);
            ctx.set_error(500, "Internal server error");
        }

        // 如果响应成功且有缓存键，存储到缓存
        if ctx.response.status_code < 400 && ctx.cache_key.is_some() {
            if let Err(e) = cache_processor.store_response(&ctx).await {
                warn!("Failed to store response in cache: {}", e);
            }
        }

        // 构建响应
        let response = Self::build_response(&ctx);
        
        let duration = start_time.elapsed();
        info!(
            "Request {} {} completed in {:?} with status {}",
            ctx.method, ctx.path(), duration, ctx.response.status_code
        );
        
        Ok(response)
    }
    
    /// 提取HTTP头部
    fn extract_headers(headers: &HeaderMap<HeaderValue>) -> HashMap<String, String> {
        let mut header_map = HashMap::new();
        
        for (name, value) in headers {
            if let Ok(value_str) = value.to_str() {
                header_map.insert(name.to_string().to_lowercase(), value_str.to_string());
            }
        }
        
        header_map
    }
    
    /// 构建HTTP响应
    fn build_response(ctx: &RequestContext) -> Response<Full<Bytes>> {
        let mut builder = Response::builder()
            .status(StatusCode::from_u16(ctx.response.status_code).unwrap_or(StatusCode::INTERNAL_SERVER_ERROR));
        
        // 添加响应头
        for (name, value) in &ctx.response.headers {
            if let Ok(header_name) = name.parse::<http::HeaderName>() {
                if let Ok(header_value) = value.parse::<http::HeaderValue>() {
                    builder = builder.header(header_name, header_value);
                }
            }
        }
        
        // 设置响应体
        let body = ctx.response.body.clone().unwrap_or_else(|| Bytes::new());
        
        builder
            .body(Full::new(body))
            .unwrap_or_else(|_| {
                Response::builder()
                    .status(StatusCode::INTERNAL_SERVER_ERROR)
                    .body(Full::new(Bytes::from("Internal Server Error")))
                    .unwrap()
            })
    }
    
    /// 停止HTTP服务器
    pub async fn stop(&self) -> Result<()> {
        // 清理处理器链
        if let Err(e) = self.processor_chain.cleanup().await {
            warn!("Error cleaning up processor chain: {}", e);
        }

        info!("HTTP server stopped");
        Ok(())
    }
}

// 移除ProcessorChain的Clone实现，我们将使用Arc来共享

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::Config;
    
    #[tokio::test]
    async fn test_create_http_server() {
        let config = Config::default();
        let server = HttpServer::new(config).await;
        assert!(server.is_ok());
    }
    
    #[test]
    fn test_extract_headers() {
        let mut headers = HeaderMap::new();
        headers.insert("content-type", "application/json".parse().unwrap());
        headers.insert("user-agent", "test-agent".parse().unwrap());
        
        let extracted = HttpServer::extract_headers(&headers);
        
        assert_eq!(extracted.get("content-type"), Some(&"application/json".to_string()));
        assert_eq!(extracted.get("user-agent"), Some(&"test-agent".to_string()));
    }
}

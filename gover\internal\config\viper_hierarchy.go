package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/spf13/viper"
)

// ConfigHierarchy 配置分层管理器
type ConfigHierarchy struct {
	// 配置层级（从低到高优先级）
	layers []ConfigLayer
	
	// 合并后的Viper实例
	mergedViper *viper.Viper
}

// ConfigLayer 配置层
type ConfigLayer struct {
	Name        string        `json:"name"`
	Path        string        `json:"path"`
	Type        string        `json:"type"`
	Priority    int           `json:"priority"`
	Enabled     bool          `json:"enabled"`
	Viper       *viper.Viper  `json:"-"`
	Description string        `json:"description"`
}

// NewConfigHierarchy 创建配置分层管理器
func NewConfigHierarchy() *ConfigHierarchy {
	return &ConfigHierarchy{
		layers:      make([]ConfigLayer, 0),
		mergedViper: viper.New(),
	}
}

// AddLayer 添加配置层
func (ch *ConfigHierarchy) AddLayer(name, path, description string, priority int) error {
	// 检查文件扩展名确定类型
	ext := strings.ToLower(filepath.Ext(path))
	var configType string
	switch ext {
	case ".json":
		configType = "json"
	case ".yaml", ".yml":
		configType = "yaml"
	case ".toml":
		configType = "toml"
	case ".ini":
		configType = "ini"
	default:
		configType = "json" // 默认类型
	}

	// 创建Viper实例
	v := viper.New()
	v.SetConfigFile(path)
	v.SetConfigType(configType)

	// 尝试读取配置文件
	if err := v.ReadInConfig(); err != nil {
		return fmt.Errorf("读取配置层 %s 失败: %w", name, err)
	}

	layer := ConfigLayer{
		Name:        name,
		Path:        path,
		Type:        configType,
		Priority:    priority,
		Enabled:     true,
		Viper:       v,
		Description: description,
	}

	ch.layers = append(ch.layers, layer)
	ch.sortLayers()

	return nil
}

// AddOptionalLayer 添加可选配置层（文件不存在时不报错）
func (ch *ConfigHierarchy) AddOptionalLayer(name, path, description string, priority int) error {
	// 检查文件是否存在
	if _, err := os.Stat(path); os.IsNotExist(err) {
		// 文件不存在，跳过但不报错
		return nil
	}

	// 文件存在，使用常规方法添加
	return ch.AddLayer(name, path, description, priority)
}

// RemoveLayer 移除配置层
func (ch *ConfigHierarchy) RemoveLayer(name string) error {
	for i, layer := range ch.layers {
		if layer.Name == name {
			ch.layers = append(ch.layers[:i], ch.layers[i+1:]...)
			return nil
		}
	}
	return fmt.Errorf("配置层 %s 不存在", name)
}

// EnableLayer 启用配置层
func (ch *ConfigHierarchy) EnableLayer(name string) error {
	for i, layer := range ch.layers {
		if layer.Name == name {
			ch.layers[i].Enabled = true
			return nil
		}
	}
	return fmt.Errorf("配置层 %s 不存在", name)
}

// DisableLayer 禁用配置层
func (ch *ConfigHierarchy) DisableLayer(name string) error {
	for i, layer := range ch.layers {
		if layer.Name == name {
			ch.layers[i].Enabled = false
			return nil
		}
	}
	return fmt.Errorf("配置层 %s 不存在", name)
}

// sortLayers 按优先级排序配置层
func (ch *ConfigHierarchy) sortLayers() {
	// 简单的冒泡排序，按优先级从低到高排序
	n := len(ch.layers)
	for i := 0; i < n-1; i++ {
		for j := 0; j < n-i-1; j++ {
			if ch.layers[j].Priority > ch.layers[j+1].Priority {
				ch.layers[j], ch.layers[j+1] = ch.layers[j+1], ch.layers[j]
			}
		}
	}
}

// MergeConfigs 合并所有配置层
func (ch *ConfigHierarchy) MergeConfigs() (*viper.Viper, error) {
	// 重置合并后的Viper实例
	ch.mergedViper = viper.New()

	// 按优先级从低到高合并配置
	for _, layer := range ch.layers {
		if !layer.Enabled {
			continue
		}

		// 合并当前层的所有设置
		for _, key := range layer.Viper.AllKeys() {
			value := layer.Viper.Get(key)
			ch.mergedViper.Set(key, value)
		}
	}

	return ch.mergedViper, nil
}

// GetMergedConfig 获取合并后的配置
func (ch *ConfigHierarchy) GetMergedConfig() (*Config, error) {
	mergedViper, err := ch.MergeConfigs()
	if err != nil {
		return nil, err
	}

	var config Config
	if err := mergedViper.Unmarshal(&config, viper.DecodeHook(
		createDecodeHooks(),
	)); err != nil {
		return nil, fmt.Errorf("解析合并后的配置失败: %w", err)
	}

	return &config, nil
}

// GetMergedViper 获取合并后的Viper实例
func (ch *ConfigHierarchy) GetMergedViper() *viper.Viper {
	return ch.mergedViper
}

// GetLayerInfo 获取配置层信息
func (ch *ConfigHierarchy) GetLayerInfo() []ConfigLayer {
	info := make([]ConfigLayer, len(ch.layers))
	for i, layer := range ch.layers {
		info[i] = ConfigLayer{
			Name:        layer.Name,
			Path:        layer.Path,
			Type:        layer.Type,
			Priority:    layer.Priority,
			Enabled:     layer.Enabled,
			Description: layer.Description,
		}
	}
	return info
}

// ReloadLayer 重新加载指定配置层
func (ch *ConfigHierarchy) ReloadLayer(name string) error {
	for i, layer := range ch.layers {
		if layer.Name == name {
			// 重新读取配置文件
			if err := layer.Viper.ReadInConfig(); err != nil {
				return fmt.Errorf("重新加载配置层 %s 失败: %w", name, err)
			}
			ch.layers[i] = layer
			return nil
		}
	}
	return fmt.Errorf("配置层 %s 不存在", name)
}

// ReloadAllLayers 重新加载所有配置层
func (ch *ConfigHierarchy) ReloadAllLayers() error {
	for _, layer := range ch.layers {
		if err := ch.ReloadLayer(layer.Name); err != nil {
			return err
		}
	}
	return nil
}

// GetLayerValue 获取指定配置层的值
func (ch *ConfigHierarchy) GetLayerValue(layerName, key string) (interface{}, error) {
	for _, layer := range ch.layers {
		if layer.Name == layerName {
			return layer.Viper.Get(key), nil
		}
	}
	return nil, fmt.Errorf("配置层 %s 不存在", layerName)
}

// SetLayerValue 设置指定配置层的值
func (ch *ConfigHierarchy) SetLayerValue(layerName, key string, value interface{}) error {
	for i, layer := range ch.layers {
		if layer.Name == layerName {
			layer.Viper.Set(key, value)
			ch.layers[i] = layer
			return nil
		}
	}
	return fmt.Errorf("配置层 %s 不存在", layerName)
}

// GetEffectiveValue 获取有效值（考虑优先级）
func (ch *ConfigHierarchy) GetEffectiveValue(key string) interface{} {
	// 从高优先级到低优先级查找
	for i := len(ch.layers) - 1; i >= 0; i-- {
		layer := ch.layers[i]
		if !layer.Enabled {
			continue
		}
		
		if layer.Viper.IsSet(key) {
			return layer.Viper.Get(key)
		}
	}
	return nil
}

// GetValueSource 获取值的来源配置层
func (ch *ConfigHierarchy) GetValueSource(key string) string {
	// 从高优先级到低优先级查找
	for i := len(ch.layers) - 1; i >= 0; i-- {
		layer := ch.layers[i]
		if !layer.Enabled {
			continue
		}
		
		if layer.Viper.IsSet(key) {
			return layer.Name
		}
	}
	return ""
}

// ValidateHierarchy 验证配置层级
func (ch *ConfigHierarchy) ValidateHierarchy() error {
	// 检查是否有重复的优先级
	priorityMap := make(map[int]string)
	for _, layer := range ch.layers {
		if existingLayer, exists := priorityMap[layer.Priority]; exists {
			return fmt.Errorf("配置层 %s 和 %s 具有相同的优先级 %d", 
				layer.Name, existingLayer, layer.Priority)
		}
		priorityMap[layer.Priority] = layer.Name
	}

	// 检查是否有重复的名称
	nameMap := make(map[string]bool)
	for _, layer := range ch.layers {
		if nameMap[layer.Name] {
			return fmt.Errorf("配置层名称 %s 重复", layer.Name)
		}
		nameMap[layer.Name] = true
	}

	return nil
}

// CreateStandardHierarchy 创建标准配置层级
func CreateStandardHierarchy(baseConfigPath string) (*ConfigHierarchy, error) {
	ch := NewConfigHierarchy()

	// 获取配置文件目录
	configDir := filepath.Dir(baseConfigPath)
	if configDir == "." {
		configDir = "config"
	}

	// 添加默认配置层（最低优先级）
	defaultsPath := filepath.Join(configDir, "defaults.json")
	if err := ch.AddLayer("defaults", defaultsPath, "默认配置", 10); err != nil {
		return nil, fmt.Errorf("添加默认配置层失败: %w", err)
	}

	// 添加环境配置层（可选）
	envPath := filepath.Join(configDir, "production.json")
	ch.AddOptionalLayer("environment", envPath, "环境配置", 20)

	// 添加主配置文件层
	if err := ch.AddLayer("main", baseConfigPath, "主配置文件", 30); err != nil {
		return nil, fmt.Errorf("添加主配置层失败: %w", err)
	}

	// 添加本地覆盖配置层（最高优先级，可选）
	localPath := filepath.Join(configDir, "local.json")
	ch.AddOptionalLayer("local", localPath, "本地覆盖配置", 40)

	return ch, nil
}



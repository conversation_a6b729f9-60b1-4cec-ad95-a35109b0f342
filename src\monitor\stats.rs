use std::collections::VecDeque;
use std::time::{Duration, Instant, SystemTime};

/// 移动平均值计算器
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct MovingAverage {
    values: VecDeque<f64>,
    max_size: usize,
    sum: f64,
}

impl MovingAverage {
    /// 创建新的移动平均值计算器
    pub fn new(max_size: usize) -> Self {
        Self {
            values: VecDeque::with_capacity(max_size),
            max_size,
            sum: 0.0,
        }
    }

    /// 添加新的样本值
    pub fn add_sample(&mut self, value: f64) {
        if self.values.len() >= self.max_size {
            if let Some(old_value) = self.values.pop_front() {
                self.sum -= old_value;
            }
        }
        
        self.values.push_back(value);
        self.sum += value;
    }

    /// 获取平均值
    pub fn average(&self) -> f64 {
        if self.values.is_empty() {
            0.0
        } else {
            self.sum / self.values.len() as f64
        }
    }

    /// 获取最小值
    pub fn min(&self) -> f64 {
        self.values.iter().fold(f64::INFINITY, |a, &b| a.min(b))
    }

    /// 获取最大值
    pub fn max(&self) -> f64 {
        self.values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b))
    }

    /// 获取样本数量
    pub fn count(&self) -> usize {
        self.values.len()
    }

    /// 清理旧数据
    pub fn cleanup(&mut self) {
        // 移动平均值不需要特殊清理，因为它自动维护固定大小
    }

    /// 获取第p百分位数
    pub fn percentile(&self, p: f64) -> f64 {
        if self.values.is_empty() {
            return 0.0;
        }

        let mut sorted: Vec<f64> = self.values.iter().cloned().collect();
        sorted.sort_by(|a, b| a.partial_cmp(b).unwrap());

        let index = (p / 100.0 * (sorted.len() - 1) as f64).round() as usize;
        sorted[index.min(sorted.len() - 1)]
    }
}

/// 速率计数器
#[derive(Debug, Clone)]
pub struct RateCounter {
    samples: VecDeque<(Instant, f64)>,
    window: Duration,
}

impl RateCounter {
    /// 创建新的速率计数器
    pub fn new(window: Duration) -> Self {
        Self {
            samples: VecDeque::new(),
            window,
        }
    }

    /// 添加样本
    pub fn add_sample(&mut self, value: f64) {
        let now = Instant::now();
        self.samples.push_back((now, value));
        self.cleanup_old_samples(now);
    }

    /// 获取当前速率
    pub fn current_rate(&self) -> f64 {
        if self.samples.is_empty() {
            return 0.0;
        }

        let now = Instant::now();
        let cutoff = now - self.window;
        
        let sum: f64 = self.samples
            .iter()
            .filter(|(timestamp, _)| *timestamp >= cutoff)
            .map(|(_, value)| *value)
            .sum();

        sum / self.window.as_secs_f64()
    }

    /// 清理旧样本
    fn cleanup_old_samples(&mut self, now: Instant) {
        let cutoff = now - self.window;
        while let Some((timestamp, _)) = self.samples.front() {
            if *timestamp < cutoff {
                self.samples.pop_front();
            } else {
                break;
            }
        }
    }

    /// 清理旧数据
    pub fn cleanup(&mut self) {
        let now = Instant::now();
        self.cleanup_old_samples(now);
    }

    /// 获取样本数量
    pub fn count(&self) -> usize {
        self.samples.len()
    }
}

/// 直方图统计
#[derive(Debug, Clone)]
pub struct Histogram {
    buckets: Vec<(f64, u64)>, // (上界, 计数)
    total_count: u64,
    sum: f64,
}

impl Histogram {
    /// 创建新的直方图
    pub fn new(buckets: Vec<f64>) -> Self {
        let mut bucket_pairs = Vec::new();
        for bucket in buckets {
            bucket_pairs.push((bucket, 0));
        }
        
        Self {
            buckets: bucket_pairs,
            total_count: 0,
            sum: 0.0,
        }
    }

    /// 创建默认的响应时间直方图
    pub fn response_time_histogram() -> Self {
        Self::new(vec![
            1.0, 5.0, 10.0, 25.0, 50.0, 100.0, 250.0, 500.0, 1000.0, 2500.0, 5000.0, 10000.0
        ])
    }

    /// 观察一个值
    pub fn observe(&mut self, value: f64) {
        self.total_count += 1;
        self.sum += value;

        for (bucket_upper, count) in &mut self.buckets {
            if value <= *bucket_upper {
                *count += 1;
            }
        }
    }

    /// 获取总计数
    pub fn count(&self) -> u64 {
        self.total_count
    }

    /// 获取总和
    pub fn sum(&self) -> f64 {
        self.sum
    }

    /// 获取平均值
    pub fn average(&self) -> f64 {
        if self.total_count == 0 {
            0.0
        } else {
            self.sum / self.total_count as f64
        }
    }

    /// 获取百分位数估计
    pub fn quantile(&self, q: f64) -> f64 {
        if self.total_count == 0 {
            return 0.0;
        }

        let target_count = (q * self.total_count as f64) as u64;
        let mut cumulative = 0;

        for (bucket_upper, count) in &self.buckets {
            cumulative += count;
            if cumulative >= target_count {
                return *bucket_upper;
            }
        }

        // 如果没有找到，返回最大的桶
        self.buckets.last().map(|(upper, _)| *upper).unwrap_or(0.0)
    }

    /// 获取桶信息
    pub fn buckets(&self) -> &[(f64, u64)] {
        &self.buckets
    }
}

/// 计数器
#[derive(Debug, Clone)]
pub struct Counter {
    value: u64,
    last_reset: SystemTime,
}

impl Counter {
    /// 创建新的计数器
    pub fn new() -> Self {
        Self {
            value: 0,
            last_reset: SystemTime::now(),
        }
    }

    /// 增加计数
    pub fn increment(&mut self) {
        self.value += 1;
    }

    /// 增加指定数量
    pub fn add(&mut self, amount: u64) {
        self.value += amount;
    }

    /// 获取当前值
    pub fn value(&self) -> u64 {
        self.value
    }

    /// 重置计数器
    pub fn reset(&mut self) {
        self.value = 0;
        self.last_reset = SystemTime::now();
    }

    /// 获取上次重置时间
    pub fn last_reset(&self) -> SystemTime {
        self.last_reset
    }
}

/// 仪表盘（存储当前值）
#[derive(Debug, Clone)]
pub struct Gauge {
    value: f64,
    last_updated: SystemTime,
}

impl Gauge {
    /// 创建新的仪表盘
    pub fn new() -> Self {
        Self {
            value: 0.0,
            last_updated: SystemTime::now(),
        }
    }

    /// 设置值
    pub fn set(&mut self, value: f64) {
        self.value = value;
        self.last_updated = SystemTime::now();
    }

    /// 增加值
    pub fn add(&mut self, delta: f64) {
        self.value += delta;
        self.last_updated = SystemTime::now();
    }

    /// 减少值
    pub fn sub(&mut self, delta: f64) {
        self.value -= delta;
        self.last_updated = SystemTime::now();
    }

    /// 获取当前值
    pub fn value(&self) -> f64 {
        self.value
    }

    /// 获取上次更新时间
    pub fn last_updated(&self) -> SystemTime {
        self.last_updated
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::thread;

    #[test]
    fn test_moving_average() {
        let mut avg = MovingAverage::new(3);
        
        avg.add_sample(1.0);
        assert_eq!(avg.average(), 1.0);
        
        avg.add_sample(2.0);
        assert_eq!(avg.average(), 1.5);
        
        avg.add_sample(3.0);
        assert_eq!(avg.average(), 2.0);
        
        // 添加第四个值，应该移除第一个
        avg.add_sample(4.0);
        assert_eq!(avg.average(), 3.0); // (2+3+4)/3
    }

    #[test]
    fn test_rate_counter() {
        let mut counter = RateCounter::new(Duration::from_secs(1));
        
        counter.add_sample(10.0);
        thread::sleep(Duration::from_millis(100));
        counter.add_sample(20.0);
        
        let rate = counter.current_rate();
        assert!(rate > 0.0);
    }

    #[test]
    fn test_histogram() {
        let mut hist = Histogram::response_time_histogram();
        
        hist.observe(5.5);
        hist.observe(15.0);
        hist.observe(150.0);
        
        assert_eq!(hist.count(), 3);
        assert_eq!(hist.sum(), 170.5);
        assert!((hist.average() - 56.833333333333336).abs() < 0.0001);
    }

    #[test]
    fn test_counter() {
        let mut counter = Counter::new();
        assert_eq!(counter.value(), 0);
        
        counter.increment();
        assert_eq!(counter.value(), 1);
        
        counter.add(5);
        assert_eq!(counter.value(), 6);
        
        counter.reset();
        assert_eq!(counter.value(), 0);
    }

    #[test]
    fn test_gauge() {
        let mut gauge = Gauge::new();
        assert_eq!(gauge.value(), 0.0);
        
        gauge.set(10.0);
        assert_eq!(gauge.value(), 10.0);
        
        gauge.add(5.0);
        assert_eq!(gauge.value(), 15.0);
        
        gauge.sub(3.0);
        assert_eq!(gauge.value(), 12.0);
    }
}

# 反向代理路由配置说明文档

## 📋 概述

路由（Routes）是反向代理的核心组件，负责决定如何处理不同的HTTP请求。每个路由规则定义了URL模式匹配、目标上游服务器、缓存策略等配置。

## 🏗️ 配置结构

### 基本语法

```json
{
  "sites": [
    {
      "name": "example_site",
      "upstreams": [
        {
          "name": "server1",
          "address": "api1.example.com",
          "port": 8080,
          "load_balance_group": "api_group"
        },
        {
          "name": "server2",
          "address": "api2.example.com",
          "port": 8080,
          "load_balance_group": "api_group"
        }
      ],
      "routes": [
        {
          "pattern": "正则表达式模式",
          "upstream": "负载均衡组名",
          "rewrite": "URL重写规则",
          "cache": true/false,
          "static_dir": "静态文件目录",
          "dir_listing": true/false,
          "mime_types": {},
          "rate_limit": "限速配置",
          "error_pages": []
        }
      ]
    }
  ]
}
```

## 🎯 配置字段详解

### 🔄 负载均衡组概念

**重要**: 从v1.1.0开始，路由的 `upstream` 字段只能指向**负载均衡组名**，不能直接指向服务器名。

- **负载均衡组**: 由一个或多个上游服务器组成的逻辑组
- **组内负载均衡**: 请求在组内的服务器间自动分配
- **单服务器组**: 即使只有一个服务器，也必须设置组名

### 必需字段

#### `pattern` (string, 必需)
- **作用**: 定义URL路径匹配的正则表达式
- **匹配规则**: 按配置顺序依次匹配，第一个匹配成功的规则生效
- **语法**: 标准正则表达式

**示例**:
```json
{
  "pattern": "^/api/"          // 匹配以 /api/ 开头的路径
},
{
  "pattern": "^/static/"       // 匹配以 /static/ 开头的路径  
},
{
  "pattern": "\\.(css|js)$"    // 匹配以 .css 或 .js 结尾的文件
},
{
  "pattern": "^/$"             // 仅匹配根路径 /
},
{
  "pattern": "^/"              // 匹配所有路径（兜底规则）
}
```

### 可选字段

#### `upstream` (string, 可选)
- **作用**: 指定目标负载均衡组名称
- **要求**: 必须在 `upstreams` 配置中预先定义相应的负载均衡组
- **用途**: 代理模式时必需，静态文件模式时可选
- **重要**: 只能指向负载均衡组名，不能直接指向服务器名

```json
{
  "pattern": "^/api/",
  "upstream": "api_group"     // 引用负载均衡组名，不是服务器名
}
```

#### `rewrite` (string, 可选, 默认: "")
- **作用**: 简单URL路径重写规则
- **工作原理**: 将匹配的路径部分替换为指定内容
- **空字符串**: 表示不进行重写
- **变量支持**: 支持 `$1`, `$2` 等正则捕获组变量

```json
{
  "pattern": "^/old-api/",
  "upstream": "api_server",
  "rewrite": "/new-api/"       // /old-api/users → /new-api/users
},
{
  "pattern": "^/v1/(.*)$",
  "upstream": "api_server",
  "rewrite": "/api/v1/$1"      // /v1/users → /api/v1/users
}
```

#### `rewrite_advanced` (object, 可选)
- **作用**: 高级URL重写配置，提供更灵活的重写功能
- **优先级**: 高于简单 `rewrite` 配置
- **功能**: 支持条件重写、变量替换、路径拼接等

```json
{
  "pattern": "^/api/(.*)$",
  "upstream": "api_server",
  "rewrite_advanced": {
    "target": "/v2/api/$1",
    "strip_prefix": true,
    "append_path": false,
    "variables": {
      "version": "v2",
      "service": "main"
    },
    "conditions": [
      {
        "type": "header",
        "key": "X-API-Version",
        "value": "v1",
        "operator": "equals",
        "target": "/v1/api/$1"
      }
    ]
  }
}
```

#### `cache` (boolean, 可选, 默认: false)
- **作用**: 控制是否启用响应缓存
- **true**: 启用缓存，根据缓存规则和状态码TTL决定缓存时间
- **false**: 禁用缓存，每次都转发到后端

```json
{
  "pattern": "^/api/",
  "upstream": "api_server",
  "cache": false               // API接口不缓存
},
{
  "pattern": "^/static/",
  "upstream": "cdn_server",
  "cache": true                // 静态资源启用缓存
}
```

#### `static_dir` (string, 可选)
- **作用**: 启用静态文件服务模式
- **值**: 本地文件系统路径
- **行为**: 直接从本地目录提供文件，不转发到上游服务器

```json
{
  "pattern": "^/downloads/",
  "static_dir": "/var/www/downloads",
  "cache": true
}
```

#### `dir_listing` (boolean, 可选, 默认: false)
- **作用**: 是否允许目录浏览
- **要求**: 仅在 `static_dir` 模式下有效
- **true**: 显示目录文件列表
- **false**: 禁止目录浏览

```json
{
  "pattern": "^/files/",
  "static_dir": "/var/www/files",
  "dir_listing": true          // 允许浏览目录内容
}
```

#### `mime_types` (object, 可选)
- **作用**: 自定义文件扩展名的MIME类型映射
- **格式**: `{"扩展名": "MIME类型"}`
- **用途**: 覆盖默认的MIME类型设置

```json
{
  "pattern": "^/files/",
  "static_dir": "/var/www/files",
  "mime_types": {
    ".json": "application/json",
    ".xml": "application/xml",
    ".log": "text/plain"
  }
}
```

#### `rate_limit` (string, 可选)
- **作用**: 设置路由级别的流量限制
- **格式**: 带单位的数值 (如: "1MB", "500KB")
- **范围**: 仅对当前路由生效

```json
{
  "pattern": "^/download/",
  "static_dir": "/var/www/downloads",
  "rate_limit": "1MB"          // 限制下载速度为1MB/s
}
```

#### `error_pages` (array, 可选)
- **作用**: 路由级别的自定义错误页面
- **优先级**: 高于站点级错误页面配置
- **格式**: 错误页面配置数组

```json
{
  "pattern": "^/api/",
  "upstream": "api_server",
  "error_pages": [
    {
      "code": 404,
      "content": "{\"error\": \"API endpoint not found\"}"
    },
    {
      "code": 500,
      "file": "error_pages/api_500.html"
    }
  ]
}
```

## 🔄 负载均衡组配置

### 上游服务器配置

每个上游服务器必须配置 `load_balance_group` 字段：

```json
{
  "upstreams": [
    {
      "name": "api_server_1",
      "address": "api1.example.com",
      "port": 8080,
      "weight": 100,
      "backup": false,
      "load_balance_group": "api_cluster"    // 必需：负载均衡组名
    },
    {
      "name": "api_server_2",
      "address": "api2.example.com",
      "port": 8080,
      "weight": 150,
      "backup": false,
      "load_balance_group": "api_cluster"    // 同一组
    },
    {
      "name": "cdn_server",
      "address": "cdn.example.com",
      "port": 80,
      "weight": 100,
      "backup": false,
      "load_balance_group": "cdn_group"      // 独立组
    }
  ]
}
```

### 路由配置

路由的 `upstream` 字段只能指向组名：

```json
{
  "routes": [
    {
      "pattern": "^/api/",
      "upstream": "api_cluster",             // 指向负载均衡组
      "cache": false
    },
    {
      "pattern": "^/static/",
      "upstream": "cdn_group",               // 指向另一个组
      "cache": true
    }
  ]
}
```

### 负载均衡行为

- **组内负载均衡**: 请求在同组服务器间按权重分配
- **故障转移**: 主服务器故障时自动切换到备用服务器
- **健康检查**: 自动检测服务器健康状态
- **权重支持**: 支持不同权重的流量分配

## 🔄 路由匹配机制

### 匹配顺序
1. **顺序匹配**: 按照 `routes` 数组中的顺序依次检查
2. **第一匹配**: 找到第一个匹配的规则后立即使用，不再检查后续规则
3. **无匹配**: 如果没有任何规则匹配，返回404错误

### 最佳实践
```json
{
  "routes": [
    {
      "pattern": "^/api/admin/",       // 1. 最具体的规则放在前面
      "upstream": "admin_api",
      "cache": false
    },
    {
      "pattern": "^/api/",             // 2. 较通用的规则
      "upstream": "public_api", 
      "cache": false
    },
    {
      "pattern": "^/static/",          // 3. 静态资源规则
      "static_dir": "/var/www/static",
      "cache": true
    },
    {
      "pattern": "^/"                  // 4. 兜底规则放在最后
      "upstream": "web_server",
      "cache": true
    }
  ]
}
```

## 📊 常见配置模式

### 1. 微服务架构

```json
{
  "upstreams": [
    {"name": "user_api_1", "address": "user1.com", "port": 8080, "load_balance_group": "user_service"},
    {"name": "user_api_2", "address": "user2.com", "port": 8080, "load_balance_group": "user_service"},
    {"name": "order_api_1", "address": "order1.com", "port": 8080, "load_balance_group": "order_service"},
    {"name": "order_api_2", "address": "order2.com", "port": 8080, "load_balance_group": "order_service"},
    {"name": "product_api", "address": "product.com", "port": 8080, "load_balance_group": "product_service"},
    {"name": "frontend", "address": "frontend.com", "port": 3000, "load_balance_group": "frontend_service"}
  ],
  "routes": [
    {
      "pattern": "^/api/user/",
      "upstream": "user_service",
      "cache": false
    },
    {
      "pattern": "^/api/order/",
      "upstream": "order_service",
      "cache": false
    },
    {
      "pattern": "^/api/product/",
      "upstream": "product_service",
      "cache": true
    },
    {
      "pattern": "^/",
      "upstream": "frontend_service",
      "cache": true
    }
  ]
}
```

### 2. WordPress站点

```json
{
  "upstreams": [
    {"name": "wp_primary", "address": "wp1.example.com", "port": 80, "load_balance_group": "wordpress_group"},
    {"name": "wp_backup", "address": "wp2.example.com", "port": 80, "backup": true, "load_balance_group": "wordpress_group"}
  ],
  "routes": [
    {
      "pattern": "^/wp-admin/",
      "upstream": "wordpress_group",
      "cache": false
    },
    {
      "pattern": "\\.php$",
      "upstream": "wordpress_group",
      "cache": false
    },
    {
      "pattern": "^/wp-content/",
      "upstream": "wordpress_group",
      "cache": true
    },
    {
      "pattern": "^/",
      "upstream": "wordpress_group",
      "cache": true
    }
  ]
}
```

### 3. 静态文件 + API

```json
{
  "routes": [
    {
      "pattern": "^/api/",
      "upstream": "api_server",
      "cache": false
    },
    {
      "pattern": "^/static/",
      "static_dir": "/var/www/static",
      "cache": true,
      "dir_listing": false
    },
    {
      "pattern": "^/downloads/",
      "static_dir": "/var/www/downloads", 
      "cache": true,
      "dir_listing": true,
      "rate_limit": "2MB"
    },
    {
      "pattern": "^/",
      "upstream": "web_server",
      "cache": true
    }
  ]
}
```

### 4. API版本控制

```json
{
  "routes": [
    {
      "pattern": "^/api/v1/",
      "upstream": "api_v1",
      "cache": false
    },
    {
      "pattern": "^/api/v2/",
      "upstream": "api_v2",
      "cache": false
    },
    {
      "pattern": "^/api/",
      "upstream": "api_v2",
      "rewrite": "/api/v2/",
      "cache": false
    }
  ]
}
```

### 5. 高级重写功能示例

#### 基于请求头的条件重写
```json
{
  "pattern": "^/api/(.*)$",
  "upstream": "api_server",
  "rewrite_advanced": {
    "target": "/v2/api/$1",
    "conditions": [
      {
        "type": "header",
        "key": "X-API-Version",
        "value": "v1",
        "operator": "equals",
        "target": "/v1/api/$1"
      },
      {
        "type": "header",
        "key": "X-Legacy-Client",
        "value": "true",
        "operator": "equals",
        "target": "/legacy/api/$1"
      }
    ]
  }
}
```

#### 基于查询参数的重写
```json
{
  "pattern": "^/search$",
  "upstream": "search_server",
  "rewrite_advanced": {
    "target": "/search/v2",
    "conditions": [
      {
        "type": "query",
        "key": "version",
        "value": "v1",
        "operator": "equals",
        "target": "/search/v1"
      }
    ]
  }
}
```

#### 路径拼接和变量替换
```json
{
  "pattern": "^/files/(.*)$",
  "upstream": "file_server",
  "rewrite_advanced": {
    "target": "/storage/${env}/files",
    "append_path": true,
    "variables": {
      "env": "production",
      "bucket": "main-storage"
    }
  }
}
```

#### 复杂的正则捕获和重写
```json
{
  "pattern": "^/user/([0-9]+)/profile/(.*)$",
  "upstream": "user_service",
  "rewrite_advanced": {
    "target": "/users/$1/profile-data/$2",
    "variables": {
      "service": "user-management",
      "version": "v3"
    }
  }
}
```

## ⚠️ 注意事项

### 1. 正则表达式语法
- 使用标准的正则表达式语法
- 特殊字符需要转义：`\\.` 表示字面量的点
- 建议使用 `^` 和 `$` 明确匹配边界

### 2. 路由顺序很重要
- 具体的规则必须放在通用规则之前
- 兜底规则 `^/` 应该放在最后
- 错误的顺序可能导致规则被意外覆盖

### 3. 缓存策略
- API接口通常设置 `cache: false`
- 静态资源通常设置 `cache: true`
- 动态页面根据业务需求决定

### 4. 性能考虑
- 避免过于复杂的正则表达式
- 常用的路由规则放在前面
- 合理使用静态文件模式减少代理开销

## 🔧 调试技巧

### 1. 路由匹配测试
```bash
# 查看日志中的路由匹配信息
tail -f logs/proxy.log | grep "ROUTE"
```

### 2. 常见问题排查
- **404错误**: 检查是否有匹配的路由规则
- **缓存问题**: 确认 `cache` 字段设置
- **转发错误**: 验证 `upstream` 配置是否正确
- **重写问题**: 检查 `rewrite` 规则语法

### 3. 配置验证
- 使用JSON格式验证工具检查语法
- 测试正则表达式匹配效果
- 逐步添加路由规则进行测试

## 📝 配置示例模板

### 基础模板
```json
{
  "sites": [
    {
      "name": "your_site",
      "domains": ["example.com"],
      "routes": [
        {
          "pattern": "^/",
          "upstream": "main_server",
          "cache": true
        }
      ]
    }
  ]
}
```

### 完整功能模板
```json
{
  "sites": [
    {
      "name": "full_featured_site",
      "domains": ["example.com"],
      "routes": [
        {
          "pattern": "^/api/",
          "upstream": "api_server",
          "cache": false,
          "error_pages": [
            {
              "code": 404,
              "content": "{\"error\": \"Not found\"}"
            }
          ]
        },
        {
          "pattern": "^/static/",
          "static_dir": "/var/www/static",
          "cache": true,
          "dir_listing": false
        },
        {
          "pattern": "^/downloads/",
          "static_dir": "/var/www/downloads",
          "cache": true,
          "dir_listing": true,
          "rate_limit": "1MB"
        },
        {
          "pattern": "^/legacy/",
          "upstream": "main_server",
          "rewrite": "/new/",
          "cache": true
        },
        {
          "pattern": "^/",
          "upstream": "main_server",
          "cache": true
        }
      ]
    }
  ]
}
```

## 🚀 高级用法

### 1. 多域名不同路由
```json
{
  "sites": [
    {
      "name": "api_site",
      "domains": ["api.example.com"],
      "routes": [
        {
          "pattern": "^/",
          "upstream": "api_server",
          "cache": false
        }
      ]
    },
    {
      "name": "www_site",
      "domains": ["www.example.com"],
      "routes": [
        {
          "pattern": "^/",
          "upstream": "web_server",
          "cache": true
        }
      ]
    }
  ]
}
```

### 2. 文件类型路由
```json
{
  "routes": [
    {
      "pattern": "\\.(jpg|jpeg|png|gif|webp)$",
      "static_dir": "/var/www/images",
      "cache": true
    },
    {
      "pattern": "\\.(css|js)$",
      "static_dir": "/var/www/assets",
      "cache": true
    },
    {
      "pattern": "\\.(pdf|doc|docx)$",
      "static_dir": "/var/www/documents",
      "cache": true,
      "rate_limit": "500KB"
    }
  ]
}
```

### 3. 条件路由示例
```json
{
  "routes": [
    {
      "pattern": "^/mobile/",
      "upstream": "mobile_server",
      "cache": true
    },
    {
      "pattern": "^/admin/",
      "upstream": "admin_server",
      "cache": false
    },
    {
      "pattern": "^/",
      "upstream": "main_server",
      "cache": true
    }
  ]
}
```

## 🔧 重写变量参考

### 内置变量

| 变量 | 描述 | 示例 |
|------|------|------|
| `$1`, `$2`, `$3`... | 正则捕获组 | 模式 `^/api/(.*)$` 匹配 `/api/users` 时，`$1` = `users` |
| `$path` | 完整请求路径 | `/api/users/123` |
| `$remaining_path` | 匹配后的剩余路径 | 模式 `^/api/` 匹配后剩余 `users/123` |
| `$query` | 查询字符串 | `page=1&size=10` |
| `$fragment` | URL片段 | `#section1` |
| `$method` | HTTP方法 | `GET`, `POST`, `PUT` |
| `$host` | 请求主机名 | `api.example.com` |
| `$scheme` | 请求协议 | `http`, `https` |
| `$year` | 当前年份 | `2024` |
| `$month` | 当前月份 | `06` |
| `$day` | 当前日期 | `27` |
| `$timestamp` | Unix时间戳 | `1719504000` |

### 条件操作符

| 操作符 | 描述 | 示例 |
|--------|------|------|
| `equals`, `eq`, `=` | 完全匹配 | `value: "v1"` |
| `not_equals`, `ne`, `!=` | 不等于 | `value: "v2"` |
| `contains` | 包含 | `value: "mobile"` |
| `not_contains` | 不包含 | `value: "bot"` |
| `starts_with` | 以...开始 | `value: "api"` |
| `ends_with` | 以...结束 | `value: ".json"` |
| `matches`, `regex` | 正则匹配 | `value: "^v[0-9]+$"` |
| `exists` | 存在 | 检查头部或参数是否存在 |
| `not_exists` | 不存在 | 检查头部或参数是否不存在 |
| `greater_than`, `gt` | 大于 | `value: "100"` |
| `less_than`, `lt` | 小于 | `value: "1000"` |

### 条件类型

| 类型 | 描述 | Key示例 |
|------|------|---------|
| `header` | HTTP请求头 | `X-API-Version`, `User-Agent` |
| `query` | 查询参数 | `version`, `format` |
| `path` | 请求路径 | 用于路径模式匹配 |
| `method` | HTTP方法 | 用于方法检查 |
| `host` | 主机名 | 用于主机检查 |

## 📚 相关文档

- [缓存配置说明](cache-configuration.md)
- [上游服务器配置](upstream-configuration.md)
- [站点配置说明](site-configuration.md)
- [错误页面配置](error-pages-configuration.md)

## 🆘 常见问题 FAQ

### Q: 为什么我的路由规则不生效？
A: 检查以下几点：
1. 正则表达式语法是否正确
2. 路由顺序是否合理（具体规则在前，通用规则在后）
3. JSON格式是否有语法错误

### Q: 如何测试路由匹配？
A: 可以使用在线正则表达式测试工具，或者查看服务器日志中的路由匹配信息。

### Q: 静态文件和代理模式可以混用吗？
A: 可以，不同的路由规则可以使用不同的模式，通过 `static_dir` 和 `upstream` 字段区分。

### Q: 路由重写如何工作？
A: 支持两种重写方式：
1. **简单重写**: `rewrite` 字段支持基本的路径替换和正则捕获组变量
2. **高级重写**: `rewrite_advanced` 字段支持条件重写、变量替换、路径拼接等复杂功能

### Q: 如何使用正则捕获组？
A: 在路由模式中使用括号捕获，在重写中使用 `$1`, `$2` 等变量：
```json
{
  "pattern": "^/api/v([0-9]+)/(.*)$",
  "rewrite": "/api/version-$1/$2"
}
```

### Q: 高级重写和简单重写可以同时使用吗？
A: 不可以。如果配置了 `rewrite_advanced`，会忽略 `rewrite` 字段。

### Q: 条件重写如何工作？
A: 条件重写按顺序检查条件，使用第一个匹配的条件的目标路径，如果没有条件匹配则使用默认目标。

---

**版本**: 1.0
**更新日期**: 2025-06-26
**适用版本**: 反向代理服务器 v1.0.0
**文档作者**: 系统管理员

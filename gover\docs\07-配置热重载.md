# 配置热重载

## 📋 概述

配置热重载功能通过实时监控配置文件变化，在不中断服务的情况下动态更新系统配置，实现零停机的配置管理，提升运维效率和系统可用性。

## 🎯 优化目标

- **零停机更新**：无需重启服务即可更新配置
- **实时生效**：配置变更立即生效
- **安全可靠**：配置验证防止错误配置
- **回滚机制**：支持配置回滚和恢复

## 🔧 技术实现

### 1. 文件监控机制

#### 配置监控器设计
```go
type ConfigWatcher struct {
    configFile    string
    lastMod       time.Time
    lastSize      int64
    checkInterval time.Duration
    callbacks     []ReloadCallback
    ctx           context.Context
    cancel        context.CancelFunc
    wg            sync.WaitGroup
    mu            sync.RWMutex
    stats         WatcherStats
}

type ReloadCallback func(*config.Config) error
```

#### 文件变化检测
```go
func (cw *ConfigWatcher) checkAndReload() error {
    stat, err := os.Stat(cw.configFile)
    if err != nil {
        return fmt.Errorf("获取文件状态失败: %w", err)
    }
    
    // 检查文件是否有变化
    if stat.ModTime().Equal(cw.lastMod) && stat.Size() == cw.lastSize {
        return nil // 文件未变化
    }
    
    fmt.Printf("[配置热重载] 检测到配置文件变化，开始重载...\n")
    
    // 加载新配置
    newConfig, err := config.Load(cw.configFile)
    if err != nil {
        return fmt.Errorf("加载新配置失败: %w", err)
    }
    
    // 验证配置
    if err := cw.validateConfig(newConfig); err != nil {
        return fmt.Errorf("配置验证失败: %w", err)
    }
    
    // 执行回调
    return cw.executeCallbacks(newConfig)
}
```

### 2. 配置验证机制

#### 多层验证策略
```go
func (cw *ConfigWatcher) validateConfig(cfg *config.Config) error {
    // 1. 基本结构验证
    if err := cw.validateBasicStructure(cfg); err != nil {
        return err
    }
    
    // 2. 业务逻辑验证
    if err := cw.validateBusinessLogic(cfg); err != nil {
        return err
    }
    
    // 3. 资源可用性验证
    if err := cw.validateResourceAvailability(cfg); err != nil {
        return err
    }
    
    return nil
}

func (cw *ConfigWatcher) validateBasicStructure(cfg *config.Config) error {
    if len(cfg.Sites) == 0 {
        return fmt.Errorf("至少需要配置一个站点")
    }
    
    if cfg.Server.HTTPPort <= 0 && cfg.Server.HTTPSPort <= 0 {
        return fmt.Errorf("至少需要配置HTTP或HTTPS端口")
    }
    
    return nil
}

func (cw *ConfigWatcher) validateBusinessLogic(cfg *config.Config) error {
    for _, site := range cfg.Sites {
        if site.Name == "" {
            return fmt.Errorf("站点名称不能为空")
        }
        
        if len(site.Domains) == 0 {
            return fmt.Errorf("站点 %s 至少需要配置一个域名", site.Name)
        }
        
        if len(site.Upstreams) == 0 {
            return fmt.Errorf("站点 %s 至少需要配置一个上游服务器", site.Name)
        }
    }
    
    return nil
}
```

#### SSL证书验证
```go
func (cw *ConfigWatcher) validateSSLCertificates(cfg *config.Config) error {
    for _, site := range cfg.Sites {
        if site.SSL.Enabled {
            if site.SSL.CertFile == "" || site.SSL.KeyFile == "" {
                return fmt.Errorf("站点 %s 启用SSL但未配置证书文件", site.Name)
            }
            
            // 检查证书文件是否存在
            if _, err := os.Stat(site.SSL.CertFile); err != nil {
                return fmt.Errorf("站点 %s 证书文件不存在: %s", site.Name, site.SSL.CertFile)
            }
            
            if _, err := os.Stat(site.SSL.KeyFile); err != nil {
                return fmt.Errorf("站点 %s 私钥文件不存在: %s", site.Name, site.SSL.KeyFile)
            }
            
            // 验证证书和私钥匹配
            if err := cw.validateCertificateKeyPair(site.SSL.CertFile, site.SSL.KeyFile); err != nil {
                return fmt.Errorf("站点 %s 证书和私钥不匹配: %w", site.Name, err)
            }
        }
    }
    
    return nil
}
```

### 3. 热重载执行机制

#### 回调管理
```go
func (cw *ConfigWatcher) AddCallback(callback ReloadCallback) {
    cw.mu.Lock()
    defer cw.mu.Unlock()
    cw.callbacks = append(cw.callbacks, callback)
}

func (cw *ConfigWatcher) executeCallbacks(newConfig *config.Config) error {
    cw.mu.RLock()
    callbacks := make([]ReloadCallback, len(cw.callbacks))
    copy(callbacks, cw.callbacks)
    cw.mu.RUnlock()
    
    for i, callback := range callbacks {
        if err := callback(newConfig); err != nil {
            return fmt.Errorf("执行第%d个重载回调失败: %w", i+1, err)
        }
    }
    
    return nil
}
```

#### 代理服务器重载
```go
func (p *Proxy) reloadConfig(newConfig *config.Config) error {
    p.logger.Info("开始重载配置...")
    
    // 1. 更新SSL证书映射
    if p.sslManager != nil {
        p.sslManager.UpdateDomainCerts(newConfig.Sites)
        p.sslManager.ReloadCertificates()
    }
    
    // 2. 更新限流配置
    if newConfig.RateLimit.Enabled && p.rateLimiter == nil {
        p.rateLimiter = ratelimit.NewRateLimitManager(ratelimit.RateLimitConfig{
            GlobalRPS: newConfig.RateLimit.GlobalRPS,
            IPRPS:     newConfig.RateLimit.IPRPS,
            SiteRPS:   newConfig.RateLimit.SiteRPS,
            Burst:     newConfig.RateLimit.Burst,
        })
    } else if p.rateLimiter != nil {
        p.rateLimiter.UpdateConfig(ratelimit.RateLimitConfig{
            GlobalRPS: newConfig.RateLimit.GlobalRPS,
            IPRPS:     newConfig.RateLimit.IPRPS,
            SiteRPS:   newConfig.RateLimit.SiteRPS,
            Burst:     newConfig.RateLimit.Burst,
        })
    }
    
    // 3. 更新负载均衡配置
    p.updateLoadBalancerConfig(newConfig)
    
    // 4. 更新缓存配置
    p.updateCacheConfig(newConfig)
    
    // 5. 更新配置引用
    p.config = newConfig
    
    p.logger.Info("配置重载完成")
    return nil
}
```

### 4. 统计和监控

#### 重载统计
```go
type WatcherStats struct {
    TotalReloads    int64     `json:"total_reloads"`
    SuccessReloads  int64     `json:"success_reloads"`
    FailedReloads   int64     `json:"failed_reloads"`
    LastReloadTime  time.Time `json:"last_reload_time"`
    LastError       string    `json:"last_error"`
    WatchStartTime  time.Time `json:"watch_start_time"`
}

func (cw *ConfigWatcher) updateStats(success bool, errorMsg string) {
    cw.mu.Lock()
    defer cw.mu.Unlock()
    
    cw.stats.TotalReloads++
    cw.stats.LastReloadTime = time.Now()
    
    if success {
        cw.stats.SuccessReloads++
        cw.stats.LastError = ""
    } else {
        cw.stats.FailedReloads++
        cw.stats.LastError = errorMsg
    }
}
```

## ⚙️ 配置说明

### 基础热重载配置
```json
{
  "hot_reload": {
    "enabled": true,
    "check_interval": "5s"
  }
}
```

### 高级热重载配置
```json
{
  "hot_reload": {
    "enabled": true,
    "check_interval": "5s",
    "validation": {
      "strict_mode": true,
      "check_certificates": true,
      "check_upstream_connectivity": false
    },
    "backup": {
      "enabled": true,
      "backup_dir": "./config_backups",
      "max_backups": 10
    },
    "notifications": {
      "enabled": true,
      "webhook_url": "http://your-webhook/config-reload",
      "email": "<EMAIL>"
    }
  }
}
```

### 参数详解

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enabled` | bool | false | 是否启用热重载 |
| `check_interval` | duration | 5s | 检查间隔时间 |
| `strict_mode` | bool | true | 严格验证模式 |
| `check_certificates` | bool | true | 验证SSL证书 |
| `backup_dir` | string | ./backups | 配置备份目录 |
| `max_backups` | int | 10 | 最大备份数量 |

## 📊 监控指标

### 热重载统计
```json
{
  "hot_reload": {
    "enabled": true,
    "total_reloads": 25,
    "success_reloads": 23,
    "failed_reloads": 2,
    "success_rate": 0.92,
    "last_reload_time": "2024-01-01T12:30:00Z",
    "last_error": "",
    "watch_start_time": "2024-01-01T10:00:00Z",
    "check_interval": "5s"
  }
}
```

### 配置变更历史
```json
{
  "config_history": [
    {
      "timestamp": "2024-01-01T12:30:00Z",
      "action": "reload",
      "status": "success",
      "changes": [
        "sites.example.com.upstreams[0].weight: 100 -> 150",
        "rate_limit.global_rps: 1000 -> 1500"
      ]
    },
    {
      "timestamp": "2024-01-01T11:45:00Z",
      "action": "reload",
      "status": "failed",
      "error": "SSL证书文件不存在"
    }
  ]
}
```

## 🔍 故障排查

### 常见问题

#### 1. 配置重载失败
**现象**：配置文件修改后未生效
**排查**：
```bash
# 检查热重载状态
curl -u admin:password http://localhost:8080/stats | jq '.hot_reload'

# 查看重载历史
curl -u admin:password http://localhost:8080/config/history

# 检查配置文件语法
./reverse-proxy -config config.json -validate-only
```

**解决**：
- 检查配置文件语法
- 验证文件权限
- 查看错误日志

#### 2. 证书重载失败
**现象**：SSL证书更新后HTTPS访问失败
**排查**：
```bash
# 验证证书文件
openssl x509 -in cert.pem -text -noout

# 检查证书和私钥匹配
openssl x509 -noout -modulus -in cert.pem | openssl md5
openssl rsa -noout -modulus -in key.pem | openssl md5

# 测试SSL连接
openssl s_client -connect your-domain:443 -servername your-domain
```

#### 3. 性能影响
**现象**：热重载导致请求处理缓慢
**解决**：
- 增加检查间隔时间
- 优化配置验证逻辑
- 使用异步重载机制

## 🚀 最佳实践

### 1. 配置版本管理
```bash
#!/bin/bash
# 配置版本管理脚本

CONFIG_DIR="/etc/reverse-proxy"
BACKUP_DIR="/etc/reverse-proxy/backups"
CONFIG_FILE="config.json"

# 备份当前配置
backup_config() {
    timestamp=$(date +%Y%m%d_%H%M%S)
    cp "$CONFIG_DIR/$CONFIG_FILE" "$BACKUP_DIR/config_$timestamp.json"
    
    # 保留最近10个备份
    ls -t "$BACKUP_DIR"/config_*.json | tail -n +11 | xargs rm -f
}

# 验证配置
validate_config() {
    ./reverse-proxy -config "$CONFIG_DIR/$CONFIG_FILE" -validate-only
}

# 部署配置
deploy_config() {
    if validate_config; then
        backup_config
        echo "配置验证通过，已自动重载"
    else
        echo "配置验证失败，请检查配置文件"
        exit 1
    fi
}

deploy_config
```

### 2. 渐进式重载
```go
func (p *Proxy) gradualReload(newConfig *config.Config) error {
    // 1. 先更新不影响流量的配置
    p.updateNonCriticalConfig(newConfig)
    
    // 2. 逐步更新关键配置
    if err := p.updateSSLConfig(newConfig); err != nil {
        return err
    }
    
    if err := p.updateUpstreamConfig(newConfig); err != nil {
        return err
    }
    
    // 3. 最后更新路由配置
    return p.updateRoutingConfig(newConfig)
}
```

### 3. 配置差异检测
```go
type ConfigDiff struct {
    Added    []string `json:"added"`
    Modified []string `json:"modified"`
    Removed  []string `json:"removed"`
}

func (cw *ConfigWatcher) detectConfigChanges(oldConfig, newConfig *config.Config) *ConfigDiff {
    diff := &ConfigDiff{}
    
    // 检测站点变化
    oldSites := make(map[string]*config.SiteConfig)
    for _, site := range oldConfig.Sites {
        oldSites[site.Name] = &site
    }
    
    for _, newSite := range newConfig.Sites {
        if oldSite, exists := oldSites[newSite.Name]; exists {
            if !reflect.DeepEqual(oldSite, &newSite) {
                diff.Modified = append(diff.Modified, "site:"+newSite.Name)
            }
        } else {
            diff.Added = append(diff.Added, "site:"+newSite.Name)
        }
    }
    
    return diff
}
```

### 4. 回滚机制
```go
type ConfigRollback struct {
    backupConfigs []config.Config
    maxBackups    int
}

func (cr *ConfigRollback) SaveBackup(cfg *config.Config) {
    cr.backupConfigs = append(cr.backupConfigs, *cfg)
    
    // 保留最近的备份
    if len(cr.backupConfigs) > cr.maxBackups {
        cr.backupConfigs = cr.backupConfigs[1:]
    }
}

func (cr *ConfigRollback) Rollback(steps int) (*config.Config, error) {
    if steps <= 0 || steps > len(cr.backupConfigs) {
        return nil, fmt.Errorf("无效的回滚步数")
    }
    
    index := len(cr.backupConfigs) - steps
    return &cr.backupConfigs[index], nil
}
```

## 📈 性能测试

### 热重载性能测试
```bash
#!/bin/bash
# 热重载性能测试

# 测试重载时间
test_reload_time() {
    echo "测试配置重载时间:"
    
    for i in {1..10}; do
        start_time=$(date +%s%N)
        
        # 修改配置文件
        sed -i "s/\"weight\": 100/\"weight\": $((100 + i))/g" config.json
        
        # 等待重载完成
        sleep 6
        
        end_time=$(date +%s%N)
        reload_time=$(((end_time - start_time) / 1000000))
        
        echo "第${i}次重载耗时: ${reload_time}ms"
    done
}

# 测试重载期间的服务可用性
test_availability_during_reload() {
    echo "测试重载期间服务可用性:"
    
    # 启动并发请求
    ab -n 1000 -c 10 http://localhost/ &
    ab_pid=$!
    
    # 在请求过程中重载配置
    sleep 2
    sed -i "s/\"weight\": 100/\"weight\": 200/g" config.json
    
    # 等待测试完成
    wait $ab_pid
    
    echo "重载期间服务可用性测试完成"
}

test_reload_time
test_availability_during_reload
```

## 🔧 高级功能

### 1. 分布式配置同步
```go
type DistributedConfigSync struct {
    etcdClient *clientv3.Client
    configKey  string
}

func (dcs *DistributedConfigSync) WatchRemoteConfig() {
    watchChan := dcs.etcdClient.Watch(context.Background(), dcs.configKey)
    
    for watchResp := range watchChan {
        for _, event := range watchResp.Events {
            if event.Type == clientv3.EventTypePut {
                newConfig := &config.Config{}
                if err := json.Unmarshal(event.Kv.Value, newConfig); err == nil {
                    dcs.applyRemoteConfig(newConfig)
                }
            }
        }
    }
}
```

### 2. 配置模板系统
```go
type ConfigTemplate struct {
    template *template.Template
    vars     map[string]interface{}
}

func (ct *ConfigTemplate) RenderConfig() (*config.Config, error) {
    var buf bytes.Buffer
    if err := ct.template.Execute(&buf, ct.vars); err != nil {
        return nil, err
    }
    
    cfg := &config.Config{}
    if err := json.Unmarshal(buf.Bytes(), cfg); err != nil {
        return nil, err
    }
    
    return cfg, nil
}
```

### 3. A/B测试配置
```go
type ABTestConfig struct {
    configA *config.Config
    configB *config.Config
    ratio   float64  // A配置的流量比例
}

func (ab *ABTestConfig) GetConfigForRequest(r *http.Request) *config.Config {
    hash := crc32.ChecksumIEEE([]byte(r.RemoteAddr))
    if float64(hash%100)/100 < ab.ratio {
        return ab.configA
    }
    return ab.configB
}
```

配置热重载是现代服务管理的重要功能，实现零停机的配置更新，提升系统的可维护性和运维效率。

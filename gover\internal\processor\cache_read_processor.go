package processor

import (
	"strings"
	"time"
	"reverse-proxy/internal/cache"
	"github.com/sirupsen/logrus"
)

// CacheReadProcessor 缓存读取处理器 - 负责从缓存中读取内容
type CacheReadProcessor struct {
	*BaseProcessor
	enabled      bool
	cacheManager *cache.CacheManager
}

// NewCacheReadProcessor 创建新的缓存读取处理器
func NewCacheReadProcessor(enabled bool, cacheManager *cache.CacheManager, logger *logrus.Logger) *CacheReadProcessor {
	return &CacheReadProcessor{
		BaseProcessor: NewBaseProcessor("CacheReadProcessor", 150, logger),
		enabled:       enabled,
		cacheManager:  cacheManager,
	}
}

// Process 处理缓存读取逻辑
func (crp *CacheReadProcessor) Process(req *RequestContext, resp *ResponseContext) *ProcessResult {
	// 使用数字标识进行高效判断
	if req.CacheReadFlag == 0 || !crp.enabled || !req.CacheEnabled {
		crp.LogDebug("跳过缓存读取: CacheReadFlag=%d, enabled=%v, CacheEnabled=%v", 
			req.CacheReadFlag, crp.enabled, req.CacheEnabled)
		return Success() // 缓存读取被跳过，继续处理
	}

	// 只缓存GET和HEAD请求
	if req.Method != "GET" && req.Method != "HEAD" {
		return Success()
	}

	// 生成缓存键
	cacheKey := crp.generateCacheKey(req)
	req.CacheKey = cacheKey

	// 尝试从缓存读取
	if cached, found := crp.cacheManager.Get(cacheKey); found {
		// 缓存命中
		resp.StatusCode = cached.StatusCode
		resp.Body = cached.Body
		resp.ContentType = crp.getContentTypeFromHeaders(cached.Headers)
		resp.ContentLength = int64(len(cached.Body))
		resp.CacheStatus = "HIT"
		resp.CacheSource = "FILE"
		resp.BackendIP = cached.BackendIP
		resp.BackendPort = cached.BackendPort

		// 设置内容来源为缓存，跳过后续处理
		req.ContentSource = "cache"
		req.StaticFlag = 0      // 跳过静态文件处理
		req.ProxyFlag = 0       // 跳过代理处理
		req.MinifyFlag = 0      // 跳过Minify处理（缓存的内容已经是处理过的）
		req.CacheWriteFlag = 0  // 跳过缓存写入
		req.ErrorFlag = 1       // 保持错误处理
		req.HeaderFlag = 1      // 保持头部处理

		// 兼容性设置
		req.SkipUpstream = true
		req.SkipMinify = true

		// 复制头部
		for key, values := range cached.Headers {
			for _, value := range values {
				resp.Headers.Add(key, value)
			}
		}

		crp.LogDebug("缓存命中: %s", cacheKey)

		return SuccessWithMetadata(map[string]interface{}{
			"cache_key": cacheKey,
			"cache_age": time.Since(cached.CachedAt),
			"expire_at": cached.ExpireAt,
		})
	} else {
		// 缓存未命中
		resp.CacheStatus = "MISS"
		crp.LogDebug("缓存未命中: %s", cacheKey)
	}

	return Success() // 继续处理，后续会在缓存写入阶段缓存结果
}

// ShouldProcess 判断是否应该处理
func (crp *CacheReadProcessor) ShouldProcess(req *RequestContext) bool {
	return crp.enabled && req.CacheEnabled
}

// generateCacheKey 生成缓存键
func (crp *CacheReadProcessor) generateCacheKey(req *RequestContext) string {
	// 使用strings.Builder进行高效的字符串拼接
	var builder strings.Builder

	// 预估容量以减少内存分配
	estimatedLen := len(req.SiteID) + len(req.Method) + len(req.URL.Path) + len(req.URL.RawQuery) + 4
	builder.Grow(estimatedLen)

	builder.WriteString(req.SiteID)
	builder.WriteByte('|')
	builder.WriteString(req.Method)
	builder.WriteByte('|')
	builder.WriteString(req.URL.Path)

	// 添加查询参数（如果有）
	if req.URL.RawQuery != "" {
		builder.WriteByte('|')
		builder.WriteString(req.URL.RawQuery)
	}

	return builder.String()
}

// getContentTypeFromHeaders 从头部获取Content-Type
func (crp *CacheReadProcessor) getContentTypeFromHeaders(headers map[string][]string) string {
	if contentTypes, exists := headers["Content-Type"]; exists && len(contentTypes) > 0 {
		return contentTypes[0]
	}
	return "application/octet-stream"
}

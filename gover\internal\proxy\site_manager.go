package proxy

import (
	"fmt"
	"sync"

	"reverse-proxy/internal/config"
	"reverse-proxy/internal/logger"
	"github.com/sirupsen/logrus"
)

// SiteManager 站点管理器
type SiteManager struct {
	sites  map[string]*Site // key: site_id, value: Site实例
	domains map[string]string // key: domain, value: site_id (域名到站点ID的映射)
	mu     sync.RWMutex
	logger *logrus.Logger
	proxy  *Proxy // 代理实例引用，用于创建站点
}

// NewSiteManager 创建站点管理器
func NewSiteManager(proxy *Proxy) *SiteManager {
	return &SiteManager{
		sites:   make(map[string]*Site),
		domains: make(map[string]string),
		logger:  proxy.logger,
		proxy:   proxy,
	}
}

// LoadSites 加载站点配置（初始化或热重载）
func (sm *SiteManager) LoadSites(siteConfigs []config.SiteConfig) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	sm.logger.Infof("开始加载站点配置，站点数量: %d", len(siteConfigs))

	// 1. 收集新配置中的所有站点ID
	newSiteIDs := make(map[string]bool)
	for _, siteConfig := range siteConfigs {
		newSiteIDs[siteConfig.SiteID] = true
	}

	// 2. 删除配置中不存在的站点
	for siteID, site := range sm.sites {
		if !newSiteIDs[siteID] {
			sm.logger.Infof("删除不存在的站点: %s (ID: %s)", site.config.Name, siteID)
			sm.removeSiteUnsafe(siteID)
		}
	}

	// 3. 注册或更新每个站点
	for _, siteConfig := range siteConfigs {
		if err := sm.registerOrUpdateSiteUnsafe(&siteConfig); err != nil {
			return fmt.Errorf("注册/更新站点 %s (ID: %s) 失败: %w", siteConfig.Name, siteConfig.SiteID, err)
		}
	}

	sm.logger.Infof("站点配置加载完成，当前站点数量: %d", len(sm.sites))
	return nil
}

// registerOrUpdateSiteUnsafe 注册或更新站点（内部方法，不加锁）
func (sm *SiteManager) registerOrUpdateSiteUnsafe(siteConfig *config.SiteConfig) error {
	siteID := siteConfig.SiteID
	
	// 检查站点是否已存在
	if existingSite, exists := sm.sites[siteID]; exists {
		// 站点已存在，更新配置
		sm.logger.Infof("更新站点配置: %s (ID: %s)", siteConfig.Name, siteID)
		
		// 先清理旧的域名映射
		sm.clearDomainMappingsForSiteUnsafe(siteID)
		
		// 关闭旧站点的资源
		if err := sm.closeSiteResourcesUnsafe(existingSite); err != nil {
			sm.logger.Warnf("关闭旧站点资源失败: %v", err)
		}
	} else {
		// 新站点
		sm.logger.Infof("注册新站点: %s (ID: %s)", siteConfig.Name, siteID)
	}

	// 创建新的站点实例
	site, err := sm.proxy.createSite(siteConfig)
	if err != nil {
		return fmt.Errorf("创建站点实例失败: %w", err)
	}



	// 注册站点
	sm.sites[siteID] = site

	// 创建域名映射
	for _, domain := range siteConfig.Domains {
		sm.domains[domain] = siteID
		sm.logger.Debugf("创建域名映射: %s -> %s", domain, siteID)
	}

	// 更新站点级连接数限制
	if siteConfig.MaxConnections > 0 && sm.proxy.connectionLimiter != nil {
		sm.proxy.connectionLimiter.SetSiteLimit(siteID, siteConfig.MaxConnections)
	}

	// 创建或更新站点日志器
	if len(siteConfig.LogTargets) > 0 {
		sm.proxy.loggers[siteID] = logger.NewMultiLogger(siteConfig.LogTargets, map[string]string{"site": siteID}).(logger.LoggerInterface)
		sm.logger.Debugf("为站点 %s (ID: %s) 创建了新的日志器", siteConfig.Name, siteID)
	}

	// 添加或更新监控站点
	if sm.proxy.monitor != nil {
		sm.proxy.monitor.AddSite(siteID, siteConfig.Name)
		sm.logger.Debugf("为站点添加监控: %s (ID: %s)", siteConfig.Name, siteID)
	}

	return nil
}

// removeSiteUnsafe 删除站点（内部方法，不加锁）
func (sm *SiteManager) removeSiteUnsafe(siteID string) {
	site, exists := sm.sites[siteID]
	if !exists {
		return
	}

	// 清理域名映射
	sm.clearDomainMappingsForSiteUnsafe(siteID)

	// 关闭站点资源
	if err := sm.closeSiteResourcesUnsafe(site); err != nil {
		sm.logger.Warnf("关闭站点资源失败: %v", err)
	}

	// 清理站点相关资源
	if logger, exists := sm.proxy.loggers[siteID]; exists {
		if closer, ok := logger.(interface{ Close() error }); ok {
			closer.Close()
		}
		delete(sm.proxy.loggers, siteID)
	}

	// 从站点映射中删除
	delete(sm.sites, siteID)
}

// clearDomainMappingsForSiteUnsafe 清理站点的域名映射（内部方法，不加锁）
func (sm *SiteManager) clearDomainMappingsForSiteUnsafe(siteID string) {
	domainsToDelete := make([]string, 0)
	for domain, mappedSiteID := range sm.domains {
		if mappedSiteID == siteID {
			domainsToDelete = append(domainsToDelete, domain)
		}
	}
	
	for _, domain := range domainsToDelete {
		delete(sm.domains, domain)
		sm.logger.Debugf("删除域名映射: %s -> %s", domain, siteID)
	}
}

// closeSiteResourcesUnsafe 关闭站点资源（内部方法，不加锁）
func (sm *SiteManager) closeSiteResourcesUnsafe(site *Site) error {
	// 关闭站点ACL
	if site.acl != nil {
		if err := site.acl.Close(); err != nil {
			return fmt.Errorf("关闭站点ACL失败: %w", err)
		}
		sm.logger.Debug("关闭旧站点ACL")
	}
	return nil
}

// FindSiteByDomain 通过域名查找站点
func (sm *SiteManager) FindSiteByDomain(domain string) *Site {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	// 首先尝试精确匹配域名
	if siteID, exists := sm.domains[domain]; exists {
		if site, exists := sm.sites[siteID]; exists {
			sm.logger.Debugf("找到站点映射: %s -> %s (ACL实例地址: %p)", domain, siteID, site.acl)
			return site
		}
	}

	// 如果没有找到匹配的域名，查找默认站点
	for _, site := range sm.sites {
		if site.config.DefaultSite {
			sm.logger.Debugf("使用默认站点: %s (ID: %s) 处理请求 Host: %s", site.config.Name, site.config.SiteID, domain)
			return site
		}
	}

	sm.logger.Debugf("未找到站点映射且无默认站点: %s", domain)
	return nil
}

// GetAllSites 获取所有站点（用于服务器处理器更新）
func (sm *SiteManager) GetAllSites() map[string]*Site {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	// 返回副本以避免并发问题
	sites := make(map[string]*Site)
	for siteID, site := range sm.sites {
		sites[siteID] = site
	}
	return sites
}

// GetSiteCount 获取站点数量
func (sm *SiteManager) GetSiteCount() int {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	return len(sm.sites)
}

// GetSiteByID 通过站点ID获取站点
func (sm *SiteManager) GetSiteByID(siteID string) *Site {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	return sm.sites[siteID]
}

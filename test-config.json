{"server": {"listen": [{"address": "0.0.0.0", "port": 8080, "protocol": "http"}], "worker_threads": 4, "max_connections": 10000, "keep_alive_timeout": "60s", "request_timeout": "30s", "send_timeout": "30s"}, "logging": {"level": "info", "format": "text", "outputs": [{"type": "console"}]}, "ssl": {"enabled": false}, "sites": [{"site_id": "qiank-site", "name": "qiank.com", "domains": ["qiank.com"], "default_site": true, "max_connections": 3000, "debug_mode": false, "ssl": {"enabled": false}, "upstreams": [{"name": "backend_server", "address": "**************", "port": 80, "protocol": "http", "https_port": 443, "weight": 100, "max_fails": 3, "fail_timeout": "30s", "backup": false, "load_balance_group": "main_group"}], "routes": [{"pattern": "^/", "upstream": "main_group", "cache": false}]}]}
{"server": {"http_port": 8080, "https_port": 8443, "read_timeout": "30s", "write_timeout": "30s", "idle_timeout": "60s", "max_connections": 1000}, "logging": {"level": "info", "format": "text", "outputs": [{"type": "console"}]}, "ssl": {"enabled": false}, "sites": [{"site_id": "qiank-site", "name": "qiank.com", "domains": ["qiank.com"], "default_site": true, "max_connections": 3000, "debug_mode": false, "ssl": {"enabled": false}, "upstreams": [{"name": "backend_server", "address": "**************", "port": 80, "protocol": "http", "https_port": 443, "weight": 100, "max_fails": 3, "fail_timeout": "30s", "backup": false, "load_balance_group": "main_group"}], "routes": [{"pattern": "^/", "upstream": "main_group", "cache": false}]}]}
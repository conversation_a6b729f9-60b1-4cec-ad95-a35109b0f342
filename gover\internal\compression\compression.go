package compression

import (
	"bytes"
	"compress/flate"
	"compress/gzip"
	"fmt"
	"io"
	"net/http"
	"strings"
	"sync"
	"time"

	"reverse-proxy/internal/config"

	"github.com/andybalholm/brotli"
	"github.com/klauspost/compress/zstd"
)

// CompressionType 压缩类型
type CompressionType int

const (
	CompressionNone CompressionType = iota
	CompressionGzip
	CompressionBrotli
	CompressionDeflate
	CompressionZstd
)

// CompressionAlgorithm 压缩算法信息
type CompressionAlgorithm struct {
	Name     string
	Encoding string
	Priority int // 优先级，数字越小优先级越高
}

var SupportedAlgorithms = []CompressionAlgorithm{
	{"zstd", "zstd", 1},       // Zstandard最高优先级
	{"br", "br", 2},           // Brotli次之
	{"gzip", "gzip", 3},       // Gzip第三
	{"deflate", "deflate", 4}, // Deflate最低
}

// CompressibleWriter 可压缩的响应写入器
type CompressibleWriter struct {
	http.ResponseWriter
	writer          io.Writer
	config          config.CompressionConfig
	written         bool
	compressionType CompressionType
	compressor      io.WriteCloser
	encoding        string // 保存压缩编码名称
}

// NewCompressibleWriter 创建可压缩的响应写入器
func NewCompressibleWriter(w http.ResponseWriter, r *http.Request, cfg config.CompressionConfig) *CompressibleWriter {
	if !cfg.Enabled {
		return &CompressibleWriter{
			ResponseWriter:  w,
			writer:          w,
			config:          cfg,
			compressionType: CompressionNone,
		}
	}

	// 选择最佳压缩算法
	compressionType, encoding := selectBestCompression(r, cfg)
	if compressionType == CompressionNone {
		return &CompressibleWriter{
			ResponseWriter:  w,
			writer:          w,
			config:          cfg,
			compressionType: CompressionNone,
		}
	}

	// 修复：延迟创建压缩器和设置头部，等到第一次Write时再决定
	// 这样可以根据实际的Content-Type来决定是否压缩
	return &CompressibleWriter{
		ResponseWriter:  w,
		writer:          w, // 初始时使用原始writer
		config:          cfg,
		compressionType: compressionType, // 保存预选的压缩类型
		compressor:      nil,             // 延迟创建
		encoding:        encoding,        // 保存编码名称
	}
}

// selectBestCompression 选择最佳压缩算法
func selectBestCompression(r *http.Request, cfg config.CompressionConfig) (CompressionType, string) {
	acceptEncoding := cleanAcceptEncodingHeader(r.Header.Get("Accept-Encoding"))
	if acceptEncoding == "" {
		return CompressionNone, ""
	}

	// 如果配置了特定算法，按配置优先级选择
	if len(cfg.Algorithms) > 0 {
		for _, algorithm := range cfg.Algorithms {
			if isEncodingSupported(acceptEncoding, algorithm) {
				switch algorithm {
				case "zstd":
					return CompressionZstd, "zstd"
				case "br":
					return CompressionBrotli, "br"
				case "gzip":
					return CompressionGzip, "gzip"
				case "deflate":
					return CompressionDeflate, "deflate"
				}
			}
		}
	} else {
		// 默认按优先级选择
		for _, alg := range SupportedAlgorithms {
			if isEncodingSupported(acceptEncoding, alg.Encoding) {
				switch alg.Name {
				case "zstd":
					return CompressionZstd, "zstd"
				case "br":
					return CompressionBrotli, "br"
				case "gzip":
					return CompressionGzip, "gzip"
				case "deflate":
					return CompressionDeflate, "deflate"
				}
			}
		}
	}

	return CompressionNone, ""
}

// SelectBestCompression 选择最佳压缩算法
func SelectBestCompression(acceptEncoding string, cfg config.CompressionConfig) (CompressionType, string) {
	if !cfg.Enabled {
		return CompressionNone, ""
	}

	// 清理Accept-Encoding头部
	acceptEncoding = cleanAcceptEncodingHeader(acceptEncoding)

	// 如果acceptEncoding为空，选择配置中优先级最高的算法（用于存储）
	if acceptEncoding == "" {
		if len(cfg.Algorithms) > 0 {
			// 使用配置中的第一个算法（优先级最高）
			algorithm := cfg.Algorithms[0]
			switch algorithm {
			case "zstd":
				return CompressionZstd, "zstd"
			case "br":
				return CompressionBrotli, "br"
			case "gzip":
				return CompressionGzip, "gzip"
			case "deflate":
				return CompressionDeflate, "deflate"
			}
		} else {
			// 使用默认最高优先级算法
			return CompressionZstd, "zstd"
		}
	}

	// 如果配置了特定算法，按优先级检查
	if len(cfg.Algorithms) > 0 {
		for _, algorithm := range cfg.Algorithms {
			if isEncodingSupported(acceptEncoding, algorithm) {
				switch algorithm {
				case "zstd":
					return CompressionZstd, "zstd"
				case "br":
					return CompressionBrotli, "br"
				case "gzip":
					return CompressionGzip, "gzip"
				case "deflate":
					return CompressionDeflate, "deflate"
				}
			}
		}
	} else {
		// 使用默认优先级
		for _, alg := range SupportedAlgorithms {
			if isEncodingSupported(acceptEncoding, alg.Encoding) {
				switch alg.Name {
				case "zstd":
					return CompressionZstd, "zstd"
				case "br":
					return CompressionBrotli, "br"
				case "gzip":
					return CompressionGzip, "gzip"
				case "deflate":
					return CompressionDeflate, "deflate"
				}
			}
		}
	}

	return CompressionNone, ""
}

// isEncodingSupported 检查Accept-Encoding是否支持指定的编码
func isEncodingSupported(acceptEncoding, encoding string) bool {
	if acceptEncoding == "" {
		return false
	}

	// 将Accept-Encoding按逗号分割，去除空格
	encodings := strings.Split(acceptEncoding, ",")
	for _, enc := range encodings {
		enc = strings.TrimSpace(enc)
		// 检查是否完全匹配或者是带权重的匹配（如 "gzip;q=0.8"）
		if enc == encoding || strings.HasPrefix(enc, encoding+";") {
			return true
		}
	}
	return false
}

// CreateCompressor 创建压缩器
func CreateCompressor(w io.Writer, compressionType CompressionType, cfg config.CompressionConfig) (io.WriteCloser, error) {
	switch compressionType {
	case CompressionZstd:
		level := cfg.ZstdLevel
		if level < 1 || level > 22 {
			level = 3 // 默认级别
		}
		encoder, err := zstd.NewWriter(w, zstd.WithEncoderLevel(zstd.EncoderLevelFromZstd(level)))
		if err != nil {
			return nil, err
		}
		return encoder, nil

	case CompressionBrotli:
		quality := cfg.BrotliQuality
		if quality < 0 || quality > 11 {
			quality = 6 // 默认质量
		}
		return brotli.NewWriterLevel(w, quality), nil

	case CompressionGzip:
		level := cfg.Level
		if level < 1 || level > 9 {
			level = gzip.DefaultCompression
		}
		return gzip.NewWriterLevel(w, level)

	case CompressionDeflate:
		level := cfg.Level
		if level < 1 || level > 9 {
			level = flate.DefaultCompression
		}
		return flate.NewWriter(w, level)

	default:
		return nil, nil
	}
}

// Write 写入数据
func (cw *CompressibleWriter) Write(data []byte) (int, error) {
	if !cw.written {
		cw.written = true

		// 检查响应是否已经被压缩
		if cw.isAlreadyCompressed() {
			// 如果已经压缩，禁用压缩
			cw.compressionType = CompressionNone
		} else {
			// 检查内容类型是否需要压缩
			contentType := cw.Header().Get("Content-Type")
			if cw.shouldCompress(contentType, len(data)) && cw.compressionType != CompressionNone {
				// 需要压缩且还没有创建压缩器，现在创建
				if cw.compressor == nil {
					compressor, err := CreateCompressor(cw.ResponseWriter, cw.compressionType, cw.config)
					if err == nil {
						cw.compressor = compressor
						cw.writer = compressor
						// 设置压缩头部
						cw.Header().Set("Content-Encoding", cw.encoding)
						cw.Header().Set("Vary", "Accept-Encoding")
					} else {
						// 压缩器创建失败，禁用压缩
						cw.compressionType = CompressionNone
					}
				}
			} else {
				// 不需要压缩，禁用压缩
				cw.compressionType = CompressionNone
			}
		}
	}

	return cw.writer.Write(data)
}

// isAlreadyCompressed 检查响应是否已经被压缩
func (cw *CompressibleWriter) isAlreadyCompressed() bool {
	// 检查Content-Encoding头部
	contentEncoding := cw.Header().Get("Content-Encoding")
	if contentEncoding != "" {
		// 如果已经设置了Content-Encoding，说明上游已经压缩了
		return true
	}

	// 检查Transfer-Encoding头部
	transferEncoding := cw.Header().Get("Transfer-Encoding")
	if strings.Contains(strings.ToLower(transferEncoding), "gzip") ||
		strings.Contains(strings.ToLower(transferEncoding), "deflate") ||
		strings.Contains(strings.ToLower(transferEncoding), "br") {
		return true
	}

	return false
}

// shouldCompress 判断是否应该压缩
func (cw *CompressibleWriter) shouldCompress(contentType string, size int) bool {
	if !cw.config.Enabled {
		return false
	}

	// 检查最小大小限制
	if size < cw.config.MinSize.Int() {
		return false
	}

	// 检查最大大小限制
	if cw.config.MaxSize.Int() > 0 && size > cw.config.MaxSize.Int() {
		return false
	}

	// 检查内容类型
	if len(cw.config.Types) == 0 {
		// 默认压缩类型
		return strings.Contains(contentType, "text/") ||
			strings.Contains(contentType, "application/json") ||
			strings.Contains(contentType, "application/javascript") ||
			strings.Contains(contentType, "application/xml")
	}

	for _, t := range cw.config.Types {
		if strings.Contains(contentType, t) {
			return true
		}
	}

	return false
}

// Close 关闭压缩写入器
func (cw *CompressibleWriter) Close() error {
	if cw.compressor != nil {
		return cw.compressor.Close()
	}
	return nil
}

// GetCompressionType 获取当前使用的压缩类型
func (cw *CompressibleWriter) GetCompressionType() CompressionType {
	return cw.compressionType
}

// GetCompressionName 获取压缩算法名称
func (cw *CompressibleWriter) GetCompressionName() string {
	switch cw.compressionType {
	case CompressionBrotli:
		return "brotli"
	case CompressionGzip:
		return "gzip"
	case CompressionDeflate:
		return "deflate"
	default:
		return "none"
	}
}

// CompressionMiddleware 压缩中间件
func CompressionMiddleware(cfg config.CompressionConfig) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if cfg.Enabled {
				cw := NewCompressibleWriter(w, r, cfg)
				defer cw.Close()
				next.ServeHTTP(cw, r)
			} else {
				next.ServeHTTP(w, r)
			}
		})
	}
}

// CompressionMiddlewareWithStats 带统计的压缩中间件
func CompressionMiddlewareWithStats(cfg config.CompressionConfig, statsCallback func(string, int64, int64, time.Duration)) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if cfg.Enabled {
				cw := NewCompressibleWriter(w, r, cfg)
				defer func() {
					cw.Close()
					// 记录压缩统计（这里需要实际的大小数据）
					if statsCallback != nil && cw.compressionType != CompressionNone {
						// 注意：这里需要实际的压缩前后大小数据
						// 在实际实现中，需要在CompressibleWriter中跟踪这些数据
						statsCallback(cw.GetCompressionName(), 0, 0, 0)
					}
				}()
				next.ServeHTTP(cw, r)
			} else {
				next.ServeHTTP(w, r)
			}
		})
	}
}

// 压缩器对象池
var (
	gzipWriterPool = sync.Pool{
		New: func() interface{} {
			w, _ := gzip.NewWriterLevel(nil, gzip.DefaultCompression)
			return w
		},
	}

	brotliWriterPool = sync.Pool{
		New: func() interface{} {
			return brotli.NewWriterLevel(nil, 6)
		},
	}

	deflateWriterPool = sync.Pool{
		New: func() interface{} {
			w, _ := flate.NewWriter(nil, flate.DefaultCompression)
			return w
		},
	}
)

// getPooledCompressor 从对象池获取压缩器
func getPooledCompressor(w io.Writer, compressionType CompressionType, cfg config.CompressionConfig) (io.WriteCloser, error) {
	switch compressionType {
	case CompressionBrotli:
		writer := brotliWriterPool.Get().(*brotli.Writer)
		quality := cfg.BrotliQuality
		if quality < 0 || quality > 11 {
			quality = 6
		}
		writer.Reset(w)
		// Note: brotli.Writer doesn't have SetLevel method, so we create new one
		brotliWriterPool.Put(writer)
		return brotli.NewWriterLevel(w, quality), nil

	case CompressionGzip:
		writer := gzipWriterPool.Get().(*gzip.Writer)
		level := cfg.Level
		if level < 1 || level > 9 {
			level = gzip.DefaultCompression
		}
		writer.Reset(w)
		// Reset level if needed
		gzipWriterPool.Put(writer)
		return gzip.NewWriterLevel(w, level)

	case CompressionDeflate:
		writer := deflateWriterPool.Get().(*flate.Writer)
		level := cfg.Level
		if level < 1 || level > 9 {
			level = flate.DefaultCompression
		}
		writer.Reset(w)
		deflateWriterPool.Put(writer)
		return flate.NewWriter(w, level)

	default:
		return nil, nil
	}
}

// DefaultCompressionConfig 默认压缩配置
func DefaultCompressionConfig() config.CompressionConfig {
	return config.CompressionConfig{
		Enabled: true,
		Types: []string{
			"text/html",
			"text/css",
			"text/javascript",
			"application/json",
			"application/javascript",
			"application/xml",
			"text/xml",
			"text/plain",
		},
		MinSize:             config.Size(1024),                 // 1KB
		MaxSize:             config.Size(10 * 1024 * 1024),     // 10MB
		Level:               6,                                 // 平衡压缩率和速度
		Algorithms:          []string{"br", "gzip", "deflate"}, // 优先使用Brotli
		BrotliQuality:       6,                                 // Brotli默认质量
		EnableRecompression: true,                              // 默认启用重压缩
		RecompressionMode:   "auto",                            // 自动模式
	}
}

// GetCompressionStats 获取压缩统计信息
type CompressionStats struct {
	Algorithm        string  `json:"algorithm"`
	OriginalSize     int64   `json:"original_size"`
	CompressedSize   int64   `json:"compressed_size"`
	CompressionRatio float64 `json:"compression_ratio"`
	CompressionTime  int64   `json:"compression_time_ns"`
}

// BenchmarkCompressionAlgorithms 基准测试不同压缩算法
func BenchmarkCompressionAlgorithms(data []byte) map[string]CompressionStats {
	results := make(map[string]CompressionStats)

	// 测试Brotli
	if stats := benchmarkBrotli(data); stats != nil {
		results["brotli"] = *stats
	}

	// 测试Gzip
	if stats := benchmarkGzip(data); stats != nil {
		results["gzip"] = *stats
	}

	// 测试Deflate
	if stats := benchmarkDeflate(data); stats != nil {
		results["deflate"] = *stats
	}

	return results
}

// benchmarkBrotli Brotli压缩基准测试
func benchmarkBrotli(data []byte) *CompressionStats {
	var buf strings.Builder
	start := time.Now()

	writer := brotli.NewWriterLevel(&buf, 6)
	_, err := writer.Write(data)
	if err != nil {
		return nil
	}
	writer.Close()

	duration := time.Since(start)
	compressed := buf.String()

	return &CompressionStats{
		Algorithm:        "brotli",
		OriginalSize:     int64(len(data)),
		CompressedSize:   int64(len(compressed)),
		CompressionRatio: float64(len(compressed)) / float64(len(data)),
		CompressionTime:  duration.Nanoseconds(),
	}
}

// benchmarkGzip Gzip压缩基准测试
func benchmarkGzip(data []byte) *CompressionStats {
	var buf strings.Builder
	start := time.Now()

	writer := gzip.NewWriter(&buf)
	_, err := writer.Write(data)
	if err != nil {
		return nil
	}
	writer.Close()

	duration := time.Since(start)
	compressed := buf.String()

	return &CompressionStats{
		Algorithm:        "gzip",
		OriginalSize:     int64(len(data)),
		CompressedSize:   int64(len(compressed)),
		CompressionRatio: float64(len(compressed)) / float64(len(data)),
		CompressionTime:  duration.Nanoseconds(),
	}
}

// benchmarkDeflate Deflate压缩基准测试
func benchmarkDeflate(data []byte) *CompressionStats {
	var buf strings.Builder
	start := time.Now()

	writer, err := flate.NewWriter(&buf, flate.DefaultCompression)
	if err != nil {
		return nil
	}
	_, err = writer.Write(data)
	if err != nil {
		return nil
	}
	writer.Close()

	duration := time.Since(start)
	compressed := buf.String()

	return &CompressionStats{
		Algorithm:        "deflate",
		OriginalSize:     int64(len(data)),
		CompressedSize:   int64(len(compressed)),
		CompressionRatio: float64(len(compressed)) / float64(len(data)),
		CompressionTime:  duration.Nanoseconds(),
	}
}

// RecompressionWriter 重压缩写入器
type RecompressionWriter struct {
	http.ResponseWriter
	request       *http.Request
	config        config.CompressionConfig
	headerWritten bool
	buffer        bytes.Buffer
}

// NewRecompressionWriter 创建重压缩写入器
func NewRecompressionWriter(w http.ResponseWriter, r *http.Request, cfg config.CompressionConfig) *RecompressionWriter {
	return &RecompressionWriter{
		ResponseWriter: w,
		request:        r,
		config:         cfg,
		headerWritten:  false,
	}
}

// Write 写入数据时检查压缩处理
func (rw *RecompressionWriter) Write(data []byte) (int, error) {
	if !rw.headerWritten {
		rw.headerWritten = true

		upstreamEncoding := rw.Header().Get("Content-Encoding")

		// 情况1: 上游未压缩 - 进行算法优先级选择压缩
		if upstreamEncoding == "" {
			return rw.handleUpstreamUncompressed(data)
		}

		// 情况2: 上游已压缩 - 检查兼容性，不兼容时兜底重压缩
		if rw.needsRecompression() {
			return rw.handleRecompression(data)
		}
	}

	// 情况3: 上游已压缩且兼容 - 直接透传（大多数情况）
	return rw.ResponseWriter.Write(data)
}

// needsRecompression 检查是否需要重新压缩（兜底策略）
func (rw *RecompressionWriter) needsRecompression() bool {
	if !rw.config.EnableRecompression {
		return false
	}

	upstreamEncoding := rw.Header().Get("Content-Encoding")
	if upstreamEncoding == "" {
		return false // 上游未压缩，直接透传
	}

	clientAccept := rw.request.Header.Get("Accept-Encoding")
	if clientAccept == "" {
		// 客户端不支持任何压缩，需要解压发送原始数据
		return true
	}

	// 兜底策略：只有当客户端不支持上游格式时才重压缩
	// 大多数情况下这里会返回false，直接透传，性能最优
	return !strings.Contains(clientAccept, upstreamEncoding)
}

// handleRecompression 处理重新压缩（兜底策略）
func (rw *RecompressionWriter) handleRecompression(data []byte) (int, error) {
	upstreamEncoding := rw.Header().Get("Content-Encoding")

	// 解压上游数据
	decompressed, err := rw.decompress(data, upstreamEncoding)
	if err != nil {
		// 解压失败，直接透传原数据（保证服务可用性）
		return rw.ResponseWriter.Write(data)
	}

	// 兜底策略：简单选择客户端支持的任一格式，不考虑优先级
	clientAccept := rw.request.Header.Get("Accept-Encoding")
	clientEncoding := rw.selectCompatibleEncoding(clientAccept)

	if clientEncoding == "" {
		// 客户端不支持任何压缩，发送解压后的原始数据
		rw.Header().Del("Content-Encoding")
		rw.Header().Set("Content-Length", fmt.Sprintf("%d", len(decompressed)))
		return rw.ResponseWriter.Write(decompressed)
	}

	// 重新压缩为兼容格式
	clientType := rw.getCompressionType(clientEncoding)
	recompressed, err := rw.recompress(decompressed, clientType)
	if err != nil {
		// 重压缩失败，发送解压后的数据（保证服务可用性）
		rw.Header().Del("Content-Encoding")
		rw.Header().Set("Content-Length", fmt.Sprintf("%d", len(decompressed)))
		return rw.ResponseWriter.Write(decompressed)
	}

	// 更新响应头
	rw.Header().Set("Content-Encoding", clientEncoding)
	rw.Header().Set("Content-Length", fmt.Sprintf("%d", len(recompressed)))

	return rw.ResponseWriter.Write(recompressed)
}

// selectCompatibleEncoding 选择客户端支持的任一压缩格式（不考虑优先级）
func (rw *RecompressionWriter) selectCompatibleEncoding(clientAccept string) string {
	// 简单检查客户端支持的格式，返回第一个匹配的
	supportedEncodings := []string{"gzip", "br", "deflate"}

	for _, encoding := range supportedEncodings {
		if strings.Contains(clientAccept, encoding) {
			return encoding
		}
	}

	return "" // 客户端不支持任何压缩
}

// getCompressionType 根据编码名称获取压缩类型
func (rw *RecompressionWriter) getCompressionType(encoding string) CompressionType {
	switch encoding {
	case "gzip":
		return CompressionGzip
	case "br":
		return CompressionBrotli
	case "deflate":
		return CompressionDeflate
	default:
		return CompressionNone
	}
}

// decompress 解压数据
func (rw *RecompressionWriter) decompress(data []byte, encoding string) ([]byte, error) {
	reader := bytes.NewReader(data)

	switch encoding {
	case "gzip":
		gzReader, err := gzip.NewReader(reader)
		if err != nil {
			return nil, err
		}
		defer gzReader.Close()
		return io.ReadAll(gzReader)

	case "br":
		brReader := brotli.NewReader(reader)
		return io.ReadAll(brReader)

	case "deflate":
		deflateReader := flate.NewReader(reader)
		defer deflateReader.Close()
		return io.ReadAll(deflateReader)

	default:
		return data, nil // 未知编码，返回原数据
	}
}

// recompress 重新压缩数据
func (rw *RecompressionWriter) recompress(data []byte, compressionType CompressionType) ([]byte, error) {
	var buf bytes.Buffer

	switch compressionType {
	case CompressionGzip:
		level := rw.config.Level
		if level < 1 || level > 9 {
			level = gzip.DefaultCompression
		}
		writer, err := gzip.NewWriterLevel(&buf, level)
		if err != nil {
			return nil, err
		}
		_, err = writer.Write(data)
		if err != nil {
			return nil, err
		}
		err = writer.Close()
		if err != nil {
			return nil, err
		}

	case CompressionBrotli:
		quality := rw.config.BrotliQuality
		if quality < 0 || quality > 11 {
			quality = 6
		}
		writer := brotli.NewWriterLevel(&buf, quality)
		_, err := writer.Write(data)
		if err != nil {
			return nil, err
		}
		err = writer.Close()
		if err != nil {
			return nil, err
		}

	case CompressionDeflate:
		level := rw.config.Level
		if level < 1 || level > 9 {
			level = flate.DefaultCompression
		}
		writer, err := flate.NewWriter(&buf, level)
		if err != nil {
			return nil, err
		}
		_, err = writer.Write(data)
		if err != nil {
			return nil, err
		}
		err = writer.Close()
		if err != nil {
			return nil, err
		}

	default:
		return data, nil
	}

	return buf.Bytes(), nil
}

// handleUpstreamUncompressed 处理上游未压缩的情况（算法优先级选择）
func (rw *RecompressionWriter) handleUpstreamUncompressed(data []byte) (int, error) {
	// 检查是否需要压缩
	if !rw.shouldCompress(data) {
		// 不需要压缩，直接发送原始数据
		return rw.ResponseWriter.Write(data)
	}

	// 选择最佳压缩算法（按优先级）
	compressionType, encoding := selectBestCompression(rw.request, rw.config)
	if compressionType == CompressionNone {
		// 客户端不支持压缩，发送原始数据
		return rw.ResponseWriter.Write(data)
	}

	// 压缩数据
	compressed, err := rw.recompress(data, compressionType)
	if err != nil {
		// 压缩失败，发送原始数据
		return rw.ResponseWriter.Write(data)
	}

	// 设置压缩头部
	rw.Header().Set("Content-Encoding", encoding)
	rw.Header().Set("Content-Length", fmt.Sprintf("%d", len(compressed)))

	return rw.ResponseWriter.Write(compressed)
}

// shouldCompress 判断是否应该压缩（基于内容大小和类型）
func (rw *RecompressionWriter) shouldCompress(data []byte) bool {
	dataSize := len(data)

	// 检查最小大小限制
	if dataSize < rw.config.MinSize.Int() {
		return false
	}

	// 检查最大大小限制
	if rw.config.MaxSize.Int() > 0 && dataSize > rw.config.MaxSize.Int() {
		return false
	}

	// 检查内容类型
	contentType := rw.Header().Get("Content-Type")
	if len(rw.config.Types) == 0 {
		// 默认压缩类型
		return strings.Contains(contentType, "text/") ||
			strings.Contains(contentType, "application/json") ||
			strings.Contains(contentType, "application/javascript") ||
			strings.Contains(contentType, "application/xml")
	}

	for _, t := range rw.config.Types {
		if strings.Contains(contentType, t) {
			return true
		}
	}

	return false
}

// Close 关闭重压缩写入器
func (rw *RecompressionWriter) Close() error {
	// 重压缩写入器无需特殊关闭操作
	return nil
}

// cleanAcceptEncodingHeader 清理Accept-Encoding头部
// 解决Windows 2012等系统上HTTP头部被截断或包含换行符的问题
func cleanAcceptEncodingHeader(acceptEncoding string) string {
	if acceptEncoding == "" {
		return ""
	}

	// 移除所有换行符和回车符
	cleaned := strings.ReplaceAll(acceptEncoding, "\n", "")
	cleaned = strings.ReplaceAll(cleaned, "\r", "")

	// 检查是否被截断（通过检查常见的不完整模式）
	truncated := false

	// 检查是否以逗号结尾但没有后续内容（可能被截断）
	if strings.HasSuffix(strings.TrimSpace(cleaned), ",") {
		truncated = true
	}

	// 检查是否缺少常见的zstd算法（如果有br但没有zstd，可能被截断）
	if strings.Contains(cleaned, "br") && !strings.Contains(cleaned, "zstd") {
		truncated = true
	}

	// 如果检测到截断，尝试修复
	if truncated {
		// 移除末尾的逗号和空格
		cleaned = strings.TrimRight(cleaned, ", ")

		// 如果有br但没有zstd，添加zstd（现代浏览器通常都支持）
		if strings.Contains(cleaned, "br") && !strings.Contains(cleaned, "zstd") {
			cleaned += ", zstd"
		}
	}

	return cleaned
}

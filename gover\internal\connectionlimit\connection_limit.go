package connectionlimit

import (
	"fmt"
	"net/http"
	"sync"
	"sync/atomic"
	"time"
)

// ConnectionLimiter 连接限制器接口
type ConnectionLimiter interface {
	AcquireConnection(siteName string) error
	ReleaseConnection(siteName string)
	GetStats() map[string]*ConnectionStats
	GetSiteStats(siteName string) *ConnectionStats
}

// ConnectionStats 连接统计信息
type ConnectionStats struct {
	CurrentConnections  int64     `json:"current_connections"`
	MaxConnections      int       `json:"max_connections"`
	TotalConnections    int64     `json:"total_connections"`
	RejectedConnections int64     `json:"rejected_connections"`
	LastRejectTime      time.Time `json:"last_reject_time"`
}

// SiteConnectionLimiter 站点级连接限制器
type SiteConnectionLimiter struct {
	siteConfigs map[string]int              // 站点名 -> 最大连接数
	siteStats   map[string]*ConnectionStats // 站点统计
	globalLimit int                         // 全局连接数限制
	globalStats *ConnectionStats            // 全局统计
	mu          sync.RWMutex
}

// NewSiteConnectionLimiter 创建站点级连接限制器
func NewSiteConnectionLimiter(globalLimit int) *SiteConnectionLimiter {
	return &SiteConnectionLimiter{
		siteConfigs: make(map[string]int),
		siteStats:   make(map[string]*ConnectionStats),
		globalLimit: globalLimit,
		globalStats: &ConnectionStats{
			MaxConnections: globalLimit,
		},
	}
}

// SetSiteLimit 设置站点连接数限制
func (scl *SiteConnectionLimiter) SetSiteLimit(siteName string, maxConnections int) {
	scl.mu.Lock()
	defer scl.mu.Unlock()

	scl.siteConfigs[siteName] = maxConnections

	// 初始化站点统计
	if _, exists := scl.siteStats[siteName]; !exists {
		scl.siteStats[siteName] = &ConnectionStats{
			MaxConnections: maxConnections,
		}
	} else {
		scl.siteStats[siteName].MaxConnections = maxConnections
	}
}

// AcquireConnection 获取连接（检查限制）
func (scl *SiteConnectionLimiter) AcquireConnection(siteName string) error {
	scl.mu.RLock()
	siteLimit, hasSiteLimit := scl.siteConfigs[siteName]
	siteStats, hasSiteStats := scl.siteStats[siteName]
	scl.mu.RUnlock()

	// 检查全局连接数限制
	if scl.globalLimit > 0 {
		currentGlobal := atomic.LoadInt64(&scl.globalStats.CurrentConnections)
		if currentGlobal >= int64(scl.globalLimit) {
			atomic.AddInt64(&scl.globalStats.RejectedConnections, 1)
			scl.globalStats.LastRejectTime = time.Now()
			return fmt.Errorf("全局连接数超限: %d/%d", currentGlobal, scl.globalLimit)
		}
	}

	// 检查站点连接数限制
	if hasSiteLimit && siteLimit > 0 && hasSiteStats {
		currentSite := atomic.LoadInt64(&siteStats.CurrentConnections)
		if currentSite >= int64(siteLimit) {
			atomic.AddInt64(&siteStats.RejectedConnections, 1)
			siteStats.LastRejectTime = time.Now()
			return fmt.Errorf("站点 %s 连接数超限: %d/%d", siteName, currentSite, siteLimit)
		}
	}

	// 增加连接计数
	atomic.AddInt64(&scl.globalStats.CurrentConnections, 1)
	atomic.AddInt64(&scl.globalStats.TotalConnections, 1)

	if hasSiteStats {
		atomic.AddInt64(&siteStats.CurrentConnections, 1)
		atomic.AddInt64(&siteStats.TotalConnections, 1)
	}

	return nil
}

// ReleaseConnection 释放连接
func (scl *SiteConnectionLimiter) ReleaseConnection(siteName string) {
	// 减少全局连接计数
	atomic.AddInt64(&scl.globalStats.CurrentConnections, -1)

	// 减少站点连接计数
	scl.mu.RLock()
	siteStats, exists := scl.siteStats[siteName]
	scl.mu.RUnlock()

	if exists {
		atomic.AddInt64(&siteStats.CurrentConnections, -1)
	}
}

// GetStats 获取所有站点统计信息
func (scl *SiteConnectionLimiter) GetStats() map[string]*ConnectionStats {
	scl.mu.RLock()
	defer scl.mu.RUnlock()

	stats := make(map[string]*ConnectionStats)

	// 添加全局统计
	stats["global"] = &ConnectionStats{
		CurrentConnections:  atomic.LoadInt64(&scl.globalStats.CurrentConnections),
		MaxConnections:      scl.globalStats.MaxConnections,
		TotalConnections:    atomic.LoadInt64(&scl.globalStats.TotalConnections),
		RejectedConnections: atomic.LoadInt64(&scl.globalStats.RejectedConnections),
		LastRejectTime:      scl.globalStats.LastRejectTime,
	}

	// 添加站点统计
	for siteName, siteStats := range scl.siteStats {
		stats[siteName] = &ConnectionStats{
			CurrentConnections:  atomic.LoadInt64(&siteStats.CurrentConnections),
			MaxConnections:      siteStats.MaxConnections,
			TotalConnections:    atomic.LoadInt64(&siteStats.TotalConnections),
			RejectedConnections: atomic.LoadInt64(&siteStats.RejectedConnections),
			LastRejectTime:      siteStats.LastRejectTime,
		}
	}

	return stats
}

// GetSiteStats 获取特定站点统计信息
func (scl *SiteConnectionLimiter) GetSiteStats(siteName string) *ConnectionStats {
	scl.mu.RLock()
	defer scl.mu.RUnlock()

	if siteStats, exists := scl.siteStats[siteName]; exists {
		return &ConnectionStats{
			CurrentConnections:  atomic.LoadInt64(&siteStats.CurrentConnections),
			MaxConnections:      siteStats.MaxConnections,
			TotalConnections:    atomic.LoadInt64(&siteStats.TotalConnections),
			RejectedConnections: atomic.LoadInt64(&siteStats.RejectedConnections),
			LastRejectTime:      siteStats.LastRejectTime,
		}
	}

	return nil
}

// UpdateGlobalLimit 更新全局连接数限制
func (scl *SiteConnectionLimiter) UpdateGlobalLimit(newLimit int) {
	scl.mu.Lock()
	defer scl.mu.Unlock()

	scl.globalLimit = newLimit
	scl.globalStats.MaxConnections = newLimit
}

// RemoveSite 移除站点配置
func (scl *SiteConnectionLimiter) RemoveSite(siteName string) {
	scl.mu.Lock()
	defer scl.mu.Unlock()

	delete(scl.siteConfigs, siteName)
	delete(scl.siteStats, siteName)
}

// GetSiteLimit 获取站点连接数限制
func (scl *SiteConnectionLimiter) GetSiteLimit(siteName string) (int, bool) {
	scl.mu.RLock()
	defer scl.mu.RUnlock()

	limit, exists := scl.siteConfigs[siteName]
	return limit, exists
}

// ConnectionLimitMiddleware 连接限制中间件
func ConnectionLimitMiddleware(limiter ConnectionLimiter) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// 从请求上下文中获取站点名（需要在路由中设置）
			siteName := r.Header.Get("X-Site-Name")
			if siteName == "" {
				siteName = "default"
			}

			// 尝试获取连接
			if err := limiter.AcquireConnection(siteName); err != nil {
				http.Error(w, "连接数超限: "+err.Error(), http.StatusServiceUnavailable)
				return
			}

			// 确保在请求完成后释放连接
			defer limiter.ReleaseConnection(siteName)

			// 继续处理请求
			next.ServeHTTP(w, r)
		})
	}
}

// ConnectionLimitWriter 带连接限制的响应写入器
type ConnectionLimitWriter struct {
	http.ResponseWriter
	limiter  ConnectionLimiter
	siteName string
	released bool
}

// NewConnectionLimitWriter 创建带连接限制的响应写入器
func NewConnectionLimitWriter(w http.ResponseWriter, limiter ConnectionLimiter, siteName string) (*ConnectionLimitWriter, error) {
	// 尝试获取连接
	if err := limiter.AcquireConnection(siteName); err != nil {
		return nil, err
	}

	return &ConnectionLimitWriter{
		ResponseWriter: w,
		limiter:        limiter,
		siteName:       siteName,
		released:       false,
	}, nil
}

// Close 释放连接
func (clw *ConnectionLimitWriter) Close() {
	if !clw.released {
		clw.limiter.ReleaseConnection(clw.siteName)
		clw.released = true
	}
}

// Write 写入响应数据
func (clw *ConnectionLimitWriter) Write(data []byte) (int, error) {
	return clw.ResponseWriter.Write(data)
}

// WriteHeader 写入响应头
func (clw *ConnectionLimitWriter) WriteHeader(statusCode int) {
	clw.ResponseWriter.WriteHeader(statusCode)
}

# 静态文件与路径访问控制说明

本项目支持灵活的静态文件和路径访问控制，帮助你安全地隐藏、禁止或限制某些文件和目录的访问。

---

## 一、全站文件类型/路径拒绝访问

### 1. deny_types

- 用于禁止访问指定后缀的所有文件（无论是静态文件还是代理请求）。
- 配置示例：

```json
"deny_types": [".php", ".exe", ".bak", ".env"]
```

- 作用：凡是URL以这些后缀结尾的请求，都会被直接拒绝（HTTP 403）。

### 2. deny_urls

- 用于禁止访问指定路径或文件，支持正则表达式。
- 配置示例：

```json
"deny_urls": [
  "^/static/secret/",         // 禁止访问/static/secret/目录及其下所有内容
  "^/static/.*\\.log$",      // 禁止所有.log文件
  "^/static/.*\\.bak$",      // 禁止所有.bak文件
  "\\.env$",                 // 禁止任何.env文件
  "^/private/"
]
```

- 作用：只要URL路径匹配这些正则表达式，都会被直接拒绝（HTTP 403）。

- **deny_types 和 deny_urls 优先级最高**，无论是静态文件还是反向代理请求，都会先检查这些规则。

---

## 二、目录索引控制

- 每条静态路由可通过 `dir_listing` 字段控制是否允许列出目录内容。
- 配置示例：

```json
"routes": [
  {
    "pattern": "^/static/",
    "static_dir": "./static",
    "dir_listing": false
  }
]
```

- `dir_listing: false` 时，访问目录会被禁止列出内容（403），只能通过已知文件名访问。
- `dir_listing: true` 时，允许浏览器列出目录下的文件（不建议生产环境开启）。

---

## 三、典型配置示例

### 1. 禁止访问敏感文件和目录

```json
"deny_types": [".php", ".exe", ".bak", ".env"],
"deny_urls": [
  "^/static/secret/",
  "^/static/.*\\.log$",
  "^/private/"
]
```

### 2. 只允许访问白名单文件类型（如只允许图片）

```json
"deny_types": [".php", ".exe", ".bak", ".env", ".txt", ".md", ".zip", ".tar", ".sh", ".bat", ".js", ".css", ".html", ".log", ".json"]
```

### 3. 禁止访问所有以"/admin/"开头的路径

```json
"deny_urls": ["^/admin/"]
```

---

## 四、注意事项

- deny_types/deny_urls 配置在站点级（每个site都可单独设置）。
- 规则支持热加载，无需重启服务。
- deny_types 匹配时不区分大小写。
- deny_urls 支持标准正则表达式，建议测试正则表达式的准确性。
- 目录索引建议生产环境关闭（dir_listing: false）。
- 这些规则对所有请求生效，包括静态文件和反向代理。

---

## 五、最佳实践建议

- **最小暴露原则**：只开放必要的静态目录和文件类型。
- **定期审查**：定期检查 deny_types/deny_urls 配置，防止敏感文件泄露。
- **配合日志**：开启详细访问日志，及时发现被拒绝的访问尝试。

---

如需更复杂的访问控制（如白名单、按用户、按时间段等），可联系开发者进一步扩展。

---

## 六、目录索引递归隐藏规则（hidden_in_listing）

- `hidden_in_listing` 支持通配符（如 `*.log`、`secret*`、`/private/*`）和正则表达式混用。
- 规则会递归作用于所有子目录：只要相对于 static_dir 的完整路径或文件名匹配规则，就会在所有目录索引页面被隐藏。
- 仅影响目录列表显示，不影响实际访问（实际访问仍受 deny_types/deny_urls 控制）。

### 配置示例

```json
"routes": [
  {
    "pattern": "^/static/",
    "static_dir": "./static",
    "dir_listing": true,
    "hidden_in_listing": [
      "*.log",           // 隐藏所有目录下的 .log 文件
      "secret*",         // 隐藏所有以 secret 开头的文件/目录
      "/private/*",      // 隐藏 /private/ 及其下所有内容
      "**/.env",         // 隐藏所有目录下的 .env 文件
      "^/foo/.*\\.bak$"  // 隐藏 /foo/ 目录下所有 .bak 文件（正则）
    ]
  }
]
```

- 你可以用通配符（*、?）或正则表达式灵活隐藏任意层级的文件和目录。
- 规则会自动转换为正则表达式进行递归匹配。

---

如需更复杂的递归规则（如只递归隐藏文件/目录、支持Ant风格通配符等），欢迎继续提出！ 
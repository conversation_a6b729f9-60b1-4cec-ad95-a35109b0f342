# 🐛 ACL站点映射Bug最终修复

## 📋 Bug确认

通过实例地址对比，确认了真正的问题：

### **重载时创建的新ACL**
```
创建新站点ACL实例: qiank, 实例地址: 0xc000219680, 站点ACL[文件允许:, 实际允许:0个]
```

### **访问时使用的ACL**
```
检查站点ACL: 站点ACL[实例:0xc000218f00, 文件允许:badip.txt, 实际允许:1个]
```

**问题确认**：实例地址不同（`0xc000219680` vs `0xc000218f00`），说明**站点映射没有被正确更新**！

## 🔍 **根本原因**

### **问题分析**
1. **多域名站点**：`qiank`站点有两个域名：`qiank.com`和`www.qiank.com`
2. **映射删除不完整**：在`onSiteConfigChanged`中删除旧映射时，可能存在并发修改map的问题
3. **新ACL被创建**：新的ACL实例被正确创建
4. **映射未更新**：但某些域名映射仍然指向旧的ACL实例

### **问题代码（修复前）**
```go
// 边遍历边删除，可能导致map并发修改问题
for domain, site := range p.sites {
    if site.config.Name == siteName {
        if oldSite == nil {
            oldSite = site
        }
        delete(p.sites, domain)  // 在遍历中删除，可能有问题
    }
}
```

## 🔧 **最终修复**

### **修复策略**
1. **分离收集和删除操作**：先收集需要删除的域名，再统一删除
2. **确保完整清理**：确保所有旧的域名映射都被删除
3. **添加详细调试**：记录ACL实例地址和映射操作

### **修复后代码**
```go
// 先收集需要删除的域名和旧站点引用
var oldSite *Site
var domainsToDelete []string
for domain, site := range p.sites {
    if site.config.Name == siteName {
        if oldSite == nil {
            oldSite = site // 保存旧站点引用
        }
        domainsToDelete = append(domainsToDelete, domain)
    }
}

// 关闭旧站点的ACL
if oldSite != nil && oldSite.acl != nil {
    p.logger.Debugf("关闭旧站点ACL: %s (实例地址: %p)", siteName, oldSite.acl)
    if err := oldSite.acl.Close(); err != nil {
        p.logger.Warnf("关闭旧站点ACL失败: %s, 错误: %v", siteName, err)
    }
}

// 删除所有旧的域名映射
for _, domain := range domainsToDelete {
    delete(p.sites, domain)
    p.logger.Debugf("删除旧域名映射: %s -> %s", domain, siteName)
}

// 创建新的域名映射
for _, domain := range newConfig.Domains {
    p.sites[domain] = site
    p.logger.Debugf("创建新域名映射: %s -> %s (ACL实例地址: %p)", domain, siteName, site.acl)
}
```

## 🎯 **修复效果**

### **预期日志输出**
```
time="..." level=debug msg="关闭旧站点ACL: qiank (实例地址: 0xc000218f00)"
time="..." level=debug msg="删除旧域名映射: qiank.com -> qiank"
time="..." level=debug msg="删除旧域名映射: www.qiank.com -> qiank"
time="..." level=debug msg="创建新站点ACL实例: qiank, 实例地址: 0xc000219680, ..."
time="..." level=debug msg="创建新域名映射: qiank.com -> qiank (ACL实例地址: 0xc000219680)"
time="..." level=debug msg="创建新域名映射: www.qiank.com -> qiank (ACL实例地址: 0xc000219680)"
```

### **访问时的日志**
```
time="..." level=debug msg="检查站点ACL: ..., 站点ACL[实例:0xc000219680, 文件允许:, 实际允许:0个]"
```

**关键验证**：访问时的ACL实例地址应该与新创建的ACL实例地址相同。

## 📊 **测试验证**

### **验证步骤**
1. **部署新版本**并重启服务
2. **触发配置重载**（修改并保存配置文件）
3. **观察重载日志**：
   - 确认看到"删除旧域名映射"日志
   - 确认看到"创建新域名映射"日志
   - 记录新ACL的实例地址
4. **测试访问**：
   - 访问`qiank.com`或`www.qiank.com`
   - 观察"检查站点ACL"日志中的实例地址
   - 确认与新创建的ACL实例地址相同

### **成功标准**
- ✅ 重载时所有旧域名映射被删除
- ✅ 新域名映射指向新的ACL实例
- ✅ 访问时使用的ACL实例地址与新创建的相同
- ✅ ACL配置正确生效（文件为空，IP不被拒绝）

## 🚨 **影响范围**

### **修复的问题**
- ✅ 多域名站点的ACL热重载
- ✅ 站点映射的完整更新
- ✅ 防止旧ACL实例的残留

### **适用场景**
- **多域名站点**：有多个域名指向同一站点的配置
- **ACL热重载**：动态修改站点ACL配置
- **配置管理**：所有站点配置的热重载

## 📋 **技术要点**

### **关键改进**
1. **避免并发修改**：分离map的遍历和修改操作
2. **确保完整性**：收集所有需要删除的域名，统一处理
3. **增强调试**：记录ACL实例地址，便于问题追踪
4. **操作原子性**：先删除旧映射，再创建新映射

### **防御性编程**
- 详细的调试日志记录每个操作步骤
- 错误处理确保部分失败不影响整体流程
- 实例地址追踪帮助快速定位问题

## 🎉 **总结**

这个bug的根本原因是**站点映射更新不完整**，导致：
- 新的ACL被正确创建
- 但旧的域名映射没有被完全清理
- 访问时仍然使用旧的ACL实例

通过分离收集和删除操作，确保所有旧映射被完全清理，新映射正确指向新的ACL实例，彻底解决了ACL热重载的问题。

**重要性**：这是ACL热重载功能的核心bug，影响多域名站点的配置更新，现已彻底修复。

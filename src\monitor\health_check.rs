use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use tokio::sync::RwLock;
use tokio::time::{interval, Instant};
use tracing::{debug, error, info, warn};

use crate::config::MonitorConfig;
use crate::monitor::{HealthCheck, HealthStatus};

/// 健康检查器
pub struct HealthChecker {
    config: MonitorConfig,
    checks: Arc<RwLock<HashMap<String, CheckResult>>>,
    start_time: SystemTime,
}

impl HealthChecker {
    /// 创建新的健康检查器
    pub async fn new(config: &MonitorConfig) -> Result<Self> {
        let checks = Arc::new(RwLock::new(HashMap::new()));

        info!("Health checker initialized successfully");

        Ok(Self {
            config: config.clone(),
            checks,
            start_time: SystemTime::now(),
        })
    }

    /// 执行健康检查
    pub async fn check_health(&self) -> Result<HealthStatus> {
        let start = Instant::now();
        let mut all_checks = HashMap::new();
        let mut overall_status = "healthy";

        // 系统基本检查
        let system_check = self.check_system_health().await;
        if system_check.status != "healthy" {
            overall_status = "unhealthy";
        }
        all_checks.insert("system".to_string(), system_check);

        // 内存检查
        let memory_check = self.check_memory_health().await;
        if memory_check.status != "healthy" {
            overall_status = "degraded";
        }
        all_checks.insert("memory".to_string(), memory_check);

        // 磁盘检查
        let disk_check = self.check_disk_health().await;
        if disk_check.status != "healthy" {
            overall_status = "degraded";
        }
        all_checks.insert("disk".to_string(), disk_check);

        // 网络检查
        let network_check = self.check_network_health().await;
        if network_check.status != "healthy" {
            overall_status = "degraded";
        }
        all_checks.insert("network".to_string(), network_check);

        // 上游服务检查
        let upstream_check = self.check_upstream_health().await;
        if upstream_check.status != "healthy" {
            overall_status = "degraded";
        }
        all_checks.insert("upstream".to_string(), upstream_check);

        // 更新检查结果缓存
        {
            let mut checks = self.checks.write().await;
            for (name, check) in &all_checks {
                checks.insert(name.clone(), CheckResult {
                    check: check.clone(),
                    last_updated: SystemTime::now(),
                });
            }
        }

        let uptime = self.start_time
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();

        Ok(HealthStatus {
            status: overall_status.to_string(),
            timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
            uptime,
            version: env!("CARGO_PKG_VERSION").to_string(),
            checks: all_checks,
        })
    }

    /// 启动定期健康检查
    pub async fn start_periodic_checks(&self) -> Result<()> {
        let mut interval = interval(Duration::from_secs(30)); // 每30秒检查一次

        info!("Starting periodic health checks...");

        loop {
            interval.tick().await;
            
            match self.check_health().await {
                Ok(status) => {
                    debug!("Health check completed: {}", status.status);
                    
                    // 如果状态不健康，记录警告
                    if status.status != "healthy" {
                        warn!("System health status: {}", status.status);
                        for (name, check) in &status.checks {
                            if check.status != "healthy" {
                                warn!("Health check '{}' failed: {}", name, check.message);
                            }
                        }
                    }
                }
                Err(e) => {
                    error!("Health check failed: {}", e);
                }
            }
        }
    }

    /// 检查系统健康状态
    async fn check_system_health(&self) -> HealthCheck {
        let start = Instant::now();
        
        // 检查系统负载、CPU使用率等
        let status = "healthy"; // 简化实现
        let message = "System is running normally";
        
        HealthCheck {
            status: status.to_string(),
            message: message.to_string(),
            duration_ms: start.elapsed().as_millis() as u64,
            last_check: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
        }
    }

    /// 检查内存健康状态
    async fn check_memory_health(&self) -> HealthCheck {
        let start = Instant::now();
        
        // 获取内存使用情况
        let (status, message) = match self.get_memory_usage().await {
            Ok(usage) => {
                if usage > 90.0 {
                    ("unhealthy", format!("Memory usage too high: {:.1}%", usage))
                } else if usage > 80.0 {
                    ("degraded", format!("Memory usage high: {:.1}%", usage))
                } else {
                    ("healthy", format!("Memory usage normal: {:.1}%", usage))
                }
            }
            Err(e) => ("unhealthy", format!("Failed to get memory usage: {}", e)),
        };
        
        HealthCheck {
            status: status.to_string(),
            message: message,
            duration_ms: start.elapsed().as_millis() as u64,
            last_check: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
        }
    }

    /// 检查磁盘健康状态
    async fn check_disk_health(&self) -> HealthCheck {
        let start = Instant::now();
        
        // 检查磁盘空间
        let (status, message) = match self.get_disk_usage().await {
            Ok(usage) => {
                if usage > 95.0 {
                    ("unhealthy", format!("Disk usage critical: {:.1}%", usage))
                } else if usage > 85.0 {
                    ("degraded", format!("Disk usage high: {:.1}%", usage))
                } else {
                    ("healthy", format!("Disk usage normal: {:.1}%", usage))
                }
            }
            Err(e) => ("unhealthy", format!("Failed to get disk usage: {}", e)),
        };
        
        HealthCheck {
            status: status.to_string(),
            message: message,
            duration_ms: start.elapsed().as_millis() as u64,
            last_check: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
        }
    }

    /// 检查网络健康状态
    async fn check_network_health(&self) -> HealthCheck {
        let start = Instant::now();
        
        // 检查网络连接
        let status = "healthy"; // 简化实现
        let message = "Network connectivity normal";
        
        HealthCheck {
            status: status.to_string(),
            message: message.to_string(),
            duration_ms: start.elapsed().as_millis() as u64,
            last_check: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
        }
    }

    /// 检查上游服务健康状态
    async fn check_upstream_health(&self) -> HealthCheck {
        let start = Instant::now();
        
        // 检查上游服务器连接
        let (status, message) = match self.ping_upstream_servers().await {
            Ok(healthy_count) => {
                if healthy_count == 0 {
                    ("unhealthy", "No upstream servers available".to_string())
                } else {
                    ("healthy", format!("{} upstream servers healthy", healthy_count))
                }
            }
            Err(e) => ("unhealthy", format!("Upstream check failed: {}", e)),
        };
        
        HealthCheck {
            status: status.to_string(),
            message: message,
            duration_ms: start.elapsed().as_millis() as u64,
            last_check: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_secs(),
        }
    }

    /// 获取内存使用率
    async fn get_memory_usage(&self) -> Result<f64> {
        // 简化实现，实际应该读取系统内存信息
        Ok(45.0) // 返回45%的内存使用率
    }

    /// 获取磁盘使用率
    async fn get_disk_usage(&self) -> Result<f64> {
        // 简化实现，实际应该检查磁盘空间
        Ok(60.0) // 返回60%的磁盘使用率
    }

    /// 检查上游服务器
    async fn ping_upstream_servers(&self) -> Result<usize> {
        // 简化实现，实际应该ping配置的上游服务器
        Ok(1) // 返回1个健康的上游服务器
    }

    /// 获取缓存的检查结果
    pub async fn get_cached_results(&self) -> HashMap<String, CheckResult> {
        self.checks.read().await.clone()
    }
}

/// 检查结果
#[derive(Debug, Clone)]
pub struct CheckResult {
    pub check: HealthCheck,
    pub last_updated: SystemTime,
}

/// 健康检查配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthCheckConfig {
    pub enabled: bool,
    pub interval: Duration,
    pub timeout: Duration,
    pub memory_threshold: f64,
    pub disk_threshold: f64,
    pub upstream_checks: Vec<UpstreamHealthCheck>,
}

impl Default for HealthCheckConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            interval: Duration::from_secs(30),
            timeout: Duration::from_secs(5),
            memory_threshold: 80.0,
            disk_threshold: 85.0,
            upstream_checks: vec![],
        }
    }
}

/// 上游健康检查配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpstreamHealthCheck {
    pub name: String,
    pub url: String,
    pub method: String,
    pub timeout: Duration,
    pub expected_status: u16,
    pub expected_body: Option<String>,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_health_checker_creation() {
        let config = MonitorConfig {
            enabled: true,
            port: 8080,
            username: "admin".to_string(),
            password: "password".to_string(),
            api_key: "test-key".to_string(),
            acl: crate::config::MonitorAclConfig {
                allowed_ips: vec!["127.0.0.1".to_string()],
                denied_ips: vec![],
            },
        };

        let checker = HealthChecker::new(&config).await;
        assert!(checker.is_ok());
    }

    #[tokio::test]
    async fn test_health_check_execution() {
        let config = MonitorConfig {
            enabled: true,
            port: 8080,
            username: "admin".to_string(),
            password: "password".to_string(),
            api_key: "test-key".to_string(),
            acl: crate::config::MonitorAclConfig {
                allowed_ips: vec!["127.0.0.1".to_string()],
                denied_ips: vec![],
            },
        };

        let checker = HealthChecker::new(&config).await.unwrap();
        let status = checker.check_health().await;
        
        assert!(status.is_ok());
        let status = status.unwrap();
        assert!(!status.checks.is_empty());
        assert!(status.checks.contains_key("system"));
        assert!(status.checks.contains_key("memory"));
    }
}

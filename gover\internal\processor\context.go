package processor

import (
	"bytes"
	"io"
	"net"
	"net/http"
	"net/url"
	"strings"
	"time"

	"reverse-proxy/internal/config"
)

// RequestContext 请求上下文 - 包含处理请求所需的所有信息
type RequestContext struct {
	// 基础请求信息
	SiteID       string            `json:"site_id"`
	ClientIP     string            `json:"client_ip"`
	Method       string            `json:"method"`
	URL          *url.URL          `json:"url"`
	Headers      http.Header       `json:"headers"`
	Body         []byte            `json:"body,omitempty"`
	ContentType  string            `json:"content_type"`
	UserAgent    string            `json:"user_agent"`
	Referer      string            `json:"referer"`
	
	// 处理过程中的配置和规则
	Site         *SiteInfo         `json:"site"`
	Route        *RouteInfo        `json:"route"`
	Upstream     *UpstreamInfo     `json:"upstream,omitempty"`
	
	// 缓存相关
	CacheKey     string            `json:"cache_key,omitempty"`
	CacheEnabled bool              `json:"cache_enabled"`
	CacheTTL     time.Duration     `json:"cache_ttl,omitempty"`

	// 处理状态和控制标志
	StartTime       time.Time         `json:"start_time"`
	ProcessStage    string            `json:"process_stage"`
	ContentSource   string            `json:"content_source"`   // "cache", "static", "proxy"

	// 数字标识处理标志 (0=跳过, 1=执行, 2=强制执行)
	CacheReadFlag   int               `json:"cache_read_flag"`  // 缓存读取标志
	CacheWriteFlag  int               `json:"cache_write_flag"` // 缓存写入标志
	StaticFlag      int               `json:"static_flag"`      // 静态文件处理标志
	ProxyFlag       int               `json:"proxy_flag"`       // 代理处理标志
	MinifyFlag      int               `json:"minify_flag"`      // Minify处理标志
	ErrorFlag       int               `json:"error_flag"`       // 错误处理标志
	HeaderFlag      int               `json:"header_flag"`      // 头部处理标志

	// 兼容性字段（保留原有布尔值，逐步迁移）
	SkipCache       bool              `json:"skip_cache"`       // 是否跳过缓存处理
	SkipMinify      bool              `json:"skip_minify"`      // 是否跳过minify处理
	SkipUpstream    bool              `json:"skip_upstream"`    // 是否跳过上游请求
	RequireHeaders  bool              `json:"require_headers"`  // 是否需要头部处理
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
	
	// 原始HTTP请求对象（用于需要直接操作的场景）
	OriginalRequest *http.Request `json:"-"`
}

// ResponseContext 响应上下文 - 包含响应数据和处理信息
type ResponseContext struct {
	// 基础响应信息
	StatusCode    int               `json:"status_code"`
	Headers       http.Header       `json:"headers"`
	Body          []byte            `json:"body,omitempty"`
	ContentType   string            `json:"content_type"`
	ContentLength int64             `json:"content_length"`
	
	// 处理信息
	ProcessTime   time.Duration     `json:"process_time"`
	BackendIP     string            `json:"backend_ip,omitempty"`
	BackendPort   int               `json:"backend_port,omitempty"`
	
	// 缓存状态
	CacheStatus   string            `json:"cache_status"` // HIT, MISS, BYPASS, EXPIRED
	CacheSource   string            `json:"cache_source,omitempty"` // FILE, MEMORY

	// 响应体优化字段
	ResponseBody      []byte        `json:"response_body,omitempty"`       // 最终响应数据（用于缓存和发送）
	UseMemoryResponse bool          `json:"use_memory_response"`           // 是否直接使用内存数据发送响应
	ShouldOptimize    bool          `json:"should_optimize"`               // 是否应该进行响应体优化

	// 调试信息
	ProcessChain  []string          `json:"process_chain,omitempty"`
	DebugInfo     map[string]interface{} `json:"debug_info,omitempty"`
	
	// 错误信息
	Error         error             `json:"error,omitempty"`
	ErrorStage    string            `json:"error_stage,omitempty"`
	
	// 性能指标
	Metrics       *ProcessMetrics   `json:"metrics,omitempty"`
}

// SiteInfo 站点信息（从config.SiteConfig提取的关键信息）
type SiteInfo struct {
	SiteID         string                   `json:"site_id"`
	Name           string                   `json:"name"`
	Domains        []string                 `json:"domains"`
	DefaultSite    bool                     `json:"default_site"`
	ErrorPages     []config.ErrorPageConfig `json:"error_pages,omitempty"`
	Headers        config.HeadersConfig     `json:"headers,omitempty"`
	DenyTypes      []string                 `json:"deny_types,omitempty"`
	DenyURLs       []string                 `json:"deny_urls,omitempty"`
	CacheHeaders   *config.CacheHeadersConfig `json:"cache_headers,omitempty"`
	BandwidthLimit config.RateLimit         `json:"bandwidth_limit,omitempty"`
	MaxConnections int                      `json:"max_connections,omitempty"`
	Minify         *config.MinifyConfig     `json:"minify,omitempty"`
	Rules          []config.CacheRule       `json:"rules,omitempty"`        // 新增：站点级缓存规则
	StatusTTL      map[string]time.Duration `json:"status_ttl,omitempty"`   // 新增：站点级状态码TTL
	Config         *config.SiteConfig       `json:"-"`                      // 新增：完整配置引用（不序列化）
}

// RouteInfo 路由信息（从config.RouteConfig提取的关键信息）
type RouteInfo struct {
	Pattern         string                   `json:"pattern"`
	Upstream        string                   `json:"upstream,omitempty"`
	Rewrite         string                   `json:"rewrite,omitempty"`
	RewriteAdvanced *config.RewriteConfig    `json:"rewrite_advanced,omitempty"`
	Cache           bool                     `json:"cache"`
	StaticDir       string                   `json:"static_dir,omitempty"`
	MimeTypes       map[string]string        `json:"mime_types,omitempty"`
	DirListing      bool                     `json:"dir_listing"`
	IndexFiles      []string                 `json:"index_files,omitempty"`
	RateLimit       config.RateLimit         `json:"rate_limit,omitempty"`
	HiddenInListing []string                 `json:"hidden_in_listing,omitempty"`
	ErrorPages      []config.ErrorPageConfig `json:"error_pages,omitempty"`
	Headers         *config.HeadersConfig    `json:"headers,omitempty"`
	Minify          *config.MinifyConfig     `json:"minify,omitempty"`
}

// UpstreamInfo 上游服务器信息
type UpstreamInfo struct {
	Name      string `json:"name"`
	Address   string `json:"address"`
	Port      int    `json:"port"`
	HTTPSPort int    `json:"https_port,omitempty"`
	Protocol  string `json:"protocol"`
	Weight    int    `json:"weight"`
	Group     string `json:"group,omitempty"`
}

// ProcessMetrics 处理性能指标
type ProcessMetrics struct {
	DNSLookupTime    time.Duration `json:"dns_lookup_time,omitempty"`
	ConnectTime      time.Duration `json:"connect_time,omitempty"`
	TLSHandshakeTime time.Duration `json:"tls_handshake_time,omitempty"`
	RequestTime      time.Duration `json:"request_time,omitempty"`
	ResponseTime     time.Duration `json:"response_time,omitempty"`
	TotalTime        time.Duration `json:"total_time"`
	BytesReceived    int64         `json:"bytes_received"`
	BytesSent        int64         `json:"bytes_sent"`
}

// ProcessResult 处理结果
type ProcessResult struct {
	Success      bool              `json:"success"`
	Continue     bool              `json:"continue"`     // 是否继续执行后续处理器
	Error        error             `json:"error,omitempty"`
	Message      string            `json:"message,omitempty"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
}

// NewRequestContext 创建新的请求上下文
func NewRequestContext(r *http.Request, siteID string) *RequestContext {
	ctx := &RequestContext{
		SiteID:          siteID,
		ClientIP:        getClientIP(r),
		Method:          r.Method,
		URL:             r.URL,
		Headers:         r.Header.Clone(),
		ContentType:     r.Header.Get("Content-Type"),
		UserAgent:       r.Header.Get("User-Agent"),
		Referer:         r.Header.Get("Referer"),
		StartTime:       time.Now(),
		ProcessStage:    "INIT",
		Metadata:        make(map[string]interface{}),
		OriginalRequest: r,
	}

	// 读取请求体（对于POST、PUT、PATCH等方法）
	if r.Body != nil && (r.Method == "POST" || r.Method == "PUT" || r.Method == "PATCH" || r.Method == "DELETE") {
		if body, err := io.ReadAll(r.Body); err == nil {
			ctx.Body = body
			// 重新设置请求体，以便后续处理器可以再次读取
			r.Body = io.NopCloser(bytes.NewReader(body))
		}
	}

	// 设置默认处理标志（1=执行）
	ctx.initProcessFlags()

	return ctx
}

// initProcessFlags 初始化处理标志为默认值
func (req *RequestContext) initProcessFlags() {
	req.CacheReadFlag = 1   // 默认执行缓存读取
	req.CacheWriteFlag = 1  // 默认执行缓存写入
	req.StaticFlag = 1      // 默认执行静态文件处理
	req.ProxyFlag = 1       // 默认执行代理处理
	req.MinifyFlag = 1      // 默认执行Minify处理
	req.ErrorFlag = 1       // 默认执行错误处理
	req.HeaderFlag = 1      // 默认执行头部处理
}

// NewResponseContext 创建新的响应上下文
func NewResponseContext() *ResponseContext {
	return &ResponseContext{
		Headers:      make(http.Header),
		ProcessChain: make([]string, 0),
		DebugInfo:    make(map[string]interface{}),
		Metrics:      &ProcessMetrics{},
	}
}

// AddProcessStep 添加处理步骤记录
func (rc *ResponseContext) AddProcessStep(step string) {
	if rc.ProcessChain == nil {
		rc.ProcessChain = make([]string, 0)
	}
	rc.ProcessChain = append(rc.ProcessChain, step)
}

// SetDebugInfo 设置调试信息
func (rc *ResponseContext) SetDebugInfo(key string, value interface{}) {
	if rc.DebugInfo == nil {
		rc.DebugInfo = make(map[string]interface{})
	}
	rc.DebugInfo[key] = value
}

// SetMetadata 设置元数据
func (req *RequestContext) SetMetadata(key string, value interface{}) {
	if req.Metadata == nil {
		req.Metadata = make(map[string]interface{})
	}
	req.Metadata[key] = value
}

// GetMetadata 获取元数据
func (req *RequestContext) GetMetadata(key string) (interface{}, bool) {
	if req.Metadata == nil {
		return nil, false
	}
	value, exists := req.Metadata[key]
	return value, exists
}

// IsStaticRequest 判断是否为静态文件请求
func (req *RequestContext) IsStaticRequest() bool {
	return req.Route != nil && req.Route.StaticDir != ""
}

// IsProxyRequest 判断是否为代理请求
func (req *RequestContext) IsProxyRequest() bool {
	return req.Route != nil && req.Route.Upstream != ""
}

// getClientIP 获取客户端真实IP
func getClientIP(r *http.Request) string {
	// 优先级：X-Forwarded-For > X-Real-IP > RemoteAddr
	if xff := r.Header.Get("X-Forwarded-For"); xff != "" {
		// X-Forwarded-For 可能包含多个IP，取第一个
		if idx := strings.Index(xff, ","); idx != -1 {
			return strings.TrimSpace(xff[:idx])
		}
		return strings.TrimSpace(xff)
	}
	
	if xri := r.Header.Get("X-Real-IP"); xri != "" {
		return strings.TrimSpace(xri)
	}
	
	// 从 RemoteAddr 中提取IP（去除端口）
	if host, _, err := net.SplitHostPort(r.RemoteAddr); err == nil {
		return host
	}
	
	return r.RemoteAddr
}

use async_trait::async_trait;
use bytes::Bytes;
use std::collections::HashMap;
use std::path::PathBuf;
use std::time::SystemTime;
use tracing::{debug, error, info, warn};
use tokio::fs;

use super::{Processor, ProcessResult, RequestContext};

/// 错误页面配置
#[derive(Debug, <PERSON>lone)]
pub struct ErrorPageConfig {
    /// 自定义错误页面路径映射 (状态码 -> 文件路径)
    pub custom_pages: HashMap<u16, PathBuf>,
    /// 默认错误页面目录
    pub error_pages_dir: Option<PathBuf>,
    /// 是否显示详细错误信息
    pub show_details: bool,
    /// 是否显示堆栈跟踪
    pub show_stack_trace: bool,
    /// 错误页面缓存时间（秒）
    pub cache_duration: u64,
}

impl Default for ErrorPageConfig {
    fn default() -> Self {
        Self {
            custom_pages: HashMap::new(),
            error_pages_dir: None,
            show_details: false,
            show_stack_trace: false,
            cache_duration: 0, // 不缓存错误页面
        }
    }
}

/// 错误统计信息
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult)]
pub struct ErrorStats {
    pub total_errors: u64,
    pub client_errors: u64,  // 4xx
    pub server_errors: u64,  // 5xx
    pub error_counts: HashMap<u16, u64>, // 每个状态码的计数
    pub last_error_time: Option<SystemTime>,
}

/// 错误处理器配置
#[derive(Debug, Clone)]
pub struct ErrorProcessorConfig {
    pub page_config: ErrorPageConfig,
    pub enable_stats: bool,
    pub log_client_errors: bool,
    pub log_server_errors: bool,
    pub server_name: String,
}

impl Default for ErrorProcessorConfig {
    fn default() -> Self {
        Self {
            page_config: ErrorPageConfig::default(),
            enable_stats: true,
            log_client_errors: true,
            log_server_errors: true,
            server_name: "RustProxy/1.0".to_string(),
        }
    }
}

/// 错误处理器
pub struct ErrorProcessor {
    config: ErrorProcessorConfig,
    stats: std::sync::Arc<std::sync::Mutex<ErrorStats>>,
}

impl ErrorProcessor {
    /// 创建新的错误处理器
    pub fn new() -> Self {
        Self {
            config: ErrorProcessorConfig::default(),
            stats: std::sync::Arc::new(std::sync::Mutex::new(ErrorStats::default())),
        }
    }

    /// 使用指定配置创建错误处理器
    pub fn with_config(config: ErrorProcessorConfig) -> Self {
        Self {
            config,
            stats: std::sync::Arc::new(std::sync::Mutex::new(ErrorStats::default())),
        }
    }

    /// 获取错误统计信息
    pub fn get_stats(&self) -> ErrorStats {
        self.stats.lock().unwrap().clone()
    }

    /// 尝试加载自定义错误页面
    async fn load_custom_error_page(&self, status_code: u16) -> Option<String> {
        // 首先检查特定状态码的自定义页面
        if let Some(page_path) = self.config.page_config.custom_pages.get(&status_code) {
            if let Ok(content) = fs::read_to_string(page_path).await {
                return Some(content);
            }
        }

        // 然后检查错误页面目录中的文件
        if let Some(ref error_dir) = self.config.page_config.error_pages_dir {
            let page_path = error_dir.join(format!("{}.html", status_code));
            if let Ok(content) = fs::read_to_string(&page_path).await {
                return Some(content);
            }

            // 尝试通用错误页面
            let generic_paths = [
                error_dir.join("error.html"),
                error_dir.join("default.html"),
            ];

            for path in &generic_paths {
                if let Ok(content) = fs::read_to_string(path).await {
                    return Some(content);
                }
            }
        }

        None
    }

    /// 生成默认错误页面
    fn generate_error_page(&self, status_code: u16, message: &str, details: Option<&str>) -> String {
        let details_section = if self.config.page_config.show_details && details.is_some() {
            format!(
                r#"<div class="details">
                    <h3>Details</h3>
                    <pre>{}</pre>
                </div>"#,
                details.unwrap_or("")
            )
        } else {
            String::new()
        };

        let error_class = if status_code >= 500 { "server-error" } else { "client-error" };

        format!(
            r#"<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error {} - {}</title>
    <style>
        * {{ margin: 0; padding: 0; box-sizing: border-box; }}
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }}
        .error-container {{
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 600px;
            width: 90%;
            text-align: center;
        }}
        .error-icon {{
            font-size: 4rem;
            margin-bottom: 20px;
        }}
        .server-error .error-icon {{ color: #e74c3c; }}
        .client-error .error-icon {{ color: #f39c12; }}
        h1 {{
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }}
        .server-error h1 {{ color: #e74c3c; }}
        .client-error h1 {{ color: #f39c12; }}
        .error-code {{
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 20px;
        }}
        .error-message {{
            font-size: 1.1rem;
            color: #555;
            margin-bottom: 30px;
            line-height: 1.6;
        }}
        .details {{
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }}
        .details h3 {{
            color: #666;
            margin-bottom: 10px;
            font-size: 1rem;
        }}
        .details pre {{
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
            font-size: 0.9rem;
            line-height: 1.4;
        }}
        .footer {{
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #999;
            font-size: 0.9rem;
        }}
        .back-button {{
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            text-decoration: none;
            margin-top: 20px;
            transition: background 0.3s;
        }}
        .back-button:hover {{
            background: #5a6fd8;
        }}
    </style>
</head>
<body>
    <div class="error-container {}">
        <div class="error-icon">⚠️</div>
        <h1>Error {}</h1>
        <div class="error-code">HTTP {}</div>
        <div class="error-message">{}</div>
        {}
        <a href="javascript:history.back()" class="back-button">Go Back</a>
        <div class="footer">
            <p>Powered by {}</p>
            <p>Request ID: <code>{}</code></p>
        </div>
    </div>
</body>
</html>"#,
            status_code, message, error_class, status_code, status_code, message,
            details_section, self.config.server_name, "unknown"
        )
    }
    
    /// 获取状态码对应的默认消息
    fn get_default_message(&self, status_code: u16) -> &'static str {
        match status_code {
            // 4xx Client Errors
            400 => "The request could not be understood by the server due to malformed syntax.",
            401 => "The request requires user authentication.",
            403 => "The server understood the request, but is refusing to fulfill it.",
            404 => "The requested resource could not be found on this server.",
            405 => "The method specified in the request is not allowed for the resource.",
            406 => "The resource is not capable of generating content acceptable according to the Accept headers.",
            408 => "The server timed out waiting for the request.",
            409 => "The request could not be completed due to a conflict with the current state of the resource.",
            410 => "The requested resource is no longer available and will not be available again.",
            411 => "The request cannot be handled without a defined Content-Length.",
            412 => "The precondition given in one or more of the request-header fields evaluated to false.",
            413 => "The request entity is larger than the server is willing or able to process.",
            414 => "The Request-URI is longer than the server is willing to interpret.",
            415 => "The request entity has a media type which the server does not support.",
            416 => "The requested range cannot be satisfied.",
            417 => "The expectation given in an Expect request-header field could not be met.",
            418 => "I'm a teapot (RFC 2324).",
            422 => "The request was well-formed but was unable to be followed due to semantic errors.",
            423 => "The resource that is being accessed is locked.",
            424 => "The request failed due to failure of a previous request.",
            426 => "The client should switch to a different protocol.",
            428 => "The origin server requires the request to be conditional.",
            429 => "The user has sent too many requests in a given amount of time.",
            431 => "The server is unwilling to process the request because either an individual header field, or all the header fields collectively, are too large.",
            451 => "The server is denying access to the resource as a consequence of a legal demand.",

            // 5xx Server Errors
            500 => "The server encountered an unexpected condition which prevented it from fulfilling the request.",
            501 => "The server does not support the functionality required to fulfill the request.",
            502 => "The server, while acting as a gateway or proxy, received an invalid response from the upstream server.",
            503 => "The server is currently unable to handle the request due to a temporary overloading or maintenance.",
            504 => "The server, while acting as a gateway or proxy, did not receive a timely response from the upstream server.",
            505 => "The server does not support the HTTP protocol version used in the request.",
            506 => "Transparent content negotiation for the request results in a circular reference.",
            507 => "The server is unable to store the representation needed to complete the request.",
            508 => "The server detected an infinite loop while processing the request.",
            510 => "Further extensions to the request are required for the server to fulfill it.",
            511 => "The client needs to authenticate to gain network access.",

            _ => "An unknown error occurred.",
        }
    }

    /// 更新错误统计
    fn update_stats(&self, status_code: u16) {
        if !self.config.enable_stats {
            return;
        }

        if let Ok(mut stats) = self.stats.lock() {
            stats.total_errors += 1;
            stats.last_error_time = Some(SystemTime::now());

            if status_code >= 400 && status_code < 500 {
                stats.client_errors += 1;
            } else if status_code >= 500 {
                stats.server_errors += 1;
            }

            *stats.error_counts.entry(status_code).or_insert(0) += 1;
        }
    }

    /// 记录错误日志
    fn log_error(&self, ctx: &RequestContext, status_code: u16, message: &str) {
        let should_log = if status_code >= 500 {
            self.config.log_server_errors
        } else {
            self.config.log_client_errors
        };

        if !should_log {
            return;
        }

        let log_level = if status_code >= 500 { "ERROR" } else { "WARN" };
        let user_agent = ctx.get_header("user-agent").map(|s| s.as_str()).unwrap_or("Unknown");
        let referer = ctx.get_header("referer").map(|s| s.as_str()).unwrap_or("None");

        let log_message = format!(
            "[{}] {} {} - Status: {} - Message: {} - User-Agent: {} - Referer: {} - Remote: {}",
            log_level,
            ctx.method,
            ctx.path(),
            status_code,
            message,
            user_agent,
            referer,
            ctx.client_addr
        );

        if status_code >= 500 {
            error!("{}", log_message);
        } else {
            warn!("{}", log_message);
        }
    }

    /// 设置错误响应头
    fn set_error_headers(&self, ctx: &mut RequestContext, status_code: u16) {
        // 设置基本头部
        ctx.response.headers.insert("content-type".to_string(), "text/html; charset=utf-8".to_string());

        // 设置缓存头部
        if self.config.page_config.cache_duration > 0 {
            ctx.response.headers.insert(
                "cache-control".to_string(),
                format!("public, max-age={}", self.config.page_config.cache_duration)
            );
        } else {
            ctx.response.headers.insert("cache-control".to_string(), "no-cache, no-store, must-revalidate".to_string());
            ctx.response.headers.insert("pragma".to_string(), "no-cache".to_string());
            ctx.response.headers.insert("expires".to_string(), "0".to_string());
        }

        // 设置错误相关头部
        ctx.response.headers.insert("x-error-code".to_string(), status_code.to_string());

        // 对于某些错误，添加特定头部
        match status_code {
            401 => {
                ctx.response.headers.insert("www-authenticate".to_string(), "Basic realm=\"Restricted\"".to_string());
            }
            405 => {
                ctx.response.headers.insert("allow".to_string(), "GET, POST, PUT, DELETE, OPTIONS".to_string());
            }
            429 => {
                ctx.response.headers.insert("retry-after".to_string(), "60".to_string());
            }
            503 => {
                ctx.response.headers.insert("retry-after".to_string(), "300".to_string());
            }
            _ => {}
        }
    }

    /// 替换模板变量
    fn replace_template_variables(&self, content: &str, ctx: &RequestContext, status_code: u16, message: &str) -> String {
        let mut result = content.to_string();

        // 基本变量
        result = result.replace("{{STATUS_CODE}}", &status_code.to_string());
        result = result.replace("{{MESSAGE}}", message);
        result = result.replace("{{REQUEST_ID}}", &ctx.request_id);
        result = result.replace("{{METHOD}}", &ctx.method);
        result = result.replace("{{PATH}}", ctx.path());
        result = result.replace("{{SERVER_NAME}}", &self.config.server_name);

        // 时间相关
        let now = chrono::Utc::now();
        result = result.replace("{{TIMESTAMP}}", &now.to_rfc3339());
        result = result.replace("{{DATE}}", &now.format("%Y-%m-%d").to_string());
        result = result.replace("{{TIME}}", &now.format("%H:%M:%S").to_string());

        // 请求信息
        if let Some(user_agent) = ctx.get_header("user-agent") {
            result = result.replace("{{USER_AGENT}}", user_agent);
        } else {
            result = result.replace("{{USER_AGENT}}", "Unknown");
        }

        if let Some(referer) = ctx.get_header("referer") {
            result = result.replace("{{REFERER}}", referer);
        } else {
            result = result.replace("{{REFERER}}", "None");
        }

        result = result.replace("{{REMOTE_ADDR}}", &ctx.client_addr.to_string());

        // 处理时间
        let processing_time = ctx.start_time.elapsed().as_millis();
        result = result.replace("{{PROCESSING_TIME}}", &processing_time.to_string());

        // 错误详情（如果启用）
        if self.config.page_config.show_details {
            if let Some(body) = &ctx.response.body {
                if let Ok(details) = std::str::from_utf8(body) {
                    result = result.replace("{{ERROR_DETAILS}}", details);
                } else {
                    result = result.replace("{{ERROR_DETAILS}}", "Binary content");
                }
            } else {
                result = result.replace("{{ERROR_DETAILS}}", "No details available");
            }
        } else {
            result = result.replace("{{ERROR_DETAILS}}", "");
        }

        result
    }
}

impl Default for ErrorProcessor {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl Processor for ErrorProcessor {
    fn name(&self) -> &'static str {
        "ErrorProcessor"
    }
    
    fn priority(&self) -> u8 {
        100 // 最后执行，处理所有错误
    }
    
    fn should_process(&self, ctx: &RequestContext) -> bool {
        // 如果有错误或者响应状态码表示错误
        ctx.has_error() || ctx.response.status_code >= 400
    }
    
    async fn process(&self, ctx: &mut RequestContext) -> ProcessResult {
        let status_code = if let Some(error) = &ctx.error {
            error.status_code
        } else {
            ctx.response.status_code
        };

        let message = if let Some(error) = &ctx.error {
            error.message.clone()
        } else {
            self.get_default_message(status_code).to_string()
        };

        debug!(
            "ErrorProcessor: Handling error {} for request {}: {}",
            status_code, ctx.request_id, message
        );

        // 更新错误统计
        self.update_stats(status_code);

        // 记录错误日志
        self.log_error(ctx, status_code, &message);

        // 设置错误响应头
        self.set_error_headers(ctx, status_code);

        // 尝试加载自定义错误页面
        let error_content = if let Some(custom_content) = self.load_custom_error_page(status_code).await {
            // 替换模板变量
            self.replace_template_variables(&custom_content, ctx, status_code, &message)
        } else {
            // 生成默认错误页面
            let details = if self.config.page_config.show_details {
                ctx.response.body.as_ref().and_then(|b| std::str::from_utf8(b).ok())
            } else {
                None
            };
            self.generate_error_page(status_code, &message, details)
        };

        // 设置响应
        ctx.response.status_code = status_code;
        ctx.response.body = Some(Bytes::from(error_content));

        info!(
            "ErrorProcessor: Generated error page for status {} on request {} ({})",
            status_code, ctx.request_id, message
        );
        
        ProcessResult::Stop // 错误处理完成，停止处理链
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::processor::ProcessError;
    use std::collections::HashMap;
    use std::net::SocketAddr;
    
    fn create_test_context() -> RequestContext {
        RequestContext::new(
            "GET".to_string(),
            "/test".to_string(),
            HashMap::new(),
            "127.0.0.1:8080".parse::<SocketAddr>().unwrap(),
        )
    }
    
    #[tokio::test]
    async fn test_should_process_with_error() {
        let processor = ErrorProcessor::new();
        let mut ctx = create_test_context();
        
        // 没有错误时不应该处理
        assert!(!processor.should_process(&ctx));
        
        // 有错误时应该处理
        ctx.set_error(404, "Not found");
        assert!(processor.should_process(&ctx));
    }
    
    #[tokio::test]
    async fn test_should_process_with_error_status() {
        let processor = ErrorProcessor::new();
        let mut ctx = create_test_context();
        
        // 设置错误状态码
        ctx.response.status_code = 500;
        assert!(processor.should_process(&ctx));
        
        // 成功状态码不应该处理
        ctx.response.status_code = 200;
        assert!(!processor.should_process(&ctx));
    }
    
    #[tokio::test]
    async fn test_process_error() {
        let processor = ErrorProcessor::new();
        let mut ctx = create_test_context();
        ctx.set_error(404, "Page not found");
        
        let result = processor.process(&mut ctx).await;
        
        assert!(matches!(result, ProcessResult::Stop));
        assert_eq!(ctx.response.status_code, 404);
        assert!(ctx.response.body.is_some());
        assert_eq!(ctx.response.get_header("content-type"), Some(&"text/html; charset=utf-8".to_string()));
    }
    
    #[test]
    fn test_default_messages() {
        let processor = ErrorProcessor::new();
        
        assert_eq!(processor.get_default_message(404), "Not Found");
        assert_eq!(processor.get_default_message(500), "Internal Server Error");
        assert_eq!(processor.get_default_message(999), "Unknown Error");
    }
    
    #[test]
    fn test_generate_error_page() {
        let processor = ErrorProcessor::new();
        let html = processor.generate_error_page(404, "Not Found");
        
        assert!(html.contains("Error 404"));
        assert!(html.contains("Not Found"));
        assert!(html.contains("RustProxy/1.0"));
    }
}

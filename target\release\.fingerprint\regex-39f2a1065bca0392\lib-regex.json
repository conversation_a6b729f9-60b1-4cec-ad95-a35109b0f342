{"rustc": 28845397767708332, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "target": 7808655059340043471, "profile": 13540511011255890580, "path": 11445311098856210337, "deps": [[2421869795823446512, "aho_corasick", false, 17480596019571997746], [7864249588615721249, "regex_syntax", false, 16069410841654447692], [16554181743699084660, "regex_automata", false, 15019820113410397862], [17151257692364156824, "memchr", false, 5880979238900167707]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\regex-39f2a1065bca0392\\dep-lib-regex"}}], "rustflags": [], "metadata": 13774875050488081270, "config": 2202906307356721367, "compile_kind": 0}
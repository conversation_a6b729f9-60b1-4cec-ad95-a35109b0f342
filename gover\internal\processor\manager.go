package processor

import (
	"fmt"

	"github.com/sirupsen/logrus"
	"reverse-proxy/internal/config"
	"reverse-proxy/internal/cache"
)

// ProcessorManager 处理器管理器
type ProcessorManager struct {
	chain           *ProcessorChain
	routeProcessor      *RouteProcessor
	staticProcessor     *StaticProcessor
	proxyProcessor      *ProxyProcessor
	cacheReadProcessor  *CacheReadProcessor
	cacheWriteProcessor *CacheWriteProcessor
	errorProcessor      *ErrorProcessor
	headerProcessor     *HeaderProcessor
	minifyProcessor     *MinifyProcessor
	logger          *logrus.Logger
	monitor         MonitorInterface // 添加监控接口
}

// MonitorInterface 监控接口（避免循环依赖）
type MonitorInterface interface {
	RecordUpstreamTrafficWithKey(upKey string, bytesReceived, bytesSent int64)
}

// NewProcessorManager 创建处理器管理器
func NewProcessorManager(logger *logrus.Logger, globalConfig *config.Config) *ProcessorManager {
	pm := &ProcessorManager{
		logger: logger,
	}
	
	// 创建处理器链
	pm.chain = NewProcessorChain(logger)
	
	// 创建各个处理器
	pm.routeProcessor = NewRouteProcessor(logger)
	pm.staticProcessor = NewStaticProcessor(logger)

	// 创建 ProxyProcessor，传递响应体优化配置
	var optimizeConfig *config.ResponseOptimizeConfig
	if globalConfig != nil && globalConfig.Performance.ResponseOptimize.Enabled {
		optimizeConfig = &globalConfig.Performance.ResponseOptimize
	}
	pm.proxyProcessor = NewProxyProcessor(logger, optimizeConfig)
	pm.errorProcessor = NewErrorProcessor(logger)

	// 创建 HeaderProcessor，传递全局缓存头部配置和全局头部配置
	var globalCacheHeadersConfig *config.CacheHeadersConfig
	if globalConfig != nil && globalConfig.Cache.CacheHeaders != nil {
		globalCacheHeadersConfig = globalConfig.Cache.CacheHeaders
	}

	var globalHeadersConfig *config.HeadersConfig
	if globalConfig != nil {
		globalHeadersConfig = &globalConfig.Headers
	}

	pm.headerProcessor = NewHeaderProcessor(logger, globalCacheHeadersConfig, globalHeadersConfig)

	// 创建 MinifyProcessor，传递全局配置
	var globalMinifyConfig *config.MinifyConfig
	if globalConfig != nil {
		globalMinifyConfig = &globalConfig.Minify
	}
	pm.minifyProcessor = NewMinifyProcessor(logger, globalMinifyConfig)
	
	// 创建缓存管理器
	var cacheManager *cache.CacheManager
	cacheEnabled := false

	if globalConfig != nil && globalConfig.Cache.Enabled {
		var err error
		cacheManager, err = cache.NewCacheManager(globalConfig.Cache)
		if err != nil {
			logger.Errorf("创建缓存管理器失败: %v", err)
		} else {
			cacheEnabled = true
		}
	}

	// 创建分离的缓存处理器
	pm.cacheReadProcessor = NewCacheReadProcessor(cacheEnabled, cacheManager, logger)
	pm.cacheWriteProcessor = NewCacheWriteProcessor(cacheEnabled, cacheManager, logger)

	// 按优先级添加处理器到链中
	pm.chain.AddProcessor(pm.routeProcessor)       // 100 - 路由匹配和状态设置
	pm.chain.AddProcessor(pm.cacheReadProcessor)   // 150 - 缓存读取
	pm.chain.AddProcessor(pm.staticProcessor)      // 300 - 静态文件处理
	pm.chain.AddProcessor(pm.proxyProcessor)       // 400 - 反向代理处理
	pm.chain.AddProcessor(pm.minifyProcessor)      // 500 - 内容最小化
	pm.chain.AddProcessor(pm.cacheWriteProcessor)  // 550 - 缓存写入
	pm.chain.AddProcessor(pm.errorProcessor)       // 800 - 错误页面处理
	pm.chain.AddProcessor(pm.headerProcessor)      // 900 - 最终头部处理
	
	logger.Info("处理器管理器初始化完成")
	return pm
}

// SetMonitor 设置监控接口
func (pm *ProcessorManager) SetMonitor(monitor MonitorInterface) {
	pm.monitor = monitor
	// 将监控接口传递给需要的处理器
	if pm.proxyProcessor != nil {
		pm.proxyProcessor.SetMonitor(monitor)
		pm.logger.Info("监控接口已设置到ProxyProcessor")
	} else {
		pm.logger.Warn("ProxyProcessor为nil，无法设置监控接口")
	}
	pm.logger.Info("监控接口已设置到处理器管理器")
}

// ProcessRequest 处理请求
func (pm *ProcessorManager) ProcessRequest(req *RequestContext, resp *ResponseContext) error {
	// 执行处理器链
	if err := pm.chain.Execute(req, resp); err != nil {
		pm.logger.Errorf("处理器链执行失败: %v", err)
		
		// 如果没有设置状态码，设置为500
		if resp.StatusCode == 0 {
			resp.StatusCode = 500
		}
		
		// 尝试渲染错误页面
		pm.renderErrorResponse(req, resp, err)
		
		return err
	}
	
	// 后处理阶段
	pm.postProcess(req, resp)
	
	return nil
}

// postProcess 后处理阶段 - 缓存写入现在由CacheWriteProcessor处理
func (pm *ProcessorManager) postProcess(req *RequestContext, resp *ResponseContext) {
	// 缓存写入现在由处理器链中的CacheWriteProcessor处理
	// 这里保留方法以保持兼容性，但实际逻辑已移至处理器链
}

// renderErrorResponse 渲染错误响应
func (pm *ProcessorManager) renderErrorResponse(req *RequestContext, resp *ResponseContext, err error) {
	// 如果错误处理器还没有处理过，尝试处理
	if resp.ErrorStage != "ErrorProcessor" {
		result := pm.errorProcessor.Process(req, resp)
		if result != nil && !result.Success {
			// 错误处理器也失败了，使用默认错误页面
			pm.useDefaultErrorPage(req, resp)
		}
	} else {
		// 错误处理器已经处理过了，使用默认错误页面
		pm.useDefaultErrorPage(req, resp)
	}
}

// useDefaultErrorPage 使用默认错误页面
func (pm *ProcessorManager) useDefaultErrorPage(req *RequestContext, resp *ResponseContext) {
	defaultHTML := pm.errorProcessor.GenerateDefaultErrorPage(req, resp)
	resp.Body = []byte(defaultHTML)
	resp.ContentLength = int64(len(defaultHTML))
	resp.ContentType = "text/html; charset=utf-8"
	resp.Headers.Set("Content-Type", "text/html; charset=utf-8")
}

// finalizeHeaders 最终化响应头
func (pm *ProcessorManager) finalizeHeaders(req *RequestContext, resp *ResponseContext) {
	// 确保Content-Length正确
	if resp.ContentLength == 0 && len(resp.Body) > 0 {
		resp.ContentLength = int64(len(resp.Body))
	}

	// 设置Content-Length头
	if resp.ContentLength > 0 {
		resp.Headers.Set("Content-Length", fmt.Sprintf("%d", resp.ContentLength))
	}

	// 设置Content-Type头（如果还没有设置）
	if resp.ContentType != "" && resp.Headers.Get("Content-Type") == "" {
		resp.Headers.Set("Content-Type", resp.ContentType)
	}

	// 处理头部（包括缓存头部和安全头部）
	pm.headerProcessor.Process(req, resp)
}

// UpdateSites 更新站点配置
func (pm *ProcessorManager) UpdateSites(sites map[string]*config.SiteConfig) error {
	// 转换站点配置
	siteInfos := make(map[string]*SiteInfo)
	upstreams := make(map[string]*UpstreamInfo)
	
	for siteID, siteConfig := range sites {
		// 转换站点信息
		siteInfo := &SiteInfo{
			SiteID:         siteConfig.SiteID,
			Name:           siteConfig.Name,
			Domains:        siteConfig.Domains,
			DefaultSite:    siteConfig.DefaultSite,
			ErrorPages:     siteConfig.ErrorPages,
			Headers:        siteConfig.Headers,
			DenyTypes:      siteConfig.DenyTypes,
			DenyURLs:       siteConfig.DenyURLs,
			CacheHeaders:   siteConfig.CacheHeaders,
			BandwidthLimit: siteConfig.BandwidthLimit,
			MaxConnections: siteConfig.MaxConnections,
			Rules:          siteConfig.Rules,     // 新增：站点级缓存规则
			StatusTTL:      siteConfig.StatusTTL, // 新增：站点级状态码TTL
			Config:         siteConfig,           // 新增：完整配置引用
		}
		siteInfos[siteID] = siteInfo
		
		// 收集上游服务器信息
		for _, routeConfig := range siteConfig.Routes {
			if routeConfig.Upstream != "" {
				// 从站点的上游服务器配置中查找
				if _, exists := upstreams[routeConfig.Upstream]; !exists {
					// 查找负载均衡组中的第一个可用服务器
					for _, upstreamConfig := range siteConfig.Upstreams {
						if upstreamConfig.LoadBalanceGroup == routeConfig.Upstream {
							protocol := upstreamConfig.Protocol
							port := upstreamConfig.Port

							// 处理协议和端口的逻辑
							if protocol == "" || protocol == "passthrough" {
								// 对于passthrough协议，使用客户端请求的协议
								// 这个逻辑需要在实际处理请求时根据客户端协议来确定
								// 这里先设置为passthrough，在ProxyProcessor中再处理
								protocol = "passthrough"
								if port == 0 {
									port = 80 // 默认端口，实际使用时会根据协议调整
								}
							} else {
								// 明确指定了协议，使用对应的默认端口
								if port == 0 {
									if protocol == "https" {
										port = 443
									} else {
										port = 80
									}
								}
							}

							upstreams[routeConfig.Upstream] = &UpstreamInfo{
								Name:      routeConfig.Upstream,
								Address:   upstreamConfig.Address,
								Port:      port,
								HTTPSPort: upstreamConfig.HTTPSPort,
								Protocol:  protocol,
								Group:     upstreamConfig.LoadBalanceGroup,
							}
							break // 使用第一个匹配的服务器
						}
					}
				}
			}
		}
	}
	
	// 更新路由处理器
	if err := pm.routeProcessor.UpdateSites(sites); err != nil {
		return fmt.Errorf("更新路由处理器失败: %w", err)
	}
	
	// 更新代理处理器
	pm.proxyProcessor.UpdateUpstreams(upstreams)
	
	pm.logger.Infof("更新了 %d 个站点配置", len(sites))
	return nil
}

// UpdateUpstreams 更新上游服务器配置
func (pm *ProcessorManager) UpdateUpstreams(upstreams map[string]*config.UpstreamConfig) {
	upstreamInfos := make(map[string]*UpstreamInfo)
	
	for name, upstream := range upstreams {
		upstreamInfos[name] = &UpstreamInfo{
			Name:     name,
			Address:  upstream.Address,
			Port:     upstream.Port,
			Protocol: upstream.Protocol,
			Weight:   upstream.Weight,
			Group:    upstream.LoadBalanceGroup,
		}
	}
	
	pm.proxyProcessor.UpdateUpstreams(upstreamInfos)
	pm.logger.Infof("更新了 %d 个上游服务器配置", len(upstreams))
}

// GetStats 获取处理器统计信息
func (pm *ProcessorManager) GetStats() map[string]interface{} {
	stats := map[string]interface{}{
		"processors": make(map[string]interface{}),
	}
	
	// 缓存统计 - 现在由分离的缓存处理器提供
	// TODO: 实现分离缓存处理器的统计功能
	
	// 处理器链统计
	processors := pm.chain.GetProcessors()
	processorNames := make([]string, len(processors))
	for i, processor := range processors {
		processorNames[i] = processor.Name()
	}
	stats["processor_chain"] = processorNames
	
	return stats
}

// CleanCache 清理缓存
func (pm *ProcessorManager) CleanCache() error {
	// 缓存清理现在由CacheManager处理
	// TODO: 实现通过缓存管理器清理缓存
	return nil
}

// GetProcessor 获取指定处理器
func (pm *ProcessorManager) GetProcessor(name string) Processor {
	processors := pm.chain.GetProcessors()
	for _, processor := range processors {
		if processor.Name() == name {
			return processor
		}
	}
	return nil
}

// RemoveProcessor 移除处理器
func (pm *ProcessorManager) RemoveProcessor(name string) bool {
	return pm.chain.RemoveProcessor(name)
}

// AddProcessor 添加处理器
func (pm *ProcessorManager) AddProcessor(processor Processor) {
	pm.chain.AddProcessor(processor)
}

// GetChain 获取处理器链
func (pm *ProcessorManager) GetChain() *ProcessorChain {
	return pm.chain
}

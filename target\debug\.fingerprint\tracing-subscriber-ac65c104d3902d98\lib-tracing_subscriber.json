{"rustc": 28845397767708332, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\"]", "target": 2945592894759063892, "profile": 3769674782862552539, "path": 12176239129103550620, "deps": [[499765152579515920, "matchers", false, 12800730012900219197], [3434590410718299011, "once_cell", false, 6348901782573188797], [4119535177386780416, "regex", false, 10454634442856568169], [4895452409045649178, "tracing", false, 9034465696668269535], [5565040350528839985, "nu_ansi_term", false, 11697553626142144463], [8011159453640554525, "sharded_slab", false, 8393441863644199106], [10539385342026833603, "smallvec", false, 1957887198712026695], [11178864870005554864, "tracing_log", false, 9289235069439578812], [12016117908302098842, "thread_local", false, 7039553016859540278], [17427259574003854778, "tracing_core", false, 4839774607853583335]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tracing-subscriber-ac65c104d3902d98\\dep-lib-tracing_subscriber"}}], "rustflags": [], "metadata": 1308049149787906361, "config": 2202906307356721367, "compile_kind": 0}
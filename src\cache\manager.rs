use anyhow::Result;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::RwLock;
use tokio::time::interval;
use tracing::{debug, error, info, warn};

use super::{
    CacheBackend, CacheConfig, CacheEntry, CacheEvent, CacheEventListener, CacheResult,
    CacheStats, CacheType, LoggingCacheEventListener,
};
use super::file_cache::FileCache;
use super::memory_cache::MemoryCache;
use super::hybrid_cache::HybridCache;

#[cfg(feature = "redis-cache")]
use super::redis_cache::RedisCache;

/// 缓存管理器
pub struct CacheManager {
    backend: Arc<dyn CacheBackend>,
    config: CacheConfig,
    stats: Arc<RwLock<CacheStats>>,
    event_listeners: Vec<Arc<dyn CacheEventListener>>,
    cleanup_handle: Option<tokio::task::Join<PERSON><PERSON><PERSON><()>>,
}

impl CacheManager {
    /// 创建新的缓存管理器
    pub async fn new(config: CacheConfig) -> Result<Self> {
        let backend = Self::create_backend(&config).await?;
        let stats = Arc::new(RwLock::new(CacheStats::default()));
        
        let mut manager = Self {
            backend,
            config: config.clone(),
            stats,
            event_listeners: vec![Arc::new(LoggingCacheEventListener)],
            cleanup_handle: None,
        };
        
        // 启动清理任务
        if config.async_cleanup {
            manager.start_cleanup_task().await;
        }
        
        info!("Cache manager initialized with type: {:?}", config.cache_type);
        Ok(manager)
    }
    
    /// 创建缓存后端
    async fn create_backend(config: &CacheConfig) -> Result<Arc<dyn CacheBackend>> {
        match config.cache_type {
            CacheType::Memory => {
                let cache = MemoryCache::new(config.memory_config.clone()).await?;
                Ok(Arc::new(cache))
            }
            CacheType::File => {
                let path = config.file_path.as_ref()
                    .ok_or_else(|| anyhow::anyhow!("File path required for file cache"))?;
                let cache = FileCache::new(path.clone(), config.max_size).await?;
                Ok(Arc::new(cache))
            }
            #[cfg(feature = "redis-cache")]
            CacheType::Redis => {
                let redis_config = config.redis_config.as_ref()
                    .ok_or_else(|| anyhow::anyhow!("Redis config required for Redis cache"))?;
                let cache = RedisCache::new(redis_config.clone()).await?;
                Ok(Arc::new(cache))
            }
            #[cfg(not(feature = "redis-cache"))]
            CacheType::Redis => {
                Err(anyhow::anyhow!("Redis cache not enabled. Enable 'redis-cache' feature"))
            }
            CacheType::Hybrid => {
                let file_path = config.file_path.as_ref()
                    .ok_or_else(|| anyhow::anyhow!("File path required for hybrid cache"))?;
                let cache = HybridCache::new(
                    config.memory_config.clone(),
                    file_path.clone(),
                    config.max_size,
                ).await?;
                Ok(Arc::new(cache))
            }
        }
    }
    
    /// 获取缓存条目
    pub async fn get(&self, key: &str) -> CacheResult {
        match self.backend.get(key).await {
            Ok(Some(mut entry)) => {
                if entry.is_expired() {
                    // 异步删除过期条目
                    let backend = self.backend.clone();
                    let key = key.to_string();
                    tokio::spawn(async move {
                        if let Err(e) = backend.delete(&key).await {
                            error!("Failed to delete expired cache entry {}: {}", key, e);
                        }
                    });
                    
                    self.record_miss(key).await;
                    CacheResult::Miss
                } else {
                    entry.update_access();
                    self.record_hit(key, entry.size).await;
                    CacheResult::Hit(entry)
                }
            }
            Ok(None) => {
                self.record_miss(key).await;
                CacheResult::Miss
            }
            Err(e) => {
                error!("Cache get error for key {}: {}", key, e);
                CacheResult::Error(e)
            }
        }
    }
    
    /// 设置缓存条目
    pub async fn set(&self, key: &str, entry: CacheEntry) -> Result<()> {
        let size = entry.size;
        let ttl = entry.expires_at.duration_since(entry.created_at)
            .unwrap_or(self.config.default_ttl);
        
        match self.backend.set(key, entry).await {
            Ok(()) => {
                self.record_write(key, size, ttl).await;
                Ok(())
            }
            Err(e) => {
                error!("Cache set error for key {}: {}", key, e);
                Err(e)
            }
        }
    }
    
    /// 删除缓存条目
    pub async fn delete(&self, key: &str) -> Result<bool> {
        match self.backend.delete(key).await {
            Ok(deleted) => {
                if deleted {
                    self.record_delete(key).await;
                }
                Ok(deleted)
            }
            Err(e) => {
                error!("Cache delete error for key {}: {}", key, e);
                Err(e)
            }
        }
    }
    
    /// 检查缓存条目是否存在
    pub async fn exists(&self, key: &str) -> Result<bool> {
        self.backend.exists(key).await
    }
    
    /// 清空所有缓存
    pub async fn clear(&self) -> Result<()> {
        self.backend.clear().await?;
        
        // 重置统计信息
        let mut stats = self.stats.write().await;
        *stats = CacheStats::default();
        
        info!("Cache cleared");
        Ok(())
    }
    
    /// 获取缓存统计信息
    pub async fn stats(&self) -> Result<CacheStats> {
        // 合并本地统计和后端统计
        let local_stats = self.stats.read().await.clone();
        let backend_stats = self.backend.stats().await?;
        
        Ok(CacheStats {
            total_requests: local_stats.total_requests,
            hits: local_stats.hits,
            misses: local_stats.misses,
            writes: local_stats.writes,
            deletes: local_stats.deletes,
            size_bytes: backend_stats.size_bytes,
            entry_count: backend_stats.entry_count,
            cleanup_count: backend_stats.cleanup_count,
        })
    }
    
    /// 手动清理过期条目
    pub async fn cleanup_expired(&self) -> Result<u64> {
        let start = std::time::Instant::now();
        let removed = self.backend.cleanup_expired().await?;
        let duration = start.elapsed();
        
        if removed > 0 {
            self.emit_event(CacheEvent::Cleanup { removed, duration }).await;
            info!("Cleaned up {} expired cache entries in {:?}", removed, duration);
        }
        
        Ok(removed)
    }
    
    /// 获取所有缓存键
    pub async fn keys(&self) -> Result<Vec<String>> {
        self.backend.keys().await
    }
    
    /// 获取缓存大小
    pub async fn size(&self) -> Result<u64> {
        self.backend.size().await
    }
    
    /// 添加事件监听器
    pub fn add_event_listener(&mut self, listener: Arc<dyn CacheEventListener>) {
        self.event_listeners.push(listener);
    }
    
    /// 启动清理任务
    async fn start_cleanup_task(&mut self) {
        let backend = self.backend.clone();
        let cleanup_interval = self.config.cleanup_interval;
        let event_listeners = self.event_listeners.clone();
        
        let handle = tokio::spawn(async move {
            let mut interval = interval(cleanup_interval);
            
            loop {
                interval.tick().await;
                
                let start = std::time::Instant::now();
                match backend.cleanup_expired().await {
                    Ok(removed) => {
                        if removed > 0 {
                            let duration = start.elapsed();
                            let event = CacheEvent::Cleanup { removed, duration };
                            
                            for listener in &event_listeners {
                                listener.on_event(event.clone()).await;
                            }
                            
                            debug!("Cleanup task removed {} expired entries in {:?}", removed, duration);
                        }
                    }
                    Err(e) => {
                        error!("Cleanup task error: {}", e);
                    }
                }
            }
        });
        
        self.cleanup_handle = Some(handle);
        info!("Cache cleanup task started with interval: {:?}", cleanup_interval);
    }
    
    /// 记录缓存命中
    async fn record_hit(&self, key: &str, size: usize) {
        let mut stats = self.stats.write().await;
        stats.record_hit();
        
        self.emit_event(CacheEvent::Hit {
            key: key.to_string(),
            size,
        }).await;
    }
    
    /// 记录缓存未命中
    async fn record_miss(&self, key: &str) {
        let mut stats = self.stats.write().await;
        stats.record_miss();
        
        self.emit_event(CacheEvent::Miss {
            key: key.to_string(),
        }).await;
    }
    
    /// 记录缓存写入
    async fn record_write(&self, key: &str, size: usize, ttl: Duration) {
        let mut stats = self.stats.write().await;
        stats.record_write(size);
        
        self.emit_event(CacheEvent::Set {
            key: key.to_string(),
            size,
            ttl,
        }).await;
    }
    
    /// 记录缓存删除
    async fn record_delete(&self, key: &str) {
        self.emit_event(CacheEvent::Delete {
            key: key.to_string(),
        }).await;
    }
    
    /// 发送事件
    async fn emit_event(&self, event: CacheEvent) {
        for listener in &self.event_listeners {
            listener.on_event(event.clone()).await;
        }
    }
    
    /// 获取站点缓存目录
    pub fn get_site_cache_dir(&self, site_name: &str) -> Option<std::path::PathBuf> {
        self.config.file_path.as_ref().map(|base| base.join(site_name))
    }
}

impl Drop for CacheManager {
    fn drop(&mut self) {
        if let Some(handle) = self.cleanup_handle.take() {
            handle.abort();
        }
    }
}

/// 缓存管理器构建器
pub struct CacheManagerBuilder {
    config: CacheConfig,
    event_listeners: Vec<Arc<dyn CacheEventListener>>,
}

impl CacheManagerBuilder {
    /// 创建新的构建器
    pub fn new(cache_type: CacheType) -> Self {
        Self {
            config: CacheConfig {
                cache_type,
                file_path: None,
                redis_config: None,
                memory_config: super::MemoryCacheConfig {
                    max_size: 64 * 1024 * 1024, // 64MB
                    max_entries: 10000,
                    ttl: Duration::from_secs(3600), // 1 hour
                    cleanup_interval: Duration::from_secs(300), // 5 minutes
                },
                default_ttl: Duration::from_secs(3600),
                max_size: 1024 * 1024 * 1024, // 1GB
                cleanup_interval: Duration::from_secs(300),
                async_cleanup: true,
            },
            event_listeners: vec![],
        }
    }
    
    /// 设置文件路径
    pub fn file_path(mut self, path: std::path::PathBuf) -> Self {
        self.config.file_path = Some(path);
        self
    }
    
    /// 设置Redis配置
    pub fn redis_config(mut self, config: super::RedisConfig) -> Self {
        self.config.redis_config = Some(config);
        self
    }
    
    /// 设置内存缓存配置
    pub fn memory_config(mut self, config: super::MemoryCacheConfig) -> Self {
        self.config.memory_config = config;
        self
    }
    
    /// 设置默认TTL
    pub fn default_ttl(mut self, ttl: Duration) -> Self {
        self.config.default_ttl = ttl;
        self
    }
    
    /// 设置最大缓存大小
    pub fn max_size(mut self, size: u64) -> Self {
        self.config.max_size = size;
        self
    }
    
    /// 设置清理间隔
    pub fn cleanup_interval(mut self, interval: Duration) -> Self {
        self.config.cleanup_interval = interval;
        self
    }
    
    /// 设置是否启用异步清理
    pub fn async_cleanup(mut self, enabled: bool) -> Self {
        self.config.async_cleanup = enabled;
        self
    }
    
    /// 添加事件监听器
    pub fn add_event_listener(mut self, listener: Arc<dyn CacheEventListener>) -> Self {
        self.event_listeners.push(listener);
        self
    }
    
    /// 构建缓存管理器
    pub async fn build(self) -> Result<CacheManager> {
        let mut manager = CacheManager::new(self.config).await?;
        
        for listener in self.event_listeners {
            manager.add_event_listener(listener);
        }
        
        Ok(manager)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use bytes::Bytes;
    use tempfile::TempDir;
    
    #[tokio::test]
    async fn test_memory_cache_manager() {
        let manager = CacheManagerBuilder::new(CacheType::Memory)
            .max_size(1024 * 1024) // 1MB
            .build()
            .await
            .unwrap();
        
        let entry = CacheEntry::new(
            Bytes::from("test data"),
            "text/plain".to_string(),
            200,
            vec![],
            Duration::from_secs(60),
        );
        
        // 测试设置和获取
        manager.set("test_key", entry.clone()).await.unwrap();
        
        let result = manager.get("test_key").await;
        assert!(result.is_hit());
        
        let retrieved_entry = result.into_entry().unwrap();
        assert_eq!(retrieved_entry.data, entry.data);
        assert_eq!(retrieved_entry.content_type, entry.content_type);
        
        // 测试统计信息
        let stats = manager.stats().await.unwrap();
        assert_eq!(stats.hits, 1);
        assert_eq!(stats.writes, 1);
        assert!(stats.hit_rate() > 0.0);
    }
    
    #[tokio::test]
    async fn test_file_cache_manager() {
        let temp_dir = TempDir::new().unwrap();
        let cache_path = temp_dir.path().to_path_buf();
        
        let manager = CacheManagerBuilder::new(CacheType::File)
            .file_path(cache_path)
            .max_size(1024 * 1024) // 1MB
            .build()
            .await
            .unwrap();
        
        let entry = CacheEntry::new(
            Bytes::from("file test data"),
            "text/plain".to_string(),
            200,
            vec![],
            Duration::from_secs(60),
        );
        
        // 测试设置和获取
        manager.set("file_test_key", entry.clone()).await.unwrap();
        
        let result = manager.get("file_test_key").await;
        assert!(result.is_hit());
        
        let retrieved_entry = result.into_entry().unwrap();
        assert_eq!(retrieved_entry.data, entry.data);
    }
    
    #[tokio::test]
    async fn test_cache_expiration() {
        let manager = CacheManagerBuilder::new(CacheType::Memory)
            .build()
            .await
            .unwrap();
        
        let entry = CacheEntry::new(
            Bytes::from("expiring data"),
            "text/plain".to_string(),
            200,
            vec![],
            Duration::from_millis(100), // 很短的TTL
        );
        
        manager.set("expiring_key", entry).await.unwrap();
        
        // 立即获取应该命中
        let result = manager.get("expiring_key").await;
        assert!(result.is_hit());
        
        // 等待过期
        tokio::time::sleep(Duration::from_millis(150)).await;
        
        // 再次获取应该未命中
        let result = manager.get("expiring_key").await;
        assert!(result.is_miss());
    }
}

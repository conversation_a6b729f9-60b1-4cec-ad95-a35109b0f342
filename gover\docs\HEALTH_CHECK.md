# 健康检查功能说明

## 概述

健康检查功能用于监控后端服务器的状态，实现自动故障转移和高可用性。支持域名健康检查，特别适用于PHP-FPM等应用服务器的监控。

## 健康检查机制

### 重要原则
**健康检查使用IP地址连接，但设置正确的域名Host头**

### 工作原理
1. **网络连接**: 直接使用后端服务器IP地址建立TCP连接
2. **HTTP请求**: 发送HTTP请求到IP地址
3. **Host头**: 设置Host头为配置的域名
4. **避免DNS**: 不进行DNS解析，确保检查的准确性

### 优势
- **避免DNS依赖**: 不依赖DNS解析，确保健康检查的准确性
- **减少延迟**: 直接连接IP地址，减少DNS解析时间
- **正确模拟**: 设置正确的Host头，模拟真实客户端请求
- **虚拟主机支持**: 支持基于域名的虚拟主机配置

## 配置参数

### 基本配置

```json
{
  "name": "primary",
  "address": "*************",
  "port": 80,
  "protocol": "passthrough",
  "https_port": 443,
  "weight": 100,
  "max_fails": 3,
  "fail_timeout": "30s",
  "backup": false,
  "health_check": "http://*************/health.php",
  "health_host": "tingtao.org",
  "health_interval": "10s",
  "health_timeout": "5s",
  "health_path": "/health.php"
}
```

### 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `health_check` | string | - | 完整的健康检查URL（使用IP地址） |
| `health_host` | string | - | **健康检查Host头** - 设置HTTP请求的Host头 |
| `health_interval` | duration | "10s" | **检查间隔** - 健康检查的执行频率 |
| `health_timeout` | duration | "5s" | **检查超时** - 单次健康检查的超时时间 |
| `health_path` | string | "/health" | 健康检查路径 |
| `max_fails` | int | 3 | 最大失败次数 |
| `fail_timeout` | duration | "30s" | 失败超时时间 |

## 域名健康检查

### 为什么需要域名健康检查？

1. **多域名支持**: 后端服务器可能绑定多个域名
2. **应用层检查**: 重点检查PHP-FPM等应用服务状态
3. **虚拟主机**: 支持基于域名的虚拟主机配置
4. **正确模拟**: 模拟真实客户端的请求行为

### 域名健康检查配置

#### 方式1：使用 `health_host` 字段
```json
{
  "name": "primary",
  "address": "*************",
  "port": 80,
  "health_host": "example.com",
  "health_path": "/health.php"
}
```
**实际请求**:
- 连接: `*************:80`
- Host头: `example.com`
- URL: `http://*************:80/health.php`

#### 方式2：使用 `health_check` 完整URL
```json
{
  "name": "primary",
  "address": "*************",
  "port": 80,
  "health_check": "http://*************/health.php",
  "health_host": "example.com"
}
```
**实际请求**:
- 连接: `*************:80`
- Host头: `example.com`
- URL: `http://*************:80/health.php`

#### 方式3：多域名健康检查
```json
"upstreams": [
  {
    "name": "primary",
    "address": "*************",
    "port": 80,
    "health_host": "example.com",
    "health_path": "/health.php"
  },
  {
    "name": "backup",
    "address": "*************",
    "port": 80,
    "health_host": "www.example.com",
    "health_path": "/health.php"
  }
]
```

## PHP-FPM健康检查

### PHP健康检查页面

#### 简单版本 (`health.php`)
```php
<?php
header('Content-Type: application/json');
header('Cache-Control: no-cache, no-store, must-revalidate');

$status = 'ok';
$message = 'PHP-FPM运行正常';

// 检查PHP-FPM函数是否可用
if (!function_exists('fastcgi_finish_request')) {
    $status = 'error';
    $message = 'PHP-FPM不可用';
    http_response_code(503);
} else {
    http_response_code(200);
}

echo json_encode([
    'status' => $status,
    'message' => $message,
    'timestamp' => time(),
    'datetime' => date('Y-m-d H:i:s'),
    'php_version' => PHP_VERSION
], JSON_UNESCAPED_UNICODE);
?>
```

#### 完整版本 (`health_detailed.php`)
```php
<?php
header('Content-Type: application/json');
header('Cache-Control: no-cache, no-store, must-revalidate');

$status = [
    'status' => 'ok',
    'timestamp' => time(),
    'datetime' => date('Y-m-d H:i:s'),
    'checks' => []
];

// 检查PHP-FPM状态
if (function_exists('fastcgi_finish_request')) {
    $status['checks']['php_fpm'] = [
        'status' => 'ok',
        'value' => 'available',
        'message' => 'PHP-FPM可用'
    ];
} else {
    $status['checks']['php_fpm'] = [
        'status' => 'error',
        'value' => 'unavailable',
        'message' => 'PHP-FPM不可用'
    ];
    $status['status'] = 'error';
}

// 检查数据库连接
try {
    $pdo = new PDO("mysql:host=localhost;dbname=test", "user", "pass");
    $status['checks']['database'] = [
        'status' => 'ok',
        'value' => 'connected',
        'message' => '数据库连接正常'
    ];
} catch (PDOException $e) {
    $status['checks']['database'] = [
        'status' => 'error',
        'value' => 'disconnected',
        'message' => '数据库连接失败'
    ];
    $status['status'] = 'error';
}

// 设置HTTP状态码
if ($status['status'] === 'ok') {
    http_response_code(200);
} else {
    http_response_code(503);
}

echo json_encode($status, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
```

### 部署健康检查页面

#### 1. 创建健康检查目录
```bash
# 在网站根目录创建健康检查目录
mkdir -p /var/www/html/health
```

#### 2. 部署健康检查文件
```bash
# 复制健康检查文件
cp health.php /var/www/html/health/
cp health_detailed.php /var/www/html/health/

# 设置权限
chmod 644 /var/www/html/health/*.php
chown www-data:www-data /var/www/html/health/*.php
```

#### 3. 配置Nginx/Apache
```nginx
# Nginx配置
location /health {
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        include fastcgi_params;
    }
}
```

## 健康检查URL构建逻辑

```
1. 如果配置了 health_check (完整URL)
   → 解析URL获取路径部分
   → 使用upstream.Address和port构建新URL
   → 从health_check中提取域名作为Host头
   
2. 如果配置了 health_path
   → 构建URL: {scheme}://{backend_ip}:{port}{health_path}
   → 设置Host头为health_host（如果配置）
   
3. 如果未配置 health_path
   → 使用默认路径: /health
```

**重要原则**: 
- **网络连接始终使用upstream.Address和port**
- **health_check中的域名用于设置Host头**
- **确保检查的准确性和一致性**

其中 `{scheme}` 和 `{port}` 根据 `upstream.Protocol` 确定：

| Protocol | Scheme | Port | 说明 |
|----------|--------|------|------|
| `"https"` | `"https"` | `https_port` | 强制使用HTTPS |
| `"http"` | `"http"` | `port` | 强制使用HTTP |
| `"passthrough"` | `"https"` | `https_port` | 优先HTTPS（如果配置了https_port） |
| `"passthrough"` | `"http"` | `port` | 降级到HTTP（如果未配置https_port） |
| `"auto"` | `"https"` | `https_port` | 优先HTTPS（如果配置了https_port） |
| `"auto"` | `"http"` | `port` | 降级到HTTP（如果未配置https_port） |

**实际请求示例**:
```bash
# 配置
{
  "address": "*************",
  "port": 80,
  "health_check": "http://example.com/health.php"
}

# 实际HTTP请求
GET /health.php HTTP/1.1
Host: example.com
Connection: close

# 网络连接
TCP *************:80
```

**Host头设置逻辑**:
1. 如果配置了 `health_host` → 使用 `health_host`
2. 如果配置了 `health_check` 且包含域名 → 使用 `health_check` 中的域名
3. 否则 → 使用 `upstream.Address`

**注意**: 
- `passthrough` 协议在代理请求时会根据客户端请求协议动态选择，但在健康检查时会根据后端服务器配置选择协议：
  - 如果配置了 `https_port`，说明后端支持HTTPS，优先检查HTTPS
  - 如果只配置了 `port`，说明后端只支持HTTP，检查HTTP
- 健康检查始终使用后端服务器IP地址连接，但设置正确的Host头，确保虚拟主机正常工作

## 检查间隔设置

### 1. 全局默认值

在配置文件中可以设置全局默认值：

```json
{
  "upstream": {
    "health_interval": "10s",   // 全局默认检查间隔
    "health_timeout": "5s",     // 全局默认检查超时
    "health_path": "/health",   // 全局默认检查路径
    "max_fails": 3,             // 全局默认最大失败次数
    "fail_timeout": "30s"       // 全局默认失败超时
  }
}
```

### 2. 站点级别配置

每个上游服务器可以单独配置：

```json
"upstreams": [
  {
    "name": "primary",
    "health_host": "example.com",
    "health_interval": "5s",    // 每5秒检查一次
    "health_timeout": "3s",     // 3秒超时
    "health_path": "/health.php" // 使用PHP健康检查
  },
  {
    "name": "backup",
    "health_host": "www.example.com",
    "health_interval": "30s",   // 每30秒检查一次
    "health_timeout": "10s",    // 10秒超时
    "health_path": "/health.php" // 使用PHP健康检查
  }
]
```

### 3. 推荐配置

#### PHP应用环境
```json
{
  "health_interval": "30s",     // PHP应用检查间隔稍长
  "health_timeout": "10s",      // PHP处理需要更多时间
  "max_fails": 2,               // 2次失败后标记故障
  "fail_timeout": "60s",        // 60秒后重新检查
  "health_path": "/health.php"  // 使用PHP健康检查页面
}
```

#### 静态文件环境
```json
{
  "health_interval": "10s",     // 静态文件检查间隔短
  "health_timeout": "3s",       // 静态文件响应快
  "max_fails": 3,               // 3次失败后标记故障
  "fail_timeout": "30s",        // 30秒后重新检查
  "health_path": "/health.html" // 使用静态健康检查页面
}
```

#### 高可用环境
```json
{
  "health_interval": "5s",      // 5秒检查间隔
  "health_timeout": "3s",       // 3秒超时
  "max_fails": 2,               // 2次失败后标记故障
  "fail_timeout": "15s",        // 15秒后重新检查
  "health_path": "/health.php"  // 使用PHP健康检查
}
```

## 工作原理

### 1. 检查流程

```
1. 启动定时器 (health_interval)
2. 构建健康检查URL (支持域名)
3. 发送HTTP GET请求到健康检查URL
4. 等待响应 (health_timeout)
5. 检查响应状态码
   - 200-299: 健康
   - 其他: 不健康
6. 更新失败计数器
7. 根据max_fails判断是否标记故障
8. 等待下次检查
```

### 2. 故障转移

```
1. 主节点正常工作 → 流量路由到主节点
2. 主节点健康检查失败 → 计数器+1
3. 连续失败达到max_fails → 主节点标记为故障
4. 流量自动切换到备用节点
5. 经过fail_timeout时间 → 重新检查主节点
6. 主节点恢复 → 流量切回主节点
```

### 3. 状态管理

- **健康状态**: 服务器正常响应
- **故障状态**: 连续失败达到阈值
- **恢复状态**: 故障服务器重新健康

## 监控和日志

### 1. 日志输出

```
[INFO] 健康检查成功: example.com:80
[WARN] 健康检查失败: example.com:80, 错误: connection refused
[WARN] 服务器标记为故障: example.com:80, 失败次数: 3
[INFO] 切换到备用服务器: www.example.com:80
[INFO] 服务器恢复健康: example.com:80
```

### 2. 状态查询

可以通过API查询健康检查状态：

```bash
curl http://localhost:8080/health/status
```

返回格式：
```json
{
  "example.com:80": {
    "healthy": true,
    "fail_count": 0,
    "last_check": "2024-01-01T12:00:00Z",
    "last_change": "2024-01-01T12:00:00Z",
    "address": "*************",
    "port": 80,
    "health_host": "example.com"
  }
}
```

## 最佳实践

### 1. 检查间隔设置

- **PHP应用**: 30-60秒，避免频繁检查影响性能
- **静态文件**: 10-30秒，可以更频繁检查
- **API服务**: 10-30秒，根据业务需求调整
- **数据库服务**: 5-15秒，快速检测故障

### 2. 超时设置

- **PHP应用**: 10-30秒，考虑PHP处理时间
- **静态文件**: 3-10秒，响应较快
- **API服务**: 5-15秒，根据API复杂度调整
- **数据库服务**: 3-10秒，快速响应

### 3. 失败次数

- **稳定环境**: 3-5次，避免网络波动误判
- **不稳定环境**: 2-3次，快速故障转移
- **高可用环境**: 1-2次，极速故障转移

### 4. 健康检查页面

- **轻量级**: 避免影响服务器性能
- **快速响应**: 减少检查时间
- **稳定可靠**: 不要经常变更
- **应用相关**: 检查关键应用组件

## 故障排除

### 1. 常见问题

#### 健康检查失败
- 检查后端服务器是否正常运行
- 检查健康检查URL是否正确
- 检查网络连接是否正常
- 检查防火墙设置
- 检查DNS解析是否正常

#### PHP-FPM检查失败
- 检查PHP-FPM服务是否运行
- 检查PHP健康检查页面是否存在
- 检查文件权限是否正确
- 检查Nginx/Apache配置
- 检查PHP错误日志

#### 误判故障
- 增加max_fails值
- 增加health_timeout值
- 检查网络稳定性
- 优化健康检查页面
- 检查DNS解析稳定性

#### 故障转移延迟
- 减少health_interval值
- 减少max_fails值
- 减少fail_timeout值
- 检查负载均衡配置

### 2. 调试方法

```bash
# 手动测试健康检查
curl -v http://example.com/health.php

# 测试DNS解析
nslookup example.com

# 查看健康检查日志
tail -f logs/proxy.log | grep health

# 查看健康检查状态
curl http://localhost:8080/health/status
```

## 总结

健康检查功能支持域名检查，特别适用于PHP-FPM等应用服务器的监控：

1. **域名支持**: 通过 `health_host` 字段支持域名健康检查
2. **PHP-FPM检查**: 提供专门的PHP健康检查页面
3. **灵活配置**: 支持完整URL和自动构建URL两种方式
4. **应用层监控**: 重点检查应用服务状态而非仅网络连通性

合理配置健康检查可以确保应用的高可用性和稳定性。 
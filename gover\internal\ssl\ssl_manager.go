package ssl

import (
	"crypto/tls"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"sync"

	"reverse-proxy/internal/config"
)

// SSLManager SSL/TLS管理器
type SSLManager struct {
	certCache    map[string]*tls.Certificate
	certMu       sync.RWMutex
	domainCerts  map[string]config.SSLConfig
	sessionCache tls.ClientSessionCache
	ocspStapler  *OCSPStapler
}

// NewSSLManager 创建SSL管理器
func NewSSLManager() *SSLManager {
	return &SSLManager{
		certCache:    make(map[string]*tls.Certificate),
		domainCerts:  make(map[string]config.SSLConfig),
		sessionCache: tls.NewLRUClientSessionCache(1000),                // 默认1000个会话缓存
		ocspStapler:  NewOCSPStapler(config.OCSPConfig{Enabled: false}), // 默认禁用
	}
}

// UpdateDomainCerts 更新域名证书映射
func (sm *SSLManager) UpdateDomainCerts(sites []config.SiteConfig) {
	sm.certMu.Lock()
	defer sm.certMu.Unlock()

	// 清空现有映射
	sm.domainCerts = make(map[string]config.SSLConfig)

	// 重建映射
	for _, site := range sites {
		if site.SSL.Enabled {
			for _, domain := range site.Domains {
				sm.domainCerts[domain] = site.SSL
			}
		}
	}
}

// GetCertificate 获取证书（用于tls.Config.GetCertificate）
func (sm *SSLManager) GetCertificate(hello *tls.ClientHelloInfo) (*tls.Certificate, error) {
	host := hello.ServerName

	// 尝试从缓存获取
	sm.certMu.RLock()
	if cert, ok := sm.certCache[host]; ok {
		sm.certMu.RUnlock()
		return cert, nil
	}

	// 泛域名匹配
	for pattern, cert := range sm.certCache {
		if strings.HasPrefix(pattern, "*.") {
			if strings.HasSuffix(host, pattern[1:]) {
				sm.certMu.RUnlock()
				return cert, nil
			}
		}
	}
	sm.certMu.RUnlock()

	// 加载证书
	sm.certMu.Lock()
	defer sm.certMu.Unlock()

	// 双重检查
	if cert, ok := sm.certCache[host]; ok {
		return cert, nil
	}

	// 查找配置
	var sslConfig config.SSLConfig
	var found bool

	// 精确匹配
	if cfg, ok := sm.domainCerts[host]; ok {
		sslConfig = cfg
		found = true
	} else {
		// 泛域名匹配
		for pattern, cfg := range sm.domainCerts {
			if strings.HasPrefix(pattern, "*.") && strings.HasSuffix(host, pattern[1:]) {
				sslConfig = cfg
				found = true
				break
			}
		}
	}

	if !found {
		return nil, fmt.Errorf("未找到域名 %s 的SSL配置", host)
	}

	// 加载证书
	cert, err := tls.LoadX509KeyPair(sslConfig.CertFile, sslConfig.KeyFile)
	if err != nil {
		return nil, fmt.Errorf("加载证书失败: %w", err)
	}

	// 缓存证书
	sm.certCache[host] = &cert

	return &cert, nil
}

// CreateTLSConfig 创建优化的TLS配置
func (sm *SSLManager) CreateTLSConfig(sslConfig config.SSLConfig) *tls.Config {
	tlsConfig := &tls.Config{
		GetCertificate:     sm.GetCertificate,
		ClientSessionCache: sm.sessionCache,
	}

	// 设置TLS版本
	if sslConfig.MinVersion != "" {
		tlsConfig.MinVersion = sm.parseTLSVersion(sslConfig.MinVersion)
	} else {
		tlsConfig.MinVersion = tls.VersionTLS12 // 默认最低TLS 1.2
	}

	if sslConfig.MaxVersion != "" {
		tlsConfig.MaxVersion = sm.parseTLSVersion(sslConfig.MaxVersion)
	}

	// 设置加密套件
	if len(sslConfig.CipherSuites) > 0 {
		tlsConfig.CipherSuites = sm.parseCipherSuites(sslConfig.CipherSuites)
	} else {
		// 使用安全的默认加密套件
		tlsConfig.CipherSuites = []uint16{
			tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
			tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305,
			tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
			tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
			tls.TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305,
			tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
		}
	}

	// 服务器优先选择加密套件
	tlsConfig.PreferServerCipherSuites = sslConfig.PreferServerCiphers

	// 会话票据
	if !sslConfig.SessionTickets {
		tlsConfig.SessionTicketsDisabled = true
	}

	// HTTP/2支持
	if strings.Contains(sslConfig.Protocols, "http2") {
		tlsConfig.NextProtos = []string{"h2", "http/1.1"}
	} else {
		tlsConfig.NextProtos = []string{"http/1.1"}
	}

	// OCSP装订支持
	if sslConfig.OCSP.Enabled {
		sm.ocspStapler = NewOCSPStapler(sslConfig.OCSP)
		tlsConfig.GetCertificate = sm.GetCertificateWithOCSP
	}

	return tlsConfig
}

// parseTLSVersion 解析TLS版本
func (sm *SSLManager) parseTLSVersion(version string) uint16 {
	switch strings.ToUpper(version) {
	case "TLS1.0":
		return tls.VersionTLS10
	case "TLS1.1":
		return tls.VersionTLS11
	case "TLS1.2":
		return tls.VersionTLS12
	case "TLS1.3":
		return tls.VersionTLS13
	default:
		return tls.VersionTLS12
	}
}

// parseCipherSuites 解析加密套件
func (sm *SSLManager) parseCipherSuites(suites []string) []uint16 {
	var result []uint16

	cipherMap := map[string]uint16{
		"TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384":   tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
		"TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305":    tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305,
		"TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256":   tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
		"TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384": tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
		"TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305":  tls.TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305,
		"TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256": tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
	}

	for _, suite := range suites {
		if cipher, ok := cipherMap[suite]; ok {
			result = append(result, cipher)
		}
	}

	return result
}

// AddHSTSHeaders 添加HSTS头部
func (sm *SSLManager) AddHSTSHeaders(w http.ResponseWriter, hstsConfig config.HSTSConfig) {
	if !hstsConfig.Enabled {
		return
	}

	hstsValue := "max-age=" + strconv.Itoa(hstsConfig.MaxAge)

	if hstsConfig.IncludeSubDomains {
		hstsValue += "; includeSubDomains"
	}

	if hstsConfig.Preload {
		hstsValue += "; preload"
	}

	w.Header().Set("Strict-Transport-Security", hstsValue)
}

// GetCertificateWithOCSP 获取带OCSP装订的证书
func (sm *SSLManager) GetCertificateWithOCSP(hello *tls.ClientHelloInfo) (*tls.Certificate, error) {
	// 首先获取基础证书
	cert, err := sm.GetCertificate(hello)
	if err != nil {
		return nil, err
	}

	// 如果OCSP装订启用，获取OCSP响应
	if sm.ocspStapler != nil {
		// 查找对应的证书文件
		sm.certMu.RLock()
		var certFile string
		for domain, sslConfig := range sm.domainCerts {
			if domain == hello.ServerName {
				certFile = sslConfig.CertFile
				break
			}
		}
		sm.certMu.RUnlock()

		if certFile != "" {
			ocspResp, err := sm.ocspStapler.GetOCSPResponse(certFile)
			if err == nil && len(ocspResp) > 0 {
				// 创建新的证书副本并添加OCSP响应
				newCert := *cert
				newCert.OCSPStaple = ocspResp
				return &newCert, nil
			}
		}
	}

	return cert, nil
}

// GetStats 获取SSL统计信息
func (sm *SSLManager) GetStats() map[string]interface{} {
	sm.certMu.RLock()
	defer sm.certMu.RUnlock()

	return map[string]interface{}{
		"cached_certificates": len(sm.certCache),
		"configured_domains":  len(sm.domainCerts),
		"session_cache_size":  1000, // 固定值，实际可以从ClientSessionCache获取
	}
}

// ReloadCertificates 重新加载证书（用于热重载）
func (sm *SSLManager) ReloadCertificates() error {
	sm.certMu.Lock()
	defer sm.certMu.Unlock()

	// 清空证书缓存，强制重新加载
	sm.certCache = make(map[string]*tls.Certificate)

	return nil
}

// ValidateCertificate 验证证书文件
func (sm *SSLManager) ValidateCertificate(certFile, keyFile string) error {
	_, err := tls.LoadX509KeyPair(certFile, keyFile)
	return err
}

# Minify详细配置说明

## 🎯 配置级别

### 1. Conservative（保守模式）
**特点**: 最小压缩，最大兼容性，适合复杂的动态网站
```json
{
  "minify": {
    "level": "conservative"
  }
}
```

**HTML配置**:
- ✅ 保留IE条件注释
- ✅ 保留默认属性值
- ✅ 保留html/head/body标签
- ✅ 保留所有结束标签
- ✅ 保留重要空白
- ✅ 保留属性引号
- ❌ 删除HTML注释

**CSS配置**:
- ✅ 保留5位小数精度
- ❌ 删除CSS注释
- ❌ 删除多余空白

**JS配置**:
- ✅ 保留5位数字精度
- ❌ 删除JS注释
- ❌ 删除多余空白

### 2. Safe（安全模式）- 默认
**特点**: 平衡压缩和兼容性，适合大多数网站
```json
{
  "minify": {
    "level": "safe"
  }
}
```

**HTML配置**:
- ✅ 保留IE条件注释
- ❌ 删除默认属性值
- ✅ 保留html/head/body标签
- ❌ 删除可选结束标签
- ❌ 删除多余空白
- ❌ 删除不必要引号

### 3. Aggressive（激进模式）
**特点**: 高压缩率，可能影响兼容性
```json
{
  "minify": {
    "level": "aggressive"
  }
}
```

**HTML配置**:
- ❌ 删除条件注释
- ❌ 删除默认属性值
- ❌ 删除可选文档标签
- ❌ 删除可选结束标签
- ❌ 删除所有多余空白
- ❌ 删除不必要引号

### 4. Maximum（最大模式）
**特点**: 最高压缩率，可能破坏功能
```json
{
  "minify": {
    "level": "maximum"
  }
}
```

## 🔧 详细配置选项

### HTML配置
```json
{
  "minify": {
    "html": {
      "keep_conditional_comments": true,  // 保留IE条件注释 <!--[if IE]>
      "keep_default_attrvals": false,     // 删除默认属性值 type="text"
      "keep_document_tags": true,         // 保留<html><head><body>标签
      "keep_end_tags": false,             // 删除可选结束标签 </p></li>
      "keep_whitespace": false,           // 删除多余空白和换行
      "keep_quotes": false                // 删除不必要的属性引号
    }
  }
}
```

### CSS配置
```json
{
  "minify": {
    "css": {
      "precision": 3  // 保留小数位数: 1.23456 -> 1.235
    }
  }
}
```

### JavaScript配置
```json
{
  "minify": {
    "js": {
      "precision": 3  // 保留数字精度: 3.14159 -> 3.142
    }
  }
}
```

## 📋 实际应用场景

### 1. 静态资源站点
```json
{
  "sites": [
    {
      "name": "cdn_site",
      "minify": {
        "enabled": true,
        "types": ["css", "js"],
        "level": "aggressive",
        "css": { "precision": 2 },
        "js": { "precision": 2 }
      }
    }
  ]
}
```

### 2. 企业官网
```json
{
  "sites": [
    {
      "name": "corporate_site",
      "minify": {
        "enabled": true,
        "types": ["html", "css", "js"],
        "level": "safe",
        "html": {
          "keep_conditional_comments": true,
          "keep_document_tags": true
        }
      }
    }
  ]
}
```

### 3. 单页应用(SPA)
```json
{
  "sites": [
    {
      "name": "spa_site",
      "minify": {
        "enabled": true,
        "types": ["html", "css", "js"],
        "level": "conservative",
        "html": {
          "keep_whitespace": true,
          "keep_quotes": true
        }
      }
    }
  ]
}
```

### 4. API服务
```json
{
  "sites": [
    {
      "name": "api_service",
      "minify": {
        "enabled": false  // API通常不需要minify
      }
    }
  ]
}
```

## ⚠️ 注意事项

### 1. HTML Minify风险
- **内联JavaScript**: 可能破坏JS代码
- **CSS样式**: 可能影响CSS选择器
- **模板语法**: 可能破坏模板引擎语法
- **空白敏感**: 某些元素需要保留空白

### 2. CSS Minify风险
- **calc()函数**: 精度过低可能影响计算
- **动画关键帧**: 精度影响动画效果
- **媒体查询**: 精度影响响应式布局

### 3. JavaScript Minify风险
- **数字精度**: 影响数学计算
- **字符串处理**: 可能破坏正则表达式
- **注释依赖**: 某些库依赖特殊注释

## 🔍 调试和测试

### 1. 渐进式启用
```bash
# 第一步：只启用CSS
curl -H "Accept-Encoding: gzip" http://example.com/style.css

# 第二步：启用JavaScript
curl -H "Accept-Encoding: gzip" http://example.com/script.js

# 第三步：启用HTML
curl -H "Accept-Encoding: gzip" http://example.com/
```

### 2. 对比测试
```bash
# 关闭minify
curl -o original.html http://example.com/

# 开启minify
curl -o minified.html http://example.com/

# 对比文件大小
ls -la *.html
```

### 3. 功能验证
- 检查页面布局是否正常
- 验证JavaScript功能
- 测试CSS动画效果
- 确认表单提交正常

## 📊 性能指标

### 压缩率对比
| 级别 | HTML压缩率 | CSS压缩率 | JS压缩率 | 兼容性风险 |
|------|------------|-----------|----------|------------|
| Conservative | 5-10% | 10-15% | 8-12% | 极低 |
| Safe | 15-25% | 20-30% | 15-20% | 低 |
| Aggressive | 25-35% | 30-40% | 20-30% | 中等 |
| Maximum | 35-45% | 40-50% | 30-40% | 高 |

### 建议配置
- **生产环境**: Safe级别
- **CDN静态资源**: Aggressive级别
- **开发环境**: Conservative级别或关闭
- **API服务**: 关闭minify

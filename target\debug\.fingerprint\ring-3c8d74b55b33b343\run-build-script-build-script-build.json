{"rustc": 28845397767708332, "features": "", "target": 0, "profile": 0, "path": 0, "deps": [[6887511832824229771, "build_script_build", false, 12130853424013421487]], "local": [{"RerunIfChanged": {"output": "debug\\build\\ring-3c8d74b55b33b343\\output", "paths": ["crypto\\chacha\\asm\\chacha-armv4.pl", "crypto\\chacha\\asm\\chacha-armv8.pl", "crypto\\chacha\\asm\\chacha-x86.pl", "crypto\\chacha\\asm\\chacha-x86_64.pl", "crypto\\cipher\\asm\\chacha20_poly1305_armv8.pl", "crypto\\cipher\\asm\\chacha20_poly1305_x86_64.pl", "crypto\\constant_time_test.c", "crypto\\cpu_intel.c", "crypto\\crypto.c", "crypto\\curve25519\\asm\\x25519-asm-arm.S", "crypto\\curve25519\\curve25519.c", "crypto\\curve25519\\curve25519_64_adx.c", "crypto\\curve25519\\curve25519_tables.h", "crypto\\curve25519\\internal.h", "crypto\\fipsmodule\\aes\\aes_nohw.c", "crypto\\fipsmodule\\aes\\asm\\aes-gcm-avx2-x86_64.pl", "crypto\\fipsmodule\\aes\\asm\\aesni-gcm-x86_64.pl", "crypto\\fipsmodule\\aes\\asm\\aesni-x86.pl", "crypto\\fipsmodule\\aes\\asm\\aesni-x86_64.pl", "crypto\\fipsmodule\\aes\\asm\\aesv8-armx.pl", "crypto\\fipsmodule\\aes\\asm\\aesv8-gcm-armv8.pl", "crypto\\fipsmodule\\aes\\asm\\bsaes-armv7.pl", "crypto\\fipsmodule\\aes\\asm\\ghash-armv4.pl", "crypto\\fipsmodule\\aes\\asm\\ghash-neon-armv8.pl", "crypto\\fipsmodule\\aes\\asm\\ghash-x86.pl", "crypto\\fipsmodule\\aes\\asm\\ghash-x86_64.pl", "crypto\\fipsmodule\\aes\\asm\\ghashv8-armx.pl", "crypto\\fipsmodule\\aes\\asm\\vpaes-armv7.pl", "crypto\\fipsmodule\\aes\\asm\\vpaes-armv8.pl", "crypto\\fipsmodule\\aes\\asm\\vpaes-x86.pl", "crypto\\fipsmodule\\aes\\asm\\vpaes-x86_64.pl", "crypto\\fipsmodule\\bn\\asm\\armv4-mont.pl", "crypto\\fipsmodule\\bn\\asm\\armv8-mont.pl", "crypto\\fipsmodule\\bn\\asm\\x86-mont.pl", "crypto\\fipsmodule\\bn\\asm\\x86_64-mont.pl", "crypto\\fipsmodule\\bn\\asm\\x86_64-mont5.pl", "crypto\\fipsmodule\\bn\\internal.h", "crypto\\fipsmodule\\bn\\montgomery.c", "crypto\\fipsmodule\\bn\\montgomery_inv.c", "crypto\\fipsmodule\\ec\\asm\\p256-armv8-asm.pl", "crypto\\fipsmodule\\ec\\asm\\p256-x86_64-asm.pl", "crypto\\fipsmodule\\ec\\ecp_nistz.c", "crypto\\fipsmodule\\ec\\ecp_nistz.h", "crypto\\fipsmodule\\ec\\ecp_nistz384.h", "crypto\\fipsmodule\\ec\\ecp_nistz384.inl", "crypto\\fipsmodule\\ec\\gfp_p256.c", "crypto\\fipsmodule\\ec\\gfp_p384.c", "crypto\\fipsmodule\\ec\\p256-nistz-table.h", "crypto\\fipsmodule\\ec\\p256-nistz.c", "crypto\\fipsmodule\\ec\\p256-nistz.h", "crypto\\fipsmodule\\ec\\p256.c", "crypto\\fipsmodule\\ec\\p256_shared.h", "crypto\\fipsmodule\\ec\\p256_table.h", "crypto\\fipsmodule\\ec\\util.h", "crypto\\fipsmodule\\sha\\asm\\sha256-armv4.pl", "crypto\\fipsmodule\\sha\\asm\\sha512-armv4.pl", "crypto\\fipsmodule\\sha\\asm\\sha512-armv8.pl", "crypto\\fipsmodule\\sha\\asm\\sha512-x86_64.pl", "crypto\\internal.h", "crypto\\limbs\\limbs.c", "crypto\\limbs\\limbs.h", "crypto\\limbs\\limbs.inl", "crypto\\mem.c", "crypto\\perlasm\\arm-xlate.pl", "crypto\\perlasm\\x86asm.pl", "crypto\\perlasm\\x86gas.pl", "crypto\\perlasm\\x86nasm.pl", "crypto\\perlasm\\x86_64-xlate.pl", "crypto\\poly1305\\poly1305.c", "crypto\\poly1305\\poly1305_arm.c", "crypto\\poly1305\\poly1305_arm_asm.S", "include\\ring-core\\aes.h", "include\\ring-core\\asm_base.h", "include\\ring-core\\base.h", "include\\ring-core\\check.h", "include\\ring-core\\mem.h", "include\\ring-core\\target.h", "include\\ring-core\\type_check.h", "third_party/fiat\\asm\\fiat_curve25519_adx_mul.S", "third_party/fiat\\asm\\fiat_curve25519_adx_square.S", "third_party/fiat\\curve25519_32.h", "third_party/fiat\\curve25519_64.h", "third_party/fiat\\curve25519_64_adx.h", "third_party/fiat\\curve25519_64_msvc.h", "third_party/fiat\\LICENSE", "third_party/fiat\\p256_32.h", "third_party/fiat\\p256_64.h", "third_party/fiat\\p256_64_msvc.h"]}}, {"RerunIfEnvChanged": {"var": "CARGO_MANIFEST_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_PKG_NAME", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_PKG_VERSION_MAJOR", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_PKG_VERSION_MINOR", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_PKG_VERSION_PATCH", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_PKG_VERSION_PRE", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_MANIFEST_LINKS", "val": null}}, {"RerunIfEnvChanged": {"var": "RING_PREGENERATE_ASM", "val": null}}, {"RerunIfEnvChanged": {"var": "OUT_DIR", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_TARGET_ARCH", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_TARGET_OS", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_TARGET_ENV", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_TARGET_ENDIAN", "val": null}}, {"RerunIfEnvChanged": {"var": "VCINSTALLDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "VSTEL_MSBuildProjectFullPath", "val": null}}, {"RerunIfEnvChanged": {"var": "VSCMD_ARG_VCVARS_SPECTRE", "val": null}}, {"RerunIfEnvChanged": {"var": "WindowsSdkDir", "val": null}}, {"RerunIfEnvChanged": {"var": "WindowsSDKVersion", "val": null}}, {"RerunIfEnvChanged": {"var": "LIB", "val": null}}, {"RerunIfEnvChanged": {"var": "INCLUDE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_i686-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_i686_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_i686_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_i686-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "VCINSTALLDIR", "val": null}}, {"RerunIfEnvChanged": {"var": "VSTEL_MSBuildProjectFullPath", "val": null}}, {"RerunIfEnvChanged": {"var": "VSCMD_ARG_VCVARS_SPECTRE", "val": null}}, {"RerunIfEnvChanged": {"var": "WindowsSdkDir", "val": null}}, {"RerunIfEnvChanged": {"var": "WindowsSDKVersion", "val": null}}, {"RerunIfEnvChanged": {"var": "LIB", "val": null}}, {"RerunIfEnvChanged": {"var": "INCLUDE", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_i686-pc-windows-msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "CC_i686_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CC", "val": null}}, {"RerunIfEnvChanged": {"var": "CRATE_CC_NO_DEFAULTS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "HOST_CFLAGS", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_i686_pc_windows_msvc", "val": null}}, {"RerunIfEnvChanged": {"var": "CFLAGS_i686-pc-windows-msvc", "val": null}}], "rustflags": [], "metadata": 0, "config": 0, "compile_kind": 0}
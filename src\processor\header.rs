use async_trait::async_trait;
use std::collections::HashMap;
use std::time::Duration;
use tracing::{debug, info, warn};

use super::{Processor, ProcessResult, RequestContext};

/// CORS配置
#[derive(Debug, Clone)]
pub struct CorsConfig {
    pub enabled: bool,
    pub allowed_origins: Vec<String>,
    pub allowed_methods: Vec<String>,
    pub allowed_headers: Vec<String>,
    pub exposed_headers: Vec<String>,
    pub allow_credentials: bool,
    pub max_age: Option<Duration>,
}

impl Default for CorsConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            allowed_origins: vec!["*".to_string()],
            allowed_methods: vec![
                "GET".to_string(),
                "POST".to_string(),
                "PUT".to_string(),
                "DELETE".to_string(),
                "OPTIONS".to_string(),
            ],
            allowed_headers: vec![
                "Content-Type".to_string(),
                "Authorization".to_string(),
                "X-Requested-With".to_string(),
            ],
            exposed_headers: vec![],
            allow_credentials: false,
            max_age: Some(Duration::from_secs(86400)), // 24小时
        }
    }
}

/// 安全头部配置
#[derive(Debug, Clone)]
pub struct SecurityHeadersConfig {
    pub enabled: bool,
    pub x_frame_options: Option<String>,
    pub x_content_type_options: bool,
    pub x_xss_protection: Option<String>,
    pub strict_transport_security: Option<String>,
    pub content_security_policy: Option<String>,
    pub referrer_policy: Option<String>,
    pub permissions_policy: Option<String>,
}

impl Default for SecurityHeadersConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            x_frame_options: Some("DENY".to_string()),
            x_content_type_options: true,
            x_xss_protection: Some("1; mode=block".to_string()),
            strict_transport_security: Some("max-age=31536000; includeSubDomains".to_string()),
            content_security_policy: Some("default-src 'self'".to_string()),
            referrer_policy: Some("strict-origin-when-cross-origin".to_string()),
            permissions_policy: Some("geolocation=(), microphone=(), camera=()".to_string()),
        }
    }
}

/// 头部处理器配置
#[derive(Debug, Clone)]
pub struct HeaderProcessorConfig {
    pub server_name: String,
    pub hide_server_info: bool,
    pub custom_headers: HashMap<String, String>,
    pub remove_headers: Vec<String>,
    pub cors: CorsConfig,
    pub security: SecurityHeadersConfig,
    pub cache_control: Option<String>,
}

impl Default for HeaderProcessorConfig {
    fn default() -> Self {
        Self {
            server_name: "RustProxy/1.0".to_string(),
            hide_server_info: false,
            custom_headers: HashMap::new(),
            remove_headers: vec![
                "X-Powered-By".to_string(),
                "Server".to_string(),
            ],
            cors: CorsConfig::default(),
            security: SecurityHeadersConfig::default(),
            cache_control: None,
        }
    }
}

/// 头部处理器
pub struct HeaderProcessor {
    config: HeaderProcessorConfig,
}

impl HeaderProcessor {
    /// 创建新的头部处理器
    pub fn new() -> Self {
        Self {
            config: HeaderProcessorConfig::default(),
        }
    }

    /// 使用指定配置创建头部处理器
    pub fn with_config(config: HeaderProcessorConfig) -> Self {
        Self { config }
    }
}

impl Default for HeaderProcessor {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl Processor for HeaderProcessor {
    fn name(&self) -> &'static str {
        "HeaderProcessor"
    }
    
    fn priority(&self) -> u8 {
        90 // 在最后处理头部
    }
    
    async fn process(&self, ctx: &mut RequestContext) -> ProcessResult {
        debug!("HeaderProcessor: Processing request {}", ctx.request_id);

        // 处理CORS预检请求
        if ctx.method == "OPTIONS" && self.config.cors.enabled {
            return self.handle_cors_preflight(ctx).await;
        }

        // 移除指定的响应头
        self.remove_headers(ctx);

        // 添加自定义头部
        self.add_custom_headers(ctx);

        // 添加服务器信息头部
        self.add_server_headers(ctx);

        // 添加安全头部
        if self.config.security.enabled {
            self.add_security_headers(ctx);
        }

        // 添加CORS头部
        if self.config.cors.enabled {
            self.add_cors_headers(ctx);
        }

        // 添加缓存控制头部
        self.add_cache_control_headers(ctx);

        info!("HeaderProcessor: Request {} processed", ctx.request_id);
        ProcessResult::Continue
    }
}

impl HeaderProcessor {
    /// 处理CORS预检请求
    async fn handle_cors_preflight(&self, ctx: &mut RequestContext) -> ProcessResult {
        debug!("Handling CORS preflight request for {}", ctx.request_id);

        // 检查Origin头部
        let origin = ctx.get_header("origin");
        if !self.is_origin_allowed(origin) {
            ctx.response.status_code = 403;
            ctx.response.body = Some("CORS: Origin not allowed".into());
            return ProcessResult::Continue;
        }

        // 检查请求方法
        if let Some(method) = ctx.get_header("access-control-request-method") {
            if !self.config.cors.allowed_methods.contains(method) {
                ctx.response.status_code = 405;
                ctx.response.body = Some("CORS: Method not allowed".into());
                return ProcessResult::Continue;
            }
        }

        // 设置CORS预检响应头
        ctx.response.status_code = 204; // No Content
        self.add_cors_headers(ctx);

        // 设置预检缓存时间
        if let Some(max_age) = self.config.cors.max_age {
            ctx.response.headers.insert(
                "access-control-max-age".to_string(),
                max_age.as_secs().to_string(),
            );
        }

        ProcessResult::Continue
    }

    /// 检查Origin是否被允许
    fn is_origin_allowed(&self, origin: Option<&String>) -> bool {
        let origin = match origin {
            Some(o) => o,
            None => return false,
        };

        // 检查是否允许所有来源
        if self.config.cors.allowed_origins.contains(&"*".to_string()) {
            return true;
        }

        // 检查具体的来源
        self.config.cors.allowed_origins.contains(origin)
    }

    /// 移除指定的响应头
    fn remove_headers(&self, ctx: &mut RequestContext) {
        for header in &self.config.remove_headers {
            ctx.response.headers.remove(&header.to_lowercase());
        }
    }

    /// 添加自定义头部
    fn add_custom_headers(&self, ctx: &mut RequestContext) {
        for (name, value) in &self.config.custom_headers {
            ctx.response.headers.insert(name.clone(), value.clone());
        }
    }

    /// 添加服务器信息头部
    fn add_server_headers(&self, ctx: &mut RequestContext) {
        if !self.config.hide_server_info {
            ctx.response.headers.insert("server".to_string(), self.config.server_name.clone());
        }

        // 添加处理时间头部
        let processing_time = ctx.start_time.elapsed().as_millis();
        ctx.response.headers.insert(
            "x-response-time".to_string(),
            format!("{}ms", processing_time),
        );

        // 添加请求ID头部
        ctx.response.headers.insert(
            "x-request-id".to_string(),
            ctx.request_id.clone(),
        );
    }

    /// 添加安全头部
    fn add_security_headers(&self, ctx: &mut RequestContext) {
        let security = &self.config.security;

        // X-Frame-Options
        if let Some(ref value) = security.x_frame_options {
            ctx.response.headers.insert("x-frame-options".to_string(), value.clone());
        }

        // X-Content-Type-Options
        if security.x_content_type_options {
            ctx.response.headers.insert("x-content-type-options".to_string(), "nosniff".to_string());
        }

        // X-XSS-Protection
        if let Some(ref value) = security.x_xss_protection {
            ctx.response.headers.insert("x-xss-protection".to_string(), value.clone());
        }

        // Strict-Transport-Security (仅HTTPS)
        if let Some(ref value) = security.strict_transport_security {
            // 检查是否为HTTPS请求
            if ctx.headers.get("x-forwarded-proto").map(|v| v == "https").unwrap_or(false) ||
               ctx.headers.get("x-forwarded-ssl").map(|v| v == "on").unwrap_or(false) {
                ctx.response.headers.insert("strict-transport-security".to_string(), value.clone());
            }
        }

        // Content-Security-Policy
        if let Some(ref value) = security.content_security_policy {
            ctx.response.headers.insert("content-security-policy".to_string(), value.clone());
        }

        // Referrer-Policy
        if let Some(ref value) = security.referrer_policy {
            ctx.response.headers.insert("referrer-policy".to_string(), value.clone());
        }

        // Permissions-Policy
        if let Some(ref value) = security.permissions_policy {
            ctx.response.headers.insert("permissions-policy".to_string(), value.clone());
        }
    }

    /// 添加CORS头部
    fn add_cors_headers(&self, ctx: &mut RequestContext) {
        let cors = &self.config.cors;

        // Access-Control-Allow-Origin
        let origin = ctx.get_header("origin");
        if self.is_origin_allowed(origin) {
            if cors.allowed_origins.contains(&"*".to_string()) && !cors.allow_credentials {
                ctx.response.headers.insert("access-control-allow-origin".to_string(), "*".to_string());
            } else if let Some(origin) = origin {
                ctx.response.headers.insert("access-control-allow-origin".to_string(), origin.clone());
            }
        }

        // Access-Control-Allow-Methods
        if !cors.allowed_methods.is_empty() {
            ctx.response.headers.insert(
                "access-control-allow-methods".to_string(),
                cors.allowed_methods.join(", "),
            );
        }

        // Access-Control-Allow-Headers
        if !cors.allowed_headers.is_empty() {
            ctx.response.headers.insert(
                "access-control-allow-headers".to_string(),
                cors.allowed_headers.join(", "),
            );
        }

        // Access-Control-Expose-Headers
        if !cors.exposed_headers.is_empty() {
            ctx.response.headers.insert(
                "access-control-expose-headers".to_string(),
                cors.exposed_headers.join(", "),
            );
        }

        // Access-Control-Allow-Credentials
        if cors.allow_credentials {
            ctx.response.headers.insert("access-control-allow-credentials".to_string(), "true".to_string());
        }

        // Vary头部，确保缓存正确处理CORS
        let vary = ctx.response.headers.get("vary")
            .map(|v| format!("{}, Origin", v))
            .unwrap_or_else(|| "Origin".to_string());
        ctx.response.headers.insert("vary".to_string(), vary);
    }

    /// 添加缓存控制头部
    fn add_cache_control_headers(&self, ctx: &mut RequestContext) {
        // 如果已经有Cache-Control头部，则不覆盖
        if ctx.response.headers.contains_key("cache-control") {
            return;
        }

        // 添加默认的缓存控制头部
        if let Some(ref cache_control) = self.config.cache_control {
            ctx.response.headers.insert("cache-control".to_string(), cache_control.clone());
        } else {
            // 根据内容类型设置默认缓存策略
            let content_type = ctx.response.headers.get("content-type")
                .map(|ct| ct.to_lowercase())
                .unwrap_or_default();

            let cache_control = if content_type.starts_with("text/html") {
                "no-cache, must-revalidate"
            } else if content_type.starts_with("application/json") || content_type.starts_with("application/xml") {
                "no-cache"
            } else if content_type.starts_with("text/css") || content_type.starts_with("application/javascript") {
                "public, max-age=31536000" // 1年
            } else if content_type.starts_with("image/") || content_type.starts_with("font/") {
                "public, max-age=2592000" // 30天
            } else {
                "public, max-age=3600" // 1小时
            };

            ctx.response.headers.insert("cache-control".to_string(), cache_control.to_string());
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::net::SocketAddr;

    fn create_test_context() -> RequestContext {
        RequestContext::new(
            "GET".to_string(),
            "/test".to_string(),
            HashMap::new(),
            "127.0.0.1:8080".parse::<SocketAddr>().unwrap(),
        )
    }

    #[test]
    fn test_default_config() {
        let config = HeaderProcessorConfig::default();
        assert_eq!(config.server_name, "RustProxy/1.0");
        assert!(!config.hide_server_info);
        assert!(config.security.enabled);
        assert!(!config.cors.enabled);
    }

    #[test]
    fn test_is_origin_allowed() {
        let mut config = HeaderProcessorConfig::default();
        config.cors.allowed_origins = vec!["https://example.com".to_string()];
        let processor = HeaderProcessor::with_config(config);

        assert!(processor.is_origin_allowed(Some(&"https://example.com".to_string())));
        assert!(!processor.is_origin_allowed(Some(&"https://evil.com".to_string())));
        assert!(!processor.is_origin_allowed(None));
    }

    #[test]
    fn test_wildcard_origin() {
        let config = HeaderProcessorConfig::default(); // 默认允许所有来源
        let processor = HeaderProcessor::with_config(config);

        assert!(processor.is_origin_allowed(Some(&"https://example.com".to_string())));
        assert!(processor.is_origin_allowed(Some(&"https://any-domain.com".to_string())));
    }

    #[tokio::test]
    async fn test_add_server_headers() {
        let processor = HeaderProcessor::new();
        let mut ctx = create_test_context();

        processor.add_server_headers(&mut ctx);

        assert!(ctx.response.headers.contains_key("server"));
        assert!(ctx.response.headers.contains_key("x-response-time"));
        assert!(ctx.response.headers.contains_key("x-request-id"));
        assert_eq!(ctx.response.headers.get("server").unwrap(), "RustProxy/1.0");
    }

    #[tokio::test]
    async fn test_add_security_headers() {
        let processor = HeaderProcessor::new();
        let mut ctx = create_test_context();

        processor.add_security_headers(&mut ctx);

        assert_eq!(ctx.response.headers.get("x-frame-options").unwrap(), "DENY");
        assert_eq!(ctx.response.headers.get("x-content-type-options").unwrap(), "nosniff");
        assert!(ctx.response.headers.contains_key("x-xss-protection"));
        assert!(ctx.response.headers.contains_key("content-security-policy"));
        assert!(ctx.response.headers.contains_key("referrer-policy"));
    }

    #[tokio::test]
    async fn test_cors_headers() {
        let mut config = HeaderProcessorConfig::default();
        config.cors.enabled = true;
        config.cors.allowed_origins = vec!["https://example.com".to_string()];
        let processor = HeaderProcessor::with_config(config);

        let mut ctx = create_test_context();
        ctx.headers.insert("origin".to_string(), "https://example.com".to_string());

        processor.add_cors_headers(&mut ctx);

        assert_eq!(
            ctx.response.headers.get("access-control-allow-origin").unwrap(),
            "https://example.com"
        );
        assert!(ctx.response.headers.contains_key("access-control-allow-methods"));
        assert!(ctx.response.headers.contains_key("vary"));
    }

    #[tokio::test]
    async fn test_cors_preflight() {
        let mut config = HeaderProcessorConfig::default();
        config.cors.enabled = true;
        let processor = HeaderProcessor::with_config(config);

        let mut ctx = create_test_context();
        ctx.method = "OPTIONS".to_string();
        ctx.headers.insert("origin".to_string(), "https://example.com".to_string());
        ctx.headers.insert("access-control-request-method".to_string(), "POST".to_string());

        let result = processor.handle_cors_preflight(&mut ctx).await;

        assert!(matches!(result, ProcessResult::Continue));
        assert_eq!(ctx.response.status_code, 204);
        assert!(ctx.response.headers.contains_key("access-control-allow-origin"));
    }

    #[tokio::test]
    async fn test_remove_headers() {
        let mut config = HeaderProcessorConfig::default();
        config.remove_headers = vec!["x-powered-by".to_string()];
        let processor = HeaderProcessor::with_config(config);

        let mut ctx = create_test_context();
        ctx.response.headers.insert("x-powered-by".to_string(), "PHP".to_string());
        ctx.response.headers.insert("server".to_string(), "Apache".to_string());

        processor.remove_headers(&mut ctx);

        assert!(!ctx.response.headers.contains_key("x-powered-by"));
        assert!(ctx.response.headers.contains_key("server"));
    }

    #[tokio::test]
    async fn test_cache_control_headers() {
        let processor = HeaderProcessor::new();
        let mut ctx = create_test_context();

        // 测试HTML内容
        ctx.response.headers.insert("content-type".to_string(), "text/html".to_string());
        processor.add_cache_control_headers(&mut ctx);
        assert_eq!(ctx.response.headers.get("cache-control").unwrap(), "no-cache, must-revalidate");

        // 测试CSS内容
        ctx.response.headers.clear();
        ctx.response.headers.insert("content-type".to_string(), "text/css".to_string());
        processor.add_cache_control_headers(&mut ctx);
        assert_eq!(ctx.response.headers.get("cache-control").unwrap(), "public, max-age=31536000");

        // 测试图片内容
        ctx.response.headers.clear();
        ctx.response.headers.insert("content-type".to_string(), "image/jpeg".to_string());
        processor.add_cache_control_headers(&mut ctx);
        assert_eq!(ctx.response.headers.get("cache-control").unwrap(), "public, max-age=2592000");
    }
}

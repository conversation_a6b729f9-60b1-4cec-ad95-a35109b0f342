# 🚀 站点配置API使用指南

## 📋 概述

现在已经完善了单个站点配置的管理功能，提供了便捷的API接口来通过JSON更新站点配置。

## 🔧 **已有的便捷接口**

### **1. 配置管理器层面**

#### **单站点JSON更新**
```go
// 更新现有站点
err := configManager.UpdateSiteFromJSON("site_name", jsonData)

// 创建新站点
err := configManager.CreateSiteFromJSON(jsonData)

// 删除站点
err := configManager.DeleteSite("site_name")

// 获取站点配置
siteConfig := configManager.GetSiteConfig("site_name")

// 获取所有站点列表
sites := configManager.ListSites()
```

#### **原有的重载接口**
```go
// 直接重载站点配置对象
err := configManager.ReloadSiteConfig("site_name", &siteConfig)
```

### **2. API层面（新增）**

#### **RESTful API接口**
```
GET    /api/sites              # 获取所有站点列表
GET    /api/sites?name=xxx     # 获取指定站点配置
POST   /api/sites              # 创建新站点
PUT    /api/sites?name=xxx     # 更新指定站点
DELETE /api/sites?name=xxx     # 删除指定站点
POST   /api/sites/batch        # 批量更新站点
GET    /api/sites/status?name=xxx # 获取站点状态
```

## 📊 **使用示例**

### **1. 通过配置管理器更新站点**

```go
// JSON数据
jsonData := `{
  "name": "example_site",
  "domains": ["example.com", "www.example.com"],
  "upstreams": [
    {
      "name": "backend1",
      "address": "*************",
      "port": 8080,
      "weight": 1
    }
  ],
  "acl": {
    "allow": ["***********/24"],
    "deny": ["********"]
  }
}`

// 更新站点
err := configManager.UpdateSiteFromJSON("example_site", []byte(jsonData))
if err != nil {
    log.Printf("更新站点失败: %v", err)
}
```

### **2. 通过HTTP API更新站点**

#### **创建站点**
```bash
curl -X POST http://localhost:8080/api/sites \
  -H "Content-Type: application/json" \
  -d '{
    "name": "new_site",
    "domains": ["new.example.com"],
    "upstreams": [
      {
        "name": "backend1",
        "address": "*************",
        "port": 8080,
        "weight": 1
      }
    ],
    "enabled": true
  }'
```

#### **更新站点**
```bash
curl -X PUT "http://localhost:8080/api/sites?name=new_site" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "new_site",
    "domains": ["new.example.com", "updated.example.com"],
    "upstreams": [
      {
        "name": "backend1",
        "address": "*************",
        "port": 8080,
        "weight": 1
      }
    ],
    "acl": {
      "allow": ["***********/24"],
      "deny": []
    }
  }'
```

#### **获取站点配置**
```bash
curl "http://localhost:8080/api/sites?name=new_site"
```

#### **删除站点**
```bash
curl -X DELETE "http://localhost:8080/api/sites?name=new_site"
```

#### **批量更新**
```bash
curl -X POST http://localhost:8080/api/sites/batch \
  -H "Content-Type: application/json" \
  -d '{
    "sites": [
      {
        "name": "site1",
        "domains": ["site1.com"],
        "upstreams": [{"name": "backend1", "address": "*************", "port": 8080}]
      },
      {
        "name": "site2", 
        "domains": ["site2.com"],
        "upstreams": [{"name": "backend2", "address": "*************", "port": 8080}]
      }
    ]
  }'
```

### **3. 站点配置JSON格式**

#### **完整站点配置示例**
```json
{
  "name": "example_site",
  "domains": ["example.com", "www.example.com"],
  "enabled": true,
  "http_port": 80,
  "https_port": 443,
  "upstreams": [
    {
      "name": "backend1",
      "address": "*************",
      "port": 8080,
      "protocol": "http",
      "weight": 1,
      "max_fails": 3,
      "fail_timeout": "30s",
      "backup": false
    }
  ],
  "routes": [
    {
      "pattern": "^/api/",
      "upstream": "backend1",
      "cache": false
    },
    {
      "pattern": "^/",
      "static_dir": "/var/www/html",
      "cache": true
    }
  ],
  "ssl": {
    "enabled": true,
    "cert_file": "/path/to/cert.pem",
    "key_file": "/path/to/key.pem"
  },
  "acl": {
    "allow": ["***********/24"],
    "deny": ["********"],
    "allow_file": "",
    "deny_file": ""
  },
  "headers": {
    "request": {
      "set": {"X-Real-IP": "*************"},
      "remove": [],
      "ignore": []
    },
    "response": {
      "set": {"Server": "MyProxy"},
      "remove": [],
      "ignore": []
    }
  }
}
```

## 🎯 **重载机制**

### **自动重载流程**
1. **接收JSON配置** → 解析和验证
2. **执行配置回调** → 更新站点实例
3. **更新服务器处理器** → 使用新的站点实例
4. **原子更新配置** → 确保一致性

### **回调执行顺序**
```
JSON解析 → 配置验证 → 站点回调 → 处理器更新 → 配置存储
```

## 🔍 **配置验证**

系统会自动验证以下内容：
- ✅ 站点名称不能为空
- ✅ 域名列表不能为空
- ✅ 上游服务器列表不能为空
- ✅ 上游服务器地址不能为空
- ✅ 域名格式检查

## 📊 **响应格式**

### **成功响应**
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    "site_name": "example_site"
  }
}
```

### **错误响应**
```json
{
  "success": false,
  "message": "错误描述"
}
```

## 🚀 **集成建议**

### **1. 在现有监控API中集成**
```go
// 在monitor包中添加站点管理
siteManager := api.NewSiteManager(configManager, logger)
siteManager.RegisterRoutes(mux)
```

### **2. 添加认证和授权**
```go
// 使用现有的认证中间件
mux.HandleFunc("/api/sites", authMiddleware(siteManager.handleSiteRequest))
```

### **3. 配置文件持久化**
当前更新只在内存中生效，如需持久化到配置文件，可以：
- 在配置管理器中添加文件写入功能
- 定期将内存配置同步到文件
- 在配置更新后立即写入文件

## 💡 **使用场景**

1. **动态站点管理**：通过API动态添加/删除站点
2. **配置热更新**：无需重启服务即可更新站点配置
3. **批量配置**：一次性更新多个站点配置
4. **外部系统集成**：其他系统通过API管理反向代理配置
5. **配置管理界面**：Web界面通过API进行配置管理

现在你可以非常方便地通过JSON来更新站点配置，支持单个更新、批量更新、创建、删除等完整的CRUD操作！

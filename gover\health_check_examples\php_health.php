<?php
/**
 * PHP健康检查页面
 * 用于检测PHP-FPM和应用程序状态
 */

// 设置响应头
header('Content-Type: application/json');
header('Cache-Control: no-cache, no-store, must-revalidate');

// 初始化状态数组
$status = [
    'status' => 'ok',
    'timestamp' => time(),
    'datetime' => date('Y-m-d H:i:s'),
    'checks' => []
];

// 1. 检查PHP版本
$status['checks']['php_version'] = [
    'status' => 'ok',
    'value' => PHP_VERSION,
    'message' => 'PHP版本正常'
];

// 2. 检查PHP-FPM状态
if (function_exists('fastcgi_finish_request')) {
    $status['checks']['php_fpm'] = [
        'status' => 'ok',
        'value' => 'available',
        'message' => 'PHP-FPM可用'
    ];
} else {
    $status['checks']['php_fpm'] = [
        'status' => 'error',
        'value' => 'unavailable',
        'message' => 'PHP-FPM不可用'
    ];
    $status['status'] = 'error';
}

// 3. 检查数据库连接（如果有配置）
if (defined('DB_HOST') && defined('DB_NAME')) {
    try {
        $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        $status['checks']['database'] = [
            'status' => 'ok',
            'value' => 'connected',
            'message' => '数据库连接正常'
        ];
    } catch (PDOException $e) {
        $status['checks']['database'] = [
            'status' => 'error',
            'value' => 'disconnected',
            'message' => '数据库连接失败: ' . $e->getMessage()
        ];
        $status['status'] = 'error';
    }
}

// 4. 检查文件系统权限
$tempDir = sys_get_temp_dir();
if (is_writable($tempDir)) {
    $status['checks']['filesystem'] = [
        'status' => 'ok',
        'value' => 'writable',
        'message' => '文件系统权限正常'
    ];
} else {
    $status['checks']['filesystem'] = [
        'status' => 'error',
        'value' => 'readonly',
        'message' => '文件系统权限异常'
    ];
    $status['status'] = 'error';
}

// 5. 检查内存使用
$memoryUsage = memory_get_usage(true);
$memoryLimit = ini_get('memory_limit');
$status['checks']['memory'] = [
    'status' => 'ok',
    'value' => [
        'usage' => formatBytes($memoryUsage),
        'limit' => $memoryLimit
    ],
    'message' => '内存使用正常'
];

// 6. 检查关键文件
$criticalFiles = [
    '/var/www/html/index.php',
    '/var/www/html/wp-config.php',  // WordPress
    '/var/www/html/config.php'      // 其他应用
];

foreach ($criticalFiles as $file) {
    if (file_exists($file)) {
        $status['checks']['file_' . basename($file)] = [
            'status' => 'ok',
            'value' => 'exists',
            'message' => '关键文件存在: ' . basename($file)
        ];
    }
}

// 7. 检查扩展
$requiredExtensions = ['curl', 'json', 'mbstring', 'openssl'];
foreach ($requiredExtensions as $ext) {
    if (extension_loaded($ext)) {
        $status['checks']['extension_' . $ext] = [
            'status' => 'ok',
            'value' => 'loaded',
            'message' => '扩展已加载: ' . $ext
        ];
    } else {
        $status['checks']['extension_' . $ext] = [
            'status' => 'error',
            'value' => 'missing',
            'message' => '扩展未加载: ' . $ext
        ];
        $status['status'] = 'error';
    }
}

// 8. 检查应用程序状态（WordPress示例）
if (function_exists('wp_version')) {
    $status['checks']['wordpress'] = [
        'status' => 'ok',
        'value' => wp_version(),
        'message' => 'WordPress运行正常'
    ];
}

// 设置HTTP状态码
if ($status['status'] === 'ok') {
    http_response_code(200);
} else {
    http_response_code(503); // Service Unavailable
}

// 输出JSON响应
echo json_encode($status, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

/**
 * 格式化字节数
 */
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}
?> 
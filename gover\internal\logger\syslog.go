package logger

import (
	"fmt"
	"net"
	"os"
	"sync"
	"time"
)

// SyslogWriter 实现跨平台的 syslog 日志发送
// 支持 UDP/TCP，线程安全

type SyslogWriter struct {
	conn    net.Conn
	mu      sync.Mutex
	network string
	address string
	closed  bool
}

// NewSyslogWriter 创建 syslog 发送器
func NewSyslogWriter(network, address string) (*SyslogWriter, error) {
	conn, err := net.Dial(network, address)
	if err != nil {
		return nil, fmt.Errorf("syslog连接失败: %w", err)
	}
	return &SyslogWriter{
		conn:    conn,
		network: network,
		address: address,
	}, nil
}

// WriteSyslog 发送一条 syslog 消息
func (w *SyslogWriter) WriteSyslog(msg string) error {
	w.mu.Lock()
	defer w.mu.Unlock()
	if w.closed {
		return fmt.Errorf("syslog连接已关闭")
	}
	if len(msg) == 0 {
		return nil
	}
	if msg[len(msg)-1] != '\n' {
		msg += "\n"
	}
	// 构造 syslog 协议头
	pri := "<14>" // info
	timestamp := time.Now().Format("Jan 02 15:04:05")
	hostname, _ := os.Hostname()
	appname := "reverse-proxy"
	pid := os.Getpid()
	header := fmt.Sprintf("%s%s %s %s[%d]: ", pri, timestamp, hostname, appname, pid)
	fullMsg := header + msg
	_, err := w.conn.Write([]byte(fullMsg))
	return err
}

// Close 关闭 syslog 连接
func (w *SyslogWriter) Close() error {
	w.mu.Lock()
	defer w.mu.Unlock()
	if w.closed {
		return nil
	}
	w.closed = true
	return w.conn.Close()
}

// 实现 io.Writer 接口
func (w *SyslogWriter) Write(p []byte) (n int, err error) {
	err = w.WriteSyslog(string(p))
	if err != nil {
		return 0, err
	}
	return len(p), nil
}

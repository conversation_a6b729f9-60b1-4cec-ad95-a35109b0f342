# 🔧 跨平台内存映射缓存路径修复

## 📋 问题描述

**问题现象：**
```
time="2025-06-30 10:34:43" level=info msg="内存映射缓存已启用, 路径:/dev/shm/proxy_mmap"
```

**问题分析：**
- `/dev/shm/proxy_mmap` 是Linux特有的共享内存路径
- 在Windows系统上不存在 `/dev/shm` 目录
- 会导致内存映射缓存功能在Windows上无法正常工作

## 🔧 修复方案

### **1. 跨平台路径处理函数**

添加了 `getCrossPlatformCachePath` 函数，根据不同操作系统选择合适的缓存路径：

```go
func getCrossPlatformCachePath(configPath string) string {
    // 如果配置路径不是Linux特有的路径，直接返回
    if !strings.HasPrefix(configPath, "/dev/shm/") && !strings.HasPrefix(configPath, "/tmp/") {
        return configPath
    }

    switch runtime.GOOS {
    case "windows":
        // Windows: 使用临时目录
        tempDir := os.TempDir()
        cacheName := filepath.Base(configPath)
        if cacheName == "" || cacheName == "/" {
            cacheName = "proxy_mmap"
        }
        return filepath.Join(tempDir, cacheName)
        
    case "darwin":
        // macOS: 使用 /tmp
        cacheName := filepath.Base(configPath)
        if cacheName == "" || cacheName == "/" {
            cacheName = "proxy_mmap"
        }
        return filepath.Join("/tmp", cacheName)
        
    case "linux":
        // Linux: 优先使用 /dev/shm，如果不存在则使用 /tmp
        if _, err := os.Stat("/dev/shm"); err == nil {
            return configPath // 使用原始路径
        }
        cacheName := filepath.Base(configPath)
        if cacheName == "" || cacheName == "/" {
            cacheName = "proxy_mmap"
        }
        return filepath.Join("/tmp", cacheName)
        
    default:
        // 其他系统：使用临时目录
        tempDir := os.TempDir()
        cacheName := filepath.Base(configPath)
        if cacheName == "" || cacheName == "/" {
            cacheName = "proxy_mmap"
        }
        return filepath.Join(tempDir, cacheName)
    }
}
```

### **2. 配置文件更新**

将配置文件中的路径从Linux特有路径改为通用路径：

```json
// 修改前
"mmap_cache": {
  "enabled": true,
  "max_size": "1GB",
  "base_path": "/dev/shm/proxy_mmap"
}

// 修改后
"mmap_cache": {
  "enabled": true,
  "max_size": "1GB",
  "base_path": "cache/mmap"
}
```

### **3. 初始化逻辑更新**

在内存映射缓存初始化时调用跨平台路径处理：

```go
// 初始化内存映射缓存
if p.config.Performance.MmapCache.Enabled {
    maxSize := parseSize(p.config.Performance.MmapCache.MaxSize)
    
    // 获取跨平台的缓存路径
    cachePath := getCrossPlatformCachePath(p.config.Performance.MmapCache.BasePath)
    
    mmapCache := performance.NewMmapCache(cachePath, maxSize)
    p.mmapCache = mmapCache

    p.logger.Infof("内存映射缓存已启用, 路径: %s", cachePath)
}
```

## 📊 **不同平台的路径映射**

| 操作系统 | 配置路径 | 实际使用路径 | 说明 |
|----------|----------|--------------|------|
| **Windows** | `cache/mmap` | `cache\mmap` | 相对路径，使用反斜杠 |
| **Windows** | `/dev/shm/proxy_mmap` | `C:\Users\<USER>\AppData\Local\Temp\proxy_mmap` | 自动映射到临时目录 |
| **Linux** | `cache/mmap` | `cache/mmap` | 相对路径保持不变 |
| **Linux** | `/dev/shm/proxy_mmap` | `/dev/shm/proxy_mmap` | 如果存在则使用原路径 |
| **Linux** | `/dev/shm/proxy_mmap` | `/tmp/proxy_mmap` | 如果/dev/shm不存在则降级 |
| **macOS** | `cache/mmap` | `cache/mmap` | 相对路径保持不变 |
| **macOS** | `/dev/shm/proxy_mmap` | `/tmp/proxy_mmap` | 自动映射到/tmp |

## 🎯 **修复效果**

### **修复前（Windows）**
```
time="..." level=info msg="内存映射缓存已启用, 路径:/dev/shm/proxy_mmap"
// 可能导致缓存功能失效
```

### **修复后（Windows）**
```
time="..." level=info msg="内存映射缓存已启用, 路径: cache\mmap"
// 或者
time="..." level=info msg="内存映射缓存已启用, 路径: C:\Users\<USER>\AppData\Local\Temp\proxy_mmap"
```

### **修复后（Linux）**
```
time="..." level=info msg="内存映射缓存已启用, 路径: /dev/shm/proxy_mmap"
// 或者
time="..." level=info msg="内存映射缓存已启用, 路径: cache/mmap"
```

## 🚀 **部署建议**

### **1. 立即部署**
- 编译新版本并部署
- 重启服务以应用新的路径逻辑

### **2. 验证效果**
观察启动日志中的缓存路径：
- **Windows**: 应该显示Windows风格的路径
- **Linux**: 应该显示Linux风格的路径
- **路径应该存在且可访问**

### **3. 配置建议**

#### **推荐配置（跨平台兼容）**
```json
"mmap_cache": {
  "enabled": true,
  "max_size": "1GB",
  "base_path": "cache/mmap"
}
```

#### **高性能配置（Linux专用）**
```json
"mmap_cache": {
  "enabled": true,
  "max_size": "1GB",
  "base_path": "/dev/shm/proxy_mmap"
}
```

## 🔍 **技术细节**

### **路径处理逻辑**
1. **检测特殊路径**：识别Linux特有的路径前缀
2. **平台判断**：使用`runtime.GOOS`判断操作系统
3. **路径映射**：根据平台特性选择最佳路径
4. **降级处理**：如果首选路径不可用，自动降级

### **性能考虑**
- **Linux**: 优先使用`/dev/shm`（内存文件系统，最快）
- **Windows**: 使用系统临时目录（通常在SSD上）
- **macOS**: 使用`/tmp`（通常在内存或SSD上）

### **兼容性保证**
- **向后兼容**：现有的相对路径配置不受影响
- **自动适配**：Linux特有路径自动转换为平台适配路径
- **错误处理**：路径不可用时有合理的降级策略

## 📋 **总结**

这个修复解决了内存映射缓存在跨平台部署时的路径兼容性问题：

- ✅ **Windows兼容**：自动使用Windows临时目录
- ✅ **Linux优化**：保持高性能的共享内存路径
- ✅ **macOS支持**：使用适合的临时路径
- ✅ **配置简化**：推荐使用相对路径配置
- ✅ **自动降级**：路径不可用时的备选方案

现在内存映射缓存功能可以在所有支持的平台上正常工作，无需手动修改配置文件。

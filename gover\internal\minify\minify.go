package minify

import (
	"bytes"
	"io"
	"mime"
	"net/http"
	"path/filepath"
	"strconv"
	"strings"

	"reverse-proxy/internal/config"
	"reverse-proxy/internal/logger"

	// "github.com/tdewolff/minify/v2"
	// "github.com/tdewolff/minify/v2/css"
	// "github.com/tdewolff/minify/v2/html"
	// "github.com/tdewolff/minify/v2/js"
)

// MinifyType 最小化类型
type MinifyType int

const (
	MinifyNone MinifyType = iota
	MinifyHTML
	MinifyCSS
	MinifyJS
)

// MinifyLevel 最小化级别
type MinifyLevel int

const (
	MinifyLevel_Conservative MinifyLevel = iota // 保守模式：最小压缩，最大兼容性
	MinifyLevel_Safe                            // 安全模式：平衡压缩和兼容性（默认）
	MinifyLevel_Aggressive                      // 激进模式：高压缩率，可能影响兼容性
	MinifyLevel_Maximum                         // 最大模式：最高压缩率，可能破坏功能
)

// Minifier 最小化器接口
type Minifier interface {
	ShouldMinify(contentType string, path string, cfg config.MinifyConfig) bool
	Minify(data []byte, contentType string, cfg config.MinifyConfig) ([]byte, error)
}

// DefaultMinifier 默认最小化器实现
type DefaultMinifier struct {
	// minifier *minify.M // 暂时禁用外部依赖
	enabled bool
}

// NewMinifier 创建新的最小化器（使用默认安全配置）
func NewMinifier() *DefaultMinifier {
	return NewMinifierWithConfig(MinifyLevel_Safe)
}

// NewMinifierWithConfig 根据配置级别创建最小化器
func NewMinifierWithConfig(level MinifyLevel) *DefaultMinifier {
	// 使用内部实现替代外部依赖
	return &DefaultMinifier{
		enabled: true,
	}
}

// NewMinifierWithDetailedConfig 根据详细配置创建最小化器
func NewMinifierWithDetailedConfig(cfg config.MinifyConfig) *DefaultMinifier {
	// 使用内部实现替代外部依赖
	return &DefaultMinifier{
		enabled: true,
	}
}

// configureConservativeMinify 配置保守模式（最小压缩，最大兼容性）
// 暂时禁用 - 外部库兼容性问题
/*
func configureConservativeMinify(m *minify.M) {
	// HTML - 只删除注释和多余空白
	m.AddFunc("text/html", func(m *minify.M, w io.Writer, r io.Reader, params map[string]string) error {
		return html.Minify(m, w, r, map[string]string{
			"keep-conditional-comments": "true", // 保留IE条件注释
			"keep-default-attrvals":     "true", // 保留默认属性值
			"keep-document-tags":        "true", // 保留html/head/body标签
			"keep-end-tags":             "true", // 保留所有结束标签
			"keep-whitespace":           "true", // 保留重要空白
			"keep-quotes":               "true", // 保留属性引号
		})
	})
	m.AddFunc("application/xhtml+xml", func(m *minify.M, w io.Writer, r io.Reader, params map[string]string) error {
		return html.Minify(m, w, r, map[string]string{
			"keep-conditional-comments": "true",
			"keep-default-attrvals":     "true",
			"keep-document-tags":        "true",
			"keep-end-tags":             "true",
			"keep-whitespace":           "true",
			"keep-quotes":               "true",
		})
	})

	// CSS - 只删除注释和多余空白
	m.AddFunc("text/css", func(m *minify.M, w io.Writer, r io.Reader, params map[string]string) error {
		return css.Minify(m, w, r, map[string]string{
			"precision": "5", // 保留5位小数精度
		})
	})

	// JavaScript - 只删除注释和多余空白
	m.AddFuncRegexp(regexp.MustCompile(`(text|application)/(x-)?javascript`), func(m *minify.M, w io.Writer, r io.Reader, params map[string]string) error {
		return js.Minify(m, w, r, map[string]string{
			"precision": "5", // 保留5位数字精度
		})
	})
}
*/

// configureSafeMinify 配置安全模式（平衡压缩和兼容性）
// 暂时禁用 - 外部库兼容性问题
/*
func configureSafeMinify(m *minify.M) {
	// HTML - 删除注释，保留重要结构
	m.AddFunc("text/html", func(m *minify.M, w io.Writer, r io.Reader, params map[string]string) error {
		return html.Minify(m, w, r, map[string]string{
			"keep-conditional-comments": "true",  // 保留IE条件注释
			"keep-default-attrvals":     "false", // 删除默认属性值
			"keep-document-tags":        "true",  // 保留html/head/body标签
			"keep-end-tags":             "false", // 删除可选的结束标签
			"keep-whitespace":           "false", // 删除多余空白
			"keep-quotes":               "false", // 删除不必要的引号
		})
	})
	m.AddFunc("application/xhtml+xml", func(m *minify.M, w io.Writer, r io.Reader, params map[string]string) error {
		return html.Minify(m, w, r, map[string]string{
			"keep-conditional-comments": "true",
			"keep-default-attrvals":     "false",
			"keep-document-tags":        "true",
			"keep-end-tags":             "true", // XHTML需要保留结束标签
			"keep-whitespace":           "false",
			"keep-quotes":               "false",
		})
	})

	// CSS - 删除注释，优化属性
	m.AddFunc("text/css", func(m *minify.M, w io.Writer, r io.Reader, params map[string]string) error {
		return css.Minify(m, w, r, map[string]string{
			"precision": "3", // 保留3位小数精度
		})
	})

	// JavaScript - 删除注释，保留语义
	m.AddFuncRegexp(regexp.MustCompile(`(text|application)/(x-)?javascript`), func(m *minify.M, w io.Writer, r io.Reader, params map[string]string) error {
		return js.Minify(m, w, r, map[string]string{
			"precision": "3", // 保留3位数字精度
		})
	})
}
*/

// configureAggressiveMinify 配置激进模式（高压缩率，可能影响兼容性）
// 暂时禁用 - 外部库兼容性问题
/*
func configureAggressiveMinify(m *minify.M) {
	// HTML - 激进压缩，删除大部分可选内容
	m.AddFunc("text/html", func(m *minify.M, w io.Writer, r io.Reader, params map[string]string) error {
		return html.Minify(m, w, r, map[string]string{
			"keep-conditional-comments": "false", // 删除条件注释
			"keep-default-attrvals":     "false", // 删除默认属性值
			"keep-document-tags":        "false", // 删除可选的html/head/body标签
			"keep-end-tags":             "false", // 删除可选的结束标签
			"keep-whitespace":           "false", // 删除所有多余空白
			"keep-quotes":               "false", // 删除不必要的引号
		})
	})
	m.AddFunc("application/xhtml+xml", func(m *minify.M, w io.Writer, r io.Reader, params map[string]string) error {
		return html.Minify(m, w, r, map[string]string{
			"keep-conditional-comments": "false",
			"keep-default-attrvals":     "false",
			"keep-document-tags":        "false",
			"keep-end-tags":             "true", // XHTML必须保留结束标签
			"keep-whitespace":           "false",
			"keep-quotes":               "false",
		})
	})

	// CSS - 激进压缩，优化所有可能的内容
	m.AddFunc("text/css", func(m *minify.M, w io.Writer, r io.Reader, params map[string]string) error {
		return css.Minify(m, w, r, map[string]string{
			"precision": "2", // 保留2位小数精度
		})
	})

	// JavaScript - 激进压缩
	m.AddFuncRegexp(regexp.MustCompile(`(text|application)/(x-)?javascript`), func(m *minify.M, w io.Writer, r io.Reader, params map[string]string) error {
		return js.Minify(m, w, r, map[string]string{
			"precision": "2", // 保留2位数字精度
		})
	})
}
*/

// configureMaximumMinify 配置最大模式（最高压缩率，可能破坏功能）
// 暂时禁用 - 外部库兼容性问题
/*
func configureMaximumMinify(m *minify.M) {
	// HTML - 最大压缩，删除所有可选内容
	m.AddFunc("text/html", func(m *minify.M, w io.Writer, r io.Reader, params map[string]string) error {
		return html.Minify(m, w, r, map[string]string{
			"keep-conditional-comments": "false", // 删除所有条件注释
			"keep-default-attrvals":     "false", // 删除所有默认属性值
			"keep-document-tags":        "false", // 删除所有可选标签
			"keep-end-tags":             "false", // 删除所有可选结束标签
			"keep-whitespace":           "false", // 删除所有空白
			"keep-quotes":               "false", // 删除所有不必要引号
		})
	})
	m.AddFunc("application/xhtml+xml", func(m *minify.M, w io.Writer, r io.Reader, params map[string]string) error {
		return html.Minify(m, w, r, map[string]string{
			"keep-conditional-comments": "false",
			"keep-default-attrvals":     "false",
			"keep-document-tags":        "false",
			"keep-end-tags":             "true", // XHTML语法要求
			"keep-whitespace":           "false",
			"keep-quotes":               "false",
		})
	})

	// CSS - 最大压缩
	m.AddFunc("text/css", func(m *minify.M, w io.Writer, r io.Reader, params map[string]string) error {
		return css.Minify(m, w, r, map[string]string{
			"precision": "1", // 最小精度
		})
	})

	// JavaScript - 最大压缩
	m.AddFuncRegexp(regexp.MustCompile(`(text|application)/(x-)?javascript`), func(m *minify.M, w io.Writer, r io.Reader, params map[string]string) error {
		return js.Minify(m, w, r, map[string]string{
			"precision": "1", // 最小精度
		})
	})
}
*/

// parseMinifyLevel 解析压缩级别字符串
func parseMinifyLevel(level string) MinifyLevel {
	switch strings.ToLower(level) {
	case "conservative":
		return MinifyLevel_Conservative
	case "safe", "":
		return MinifyLevel_Safe
	case "aggressive":
		return MinifyLevel_Aggressive
	case "maximum":
		return MinifyLevel_Maximum
	default:
		return MinifyLevel_Safe
	}
}

// buildHTMLParams 构建HTML压缩参数
func buildHTMLParams(htmlCfg config.MinifyHTMLConfig, level MinifyLevel) map[string]string {
	params := make(map[string]string)

	// 如果有具体配置，使用具体配置
	if htmlCfg != (config.MinifyHTMLConfig{}) {
		params["keep-conditional-comments"] = boolToString(htmlCfg.KeepConditionalComments)
		params["keep-default-attrvals"] = boolToString(htmlCfg.KeepDefaultAttrVals)
		params["keep-document-tags"] = boolToString(htmlCfg.KeepDocumentTags)
		params["keep-end-tags"] = boolToString(htmlCfg.KeepEndTags)
		params["keep-whitespace"] = boolToString(htmlCfg.KeepWhitespace)
		params["keep-quotes"] = boolToString(htmlCfg.KeepQuotes)
	} else {
		// 否则根据级别使用默认配置
		switch level {
		case MinifyLevel_Conservative:
			params["keep-conditional-comments"] = "true"
			params["keep-default-attrvals"] = "true"
			params["keep-document-tags"] = "true"
			params["keep-end-tags"] = "true"
			params["keep-whitespace"] = "true"
			params["keep-quotes"] = "true"
		case MinifyLevel_Safe:
			params["keep-conditional-comments"] = "true"
			params["keep-default-attrvals"] = "false"
			params["keep-document-tags"] = "true"
			params["keep-end-tags"] = "false"
			params["keep-whitespace"] = "false"
			params["keep-quotes"] = "false"
		case MinifyLevel_Aggressive:
			params["keep-conditional-comments"] = "false"
			params["keep-default-attrvals"] = "false"
			params["keep-document-tags"] = "false"
			params["keep-end-tags"] = "false"
			params["keep-whitespace"] = "false"
			params["keep-quotes"] = "false"
		case MinifyLevel_Maximum:
			params["keep-conditional-comments"] = "false"
			params["keep-default-attrvals"] = "false"
			params["keep-document-tags"] = "false"
			params["keep-end-tags"] = "false"
			params["keep-whitespace"] = "false"
			params["keep-quotes"] = "false"
		}
	}

	return params
}

// buildCSSParams 构建CSS压缩参数
func buildCSSParams(cssCfg config.MinifyCSSConfig, level MinifyLevel) map[string]string {
	params := make(map[string]string)

	precision := cssCfg.Precision
	if precision == 0 {
		// 根据级别设置默认精度
		switch level {
		case MinifyLevel_Conservative:
			precision = 5
		case MinifyLevel_Safe:
			precision = 3
		case MinifyLevel_Aggressive:
			precision = 2
		case MinifyLevel_Maximum:
			precision = 1
		}
	}

	params["precision"] = strconv.Itoa(precision)
	return params
}

// buildJSParams 构建JavaScript压缩参数
func buildJSParams(jsCfg config.MinifyJSConfig, level MinifyLevel) map[string]string {
	params := make(map[string]string)

	precision := jsCfg.Precision
	if precision == 0 {
		// 根据级别设置默认精度
		switch level {
		case MinifyLevel_Conservative:
			precision = 5
		case MinifyLevel_Safe:
			precision = 3
		case MinifyLevel_Aggressive:
			precision = 2
		case MinifyLevel_Maximum:
			precision = 1
		}
	}

	params["precision"] = strconv.Itoa(precision)
	return params
}

// boolToString 将布尔值转换为字符串
func boolToString(b bool) string {
	if b {
		return "true"
	}
	return "false"
}

// ShouldMinify 判断是否应该进行最小化
func (dm *DefaultMinifier) ShouldMinify(contentType string, path string, cfg config.MinifyConfig) bool {
	if !cfg.Enabled {
		return false
	}

	// 严格检查：拒绝处理二进制文件类型
	if isBinaryContentType(contentType) {
		logger.Debugf("跳过二进制文件类型的minify: %s", contentType)
		return false
	}

	// 检查内容类型
	minifyType := getMinifyType(contentType, path)
	if minifyType == MinifyNone {
		return false
	}

	// 检查是否在支持的类型列表中
	if len(cfg.Types) > 0 {
		typeSupported := false
		for _, t := range cfg.Types {
			switch t {
			case "html":
				if minifyType == MinifyHTML {
					typeSupported = true
				}
			case "css":
				if minifyType == MinifyCSS {
					typeSupported = true
				}
			case "js", "javascript":
				if minifyType == MinifyJS {
					typeSupported = true
				}
			}
		}
		if !typeSupported {
			return false
		}
	}

	return true
}

// ShouldMinifyWithSize 判断是否应该进行最小化（包含大小检查）
func (dm *DefaultMinifier) ShouldMinifyWithSize(contentType string, path string, size int, cfg config.MinifyConfig) bool {
	if !dm.ShouldMinify(contentType, path, cfg) {
		return false
	}

	// 检查最小大小限制
	if cfg.MinSize > 0 && size < int(cfg.MinSize) {
		logger.Debugf("文件过小，跳过minify: %s, 大小: %d, 最小限制: %d", path, size, int(cfg.MinSize))
		return false
	}

	// 检查最大大小限制
	if cfg.MaxSize > 0 && size > int(cfg.MaxSize) {
		logger.Debugf("文件过大，跳过minify: %s, 大小: %d, 最大限制: %d", path, size, int(cfg.MaxSize))
		return false
	}

	return true
}

// Minify 执行最小化
func (dm *DefaultMinifier) Minify(data []byte, contentType string, cfg config.MinifyConfig) ([]byte, error) {
	// 检查是否启用
	if !dm.enabled {
		logger.Debugf("Minify功能已禁用，返回原始数据")
		return data, nil
	}

	if !dm.ShouldMinifyWithSize(contentType, "", len(data), cfg) {
		return data, nil
	}

	// 使用内部实现的 minify
	logger.Debugf("使用内部 minify 实现处理内容类型: %s", contentType)
	return MinifyResponseInternal(data, contentType, cfg)
}

// getMinifyType 根据内容类型和路径判断最小化类型
func getMinifyType(contentType string, path string) MinifyType {
	// 首先根据 Content-Type 判断
	if contentType != "" {
		mediaType, _, _ := mime.ParseMediaType(contentType)
		switch mediaType {
		case "text/html", "application/xhtml+xml":
			return MinifyHTML
		case "text/css":
			return MinifyCSS
		case "text/javascript", "application/javascript", "application/x-javascript":
			return MinifyJS
		}
	}

	// 如果 Content-Type 无法判断，根据文件扩展名判断
	if path != "" {
		ext := strings.ToLower(filepath.Ext(path))
		switch ext {
		case ".html", ".htm", ".xhtml":
			return MinifyHTML
		case ".css":
			return MinifyCSS
		case ".js", ".mjs":
			return MinifyJS
		}
	}

	return MinifyNone
}

// isBinaryContentType 检查是否为二进制文件类型
func isBinaryContentType(contentType string) bool {
	if contentType == "" {
		return false
	}

	mediaType, _, _ := mime.ParseMediaType(contentType)

	// 明确的二进制类型
	binaryTypes := []string{
		"image/",
		"video/",
		"audio/",
		"application/octet-stream",
		"application/pdf",
		"application/zip",
		"application/x-rar-compressed",
		"application/x-7z-compressed",
		"application/x-tar",
		"application/gzip",
		"application/x-bzip2",
		"font/",
		"application/font-",
		"application/vnd.",
	}

	for _, prefix := range binaryTypes {
		if strings.HasPrefix(mediaType, prefix) {
			return true
		}
	}

	return false
}

// isValidMinifiedContent 基本的内容完整性检查
func isValidMinifiedContent(data []byte, mediaType string) bool {
	if len(data) == 0 {
		return false
	}

	content := string(data)

	switch mediaType {
	case "text/html", "application/xhtml+xml":
		// HTML基本检查：确保有基本的HTML结构
		return strings.Contains(content, "<") && strings.Contains(content, ">")

	case "text/css":
		// CSS基本检查：确保没有明显的语法错误
		// 检查括号匹配
		openBraces := strings.Count(content, "{")
		closeBraces := strings.Count(content, "}")
		return openBraces == closeBraces

	case "text/javascript", "application/javascript", "application/x-javascript":
		// JS基本检查：确保没有明显的语法错误
		// 简单检查：不应该有未闭合的字符串
		singleQuotes := strings.Count(content, "'")
		doubleQuotes := strings.Count(content, "\"")
		return singleQuotes%2 == 0 && doubleQuotes%2 == 0

	default:
		return true
	}
}

// MinifyResponse 最小化 HTTP 响应
func MinifyResponse(resp *http.Response, cfg config.MinifyConfig) error {
	if resp == nil || resp.Body == nil {
		return nil
	}

	// 根据配置创建最小化器
	var minifier *DefaultMinifier
	if cfg.Level != "" || cfg.HTML != (config.MinifyHTMLConfig{}) || cfg.CSS != (config.MinifyCSSConfig{}) || cfg.JS != (config.MinifyJSConfig{}) {
		// 使用详细配置
		minifier = NewMinifierWithDetailedConfig(cfg)
	} else {
		// 使用默认配置
		minifier = NewMinifier()
	}

	contentType := resp.Header.Get("Content-Type")

	// 先读取内容以检查大小
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}
	resp.Body.Close()

	if !minifier.ShouldMinifyWithSize(contentType, resp.Request.URL.Path, len(body), cfg) {
		// 恢复响应体
		resp.Body = io.NopCloser(bytes.NewReader(body))
		return nil
	}

	// 执行最小化
	minified, err := minifier.Minify(body, contentType, cfg)
	if err != nil {
		// 最小化失败时使用原始内容
		minified = body
	}

	// 更新响应体
	resp.Body = io.NopCloser(bytes.NewReader(minified))
	resp.ContentLength = int64(len(minified))
	resp.Header.Set("Content-Length", strconv.Itoa(len(minified)))

	return nil
}

// GetMinifyConfig 获取最小化配置（支持三级配置）
func GetMinifyConfig(routeConfig *config.RouteConfig, siteConfig *config.SiteConfig, globalConfig *config.MinifyConfig) config.MinifyConfig {
	// 路由级配置优先
	if routeConfig != nil && routeConfig.Minify != nil {
		return *routeConfig.Minify
	}

	// 站点级配置次之
	if siteConfig != nil && siteConfig.Minify != nil {
		return *siteConfig.Minify
	}

	// 全局配置最后
	if globalConfig != nil {
		return *globalConfig
	}

	// 默认配置
	return config.MinifyConfig{
		Enabled: false,
	}
}

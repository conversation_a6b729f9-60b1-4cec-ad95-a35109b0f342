# Rust Reverse Proxy Server

一个高性能、模块化的反向代理服务器，使用Rust编写，参考Go版本的优秀设计思路。

## 特性

### 🚀 高性能
- 基于Tokio的异步I/O
- 零拷贝数据传输
- 高效的连接池管理
- 多种压缩算法支持（gzip, brotli, zstd）

### 🔧 模块化设计
- 处理器链架构，易于扩展
- 插件化的缓存后端
- 灵活的路由系统
- 可配置的负载均衡算法

### 💾 多层缓存
- **内存缓存**: 热点数据快速访问
- **文件缓存**: 持久化存储，支持站点目录结构
- **Redis缓存**: 分布式缓存支持
- **混合缓存**: 内存+文件的智能分层

### 📊 监控和日志
- 结构化日志（支持文件和syslog）
- Prometheus指标导出
- 实时性能监控
- 健康检查

### 🔒 安全特性
- SSL/TLS支持（基于rustls）
- ACL访问控制
- 请求限流和熔断
- 安全头部管理

## 架构设计

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   HTTP Client   │───▶│  Reverse Proxy   │───▶│  Backend Server │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌──────────────────┐
                    │ Processor Chain  │
                    └──────────────────┘
                              │
        ┌─────────────────────┼─────────────────────┐
        ▼                     ▼                     ▼
┌─────────────┐    ┌─────────────────┐    ┌─────────────┐
│   Route     │    │     Cache       │    │   Static    │
│ Processor   │    │   Processor     │    │ Processor   │
└─────────────┘    └─────────────────┘    └─────────────┘
        │                     │                     │
        ▼                     ▼                     ▼
┌─────────────┐    ┌─────────────────┐    ┌─────────────┐
│   Proxy     │    │   Compression   │    │   Header    │
│ Processor   │    │   Processor     │    │ Processor   │
└─────────────┘    └─────────────────┘    └─────────────┘
```

## 快速开始

### 安装依赖

确保你的系统已安装：
- Rust 1.75+
- 可选：Redis服务器（如果使用Redis缓存）

### 编译

```bash
# 基础版本
cargo build --release

# 包含Redis支持
cargo build --release --features redis-cache

# 包含所有特性
cargo build --release --features default
```

### 配置

复制示例配置文件并根据需要修改：

```bash
cp config.json my-config.json
# 编辑 my-config.json
```

### 运行

```bash
# 使用默认配置
./target/release/rust-reverse-proxy

# 使用自定义配置
./target/release/rust-reverse-proxy -c my-config.json

# 设置日志级别
./target/release/rust-reverse-proxy -c my-config.json -l debug
```

## 配置说明

### 基础配置

```json
{
  "server": {
    "http_port": 80,
    "https_port": 443,
    "max_connections": 1000
  },
  "cache": {
    "enabled": true,
    "type": "hybrid",
    "path": "cache",
    "max_size": "2GB"
  }
}
```

### 站点配置

```json
{
  "sites": [
    {
      "site_id": "example-site",
      "name": "example.com",
      "domains": ["example.com", "www.example.com"],
      "upstreams": [
        {
          "name": "primary",
          "address": "127.0.0.1",
          "port": 8001,
          "weight": 100
        }
      ],
      "routes": [
        {
          "pattern": "^/static/",
          "static_dir": "/var/www/static",
          "cache": true
        },
        {
          "pattern": "^/",
          "upstream": "main_group",
          "cache": true
        }
      ]
    }
  ]
}
```

### 缓存配置

支持多种缓存类型：

```json
{
  "cache": {
    "type": "hybrid",  // memory, file, redis, hybrid
    "path": "cache",   // 文件缓存路径，支持站点子目录
    "redis": {
      "url": "redis://127.0.0.1:6379",
      "database": 0,
      "prefix": "proxy_cache:"
    }
  }
}
```

缓存目录结构：
```
cache/
├── site1/
│   ├── static_files/
│   └── dynamic_content/
└── site2/
    ├── api_responses/
    └── assets/
```

## 性能特性

### 压缩支持
- **gzip**: 标准压缩，兼容性好
- **brotli**: 更高压缩比，现代浏览器支持
- **zstd**: 快速压缩，适合实时场景

### 负载均衡算法
- **轮询 (Round Robin)**: 平均分配请求
- **权重轮询 (Weighted Round Robin)**: 按权重分配
- **最少连接 (Least Connections)**: 选择连接数最少的服务器
- **一致性哈希 (Consistent Hash)**: 会话保持

### 缓存策略
- **LRU**: 最近最少使用
- **LFU**: 最少使用频率
- **TTL**: 基于时间过期
- **智能分层**: 热点数据自动提升到内存

## 监控和运维

### 指标监控

访问 `http://localhost:8080/metrics` 获取Prometheus格式的指标：

```
# 请求统计
http_requests_total{site="example.com",method="GET",status="200"} 1234
http_request_duration_seconds{site="example.com"} 0.123

# 缓存统计
cache_hits_total{site="example.com",type="memory"} 567
cache_misses_total{site="example.com",type="memory"} 123
cache_size_bytes{site="example.com",type="file"} 1048576

# 上游服务器统计
upstream_requests_total{upstream="primary",site="example.com"} 890
upstream_response_time_seconds{upstream="primary"} 0.045
```

### 健康检查

```bash
# 检查服务器状态
curl http://localhost:8080/health

# 检查特定站点
curl http://localhost:8080/health/example.com

# 获取详细统计
curl http://localhost:8080/stats
```

### 日志格式

支持多种日志格式和目标：

```json
{
  "log": {
    "targets": [
      {
        "type": "file",
        "filename": "logs/access_{site}_{date}.log",
        "format": "combined"
      },
      {
        "type": "syslog",
        "network": "udp",
        "address": "127.0.0.1:514",
        "format": "json"
      }
    ]
  }
}
```

## 开发指南

### 项目结构

```
src/
├── main.rs              # 程序入口
├── config/              # 配置管理
│   ├── mod.rs
│   ├── validation.rs    # 配置验证
│   └── watcher.rs       # 热重载
├── cache/               # 缓存系统
│   ├── mod.rs
│   ├── manager.rs       # 缓存管理器
│   ├── memory_cache.rs  # 内存缓存
│   ├── file_cache.rs    # 文件缓存
│   ├── redis_cache.rs   # Redis缓存
│   └── hybrid_cache.rs  # 混合缓存
├── processor/           # 处理器链
├── server/              # HTTP服务器
├── load_balancer/       # 负载均衡
├── ssl/                 # SSL/TLS
├── monitor/             # 监控系统
├── logger/              # 日志系统
└── utils/               # 工具函数
```

### 添加新的处理器

```rust
use async_trait::async_trait;
use crate::processor::{Processor, ProcessResult, RequestContext};

pub struct MyProcessor;

#[async_trait]
impl Processor for MyProcessor {
    async fn process(&self, ctx: &mut RequestContext) -> ProcessResult {
        // 处理逻辑
        ProcessResult::Continue
    }
    
    fn name(&self) -> &'static str {
        "MyProcessor"
    }
    
    fn priority(&self) -> u8 {
        100
    }
}
```

### 运行测试

```bash
# 运行所有测试
cargo test

# 运行特定模块测试
cargo test cache

# 运行基准测试
cargo bench

# 包含Redis测试（需要Redis服务器）
cargo test --features redis-cache
```

## 部署

### Docker部署

```dockerfile
FROM rust:1.75 as builder
WORKDIR /app
COPY . .
RUN cargo build --release

FROM debian:bookworm-slim
RUN apt-get update && apt-get install -y ca-certificates && rm -rf /var/lib/apt/lists/*
COPY --from=builder /app/target/release/rust-reverse-proxy /usr/local/bin/
COPY config.json /etc/proxy/config.json
EXPOSE 80 443 8080
CMD ["rust-reverse-proxy", "-c", "/etc/proxy/config.json"]
```

### 系统服务

```ini
[Unit]
Description=Rust Reverse Proxy Server
After=network.target

[Service]
Type=simple
User=proxy
Group=proxy
ExecStart=/usr/local/bin/rust-reverse-proxy -c /etc/proxy/config.json
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！

## 路线图

- [ ] 完成核心处理器框架
- [ ] 实现路由和静态文件处理器
- [ ] 添加反向代理和负载均衡
- [ ] 完善SSL/TLS支持
- [ ] 实现监控和日志系统
- [ ] 性能优化和基准测试
- [ ] 文档和示例完善

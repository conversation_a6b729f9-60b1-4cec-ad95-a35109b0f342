<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rust反向代理服务器 - 测试页面</title>
    <link rel="stylesheet" href="/styles.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .header h1 {
            color: #667eea;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        .header p {
            color: #666;
            font-size: 1.2rem;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        .feature {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        .feature h3 {
            color: #667eea;
            margin-bottom: 15px;
        }
        .test-section {
            background: #e3f2fd;
            padding: 30px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .test-section h2 {
            color: #1976d2;
            margin-bottom: 20px;
        }
        .test-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .test-link {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 12px 20px;
            text-decoration: none;
            border-radius: 6px;
            text-align: center;
            transition: background 0.3s;
        }
        .test-link:hover {
            background: #5a6fd8;
        }
        .stats {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin-top: 30px;
        }
        .stats h3 {
            margin-bottom: 15px;
            color: #333;
        }
        .stat-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #ddd;
        }
        .stat-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Rust反向代理服务器</h1>
            <p>高性能、现代化的反向代理解决方案</p>
        </div>

        <div class="features">
            <div class="feature">
                <h3>🗂️ 静态文件服务</h3>
                <p>高效的静态文件服务，支持多种文件类型，自动MIME类型检测</p>
            </div>
            <div class="feature">
                <h3>⚡ 智能缓存</h3>
                <p>内存缓存系统，支持TTL、LRU淘汰策略，显著提升响应速度</p>
            </div>
            <div class="feature">
                <h3>🗜️ 响应压缩</h3>
                <p>支持Brotli、Zstd、Gzip等现代压缩算法，减少带宽使用</p>
            </div>
            <div class="feature">
                <h3>🔒 安全头部</h3>
                <p>自动添加安全头部，支持CORS、CSP、HSTS等安全策略</p>
            </div>
            <div class="feature">
                <h3>🛠️ 错误处理</h3>
                <p>统一的错误处理，自定义错误页面，详细的错误统计</p>
            </div>
            <div class="feature">
                <h3>📊 性能监控</h3>
                <p>实时性能监控，请求统计，响应时间追踪</p>
            </div>
        </div>

        <div class="test-section">
            <h2>🧪 功能测试</h2>
            <p>点击下面的链接测试各种功能：</p>
            <div class="test-links">
                <a href="/test.txt" class="test-link">📄 文本文件</a>
                <a href="/test.json" class="test-link">📋 JSON数据</a>
                <a href="/test.css" class="test-link">🎨 CSS样式</a>
                <a href="/test.js" class="test-link">⚡ JavaScript</a>
                <a href="/large.txt" class="test-link">📦 大文件（压缩测试）</a>
                <a href="/nonexistent" class="test-link">❌ 404错误</a>
                <a href="/api/test" class="test-link">🔄 代理测试</a>
                <a href="/cors-test" class="test-link">🌐 CORS测试</a>
            </div>
        </div>

        <div class="stats">
            <h3>📈 服务器信息</h3>
            <div class="stat-item">
                <span>服务器</span>
                <span id="server-info">RustProxy/1.0</span>
            </div>
            <div class="stat-item">
                <span>请求ID</span>
                <span id="request-id">-</span>
            </div>
            <div class="stat-item">
                <span>响应时间</span>
                <span id="response-time">-</span>
            </div>
            <div class="stat-item">
                <span>压缩算法</span>
                <span id="compression">-</span>
            </div>
            <div class="stat-item">
                <span>缓存状态</span>
                <span id="cache-status">-</span>
            </div>
        </div>
    </div>

    <script src="/test.js"></script>
    <script>
        // 显示响应头信息
        document.addEventListener('DOMContentLoaded', function() {
            // 这些信息通常从响应头中获取
            const headers = {
                'x-request-id': document.getElementById('request-id'),
                'x-response-time': document.getElementById('response-time'),
                'content-encoding': document.getElementById('compression'),
                'x-cache': document.getElementById('cache-status')
            };

            // 模拟显示一些信息（实际应用中会从HTTP头部获取）
            setTimeout(() => {
                document.getElementById('request-id').textContent = 'req-' + Math.random().toString(36).substr(2, 9);
                document.getElementById('response-time').textContent = Math.floor(Math.random() * 50 + 10) + 'ms';
                document.getElementById('compression').textContent = 'gzip';
                document.getElementById('cache-status').textContent = 'MISS';
            }, 100);
        });
    </script>
</body>
</html>

use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use tokio::sync::RwLock;
use tracing::{debug, error, info, warn};

use crate::config::MonitorConfig;
use crate::processor::RequestContext;

pub mod access_log;
pub mod health_check;
pub mod metrics;
pub mod stats;

/// 监控管理器
pub struct MonitorManager {
    config: MonitorConfig,
    stats: Arc<RwLock<SystemStats>>,
    access_logger: Arc<access_log::AccessLogger>,
    health_checker: Arc<health_check::HealthChecker>,
    metrics_collector: Arc<metrics::MetricsCollector>,
    start_time: Instant,
}

impl MonitorManager {
    /// 创建新的监控管理器
    pub async fn new(config: MonitorConfig) -> Result<Self> {
        let stats = Arc::new(RwLock::new(SystemStats::new()));

        // 创建访问日志记录器
        let access_logger = Arc::new(access_log::AccessLogger::new(&config).await?);

        // 创建健康检查器
        let health_checker = Arc::new(health_check::HealthChecker::new(&config).await?);

        // 创建指标收集器
        let metrics_collector = Arc::new(metrics::MetricsCollector::new(&config).await?);

        info!("Monitor manager initialized successfully");

        Ok(Self {
            config,
            stats,
            access_logger,
            health_checker,
            metrics_collector,
            start_time: Instant::now(),
        })
    }

    /// 记录请求
    pub async fn record_request(&self, ctx: &RequestContext) -> Result<()> {
        // 更新统计信息
        self.update_stats(ctx).await?;

        // 记录访问日志
        self.access_logger.log_request(ctx).await?;

        // 收集指标
        self.metrics_collector.record_request(ctx).await?;

        Ok(())
    }

    /// 更新统计信息
    async fn update_stats(&self, ctx: &RequestContext) -> Result<()> {
        let mut stats = self.stats.write().await;

        stats.total_requests += 1;
        stats.requests_per_second.add_sample(1.0);

        // 按状态码分类
        match ctx.response.status_code {
            200..=299 => stats.successful_requests += 1,
            400..=499 => stats.client_errors += 1,
            500..=599 => stats.server_errors += 1,
            _ => {}
        }

        // 记录响应时间
        let response_time = ctx.start_time.elapsed().as_millis() as f64;
        stats.response_times.add_sample(response_time);

        // 更新最大响应时间
        if response_time > stats.max_response_time {
            stats.max_response_time = response_time;
        }

        // 记录字节数
        if let Some(content_length) = ctx.response.headers.get("content-length") {
            if let Ok(bytes) = content_length.parse::<u64>() {
                stats.bytes_sent += bytes;
                stats.bytes_per_second.add_sample(bytes as f64);
            }
        }

        // 按站点统计
        if let Some(site) = &ctx.site {
            let site_id = &site.site_id;
            let site_stats = stats.site_stats.entry(site_id.clone()).or_insert_with(SiteStats::new);
            site_stats.requests += 1;
            site_stats.response_time.add_sample(response_time);

            if ctx.response.status_code >= 400 {
                site_stats.errors += 1;
            }
        }

        debug!("Updated stats for request {} ({}ms)", ctx.request_id, response_time);
        Ok(())
    }

    /// 获取系统统计信息
    pub async fn get_stats(&self) -> SystemStats {
        let stats = self.stats.read().await;
        let mut result = stats.clone();

        // 计算运行时间
        result.uptime = self.start_time.elapsed();

        // 计算平均值
        result.avg_response_time = result.response_times.average();
        result.current_rps = result.requests_per_second.current_rate();
        result.current_bps = result.bytes_per_second.current_rate();

        result
    }

    /// 获取健康状态
    pub async fn get_health_status(&self) -> Result<HealthStatus> {
        self.health_checker.check_health().await
    }

    /// 获取指标数据
    pub async fn get_metrics(&self) -> Result<HashMap<String, f64>> {
        self.metrics_collector.get_metrics().await
    }

    /// 启动监控任务
    pub async fn start_monitoring(&self) -> Result<()> {
        info!("Starting monitoring tasks...");

        // 启动健康检查任务
        let health_checker = self.health_checker.clone();
        tokio::spawn(async move {
            if let Err(e) = health_checker.start_periodic_checks().await {
                error!("Health check task failed: {}", e);
            }
        });

        // 启动指标收集任务
        let metrics_collector = self.metrics_collector.clone();
        tokio::spawn(async move {
            if let Err(e) = metrics_collector.start_collection().await {
                error!("Metrics collection task failed: {}", e);
            }
        });

        // 启动统计清理任务
        let stats = self.stats.clone();
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(60));
            loop {
                interval.tick().await;
                let mut stats = stats.write().await;
                stats.cleanup_old_data();
            }
        });

        info!("All monitoring tasks started successfully");
        Ok(())
    }

    /// 停止监控
    pub async fn stop(&self) -> Result<()> {
        info!("Stopping monitor manager...");

        // 刷新访问日志
        self.access_logger.flush().await?;

        info!("Monitor manager stopped successfully");
        Ok(())
    }

    /// 获取监控配置信息
    pub fn get_config_info(&self) -> MonitorConfigInfo {
        MonitorConfigInfo {
            enabled: self.config.enabled,
            port: self.config.port,
            access_log_enabled: true, // 从access_logger获取
            health_check_enabled: true, // 从health_checker获取
            metrics_enabled: true, // 从metrics_collector获取
            uptime: self.start_time.elapsed(),
        }
    }
}

/// 系统统计信息
#[derive(Debug, Clone)]
pub struct SystemStats {
    pub total_requests: u64,
    pub successful_requests: u64,
    pub client_errors: u64,
    pub server_errors: u64,
    pub bytes_sent: u64,
    pub uptime: Duration,
    pub max_response_time: f64,
    pub avg_response_time: f64,
    pub current_rps: f64,
    pub current_bps: f64,
    pub response_times: stats::MovingAverage,
    pub requests_per_second: stats::RateCounter,
    pub bytes_per_second: stats::RateCounter,
    pub site_stats: HashMap<String, SiteStats>,
    pub last_updated: SystemTime,
}

impl SystemStats {
    pub fn new() -> Self {
        Self {
            total_requests: 0,
            successful_requests: 0,
            client_errors: 0,
            server_errors: 0,
            bytes_sent: 0,
            uptime: Duration::from_secs(0),
            max_response_time: 0.0,
            avg_response_time: 0.0,
            current_rps: 0.0,
            current_bps: 0.0,
            response_times: stats::MovingAverage::new(1000),
            requests_per_second: stats::RateCounter::new(Duration::from_secs(60)),
            bytes_per_second: stats::RateCounter::new(Duration::from_secs(60)),
            site_stats: HashMap::new(),
            last_updated: SystemTime::now(),
        }
    }

    /// 清理旧数据
    pub fn cleanup_old_data(&mut self) {
        self.response_times.cleanup();
        self.requests_per_second.cleanup();
        self.bytes_per_second.cleanup();
        self.last_updated = SystemTime::now();
    }

    /// 计算成功率
    pub fn success_rate(&self) -> f64 {
        if self.total_requests == 0 {
            0.0
        } else {
            self.successful_requests as f64 / self.total_requests as f64 * 100.0
        }
    }

    /// 计算错误率
    pub fn error_rate(&self) -> f64 {
        if self.total_requests == 0 {
            0.0
        } else {
            (self.client_errors + self.server_errors) as f64 / self.total_requests as f64 * 100.0
        }
    }
}

/// 站点统计信息
#[derive(Debug, Clone)]
pub struct SiteStats {
    pub requests: u64,
    pub errors: u64,
    pub response_time: stats::MovingAverage,
    pub last_request: SystemTime,
}

impl SiteStats {
    pub fn new() -> Self {
        Self {
            requests: 0,
            errors: 0,
            response_time: stats::MovingAverage::new(100),
            last_request: SystemTime::now(),
        }
    }

    pub fn error_rate(&self) -> f64 {
        if self.requests == 0 {
            0.0
        } else {
            self.errors as f64 / self.requests as f64 * 100.0
        }
    }
}

/// 健康状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthStatus {
    pub status: String,
    pub timestamp: u64,
    pub uptime: u64,
    pub version: String,
    pub checks: HashMap<String, HealthCheck>,
}

/// 健康检查项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthCheck {
    pub status: String,
    pub message: String,
    pub duration_ms: u64,
    pub last_check: u64,
}

/// 监控配置信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitorConfigInfo {
    pub enabled: bool,
    pub port: u16,
    pub access_log_enabled: bool,
    pub health_check_enabled: bool,
    pub metrics_enabled: bool,
    pub uptime: Duration,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_system_stats() {
        let mut stats = SystemStats::new();
        assert_eq!(stats.total_requests, 0);
        assert_eq!(stats.success_rate(), 0.0);
        assert_eq!(stats.error_rate(), 0.0);

        stats.total_requests = 100;
        stats.successful_requests = 95;
        stats.client_errors = 3;
        stats.server_errors = 2;

        assert_eq!(stats.success_rate(), 95.0);
        assert_eq!(stats.error_rate(), 5.0);
    }

    #[test]
    fn test_site_stats() {
        let mut site_stats = SiteStats::new();
        assert_eq!(site_stats.requests, 0);
        assert_eq!(site_stats.error_rate(), 0.0);

        site_stats.requests = 50;
        site_stats.errors = 5;
        assert_eq!(site_stats.error_rate(), 10.0);
    }
}

package main

import (
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"reverse-proxy/internal/config"
	"reverse-proxy/internal/logger"
	"reverse-proxy/internal/monitor"
	"reverse-proxy/internal/proxy"
)

var (
	configFile = flag.String("config", "config.json", "配置文件路径")
	version    = flag.Bool("version", false, "显示版本信息")
)

const (
	AppName    = "反向代理服务器"
	AppVersion = "1.0.0"
)

func getCurrentDir() string {
	dir, err := os.Getwd()
	if err != nil {
		return "unknown"
	}
	return dir
}

func main() {
	flag.Parse()

	fmt.Printf("=== 程序启动 === 工作目录: %s ===\n", getCurrentDir())
	fmt.Printf("=== 配置文件路径: %s ===\n", *configFile)

	if *version {
		fmt.Printf("%s v%s\n", AppName, AppVersion)
		return
	}

	// 加载配置
	cfg, err := config.Load(*configFile)
	if err != nil {
		fmt.Fprintf(os.Stderr, "加载配置失败: %v\n", err)
		os.Exit(1)
	}
	fmt.Printf("=== 配置加载成功，站点数量: %d ===\n", len(cfg.Sites))

	// 初始化日志
	if err := logger.Init(cfg.Log); err != nil {
		fmt.Fprintf(os.Stderr, "初始化日志失败: %v\n", err)
		os.Exit(1)
	}

	logger.Info("启动反向代理服务器 v", AppVersion)

	// 创建监控器
	var monitorInstance *monitor.Monitor
	var apiServer *monitor.APIServer
	if cfg.Monitor.Enabled {
		monitorInstance = monitor.NewMonitor(cfg)

		// 启动监控API服务器
		apiServer = monitor.NewAPIServer(monitorInstance, cfg.Monitor.Port, cfg.Monitor.Username, cfg.Monitor.Password)
		go func() {
			if err := apiServer.Start(); err != nil {
				logger.Error("监控API服务器启动失败:", err)
			}
		}()
	}

	// 创建Viper配置管理器
	viperOptions := config.DefaultIntegrationOptions()
	viperOptions.EnableHierarchy = true
	viperOptions.EnableBackup = true
	viperOptions.EnableValidation = true
	viperOptions.MaxBackups = 10

	viperManager, err := config.NewViperIntegration(*configFile, logger.GetLogger(), viperOptions)
	if err != nil {
		logger.Fatal("创建Viper配置管理器失败:", err)
	}
	defer viperManager.Close()

	// 使用Viper重新加载配置（确保使用最新的配置）
	cfg, err = viperManager.LoadConfig()
	if err != nil {
		logger.Fatal("使用Viper加载配置失败:", err)
	}

	// 创建代理服务器（使用Viper管理器）
	proxyServer, err := proxy.NewProxyWithViperManager(cfg, viperManager, logger.GetLogger())
	if err != nil {
		logger.Fatal("创建代理服务器失败:", err)
	}

	// 如果启用了监控，将监控器注入到代理服务器
	if monitorInstance != nil {
		proxyServer.SetMonitor(monitorInstance)
		// 将Viper配置管理器传递给监控器（如果监控器支持的话）
		if viperManager := proxyServer.GetViperManager(); viperManager != nil {
			// 这里可以扩展监控器来支持Viper管理器
			logger.Debug("Viper配置管理器已传递给监控器")
		}
	}

	// 启用Viper热重载
	if err := proxyServer.EnableViperHotReload(); err != nil {
		logger.Warn("启用Viper热重载失败:", err)
	} else {
		logger.Info("Viper热重载已启用")
	}

	// 启动代理服务器
	go func() {
		if err := proxyServer.Start(); err != nil {
			logger.Error("代理服务器启动失败:", err)
		}
	}()

	// 等待信号
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	<-sigChan

	logger.Info("正在关闭服务器...")

	// 优雅关闭
	// 关闭监控API服务器
	if apiServer != nil {
		if err := apiServer.Stop(); err != nil {
			logger.Error("关闭监控API服务器失败:", err)
		}
	}

	// 关闭监控器
	if monitorInstance != nil {
		monitorInstance.Stop()
	}

	// 关闭代理服务器
	if err := proxyServer.Shutdown(); err != nil {
		logger.Error("关闭代理服务器失败:", err)
	}

	logger.Info("服务器已关闭")
}

package api

import (
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/sirupsen/logrus"
	"reverse-proxy/internal/config"
)

// SiteManager 站点管理API封装
type SiteManager struct {
	configManager *config.ConfigManager
	logger        *logrus.Logger
}

// NewSiteManager 创建站点管理器
func NewSiteManager(configManager *config.ConfigManager, logger *logrus.Logger) *SiteManager {
	return &SiteManager{
		configManager: configManager,
		logger:        logger,
	}
}

// SiteResponse 站点响应结构
type SiteResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// CreateSite 创建站点
func (sm *SiteManager) CreateSite(w http.ResponseWriter, r *http.Request) {
	var siteConfig config.SiteConfig
	if err := json.NewDecoder(r.Body).Decode(&siteConfig); err != nil {
		sm.writeError(w, http.StatusBadRequest, "JSON解析失败: "+err.Error())
		return
	}

	if err := sm.configManager.CreateSiteFromJSONAndSave(mustMarshal(siteConfig)); err != nil {
		sm.writeError(w, http.StatusBadRequest, "创建站点失败: "+err.Error())
		return
	}

	sm.writeSuccess(w, "站点创建成功", map[string]string{
		"site_name": siteConfig.Name,
		"site_id":   siteConfig.SiteID,
	})
	sm.logger.Infof("通过API创建站点: %s (ID: %s)", siteConfig.Name, siteConfig.SiteID)
}

// UpdateSite 更新站点（支持通过name或site_id）
func (sm *SiteManager) UpdateSite(w http.ResponseWriter, r *http.Request) {
	siteName := r.URL.Query().Get("name")
	siteID := r.URL.Query().Get("site_id")

	if siteName == "" && siteID == "" {
		sm.writeError(w, http.StatusBadRequest, "站点名称或站点ID不能为空")
		return
	}

	var siteConfig config.SiteConfig
	if err := json.NewDecoder(r.Body).Decode(&siteConfig); err != nil {
		sm.writeError(w, http.StatusBadRequest, "JSON解析失败: "+err.Error())
		return
	}

	var updateErr error
	var responseData map[string]string

	if siteID != "" {
		// 优先使用 site_id 更新
		updateErr = sm.configManager.UpdateSiteFromJSONAndSaveByID(siteID, mustMarshal(siteConfig))
		responseData = map[string]string{"site_id": siteID}
		if updateErr == nil {
			sm.logger.Infof("通过API更新站点: %s", siteID)
		}
	} else {
		// 使用 name 更新（向后兼容）
		updateErr = sm.configManager.UpdateSiteFromJSONAndSave(siteName, mustMarshal(siteConfig))
		responseData = map[string]string{"site_name": siteName}
		if updateErr == nil {
			sm.logger.Infof("通过API更新站点: %s", siteName)
		}
	}

	if updateErr != nil {
		sm.writeError(w, http.StatusBadRequest, "更新站点失败: "+updateErr.Error())
		return
	}

	sm.writeSuccess(w, "站点更新成功", responseData)
}

// DeleteSite 删除站点（支持通过name或site_id）
func (sm *SiteManager) DeleteSite(w http.ResponseWriter, r *http.Request) {
	siteName := r.URL.Query().Get("name")
	siteID := r.URL.Query().Get("site_id")

	if siteName == "" && siteID == "" {
		sm.writeError(w, http.StatusBadRequest, "站点名称或站点ID不能为空")
		return
	}

	var deleteErr error
	var responseData map[string]string

	if siteID != "" {
		// 优先使用 site_id 删除
		deleteErr = sm.configManager.DeleteSiteAndSaveByID(siteID)
		responseData = map[string]string{"site_id": siteID}
		if deleteErr == nil {
			sm.logger.Infof("通过API删除站点: %s", siteID)
		}
	} else {
		// 使用 name 删除（向后兼容）
		deleteErr = sm.configManager.DeleteSiteAndSave(siteName)
		responseData = map[string]string{"site_name": siteName}
		if deleteErr == nil {
			sm.logger.Infof("通过API删除站点: %s", siteName)
		}
	}

	if deleteErr != nil {
		sm.writeError(w, http.StatusBadRequest, "删除站点失败: "+deleteErr.Error())
		return
	}

	sm.writeSuccess(w, "站点删除成功", responseData)
}

// GetSite 获取站点配置（支持通过name或site_id）
func (sm *SiteManager) GetSite(w http.ResponseWriter, r *http.Request) {
	siteName := r.URL.Query().Get("name")
	siteID := r.URL.Query().Get("site_id")

	if siteName == "" && siteID == "" {
		sm.writeError(w, http.StatusBadRequest, "站点名称或站点ID不能为空")
		return
	}

	var siteConfig *config.SiteConfig
	if siteID != "" {
		// 优先使用 site_id 查询
		siteConfig = sm.configManager.GetSiteConfigByID(siteID)
	} else {
		// 使用 name 查询（向后兼容）
		siteConfig = sm.configManager.GetSiteConfig(siteName)
	}

	if siteConfig == nil {
		identifier := siteID
		if identifier == "" {
			identifier = siteName
		}
		sm.writeError(w, http.StatusNotFound, fmt.Sprintf("站点不存在: %s", identifier))
		return
	}

	sm.writeSuccess(w, "获取站点配置成功", siteConfig)
}

// ListSites 获取所有站点列表（返回包含ID和名称的信息）
func (sm *SiteManager) ListSites(w http.ResponseWriter, r *http.Request) {
	siteInfo := sm.configManager.ListSiteInfo()
	sm.writeSuccess(w, "获取站点列表成功", map[string]interface{}{
		"sites": siteInfo,
		"count": len(siteInfo),
	})
}

// GetSiteStatus 获取站点状态（支持通过name或site_id）
func (sm *SiteManager) GetSiteStatus(w http.ResponseWriter, r *http.Request) {
	siteName := r.URL.Query().Get("name")
	siteID := r.URL.Query().Get("site_id")

	if siteName == "" && siteID == "" {
		sm.writeError(w, http.StatusBadRequest, "站点名称或站点ID不能为空")
		return
	}

	var siteConfig *config.SiteConfig
	if siteID != "" {
		// 优先使用 site_id 查询
		siteConfig = sm.configManager.GetSiteConfigByID(siteID)
	} else {
		// 使用 name 查询（向后兼容）
		siteConfig = sm.configManager.GetSiteConfig(siteName)
	}

	if siteConfig == nil {
		identifier := siteID
		if identifier == "" {
			identifier = siteName
		}
		sm.writeError(w, http.StatusNotFound, fmt.Sprintf("站点不存在: %s", identifier))
		return
	}

	status := map[string]interface{}{
		"site_id":   siteConfig.SiteID,
		"name":      siteConfig.Name,
		"domains":   siteConfig.Domains,
		"upstreams": len(siteConfig.Upstreams),
		"ssl":       siteConfig.SSL.Enabled,
		"acl":       siteConfig.ACL.Allow != nil || siteConfig.ACL.Deny != nil,
		"routes":    len(siteConfig.Routes),
	}

	sm.writeSuccess(w, "获取站点状态成功", status)
}

// BatchUpdateSites 批量更新站点
func (sm *SiteManager) BatchUpdateSites(w http.ResponseWriter, r *http.Request) {
	var batchRequest struct {
		Sites []config.SiteConfig `json:"sites"`
	}

	if err := json.NewDecoder(r.Body).Decode(&batchRequest); err != nil {
		sm.writeError(w, http.StatusBadRequest, "JSON解析失败: "+err.Error())
		return
	}

	results := make([]map[string]interface{}, 0, len(batchRequest.Sites))

	for _, siteConfig := range batchRequest.Sites {
		result := map[string]interface{}{
			"site_name": siteConfig.Name,
		}

		if err := sm.configManager.UpdateSiteFromJSONAndSave(siteConfig.Name, mustMarshal(siteConfig)); err != nil {
			result["success"] = false
			result["error"] = err.Error()
		} else {
			result["success"] = true
		}

		results = append(results, result)
	}

	sm.writeSuccess(w, "批量更新完成", map[string]interface{}{
		"results": results,
		"total":   len(results),
	})

	sm.logger.Infof("通过API批量更新 %d 个站点", len(batchRequest.Sites))
}

// RegisterRoutes 注册路由
func (sm *SiteManager) RegisterRoutes(mux *http.ServeMux) {
	mux.HandleFunc("/api/sites", sm.HandleSiteRequest)
	mux.HandleFunc("/api/sites/batch", sm.BatchUpdateSites)
	mux.HandleFunc("/api/sites/status", sm.GetSiteStatus)
}

// HandleSiteRequest 处理站点请求路由
func (sm *SiteManager) HandleSiteRequest(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case http.MethodGet:
		if r.URL.Query().Get("name") != "" || r.URL.Query().Get("site_id") != "" {
			sm.GetSite(w, r)
		} else {
			sm.ListSites(w, r)
		}
	case http.MethodPost:
		sm.CreateSite(w, r)
	case http.MethodPut:
		sm.UpdateSite(w, r)
	case http.MethodDelete:
		sm.DeleteSite(w, r)
	default:
		sm.writeError(w, http.StatusMethodNotAllowed, "不支持的HTTP方法")
	}
}

// writeSuccess 写入成功响应
func (sm *SiteManager) writeSuccess(w http.ResponseWriter, message string, data interface{}) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	response := SiteResponse{
		Success: true,
		Message: message,
		Data:    data,
	}

	json.NewEncoder(w).Encode(response)
}

// writeError 写入错误响应
func (sm *SiteManager) writeError(w http.ResponseWriter, statusCode int, message string) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	response := SiteResponse{
		Success: false,
		Message: message,
	}

	json.NewEncoder(w).Encode(response)
}

// mustMarshal JSON序列化（必须成功）
func mustMarshal(v interface{}) []byte {
	data, err := json.Marshal(v)
	if err != nil {
		panic(fmt.Sprintf("JSON序列化失败: %v", err))
	}
	return data
}

package monitor

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"net"
	"net/http"
	"net/url"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"reverse-proxy/internal/config"
	"reverse-proxy/internal/logger"
	"reverse-proxy/internal/memorycache"
)

// Stats 统计信息
type Stats struct {
	// 全局统计
	Global struct {
		TotalRequests      int64                  `json:"total_requests"`
		ErrorRequests      int64                  `json:"error_requests"`
		BytesReceived      int64                  `json:"bytes_received"`
		BytesSent          int64                  `json:"bytes_sent"`
		UpstreamReceived   int64                  `json:"upstream_received"`
		UpstreamSent       int64                  `json:"upstream_sent"`
		CurrentConnections int64                  `json:"current_connections"`
		StartTime          time.Time              `json:"start_time"`
		Uptime             time.Duration          `json:"uptime"`
		CustomStats        map[string]interface{} `json:"custom_stats,omitempty"`
	} `json:"global"`

	// 内存和GC统计
	Memory struct {
		Alloc         uint64  `json:"alloc"`           // 当前分配的内存字节数
		TotalAlloc    uint64  `json:"total_alloc"`     // 累计分配的内存字节数
		Sys           uint64  `json:"sys"`             // 从系统获取的内存字节数
		Lookups       uint64  `json:"lookups"`         // 指针查找次数
		Mallocs       uint64  `json:"mallocs"`         // 内存分配次数
		Frees         uint64  `json:"frees"`           // 内存释放次数
		HeapAlloc     uint64  `json:"heap_alloc"`      // 堆上分配的内存字节数
		HeapSys       uint64  `json:"heap_sys"`        // 从系统获取的堆内存字节数
		HeapIdle      uint64  `json:"heap_idle"`       // 空闲的堆内存字节数
		HeapInuse     uint64  `json:"heap_inuse"`      // 正在使用的堆内存字节数
		HeapReleased  uint64  `json:"heap_released"`   // 释放给系统的堆内存字节数
		HeapObjects   uint64  `json:"heap_objects"`    // 堆上的对象数量
		StackInuse    uint64  `json:"stack_inuse"`     // 栈使用的内存字节数
		StackSys      uint64  `json:"stack_sys"`       // 从系统获取的栈内存字节数
		MSpanInuse    uint64  `json:"mspan_inuse"`     // MSpan结构使用的内存字节数
		MSpanSys      uint64  `json:"mspan_sys"`       // 从系统获取的MSpan内存字节数
		MCacheInuse   uint64  `json:"mcache_inuse"`    // MCache结构使用的内存字节数
		MCacheSys     uint64  `json:"mcache_sys"`      // 从系统获取的MCache内存字节数
		BuckHashSys   uint64  `json:"buckhash_sys"`    // 分析桶哈希表使用的内存字节数
		GCSys         uint64  `json:"gc_sys"`          // GC元数据使用的内存字节数
		OtherSys      uint64  `json:"other_sys"`       // 其他系统分配的内存字节数
		NextGC        uint64  `json:"next_gc"`         // 下次GC的目标堆大小
		LastGC        uint64  `json:"last_gc"`         // 上次GC的时间戳（纳秒）
		PauseTotalNs  uint64  `json:"pause_total_ns"`  // GC暂停的总时间（纳秒）
		PauseNs       uint64  `json:"pause_ns"`        // 最近一次GC暂停时间（纳秒）
		PauseEnd      uint64  `json:"pause_end"`       // 最近一次GC结束时间戳（纳秒）
		NumGC         uint32  `json:"num_gc"`          // GC执行次数
		NumForcedGC   uint32  `json:"num_forced_gc"`   // 强制GC次数
		GCCPUFraction float64 `json:"gc_cpu_fraction"` // GC占用的CPU时间比例
		EnableGC      bool    `json:"enable_gc"`       // 是否启用GC
		DebugGC       bool    `json:"debug_gc"`        // 是否启用GC调试
	} `json:"memory"`

	// 站点统计
	Sites map[string]*SiteStats `json:"sites"`

	// 上游服务器统计
	Upstreams map[string]*UpstreamStats `json:"upstreams"`

	// 带宽统计（最近1分钟）
	Bandwidth struct {
		IncomingRate    float64 `json:"incoming_rate"`     // bytes/sec
		OutgoingRate    float64 `json:"outgoing_rate"`     // bytes/sec
		UpstreamInRate  float64 `json:"upstream_in_rate"`  // bytes/sec
		UpstreamOutRate float64 `json:"upstream_out_rate"` // bytes/sec
	} `json:"bandwidth"`

	// 缓存清理统计
	CacheCleanup map[string]interface{} `json:"cache_cleanup"`

	// 压缩统计
	Compression struct {
		Enabled            bool                       `json:"enabled"`
		TotalRequests      int64                      `json:"total_requests"`
		CompressedRequests int64                      `json:"compressed_requests"`
		CompressionRatio   float64                    `json:"compression_ratio"`
		BytesSaved         int64                      `json:"bytes_saved"`
		ByAlgorithm        map[string]*AlgorithmStats `json:"by_algorithm"`
	} `json:"compression"`

	// 内存缓存统计
	MemoryCache map[string]interface{} `json:"memory_cache"`

	// 缓存系统统计
	Cache struct {
		Enabled      bool    `json:"enabled"`
		Type         string  `json:"type"`
		TotalSize    int64   `json:"total_size"`
		MaxSize      int64   `json:"max_size"`
		UsagePercent float64 `json:"usage_percent"`
		FileCount    int64   `json:"file_count"`
		HitCount     int64   `json:"hit_count"`
		MissCount    int64   `json:"miss_count"`
		HitRate      float64 `json:"hit_rate"`
		WriteCount   int64   `json:"write_count"`
		DeleteCount  int64   `json:"delete_count"`
		ErrorCount   int64   `json:"error_count"`
	} `json:"cache"`

	mu sync.RWMutex
}

// AlgorithmStats 压缩算法统计
type AlgorithmStats struct {
	Requests           int64   `json:"requests"`
	OriginalSize       int64   `json:"original_size"`
	CompressedSize     int64   `json:"compressed_size"`
	CompressionRatio   float64 `json:"compression_ratio"`
	AvgCompressionTime int64   `json:"avg_compression_time_ns"`
}

// SiteStats 站点统计
type SiteStats struct {
	SiteID             string        `json:"site_id"` // 站点唯一标识符
	Name               string        `json:"name"`    // 站点显示名称
	TotalRequests      int64         `json:"total_requests"`
	ErrorRequests      int64         `json:"error_requests"`
	BytesReceived      int64         `json:"bytes_received"`
	BytesSent          int64         `json:"bytes_sent"`
	OutBytes           int64         `json:"out_bytes"`
	CurrentConnections int64         `json:"current_connections"`
	StartTime          time.Time     `json:"start_time"`
	Uptime             time.Duration `json:"uptime"`
}

// UpstreamStats 上游服务器统计
type UpstreamStats struct {
	Name               string        `json:"name"`
	Address            string        `json:"address"`
	Port               int           `json:"port"`
	Healthy            bool          `json:"healthy"`
	OfflineCount       int64         `json:"offline_count"`
	LastOfflineTime    time.Time     `json:"last_offline_time"`
	TotalOfflineTime   time.Duration `json:"total_offline_time"`
	ResponseTime       time.Duration `json:"response_time"`
	ResponseTimeMs     int64         `json:"response_time_ms"`
	LastCheckTime      time.Time     `json:"last_check_time"`
	SuccessCount       int64         `json:"success_count"`
	FailCount          int64         `json:"fail_count"`
	CurrentConnections int64         `json:"current_connections"`
	BytesReceived      int64         `json:"bytes_received"`
	BytesSent          int64         `json:"bytes_sent"`
}

// Monitor 监控器
type Monitor struct {
	stats           *Stats
	config          *config.Config
	upstreams       map[string]*config.UpstreamConfig
	healthInterval  time.Duration // 健康检查间隔（使用第一个上游服务器的配置）
	cacheManager    interface{}   // 缓存管理器接口
	memoryCache     interface{}   // 内存缓存管理器接口
	configWatcher   interface{}   // 配置热重载监控器接口
	configManager   interface{}   // 配置管理器接口
	proxy           interface{}   // 代理实例接口
	ctx             context.Context
	cancel          context.CancelFunc
	wg              sync.WaitGroup
}

// NewMonitor 创建监控器
func NewMonitor(cfg *config.Config) *Monitor {
	ctx, cancel := context.WithCancel(context.Background())

	m := &Monitor{
		stats:     &Stats{},
		config:    cfg,
		upstreams: make(map[string]*config.UpstreamConfig),
		ctx:       ctx,
		cancel:    cancel,
	}

	// 初始化全局统计
	m.stats.Global.StartTime = time.Now()
	m.stats.Sites = make(map[string]*SiteStats)
	m.stats.Upstreams = make(map[string]*UpstreamStats)

	// 收集所有上游服务器配置，使用address+port+https_port作为唯一key
	backendSeen := make(map[string]bool)
	firstHealthInterval := 30 * time.Second // 默认30秒

	for i := range cfg.Sites {
		site := &cfg.Sites[i]
		for j := range site.Upstreams {
			upstream := &site.Upstreams[j]
			// 使用address+port+https_port生成唯一标识
			bk := fmt.Sprintf("%s:%d:%d", upstream.Address, upstream.Port, upstream.HTTPSPort)
			if !backendSeen[bk] {
				m.upstreams[bk] = upstream
				m.stats.Upstreams[bk] = &UpstreamStats{
					Name:          upstream.Name,
					Address:       upstream.Address,
					Port:          upstream.Port,
					Healthy:       true,
					LastCheckTime: time.Now(),
				}
				backendSeen[bk] = true

				// 使用第一个遇到的上游服务器的健康检查间隔
				if len(m.upstreams) == 1 && upstream.HealthInterval > 0 {
					firstHealthInterval = upstream.HealthInterval
				}
			}
		}
	}

	// 设置健康检查间隔
	m.healthInterval = firstHealthInterval

	// 启动健康检查
	m.startHealthChecks()

	// 启动带宽统计
	m.startBandwidthStats()

	return m
}

// SetCacheManager 设置缓存管理器
func (m *Monitor) SetCacheManager(cacheManager interface{}) {
	m.cacheManager = cacheManager
}

// SetMemoryCache 设置内存缓存管理器
func (m *Monitor) SetMemoryCache(memoryCache interface{}) {
	m.memoryCache = memoryCache
}

// SetConfigWatcher 设置配置热重载监控器
func (m *Monitor) SetConfigWatcher(configWatcher interface{}) {
	m.configWatcher = configWatcher
}

// SetConfigManager 设置配置管理器
func (m *Monitor) SetConfigManager(configManager interface{}) {
	m.configManager = configManager
}

// UpdateConfig 更新监控器配置
func (m *Monitor) UpdateConfig(newConfig *config.Config) {
	m.stats.mu.Lock()
	defer m.stats.mu.Unlock()

	// 更新配置引用
	m.config = newConfig

	logger.Debug("[监控] 配置已更新，API Key: '%s'\n", newConfig.Monitor.APIKey)
}

// RecordCacheHit 记录缓存命中
func (m *Monitor) RecordCacheHit() {
	atomic.AddInt64(&m.stats.Cache.HitCount, 1)
}

// RecordCacheMiss 记录缓存未命中
func (m *Monitor) RecordCacheMiss() {
	atomic.AddInt64(&m.stats.Cache.MissCount, 1)
}

// RecordCacheWrite 记录缓存写入
func (m *Monitor) RecordCacheWrite() {
	atomic.AddInt64(&m.stats.Cache.WriteCount, 1)
}

// RecordCacheDelete 记录缓存删除
func (m *Monitor) RecordCacheDelete() {
	atomic.AddInt64(&m.stats.Cache.DeleteCount, 1)
}

// RecordCacheError 记录缓存错误
func (m *Monitor) RecordCacheError() {
	atomic.AddInt64(&m.stats.Cache.ErrorCount, 1)
}

// RecordRequest 记录请求
func (m *Monitor) RecordRequest(siteID string, status int, bytesReceived, bytesSent int64) {
	// 全局统计
	atomic.AddInt64(&m.stats.Global.TotalRequests, 1)
	if status >= 400 {
		atomic.AddInt64(&m.stats.Global.ErrorRequests, 1)
	}
	atomic.AddInt64(&m.stats.Global.BytesReceived, bytesReceived)
	atomic.AddInt64(&m.stats.Global.BytesSent, bytesSent)

	// 站点统计
	m.stats.mu.Lock()
	if siteStats, exists := m.stats.Sites[siteID]; exists {
		atomic.AddInt64(&siteStats.TotalRequests, 1)
		if status >= 400 {
			atomic.AddInt64(&siteStats.ErrorRequests, 1)
		}
		atomic.AddInt64(&siteStats.BytesReceived, bytesReceived)
		atomic.AddInt64(&siteStats.BytesSent, bytesSent)
		atomic.AddInt64(&siteStats.OutBytes, bytesSent)
	}
	m.stats.mu.Unlock()
}

// RecordUpstreamTraffic 记录上游流量
func (m *Monitor) RecordUpstreamTraffic(bytesReceived, bytesSent int64) {
	atomic.AddInt64(&m.stats.Global.UpstreamReceived, bytesReceived)
	atomic.AddInt64(&m.stats.Global.UpstreamSent, bytesSent)
}

// RecordUpstreamTrafficWithKey 记录上游流量
func (m *Monitor) RecordUpstreamTrafficWithKey(upKey string, bytesReceived, bytesSent int64) {
	atomic.AddInt64(&m.stats.Global.UpstreamReceived, bytesReceived)
	atomic.AddInt64(&m.stats.Global.UpstreamSent, bytesSent)
	m.stats.mu.Lock()
	if us, ok := m.stats.Upstreams[upKey]; ok {
		us.BytesReceived += bytesReceived
		us.BytesSent += bytesSent
		logger.Debug(fmt.Sprintf("上游流量已记录: %s, 接收: %d bytes, 发送: %d bytes, 总计接收: %d, 总计发送: %d",
			upKey, bytesReceived, bytesSent, us.BytesReceived, us.BytesSent))
	} else {
		logger.Warn(fmt.Sprintf("上游服务器key未找到: %s, 可用keys: %v", upKey, m.getUpstreamKeys()))
	}
	m.stats.mu.Unlock()
}

// getUpstreamKeys 获取所有上游服务器的key（用于调试）
func (m *Monitor) getUpstreamKeys() []string {
	keys := make([]string, 0, len(m.stats.Upstreams))
	for k := range m.stats.Upstreams {
		keys = append(keys, k)
	}
	return keys
}

// IncrementConnection 增加连接数
func (m *Monitor) IncrementConnection(siteID string) {
	newGlobalCount := atomic.AddInt64(&m.stats.Global.CurrentConnections, 1)

	m.stats.mu.Lock()
	if siteStats, exists := m.stats.Sites[siteID]; exists {
		newSiteCount := atomic.AddInt64(&siteStats.CurrentConnections, 1)
		// 记录调试信息
		if newSiteCount > 0 && newSiteCount%10 == 0 {
			// 每10个连接记录一次
			logger.Debug("[DEBUG] 站点 %s (ID: %s) 当前连接数: %d, 全局连接数: %d\n", siteStats.Name, siteID, newSiteCount, newGlobalCount)
		}
	}
	m.stats.mu.Unlock()
}

// DecrementConnection 减少连接数
func (m *Monitor) DecrementConnection(siteID string) {
	atomic.AddInt64(&m.stats.Global.CurrentConnections, -1)

	m.stats.mu.Lock()
	if siteStats, exists := m.stats.Sites[siteID]; exists {
		atomic.AddInt64(&siteStats.CurrentConnections, -1)
	}
	m.stats.mu.Unlock()
}

// AddSite 添加站点
func (m *Monitor) AddSite(siteID, siteName string) {
	m.stats.mu.Lock()
	defer m.stats.mu.Unlock()

	if _, exists := m.stats.Sites[siteID]; !exists {
		m.stats.Sites[siteID] = &SiteStats{
			SiteID:    siteID,
			Name:      siteName,
			StartTime: time.Now(),
		}
	}
}

// UpdateSiteName 更新站点显示名称（不影响统计数据）
func (m *Monitor) UpdateSiteName(siteID, newName string) {
	m.stats.mu.Lock()
	defer m.stats.mu.Unlock()

	// 如果站点存在，只更新显示名称
	if siteStats, exists := m.stats.Sites[siteID]; exists {
		oldName := siteStats.Name
		siteStats.Name = newName
		logger.Debug("[监控] 站点显示名称已更新: %s -> %s (ID: %s)\n", oldName, newName, siteID)
	} else {
		logger.Debug("[监控] 警告：尝试更新不存在的站点名称，站点ID: %s\n", siteID)
	}
}

// updateMemoryCacheStats 更新内存缓存统计信息
func (m *Monitor) updateMemoryCacheStats(stats *Stats) {
	if m.memoryCache != nil {
		// 直接使用类型断言，因为我们知道具体的类型
		if mc, ok := m.memoryCache.(*memorycache.MemoryCacheManager); ok {
			memoryCacheStats := mc.GetStats()

			// 使用JSON序列化/反序列化来转换为map
			if statsBytes, err := json.Marshal(memoryCacheStats); err == nil {
				var statsMap map[string]interface{}
				if err := json.Unmarshal(statsBytes, &statsMap); err == nil {
					stats.MemoryCache = statsMap
				} else {
					stats.MemoryCache = map[string]interface{}{
						"error": "failed to parse memory cache stats",
					}
				}
			} else {
				stats.MemoryCache = map[string]interface{}{
					"error": "failed to serialize memory cache stats",
				}
			}
		} else {
			stats.MemoryCache = map[string]interface{}{
				"error": "memory cache manager type assertion failed",
			}
		}
	} else {
		stats.MemoryCache = map[string]interface{}{
			"enabled": false,
		}
	}
}

// updateMemoryStats 更新内存和GC统计信息
func (m *Monitor) updateMemoryStats(stats *Stats) {
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	stats.Memory.Alloc = memStats.Alloc
	stats.Memory.TotalAlloc = memStats.TotalAlloc
	stats.Memory.Sys = memStats.Sys
	stats.Memory.Lookups = memStats.Lookups
	stats.Memory.Mallocs = memStats.Mallocs
	stats.Memory.Frees = memStats.Frees
	stats.Memory.HeapAlloc = memStats.HeapAlloc
	stats.Memory.HeapSys = memStats.HeapSys
	stats.Memory.HeapIdle = memStats.HeapIdle
	stats.Memory.HeapInuse = memStats.HeapInuse
	stats.Memory.HeapReleased = memStats.HeapReleased
	stats.Memory.HeapObjects = memStats.HeapObjects
	stats.Memory.StackInuse = memStats.StackInuse
	stats.Memory.StackSys = memStats.StackSys
	stats.Memory.MSpanInuse = memStats.MSpanInuse
	stats.Memory.MSpanSys = memStats.MSpanSys
	stats.Memory.MCacheInuse = memStats.MCacheInuse
	stats.Memory.MCacheSys = memStats.MCacheSys
	stats.Memory.BuckHashSys = memStats.BuckHashSys
	stats.Memory.GCSys = memStats.GCSys
	stats.Memory.OtherSys = memStats.OtherSys
	stats.Memory.NextGC = memStats.NextGC
	stats.Memory.LastGC = memStats.LastGC
	stats.Memory.PauseTotalNs = memStats.PauseTotalNs
	if len(memStats.PauseNs) > 0 {
		stats.Memory.PauseNs = memStats.PauseNs[(memStats.NumGC+255)%256]
	}
	if len(memStats.PauseEnd) > 0 {
		stats.Memory.PauseEnd = memStats.PauseEnd[(memStats.NumGC+255)%256]
	}
	stats.Memory.NumGC = memStats.NumGC
	stats.Memory.NumForcedGC = memStats.NumForcedGC
	stats.Memory.GCCPUFraction = memStats.GCCPUFraction
	stats.Memory.EnableGC = memStats.EnableGC
	stats.Memory.DebugGC = memStats.DebugGC
}

// GetStats 获取统计信息
func (m *Monitor) GetStats() *Stats {
	m.stats.mu.RLock()
	defer m.stats.mu.RUnlock()

	// 更新运行时间
	now := time.Now()
	m.stats.Global.Uptime = now.Sub(m.stats.Global.StartTime)

	for _, siteStats := range m.stats.Sites {
		siteStats.Uptime = now.Sub(siteStats.StartTime)
	}

	// 创建副本返回
	statsCopy := *m.stats
	statsCopy.Sites = make(map[string]*SiteStats)
	statsCopy.Upstreams = make(map[string]*UpstreamStats)

	for k, v := range m.stats.Sites {
		siteCopy := *v
		statsCopy.Sites[k] = &siteCopy
	}

	for k, v := range m.stats.Upstreams {
		upstreamCopy := *v
		// 新增：赋值ms字段
		upstreamCopy.ResponseTimeMs = v.ResponseTime.Milliseconds()
		statsCopy.Upstreams[k] = &upstreamCopy
	}

	// 获取缓存清理统计
	if m.cacheManager != nil {
		if cm, ok := m.cacheManager.(interface{ GetCleanupStats() map[string]interface{} }); ok {
			statsCopy.CacheCleanup = cm.GetCleanupStats()
		}
	}

	// 更新缓存统计信息
	m.updateCacheStats(&statsCopy)

	// 更新内存缓存统计信息
	m.updateMemoryCacheStats(&statsCopy)

	// 更新内存和GC统计信息
	m.updateMemoryStats(&statsCopy)

	// 更新高性能统计信息
	m.updateHighPerformanceStats(&statsCopy)

	return &statsCopy
}

// RecordCompression 记录压缩统计
func (m *Monitor) RecordCompression(algorithm string, originalSize, compressedSize int64, compressionTime time.Duration) {
	m.stats.mu.Lock()
	defer m.stats.mu.Unlock()

	// 初始化压缩统计
	if m.stats.Compression.ByAlgorithm == nil {
		m.stats.Compression.ByAlgorithm = make(map[string]*AlgorithmStats)
	}

	// 更新全局压缩统计
	atomic.AddInt64(&m.stats.Compression.TotalRequests, 1)
	if compressedSize < originalSize {
		atomic.AddInt64(&m.stats.Compression.CompressedRequests, 1)
		atomic.AddInt64(&m.stats.Compression.BytesSaved, originalSize-compressedSize)
	}

	// 更新算法统计
	if _, exists := m.stats.Compression.ByAlgorithm[algorithm]; !exists {
		m.stats.Compression.ByAlgorithm[algorithm] = &AlgorithmStats{}
	}

	algStats := m.stats.Compression.ByAlgorithm[algorithm]
	algStats.Requests++
	algStats.OriginalSize += originalSize
	algStats.CompressedSize += compressedSize

	// 计算压缩率
	if algStats.OriginalSize > 0 {
		algStats.CompressionRatio = float64(algStats.CompressedSize) / float64(algStats.OriginalSize)
	}

	// 更新平均压缩时间
	if algStats.Requests > 0 {
		algStats.AvgCompressionTime = (algStats.AvgCompressionTime*(algStats.Requests-1) + compressionTime.Nanoseconds()) / algStats.Requests
	}

	// 更新全局压缩率
	if m.stats.Compression.TotalRequests > 0 {
		m.stats.Compression.CompressionRatio = float64(m.stats.Compression.CompressedRequests) / float64(m.stats.Compression.TotalRequests)
	}
}

// updateCacheStats 更新缓存统计信息
func (m *Monitor) updateCacheStats(stats *Stats) {
	if m.cacheManager != nil {
		if cm, ok := m.cacheManager.(interface {
			GetCacheStats() map[string]interface{}
		}); ok {
			cacheStats := cm.GetCacheStats()

			// 基本信息
			if enabled, ok := cacheStats["enabled"].(bool); ok {
				stats.Cache.Enabled = enabled
			}
			if cacheType, ok := cacheStats["type"].(string); ok {
				stats.Cache.Type = cacheType
			}
			if totalSize, ok := cacheStats["total_size"].(int64); ok {
				stats.Cache.TotalSize = totalSize
			}
			if maxSize, ok := cacheStats["max_size"].(int64); ok {
				stats.Cache.MaxSize = maxSize
			}
			if fileCount, ok := cacheStats["file_count"].(int64); ok {
				stats.Cache.FileCount = fileCount
			}

			// 计算使用率
			if stats.Cache.MaxSize > 0 {
				stats.Cache.UsagePercent = float64(stats.Cache.TotalSize) / float64(stats.Cache.MaxSize) * 100
			}
		}
	}

	// 计算命中率
	totalRequests := atomic.LoadInt64(&stats.Cache.HitCount) + atomic.LoadInt64(&stats.Cache.MissCount)
	if totalRequests > 0 {
		stats.Cache.HitRate = float64(atomic.LoadInt64(&stats.Cache.HitCount)) / float64(totalRequests) * 100
	}
}

// startHealthChecks 启动健康检查
func (m *Monitor) startHealthChecks() {
	m.wg.Add(1)
	go func() {
		defer m.wg.Done()
		ticker := time.NewTicker(m.healthInterval) // 使用配置的健康检查间隔
		defer ticker.Stop()

		logger.Debugf("健康检查已启动，间隔: %v", m.healthInterval)

		for {
			select {
			case <-m.ctx.Done():
				return
			case <-ticker.C:
				m.checkUpstreamHealth()
			}
		}
	}()
}

// checkUpstreamHealth 检查上游服务器健康状态
func (m *Monitor) checkUpstreamHealth() {
	for key, upstream := range m.upstreams {
		// 修复循环变量陷阱：创建副本
		upstreamCopy := upstream
		go m.checkSingleUpstream(key, upstreamCopy)
	}
}

// checkSingleUpstream 检查单个上游服务器
func (m *Monitor) checkSingleUpstream(key string, upstream *config.UpstreamConfig) {
	start := time.Now()

	// 修正：始终通过IP地址构建健康检查URL，只从配置中获取Path
	var healthURL string
	path := "/health" // 默认路径
	scheme := "http"
	port := upstream.Port

	if upstream.HealthCheck != "" {
		if u, err := url.Parse(upstream.HealthCheck); err == nil && u.Path != "" {
			path = u.Path
		}
	}

	// 根据协议确定 scheme 和 port
	switch upstream.Protocol {
	case "https":
		scheme = "https"
		port = upstream.HTTPSPort
	case "passthrough", "auto":
		if upstream.HTTPSPort > 0 {
			scheme = "https"
			port = upstream.HTTPSPort
		}
	}

	healthURL = fmt.Sprintf("%s://%s:%d%s", scheme, upstream.Address, port, path)

	// 提取host用于SNI
	sniHost := upstream.HealthHost
	if sniHost == "" {
		// 从healthURL中提取主机名
		u, err := url.Parse(healthURL)
		if err == nil {
			sniHost = u.Hostname()
		}
	}
	client := &http.Client{
		Timeout: 10 * time.Second,
		Transport: &http.Transport{
			// 新增自定义DialContext，强制只使用IP，禁止DNS查询
			DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
				host, port, err := net.SplitHostPort(addr)
				if err != nil {
					return nil, err
				}
				ipAddr := net.ParseIP(host)
				if ipAddr == nil {
					return nil, fmt.Errorf("健康检查地址 '%s' 不是一个有效的IP地址，DNS查询被禁止", host)
				}
				// 使用解析出的IP和原始端口进行拨号
				return (&net.Dialer{Timeout: 10 * time.Second}).DialContext(ctx, network, ipAddr.String()+":"+port)
			},
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true, ServerName: sniHost},
		},
	}

	// 创建请求
	req, err := http.NewRequest("GET", healthURL, nil)
	if err != nil {
		logger.Error("创建健康检查请求失败:", err)
		return
	}
	// 设置Host头
	if sniHost != "" {
		req.Host = sniHost
	}

	// 统一健康检查请求的标识，便于在后端或代理日志中过滤
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********")
	req.Header.Set("Referer", "HealthCheck")

	// 发送健康检查请求
	resp, err := client.Do(req)
	responseTime := time.Since(start)

	// 注意：这里的stats读写需要加锁
	m.stats.mu.Lock()
	upstreamStats := m.stats.Upstreams[key]
	upstreamStats.LastCheckTime = time.Now()
	upstreamStats.ResponseTime = responseTime

	if err != nil || (resp != nil && resp.StatusCode >= 400) {
		// 健康检查失败
		if resp != nil {
			resp.Body.Close()
			if err == nil { // 如果连接成功但状态码不对
				err = fmt.Errorf("返回了非2xx状态码: %d", resp.StatusCode)
			}
		}

		atomic.AddInt64(&upstreamStats.FailCount, 1)

		if upstreamStats.Healthy {
			// 从健康变为不健康
			upstreamStats.Healthy = false
			atomic.AddInt64(&upstreamStats.OfflineCount, 1)
			upstreamStats.LastOfflineTime = time.Now()
			logger.Warn(fmt.Sprintf("上游服务器不健康: %s, 原因: %v", upstream.Address, err))

			// 通知负载均衡器标记服务器失败
			m.notifyLoadBalancerHealth(upstream, false)
		}
	} else {
		// 健康检查成功
		if resp != nil {
			resp.Body.Close()
		}
		atomic.AddInt64(&upstreamStats.SuccessCount, 1)

		if !upstreamStats.Healthy {
			// 从不健康变为健康
			upstreamStats.Healthy = true
			// 计算离线时间
			if !upstreamStats.LastOfflineTime.IsZero() {
				upstreamStats.TotalOfflineTime += time.Since(upstreamStats.LastOfflineTime)
			}
			logger.Info(fmt.Sprintf("上游服务器恢复健康: %s", upstream.Address))

			// 通知负载均衡器标记服务器恢复
			m.notifyLoadBalancerHealth(upstream, true)
		}
	}
	m.stats.mu.Unlock()
}

// notifyLoadBalancerHealth 通知负载均衡器健康状态变化
func (m *Monitor) notifyLoadBalancerHealth(upstream *config.UpstreamConfig, healthy bool) {
	// 通过代理实例获取负载均衡器并更新健康状态
	if m.proxy != nil {
		if proxy, ok := m.proxy.(interface {
			UpdateUpstreamHealth(*config.UpstreamConfig, bool)
		}); ok {
			proxy.UpdateUpstreamHealth(upstream, healthy)
		}
	}
}

// IsHealthy 检查指定地址和端口的服务器是否健康
func (m *Monitor) IsHealthy(address string, port int) bool {
	m.stats.mu.RLock()
	defer m.stats.mu.RUnlock()

	// 需要遍历所有上游服务器，因为存储的键是3段式（address:port:https_port）
	// 而查询时只有address和port信息
	for key, upstreamStats := range m.stats.Upstreams {
		// 解析键格式：address:port:https_port
		parts := strings.Split(key, ":")
		if len(parts) >= 2 {
			keyAddress := parts[0]
			if keyPort, err := strconv.Atoi(parts[1]); err == nil {
				if keyAddress == address && keyPort == port {
					return upstreamStats.Healthy
				}
			}
		}
	}

	// 如果没有找到对应的统计信息，默认认为健康
	// 这种情况可能发生在服务器刚启动，还没有进行健康检查时
	return true
}

// TriggerImmediateCheck 触发对指定上游服务器的立即健康检查
func (m *Monitor) TriggerImmediateCheck(upstream *config.UpstreamConfig) {
	if upstream == nil {
		return
	}

	// 构造上游服务器的键（使用address+port+https_port）
	bk := fmt.Sprintf("%s:%d:%d", upstream.Address, upstream.Port, upstream.HTTPSPort)

	// 检查是否存在该上游服务器的配置
	m.stats.mu.RLock()
	upstreamConfig, exists := m.upstreams[bk]
	m.stats.mu.RUnlock()

	if !exists {
		// 使用全局logger记录调试信息
		return
	}

	// 异步执行健康检查
	go func() {
		m.checkSingleUpstream(bk, upstreamConfig)
	}()
}

// startBandwidthStats 启动带宽统计
func (m *Monitor) startBandwidthStats() {
	m.wg.Add(1)
	go func() {
		defer m.wg.Done()
		ticker := time.NewTicker(1 * time.Minute) // 每分钟计算一次带宽
		defer ticker.Stop()

		var lastBytesReceived, lastBytesSent, lastUpstreamReceived, lastUpstreamSent int64
		var lastTime time.Time

		for {
			select {
			case <-m.ctx.Done():
				return
			case <-ticker.C:
				now := time.Now()
				if !lastTime.IsZero() {
					duration := now.Sub(lastTime).Seconds()

					currentBytesReceived := atomic.LoadInt64(&m.stats.Global.BytesReceived)
					currentBytesSent := atomic.LoadInt64(&m.stats.Global.BytesSent)
					currentUpstreamReceived := atomic.LoadInt64(&m.stats.Global.UpstreamReceived)
					currentUpstreamSent := atomic.LoadInt64(&m.stats.Global.UpstreamSent)

					m.stats.Bandwidth.IncomingRate = float64(currentBytesReceived-lastBytesReceived) / duration
					m.stats.Bandwidth.OutgoingRate = float64(currentBytesSent-lastBytesSent) / duration
					m.stats.Bandwidth.UpstreamInRate = float64(currentUpstreamReceived-lastUpstreamReceived) / duration
					m.stats.Bandwidth.UpstreamOutRate = float64(currentUpstreamSent-lastUpstreamSent) / duration

					lastBytesReceived = currentBytesReceived
					lastBytesSent = currentBytesSent
					lastUpstreamReceived = currentUpstreamReceived
					lastUpstreamSent = currentUpstreamSent
				}
				lastTime = now
			}
		}
	}()
}

// Stop 停止监控
func (m *Monitor) Stop() {
	m.cancel()
	m.wg.Wait()
}

// updateHighPerformanceStats 更新高性能统计信息
func (m *Monitor) updateHighPerformanceStats(stats *Stats) {
	// 检查是否有代理实例可以获取高性能统计
	if m.proxy != nil {
		// 使用类型断言获取高性能统计
		if proxy, ok := m.proxy.(interface {
			GetHighPerformanceStats() map[string]interface{}
		}); ok {
			highPerfStats := proxy.GetHighPerformanceStats()
			if len(highPerfStats) > 0 {
				// 将高性能统计添加到全局统计中
				if stats.Global.CustomStats == nil {
					stats.Global.CustomStats = make(map[string]interface{})
				}
				stats.Global.CustomStats["high_performance"] = highPerfStats
			}
		}
	}
}

// SetProxy 设置代理实例（用于获取高性能统计）
func (m *Monitor) SetProxy(proxy interface{}) {
	m.proxy = proxy
}

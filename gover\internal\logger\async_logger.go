package logger

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"runtime"
	"sync"
	"sync/atomic"
	"time"

	"reverse-proxy/internal/config"

	"github.com/sirupsen/logrus"
	"gopkg.in/natefinch/lumberjack.v2"
)

// LogEntry 日志条目
type LogEntry struct {
	Fields    logrus.Fields
	Format    string
	Timestamp time.Time
	Size      int64 // 估算的内存大小
}

// AsyncLogger 异步日志器
type AsyncLogger struct {
	config  config.AsyncLogConfig
	writers []io.Writer
	logChan chan LogEntry
	buffer  []LogEntry
	ctx     context.Context
	cancel  context.CancelFunc
	wg      sync.WaitGroup
	mu      sync.Mutex
	closed  bool

	// 统计信息
	totalLogs   int64 // 总日志数
	droppedLogs int64 // 丢弃的日志数
	memoryUsage int64 // 当前内存使用量(字节)
	maxMemory   int64 // 最大内存限制(字节)

	// 刷新定时器
	flushTimer *time.Timer
}

// NewAsyncLogger 创建异步日志器
func NewAsyncLogger(writers []io.Writer, cfg config.AsyncLogConfig) *AsyncLogger {
	ctx, cancel := context.WithCancel(context.Background())

	// 设置默认值
	if cfg.ChannelSize <= 0 {
		cfg.ChannelSize = 1000
	}
	if cfg.BatchSize <= 0 {
		cfg.BatchSize = 100
	}
	if cfg.FlushInterval <= 0 {
		cfg.FlushInterval = 500 * time.Millisecond
	}
	if cfg.MaxMemoryMB <= 0 {
		cfg.MaxMemoryMB = 50
	}
	if cfg.DropPolicy == "" {
		cfg.DropPolicy = "drop_oldest"
	}

	logger := &AsyncLogger{
		config:    cfg,
		writers:   writers,
		logChan:   make(chan LogEntry, cfg.ChannelSize),
		buffer:    make([]LogEntry, 0, cfg.BatchSize),
		ctx:       ctx,
		cancel:    cancel,
		maxMemory: int64(cfg.MaxMemoryMB) * 1024 * 1024, // 转换为字节
	}

	// 启动异步处理协程
	logger.wg.Add(1)
	go logger.processLogs()

	return logger
}

// Log 异步记录日志
func (al *AsyncLogger) Log(fields logrus.Fields, format string) {
	if al.closed {
		return
	}

	// 估算日志条目大小
	size := al.estimateLogSize(fields, format)

	entry := LogEntry{
		Fields:    fields,
		Format:    format,
		Timestamp: time.Now(),
		Size:      size,
	}

	// 检查内存使用量
	if atomic.LoadInt64(&al.memoryUsage)+size > al.maxMemory {
		al.handleMemoryLimit(entry)
		return
	}

	// 尝试非阻塞发送到通道
	select {
	case al.logChan <- entry:
		atomic.AddInt64(&al.totalLogs, 1)
		atomic.AddInt64(&al.memoryUsage, size)
	default:
		// 通道满了，根据策略处理
		al.handleChannelFull(entry)
	}
}

// processLogs 处理日志的主循环
func (al *AsyncLogger) processLogs() {
	defer al.wg.Done()

	// 设置定时刷新
	al.flushTimer = time.NewTimer(al.config.FlushInterval)
	defer al.flushTimer.Stop()

	for {
		select {
		case entry := <-al.logChan:
			al.addToBuffer(entry)

		case <-al.flushTimer.C:
			al.flushBuffer()
			al.resetFlushTimer()

		case <-al.ctx.Done():
			// 关闭时刷新剩余日志
			al.drainChannel()
			al.flushBuffer()
			return
		}
	}
}

// addToBuffer 添加日志到缓冲区
func (al *AsyncLogger) addToBuffer(entry LogEntry) {
	al.mu.Lock()
	defer al.mu.Unlock()

	al.buffer = append(al.buffer, entry)

	// 如果缓冲区满了，立即刷新
	if len(al.buffer) >= al.config.BatchSize {
		al.flushBufferUnsafe()
		al.resetFlushTimerUnsafe()
	}
}

// flushBuffer 刷新缓冲区（线程安全）
func (al *AsyncLogger) flushBuffer() {
	al.mu.Lock()
	defer al.mu.Unlock()
	al.flushBufferUnsafe()
}

// flushBufferUnsafe 刷新缓冲区（非线程安全，需要在锁内调用）
func (al *AsyncLogger) flushBufferUnsafe() {
	if len(al.buffer) == 0 {
		return
	}

	// 批量写入所有缓冲的日志
	var totalSize int64
	for _, entry := range al.buffer {
		al.writeLogEntry(entry)
		totalSize += entry.Size
	}

	// 更新内存使用量
	atomic.AddInt64(&al.memoryUsage, -totalSize)

	// 清空缓冲区
	al.buffer = al.buffer[:0]
}

// writeLogEntry 写入单条日志
func (al *AsyncLogger) writeLogEntry(entry LogEntry) {
	formatter, exists := formatters[entry.Format]
	if !exists {
		formatter = formatters["combined"]
	}

	logEntry := logrus.NewEntry(logger)
	logEntry.Data = entry.Fields
	logEntry.Time = entry.Timestamp
	logEntry.Level = logrus.InfoLevel

	if formatted, err := formatter.Format(logEntry); err == nil {
		for _, writer := range al.writers {
			writer.Write(formatted)
		}
	}
}

// estimateLogSize 估算日志条目的内存大小
func (al *AsyncLogger) estimateLogSize(fields logrus.Fields, format string) int64 {
	size := int64(len(format) + 100) // 基础开销

	for key, value := range fields {
		size += int64(len(key))
		if str, ok := value.(string); ok {
			size += int64(len(str))
		} else {
			size += 50 // 其他类型的估算大小
		}
	}

	return size
}

// handleMemoryLimit 处理内存限制
func (al *AsyncLogger) handleMemoryLimit(entry LogEntry) {
	switch al.config.DropPolicy {
	case "drop_newest":
		// 丢弃新日志
		atomic.AddInt64(&al.droppedLogs, 1)

	case "drop_oldest":
		// 尝试清理一些旧日志
		al.forceFlush()
		// 如果还是超限，丢弃新日志
		if atomic.LoadInt64(&al.memoryUsage)+entry.Size > al.maxMemory {
			atomic.AddInt64(&al.droppedLogs, 1)
		} else {
			al.Log(entry.Fields, entry.Format) // 重试
		}

	case "block":
		// 阻塞等待（同步写入）
		al.writeLogEntry(entry)

	default:
		atomic.AddInt64(&al.droppedLogs, 1)
	}
}

// handleChannelFull 处理通道满的情况
func (al *AsyncLogger) handleChannelFull(entry LogEntry) {
	switch al.config.DropPolicy {
	case "drop_newest":
		atomic.AddInt64(&al.droppedLogs, 1)

	case "drop_oldest":
		// 尝试丢弃一个旧的日志
		select {
		case oldEntry := <-al.logChan:
			atomic.AddInt64(&al.memoryUsage, -oldEntry.Size)
			atomic.AddInt64(&al.droppedLogs, 1)
			// 尝试添加新日志
			select {
			case al.logChan <- entry:
				atomic.AddInt64(&al.totalLogs, 1)
				atomic.AddInt64(&al.memoryUsage, entry.Size)
			default:
				atomic.AddInt64(&al.droppedLogs, 1)
			}
		default:
			atomic.AddInt64(&al.droppedLogs, 1)
		}

	case "block":
		// 阻塞写入（降级为同步）
		al.writeLogEntry(entry)

	default:
		atomic.AddInt64(&al.droppedLogs, 1)
	}
}

// forceFlush 强制刷新
func (al *AsyncLogger) forceFlush() {
	select {
	case <-al.ctx.Done():
		return
	default:
		al.flushBuffer()
	}
}

// drainChannel 排空通道中的剩余日志
func (al *AsyncLogger) drainChannel() {
	for {
		select {
		case entry := <-al.logChan:
			al.addToBuffer(entry)
		default:
			return
		}
	}
}

// resetFlushTimer 重置刷新定时器
func (al *AsyncLogger) resetFlushTimer() {
	al.mu.Lock()
	defer al.mu.Unlock()
	al.resetFlushTimerUnsafe()
}

// resetFlushTimerUnsafe 重置刷新定时器（非线程安全）
func (al *AsyncLogger) resetFlushTimerUnsafe() {
	if !al.flushTimer.Stop() {
		select {
		case <-al.flushTimer.C:
		default:
		}
	}
	al.flushTimer.Reset(al.config.FlushInterval)
}

// GetStats 获取统计信息
func (al *AsyncLogger) GetStats() map[string]interface{} {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	return map[string]interface{}{
		"total_logs":   atomic.LoadInt64(&al.totalLogs),
		"dropped_logs": atomic.LoadInt64(&al.droppedLogs),
		"memory_usage": atomic.LoadInt64(&al.memoryUsage),
		"max_memory":   al.maxMemory,
		"channel_len":  len(al.logChan),
		"channel_cap":  cap(al.logChan),
		"buffer_len":   len(al.buffer),
		"buffer_cap":   cap(al.buffer),
		"go_memory_mb": m.Alloc / 1024 / 1024,
	}
}

// Close 关闭异步日志器
func (al *AsyncLogger) Close() error {
	al.mu.Lock()
	if al.closed {
		al.mu.Unlock()
		return nil
	}
	al.closed = true
	al.mu.Unlock()

	// 取消上下文，停止处理协程
	al.cancel()

	// 等待处理协程结束
	al.wg.Wait()

	return nil
}

// AsyncMultiLogger 异步多目标日志器
type AsyncMultiLogger struct {
	asyncLogger *AsyncLogger
	syncLogger  *MultiLogger // 降级时使用的同步日志器
	config      config.AsyncLogConfig
}

// NewAsyncMultiLogger 创建异步多目标日志器
func NewAsyncMultiLogger(targets []config.LogTargetConfig, vars map[string]string, cfg config.AsyncLogConfig) *AsyncMultiLogger {
	// 创建writers
	var writers []io.Writer
	for _, t := range targets {
		switch t.Type {
		case "file":
			fname := replaceLogVars(t.Filename, vars)
			_ = os.MkdirAll(filepath.Dir(fname), 0755)
			writers = append(writers, &lumberjack.Logger{
				Filename:   fname,
				MaxSize:    100,
				MaxBackups: 10,
				MaxAge:     30,
				Compress:   true,
			})
		case "syslog":
			network := t.Network
			if network == "" {
				network = "udp"
			}
			syslogWriter, err := NewSyslogWriter(network, t.Address)
			if err == nil {
				writers = append(writers, syslogWriter)
			} else {
				fmt.Fprintf(os.Stderr, "[日志] syslog 连接失败: %v\n", err)
			}
		case "console":
			writers = append(writers, os.Stdout)
		}
	}

	aml := &AsyncMultiLogger{
		config: cfg,
	}

	if cfg.Enabled {
		aml.asyncLogger = NewAsyncLogger(writers, cfg)
	} else {
		// 如果未启用异步，使用原有的同步日志器
		aml.syncLogger = &MultiLogger{
			writers: writers,
			format:  "combined",
		}
	}

	return aml
}

// Log 记录日志
func (aml *AsyncMultiLogger) Log(fields logrus.Fields, format string) {
	if aml.config.Enabled && aml.asyncLogger != nil {
		aml.asyncLogger.Log(fields, format)
	} else if aml.syncLogger != nil {
		aml.syncLogger.Log(fields, format)
	}
}

// GetStats 获取统计信息
func (aml *AsyncMultiLogger) GetStats() map[string]interface{} {
	if aml.config.Enabled && aml.asyncLogger != nil {
		return aml.asyncLogger.GetStats()
	}
	return map[string]interface{}{
		"async_enabled": false,
		"mode":          "sync",
	}
}

// Close 关闭日志器
func (aml *AsyncMultiLogger) Close() error {
	if aml.asyncLogger != nil {
		return aml.asyncLogger.Close()
	}
	return nil
}

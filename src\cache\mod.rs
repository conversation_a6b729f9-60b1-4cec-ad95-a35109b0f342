use anyhow::Result;
use async_trait::async_trait;
use bytes::Bytes;
use std::path::PathBuf;
use std::time::{Duration, SystemTime};
use tokio::sync::RwLock;
use std::sync::Arc;

pub mod file_cache;
pub mod memory_cache;
pub mod redis_cache;
pub mod hybrid_cache;
pub mod manager;

pub use manager::CacheManager;

/// 缓存条目
#[derive(Debug, Clone)]
pub struct CacheEntry {
    /// 缓存的数据
    pub data: Bytes,
    /// 内容类型
    pub content_type: String,
    /// HTTP状态码
    pub status_code: u16,
    /// 响应头
    pub headers: Vec<(String, String)>,
    /// 创建时间
    pub created_at: SystemTime,
    /// 过期时间
    pub expires_at: SystemTime,
    /// 访问次数
    pub access_count: u64,
    /// 最后访问时间
    pub last_accessed: SystemTime,
    /// 数据大小
    pub size: usize,
    /// ETag
    pub etag: Option<String>,
    /// Last-Modified
    pub last_modified: Option<String>,
}

impl CacheEntry {
    /// 创建新的缓存条目
    pub fn new(
        data: Bytes,
        content_type: String,
        status_code: u16,
        headers: Vec<(String, String)>,
        ttl: Duration,
    ) -> Self {
        let now = SystemTime::now();
        let size = data.len();
        
        Self {
            data,
            content_type,
            status_code,
            headers,
            created_at: now,
            expires_at: now + ttl,
            access_count: 0,
            last_accessed: now,
            size,
            etag: None,
            last_modified: None,
        }
    }
    
    /// 检查是否过期
    pub fn is_expired(&self) -> bool {
        SystemTime::now() > self.expires_at
    }
    
    /// 更新访问信息
    pub fn update_access(&mut self) {
        self.access_count += 1;
        self.last_accessed = SystemTime::now();
    }
    
    /// 计算缓存分数（用于LRU等算法）
    pub fn calculate_score(&self) -> f64 {
        let age = SystemTime::now()
            .duration_since(self.last_accessed)
            .unwrap_or_default()
            .as_secs() as f64;
        
        let frequency = self.access_count as f64;
        let size_penalty = (self.size as f64).log10();
        
        // 分数 = 访问频率 / (时间衰减 + 大小惩罚)
        frequency / (age / 3600.0 + size_penalty)
    }
}

/// 缓存统计信息
#[derive(Debug, Clone, Default)]
pub struct CacheStats {
    /// 总请求数
    pub total_requests: u64,
    /// 缓存命中数
    pub hits: u64,
    /// 缓存未命中数
    pub misses: u64,
    /// 缓存写入数
    pub writes: u64,
    /// 缓存删除数
    pub deletes: u64,
    /// 缓存大小（字节）
    pub size_bytes: u64,
    /// 缓存条目数
    pub entry_count: u64,
    /// 过期清理次数
    pub cleanup_count: u64,
}

impl CacheStats {
    /// 计算命中率
    pub fn hit_rate(&self) -> f64 {
        if self.total_requests == 0 {
            0.0
        } else {
            self.hits as f64 / self.total_requests as f64
        }
    }
    
    /// 记录命中
    pub fn record_hit(&mut self) {
        self.total_requests += 1;
        self.hits += 1;
    }
    
    /// 记录未命中
    pub fn record_miss(&mut self) {
        self.total_requests += 1;
        self.misses += 1;
    }
    
    /// 记录写入
    pub fn record_write(&mut self, size: usize) {
        self.writes += 1;
        self.size_bytes += size as u64;
        self.entry_count += 1;
    }
    
    /// 记录删除
    pub fn record_delete(&mut self, size: usize) {
        self.deletes += 1;
        self.size_bytes = self.size_bytes.saturating_sub(size as u64);
        self.entry_count = self.entry_count.saturating_sub(1);
    }
}

/// 缓存后端trait
#[async_trait]
pub trait CacheBackend: Send + Sync {
    /// 获取缓存条目
    async fn get(&self, key: &str) -> Result<Option<CacheEntry>>;
    
    /// 设置缓存条目
    async fn set(&self, key: &str, entry: CacheEntry) -> Result<()>;
    
    /// 删除缓存条目
    async fn delete(&self, key: &str) -> Result<bool>;
    
    /// 检查缓存条目是否存在
    async fn exists(&self, key: &str) -> Result<bool>;
    
    /// 清空所有缓存
    async fn clear(&self) -> Result<()>;
    
    /// 获取缓存统计信息
    async fn stats(&self) -> Result<CacheStats>;
    
    /// 清理过期条目
    async fn cleanup_expired(&self) -> Result<u64>;
    
    /// 获取所有键
    async fn keys(&self) -> Result<Vec<String>>;
    
    /// 获取缓存大小
    async fn size(&self) -> Result<u64>;
}

/// 缓存键生成器
pub struct CacheKeyGenerator;

impl CacheKeyGenerator {
    /// 生成缓存键
    pub fn generate_key(
        site_id: &str,
        method: &str,
        uri: &str,
        query: Option<&str>,
        headers: Option<&[(&str, &str)]>,
    ) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        
        let mut hasher = DefaultHasher::new();
        
        // 基础信息
        site_id.hash(&mut hasher);
        method.hash(&mut hasher);
        uri.hash(&mut hasher);
        
        // 查询参数
        if let Some(q) = query {
            q.hash(&mut hasher);
        }
        
        // 相关头部
        if let Some(hdrs) = headers {
            for (name, value) in hdrs {
                name.hash(&mut hasher);
                value.hash(&mut hasher);
            }
        }
        
        let hash = hasher.finish();
        format!("{}:{}:{:x}", site_id, method.to_lowercase(), hash)
    }
    
    /// 生成站点前缀
    pub fn site_prefix(site_id: &str) -> String {
        format!("{}:", site_id)
    }
    
    /// 从键中提取站点ID
    pub fn extract_site_id(key: &str) -> Option<&str> {
        key.split(':').next()
    }
}

/// 缓存配置
#[derive(Debug, Clone)]
pub struct CacheConfig {
    /// 缓存类型
    pub cache_type: CacheType,
    /// 文件缓存路径
    pub file_path: Option<PathBuf>,
    /// Redis配置
    pub redis_config: Option<RedisConfig>,
    /// 内存缓存配置
    pub memory_config: MemoryCacheConfig,
    /// 默认TTL
    pub default_ttl: Duration,
    /// 最大缓存大小
    pub max_size: u64,
    /// 清理间隔
    pub cleanup_interval: Duration,
    /// 是否启用异步清理
    pub async_cleanup: bool,
}

/// 缓存类型
#[derive(Debug, Clone, PartialEq)]
pub enum CacheType {
    Memory,
    File,
    Redis,
    Hybrid, // 内存 + 文件
}

/// Redis配置
#[derive(Debug, Clone)]
pub struct RedisConfig {
    pub url: String,
    pub pool_size: usize,
    pub connection_timeout: Duration,
    pub command_timeout: Duration,
    pub database: u8,
    pub prefix: String,
}

/// 内存缓存配置
#[derive(Debug, Clone)]
pub struct MemoryCacheConfig {
    pub max_size: u64,
    pub max_entries: usize,
    pub ttl: Duration,
    pub cleanup_interval: Duration,
}

/// 缓存操作结果
#[derive(Debug)]
pub enum CacheResult {
    Hit(CacheEntry),
    Miss,
    Error(anyhow::Error),
}

impl CacheResult {
    pub fn is_hit(&self) -> bool {
        matches!(self, CacheResult::Hit(_))
    }
    
    pub fn is_miss(&self) -> bool {
        matches!(self, CacheResult::Miss)
    }
    
    pub fn is_error(&self) -> bool {
        matches!(self, CacheResult::Error(_))
    }
    
    pub fn into_entry(self) -> Option<CacheEntry> {
        match self {
            CacheResult::Hit(entry) => Some(entry),
            _ => None,
        }
    }
}

/// 缓存事件
#[derive(Debug, Clone)]
pub enum CacheEvent {
    Hit { key: String, size: usize },
    Miss { key: String },
    Set { key: String, size: usize, ttl: Duration },
    Delete { key: String },
    Expired { key: String, count: u64 },
    Cleanup { removed: u64, duration: Duration },
}

/// 缓存事件监听器
#[async_trait]
pub trait CacheEventListener: Send + Sync {
    async fn on_event(&self, event: CacheEvent);
}

/// 默认的缓存事件监听器（记录日志）
pub struct LoggingCacheEventListener;

#[async_trait]
impl CacheEventListener for LoggingCacheEventListener {
    async fn on_event(&self, event: CacheEvent) {
        match event {
            CacheEvent::Hit { key, size } => {
                tracing::debug!("Cache hit: key={}, size={}", key, size);
            }
            CacheEvent::Miss { key } => {
                tracing::debug!("Cache miss: key={}", key);
            }
            CacheEvent::Set { key, size, ttl } => {
                tracing::debug!("Cache set: key={}, size={}, ttl={:?}", key, size, ttl);
            }
            CacheEvent::Delete { key } => {
                tracing::debug!("Cache delete: key={}", key);
            }
            CacheEvent::Expired { key, count } => {
                tracing::debug!("Cache expired: key={}, count={}", key, count);
            }
            CacheEvent::Cleanup { removed, duration } => {
                tracing::info!("Cache cleanup: removed={}, duration={:?}", removed, duration);
            }
        }
    }
}

/// 缓存工具函数
pub mod utils {
    use super::*;
    
    /// 解析大小字符串 (如 "1KB", "2MB", "1GB")
    pub fn parse_size(size_str: &str) -> Result<u64> {
        let size_str = size_str.trim().to_uppercase();
        
        if let Some(captures) = regex::Regex::new(r"^(\d+(?:\.\d+)?)(B|KB|MB|GB|TB)$")
            .unwrap()
            .captures(&size_str)
        {
            let number: f64 = captures[1].parse()?;
            let unit = &captures[2];
            
            let multiplier = match unit {
                "B" => 1,
                "KB" => 1024,
                "MB" => 1024 * 1024,
                "GB" => 1024 * 1024 * 1024,
                "TB" => 1024_u64.pow(4),
                _ => return Err(anyhow::anyhow!("Invalid size unit: {}", unit)),
            };
            
            Ok((number * multiplier as f64) as u64)
        } else {
            Err(anyhow::anyhow!("Invalid size format: {}", size_str))
        }
    }
    
    /// 格式化大小为人类可读格式
    pub fn format_size(size: u64) -> String {
        const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
        const THRESHOLD: u64 = 1024;
        
        if size == 0 {
            return "0 B".to_string();
        }
        
        let mut size = size as f64;
        let mut unit_index = 0;
        
        while size >= THRESHOLD as f64 && unit_index < UNITS.len() - 1 {
            size /= THRESHOLD as f64;
            unit_index += 1;
        }
        
        if unit_index == 0 {
            format!("{} {}", size as u64, UNITS[unit_index])
        } else {
            format!("{:.1} {}", size, UNITS[unit_index])
        }
    }
    
    /// 检查内容类型是否应该被缓存
    pub fn should_cache_content_type(content_type: &str, allowed_types: &[String]) -> bool {
        for allowed in allowed_types {
            if allowed.ends_with('*') {
                let prefix = &allowed[..allowed.len() - 1];
                if content_type.starts_with(prefix) {
                    return true;
                }
            } else if content_type == allowed {
                return true;
            }
        }
        false
    }
    
    /// 生成ETag
    pub fn generate_etag(data: &[u8]) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        
        let mut hasher = DefaultHasher::new();
        data.hash(&mut hasher);
        let hash = hasher.finish();
        
        format!("\"{}\"", hash)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_cache_key_generation() {
        let key1 = CacheKeyGenerator::generate_key(
            "site1",
            "GET",
            "/path",
            Some("param=value"),
            Some(&[("accept", "text/html")]),
        );
        
        let key2 = CacheKeyGenerator::generate_key(
            "site1",
            "GET",
            "/path",
            Some("param=value"),
            Some(&[("accept", "text/html")]),
        );
        
        let key3 = CacheKeyGenerator::generate_key(
            "site1",
            "GET",
            "/path",
            Some("param=different"),
            Some(&[("accept", "text/html")]),
        );
        
        assert_eq!(key1, key2);
        assert_ne!(key1, key3);
    }
    
    #[test]
    fn test_size_parsing() {
        assert_eq!(utils::parse_size("1KB").unwrap(), 1024);
        assert_eq!(utils::parse_size("2MB").unwrap(), 2 * 1024 * 1024);
        assert_eq!(utils::parse_size("1.5GB").unwrap(), (1.5 * 1024.0 * 1024.0 * 1024.0) as u64);
    }
    
    #[test]
    fn test_size_formatting() {
        assert_eq!(utils::format_size(1024), "1.0 KB");
        assert_eq!(utils::format_size(1536), "1.5 KB");
        assert_eq!(utils::format_size(1024 * 1024), "1.0 MB");
    }
    
    #[test]
    fn test_cache_entry_expiration() {
        let entry = CacheEntry::new(
            Bytes::from("test"),
            "text/plain".to_string(),
            200,
            vec![],
            Duration::from_secs(1),
        );
        
        assert!(!entry.is_expired());
        
        // 模拟时间过去
        std::thread::sleep(Duration::from_millis(1100));
        assert!(entry.is_expired());
    }
}

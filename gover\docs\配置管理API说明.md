# 配置管理API说明

## 📖 概述

配置管理API提供了通过HTTP接口在线修改和重载配置的功能，支持部分更新和批量更新，所有更改会自动保存到配置文件并触发热重载。

## 🔐 认证和授权

### 三层安全验证

1. **基础认证**：HTTP Basic Auth（用户名/密码）
2. **API Key认证**：X-API-Key头或api_key参数
3. **IP访问控制**：基于ACL的IP白名单/黑名单

### 配置示例
```json
{
  "monitor": {
    "enabled": true,
    "port": 8080,
    "username": "zdw",
    "password": "z7758521",
    "api_key": "config-api-key-2024",
    "acl": {
      "allowed_ips": ["127.0.0.1", "::1", "***********/16", "10.0.0.0/8"],
      "denied_ips": []
    }
  }
}
```

## 🚀 API端点

### 1. 获取配置 - GET /config

#### 获取完整配置
```bash
curl -u zdw:z7758521 \
     -H "X-API-Key: config-api-key-2024" \
     http://localhost:8080/config
```

#### 获取特定配置路径
```bash
curl -u zdw:z7758521 \
     -H "X-API-Key: config-api-key-2024" \
     "http://localhost:8080/config?path=monitor.port"
```

#### 响应格式
```json
{
  "success": true,
  "message": "Configuration retrieved successfully",
  "data": {
    "monitor": {
      "enabled": true,
      "port": 8080,
      "username": "zdw",
      "password": "z7758521"
    }
  }
}
```

### 2. 更新配置 - POST /config/update

#### 单项配置更新
```bash
curl -u zdw:z7758521 \
     -H "X-API-Key: config-api-key-2024" \
     -H "Content-Type: application/json" \
     -X POST \
     -d '{
       "path": "monitor.port",
       "value": 8081
     }' \
     http://localhost:8080/config/update
```

#### 复杂配置更新
```bash
curl -u zdw:z7758521 \
     -H "X-API-Key: config-api-key-2024" \
     -H "Content-Type: application/json" \
     -X POST \
     -d '{
       "path": "memory_cache.enabled",
       "value": true
     }' \
     http://localhost:8080/config/update
```

### 3. 批量更新配置 - POST /config/batch

```bash
curl -u zdw:z7758521 \
     -H "X-API-Key: config-api-key-2024" \
     -H "Content-Type: application/json" \
     -X POST \
     -d '{
       "updates": [
         {
           "path": "monitor.port",
           "value": 8081
         },
         {
           "path": "compression.enabled",
           "value": true
         },
         {
           "path": "memory_cache.global_memory_limit",
           "value": "512MB"
         }
       ]
     }' \
     http://localhost:8080/config/batch
```

### 4. 重载配置 - POST /config/reload

```bash
curl -u zdw:z7758521 \
     -H "X-API-Key: config-api-key-2024" \
     -X POST \
     http://localhost:8080/config/reload
```

## 🎯 配置路径规则

### 路径格式
使用点号分隔的路径格式：`section.subsection.field`

### 支持的配置路径

#### 服务器配置
- `server.port` - 服务器端口
- `server.host` - 服务器主机

#### 监控配置
- `monitor.enabled` - 是否启用监控
- `monitor.port` - 监控端口
- `monitor.username` - 监控用户名
- `monitor.password` - 监控密码
- `monitor.api_key` - API密钥

#### 压缩配置
- `compression.enabled` - 是否启用压缩
- `compression.level` - Gzip压缩级别
- `compression.brotli_quality` - Brotli质量等级

#### 内存缓存配置
- `memory_cache.enabled` - 是否启用内存缓存
- `memory_cache.global_memory_limit` - 全局内存限制
- `memory_cache.min_access_count` - 最小访问次数
- `memory_cache.score_threshold` - 评分阈值

### 数组和对象路径
```bash
# 访问数组元素（暂不支持，计划中）
sites.0.domains

# 访问嵌套对象
monitor.acl.allowed_ips
```

## 📝 请求和响应格式

### 请求格式

#### 单项更新请求
```json
{
  "path": "配置路径",
  "value": "新值"
}
```

#### 批量更新请求
```json
{
  "updates": [
    {
      "path": "配置路径1",
      "value": "新值1"
    },
    {
      "path": "配置路径2", 
      "value": "新值2"
    }
  ]
}
```

### 响应格式
```json
{
  "success": true|false,
  "message": "操作结果描述",
  "data": "返回的数据（可选）"
}
```

### HTTP状态码
- `200` - 操作成功
- `400` - 请求格式错误或配置验证失败
- `401` - 认证失败
- `403` - 权限不足（API Key或ACL验证失败）
- `405` - 方法不允许
- `500` - 服务器内部错误

## 🔧 使用示例

### 1. 修改监控端口
```bash
# 将监控端口从8080改为8081
curl -u zdw:z7758521 \
     -H "X-API-Key: config-api-key-2024" \
     -H "Content-Type: application/json" \
     -X POST \
     -d '{"path": "monitor.port", "value": 8081}' \
     http://localhost:8080/config/update

# 重载配置使更改生效
curl -u zdw:z7758521 \
     -H "X-API-Key: config-api-key-2024" \
     -X POST \
     http://localhost:8080/config/reload
```

### 2. 启用内存缓存
```bash
curl -u zdw:z7758521 \
     -H "X-API-Key: config-api-key-2024" \
     -H "Content-Type: application/json" \
     -X POST \
     -d '{
       "updates": [
         {"path": "memory_cache.enabled", "value": true},
         {"path": "memory_cache.global_memory_limit", "value": "512MB"},
         {"path": "memory_cache.min_access_count", "value": 3}
       ]
     }' \
     http://localhost:8080/config/batch
```

### 3. 调整压缩设置
```bash
curl -u zdw:z7758521 \
     -H "X-API-Key: config-api-key-2024" \
     -H "Content-Type: application/json" \
     -X POST \
     -d '{
       "updates": [
         {"path": "compression.enabled", "value": true},
         {"path": "compression.level", "value": 6},
         {"path": "compression.brotli_quality", "value": 8}
       ]
     }' \
     http://localhost:8080/config/batch
```

## 🛡️ 安全注意事项

### 1. API Key管理
- 使用强随机字符串作为API Key
- 定期轮换API Key
- 不要在日志中记录API Key

### 2. IP访问控制
```json
{
  "acl": {
    "allowed_ips": [
      "127.0.0.1",           // 本地访问
      "***********/24",      // 内网段
      "**********"           // 特定管理IP
    ],
    "denied_ips": [
      "************"         // 拒绝特定IP
    ]
  }
}
```

### 3. 网络安全
- 建议在内网环境使用
- 如需公网访问，请使用HTTPS
- 配置防火墙限制访问

## 🔍 故障排查

### 常见错误

#### 1. 认证失败 (401)
```json
{
  "success": false,
  "message": "Unauthorized"
}
```
**解决方案**：检查用户名密码是否正确

#### 2. API Key验证失败 (403)
```json
{
  "success": false,
  "message": "Invalid or missing API key"
}
```
**解决方案**：检查X-API-Key头是否正确

#### 3. IP访问被拒绝 (403)
```json
{
  "success": false,
  "message": "Access denied by ACL"
}
```
**解决方案**：检查客户端IP是否在允许列表中

#### 4. 配置路径错误 (400)
```json
{
  "success": false,
  "message": "Failed to update config: unsupported field: invalid_field"
}
```
**解决方案**：检查配置路径是否正确

### 调试技巧

#### 1. 验证API Key
```bash
curl -u zdw:z7758521 \
     -H "X-API-Key: config-api-key-2024" \
     http://localhost:8080/config?path=monitor.enabled
```

#### 2. 检查当前配置
```bash
curl -u zdw:z7758521 \
     -H "X-API-Key: config-api-key-2024" \
     http://localhost:8080/config | jq .
```

#### 3. 测试重载功能
```bash
curl -u zdw:z7758521 \
     -H "X-API-Key: config-api-key-2024" \
     -X POST \
     http://localhost:8080/config/reload
```

## 📈 最佳实践

### 1. 配置变更流程
1. 获取当前配置进行备份
2. 使用API更新配置
3. 验证配置更新是否成功
4. 触发配置重载
5. 验证服务运行状态

### 2. 批量更新策略
- 相关配置项一起更新
- 避免频繁的单项更新
- 更新后统一重载

### 3. 监控和日志
- 监控配置更新操作
- 记录配置变更历史
- 设置配置变更告警

配置管理API为反向代理服务器提供了强大的在线配置管理能力，通过安全的认证机制和灵活的更新方式，实现了配置的动态管理和热重载！🚀

{"server": {"http_port": 8080, "https_port": 8443, "read_timeout": "30s", "write_timeout": "30s", "idle_timeout": "60s", "max_connections": 1000, "connection_pool": {"max_idle_conns": 20, "max_idle_conns_per_host": 2, "idle_conn_timeout": "90s", "dial_timeout": "30s", "keep_alive": "30s", "max_conns_per_host": 0, "disable_keep_alives": false}}, "log": {"level": "info", "file": "logs/server_{date}.log", "format": "combined", "formats": {"combined": "{remote_addr} - - [{time}] \"{request}\" {status} {body_bytes_sent} \"{http_referer}\" \"{http_user_agent}\" {request_time} {site}", "common": "{remote_addr} - - [{time}] \"{request}\" {status} {body_bytes_sent}", "json": "{\"remote_addr\":\"{remote_addr}\",\"request\":\"{request}\",\"status\":\"{status}\",\"body_bytes_sent\":\"{body_bytes_sent}\",\"http_referer\":\"{http_referer}\",\"http_user_agent\":\"{http_user_agent}\",\"request_time\":{request_time},\"site\":\"{site}\"}", "short": "{site} | {remote_addr} | {request} | {status} | {request_time}s"}, "max_size": 100, "max_backups": 10, "max_age": 30, "targets": [{"type": "file", "filename": "logs/access_{site}_{date}.log", "format": "combined"}, {"type": "syslog", "network": "udp", "address": "127.0.0.1:514", "format": "json"}], "async": {"enabled": true, "channel_size": 2000, "batch_size": 50, "flush_interval": "2s", "max_memory_mb": 2, "drop_policy": "drop_oldest"}}, "cache": {"enabled": true, "type": "hybrid", "path": "cache", "max_size": "2GB", "ttl": "2h", "status_ttl": {"200": "2h", "404": "2m", "default": "2m"}, "cache_headers": {"enabled": true, "cache_status": "X-<PERSON><PERSON>-Status", "show_detail": true}, "cleanup_interval": "5m", "expired_check_interval": "1m", "enable_async_cleanup": true, "rules": [{"pattern": "\\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$", "ttl": "72h", "enabled": true}, {"pattern": "\\.(html|htm)$", "ttl": "2h", "enabled": true}, {"pattern": "\\.php$", "ttl": "0s", "enabled": true}], "redis": {"url": "redis://127.0.0.1:6379", "pool_size": 10, "connection_timeout": "5s", "command_timeout": "3s", "database": 0, "prefix": "proxy_cache:"}}, "rate_limit": {"enabled": true, "global_rps": 10000, "ip_rps": 100, "site_rps": 1000, "burst": 50}, "circuit_breaker": {"enabled": true, "max_failures": 5, "reset_timeout": "30s", "half_open_requests": 3}, "compression": {"enabled": true, "types": ["text/html", "text/css", "application/json", "application/javascript", "text/plain", "application/xml"], "min_size": "1KB", "max_size": "2MB", "level": 6, "algorithms": ["zstd", "br", "gzip", "deflate"], "brotli_quality": 6, "zstd_level": 3}, "hot_reload": {"enabled": true, "check_interval": "5s"}, "memory_cache": {"enabled": true, "global_memory_limit": "32MB", "default_site_limit": "6MB", "max_file_size": "2MB", "min_access_count": 3, "score_threshold": 10.0, "cleanup_interval": "5m", "allowed_types": ["text/html", "text/css", "text/javascript", "application/json", "application/xml", "text/plain"], "blocked_types": ["image/*", "video/*", "audio/*"], "eviction_strategy": "score_based", "sites": {"example-site": {"memory_limit": "128MB", "priority": "high"}}}, "performance": {"enabled": false, "enable_zero_copy": false, "buffer_size": "128KB", "max_connections": "1M", "connection_timeout": "5s", "keep_alive_timeout": "60s", "enable_cpu_affinity": true, "worker_threads": 0}, "headers": {"request": {"set": {}, "remove": [], "ignore": []}, "response": {"set": {"Server": "RustProxy/1.0"}, "remove": [], "ignore": []}}, "error_pages": [{"code": 404, "file": "error_pages/global_404.html"}, {"code": 500, "content": "<h1>服务器内部错误</h1><p>这是全局级别的500错误页面</p><p>请稍后重试或联系管理员</p>"}], "acl": {"enabled": true, "global_allow": [], "global_deny": [], "allow_file": "", "deny_file": "", "reload_interval": "5m"}, "monitor": {"enabled": true, "port": 8080, "username": "admin", "password": "password123", "api_key": "config-api-key-2024", "acl": {"allowed_ips": ["127.0.0.1", "::1", "***********/16", "10.0.0.0/8"], "denied_ips": []}}, "ssl": {"enabled": false, "cert_file": "ca/qiank.com/fullchain.pem", "key_file": "ca/qiank.com/privkey.pem", "protocols": "http1.1,http2", "min_version": "TLS1.2", "max_version": "TLS1.3", "prefer_server_ciphers": true, "session_cache": 1000, "session_tickets": true}, "sites": [{"site_id": "qiank-site", "name": "qiank.com", "domains": ["qiank.com"], "default_site": false, "max_connections": 3000, "http_port": 0, "https_port": 0, "debug_mode": false, "ssl": {"enabled": false, "cert_file": "ca/qiank.com/fullchain.pem", "key_file": "ca/qiank.com/privkey.pem", "protocols": "http1.1,http2", "min_version": "TLS1.2", "max_version": "TLS1.3", "prefer_server_ciphers": true, "session_cache": 1000, "session_tickets": true}, "upstreams": [{"name": "backend_server", "address": "**************", "port": 80, "protocol": "http", "https_port": 443, "weight": 100, "max_fails": 3, "fail_timeout": "30s", "backup": false, "load_balance_group": "main_group", "health_check": "http://**************/", "health_host": "qiank.com", "health_interval": "10s", "health_timeout": "5s", "health_path": "/"}], "cache_headers": {"enabled": true, "cache_status": "X-<PERSON><PERSON>-Status", "show_detail": false}, "routes": [{"pattern": "\\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$", "upstream": "main_group", "cache": true}, {"pattern": "^/static/", "static_dir": "test-files", "dir_listing": true, "index_files": ["index.html", "index.htm"], "cache": true}, {"pattern": "^/", "upstream": "main_group", "cache": false, "match_conditions": {"methods": ["POST"]}, "description": "Skip cache for POST requests"}, {"pattern": "^/", "upstream": "main_group", "cache": true, "match_conditions": {"methods": ["GET", "HEAD"]}, "description": "Cache GET/HEAD requests"}], "log_format": "json", "log_targets": [{"type": "file", "filename": "logs/example_{date}.log", "format": "json"}], "acl": {"allow": [], "deny": ["*************", "*************"], "allow_file": "", "deny_file": ""}, "headers": {"request": {"set": {"X-Forwarded-Proto": "https"}, "remove": [], "ignore": []}, "response": {"set": {"Server": "ExampleServer/1.0"}, "remove": [], "ignore": []}}, "rules": [{"pattern": "\\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$", "ttl": "72h", "enabled": true}, {"pattern": "^/api/", "ttl": "5m", "enabled": true}], "error_pages": [{"code": 404, "file": "error_pages/site_404.html"}, {"code": 403, "content": "<h1>访问被拒绝</h1><p>您没有权限访问此资源</p>"}]}]}
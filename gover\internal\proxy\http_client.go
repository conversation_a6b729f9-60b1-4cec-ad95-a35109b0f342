package proxy

import (
	"crypto/tls"
	"fmt"
	"net"
	"net/http"
	"sync"
	"time"

	"reverse-proxy/internal/config"
)

// HTTPClientManager HTTP客户端管理器
type HTTPClientManager struct {
	clients map[string]*http.Client
	mu      sync.RWMutex
	config  config.ConnectionPoolConfig
}

// NewHTTPClientManager 创建HTTP客户端管理器
func NewHTTPClientManager(cfg config.ConnectionPoolConfig) *HTTPClientManager {
	// 设置默认值
	if cfg.MaxIdleConns <= 0 {
		cfg.MaxIdleConns = 100
	}
	if cfg.MaxIdleConnsPerHost <= 0 {
		cfg.MaxIdleConnsPerHost = 10
	}
	if cfg.IdleConnTimeout <= 0 {
		cfg.IdleConnTimeout = 90 * time.Second
	}
	if cfg.DialTimeout <= 0 {
		cfg.DialTimeout = 30 * time.Second
	}
	if cfg.KeepAlive <= 0 {
		cfg.KeepAlive = 30 * time.Second
	}
	if cfg.MaxConnsPerHost <= 0 {
		cfg.MaxConnsPerHost = 0 // 0表示无限制
	}

	return &HTTPClientManager{
		clients: make(map[string]*http.Client),
		config:  cfg,
	}
}

// GetClient 获取或创建HTTP客户端
func (hcm *HTTPClientManager) GetClient(upstream *config.UpstreamConfig) *http.Client {
	// 使用上游地址作为key
	key := fmt.Sprintf("%s:%d", upstream.Address, upstream.Port)

	hcm.mu.RLock()
	if client, exists := hcm.clients[key]; exists {
		hcm.mu.RUnlock()
		return client
	}
	hcm.mu.RUnlock()

	// 创建新的客户端
	hcm.mu.Lock()
	defer hcm.mu.Unlock()

	// 双重检查
	if client, exists := hcm.clients[key]; exists {
		return client
	}

	// 创建自定义Transport
	transport := &http.Transport{
		DialContext: (&net.Dialer{
			Timeout:   hcm.config.DialTimeout,
			KeepAlive: hcm.config.KeepAlive,
		}).DialContext,
		MaxIdleConns:        hcm.config.MaxIdleConns,
		MaxIdleConnsPerHost: hcm.config.MaxIdleConnsPerHost,
		IdleConnTimeout:     hcm.config.IdleConnTimeout,
		MaxConnsPerHost:     hcm.config.MaxConnsPerHost,
		DisableKeepAlives:   hcm.config.DisableKeepAlives,

		// TLS配置
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: false,
			ServerName:         upstream.Address,
		},

		// 其他优化配置
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
		ResponseHeaderTimeout: 30 * time.Second,
		DisableCompression:    false, // 启用压缩
	}

	// 创建HTTP客户端
	client := &http.Client{
		Transport: transport,
		Timeout:   60 * time.Second, // 总超时时间
	}

	hcm.clients[key] = client
	return client
}

// GetTransport 获取自定义Transport（用于ReverseProxy）
func (hcm *HTTPClientManager) GetTransport(upstream *config.UpstreamConfig) http.RoundTripper {
	client := hcm.GetClient(upstream)
	return client.Transport
}

// CloseIdleConnections 关闭空闲连接
func (hcm *HTTPClientManager) CloseIdleConnections() {
	hcm.mu.RLock()
	defer hcm.mu.RUnlock()

	for _, client := range hcm.clients {
		if transport, ok := client.Transport.(*http.Transport); ok {
			transport.CloseIdleConnections()
		}
	}
}

// GetStats 获取连接池统计信息
func (hcm *HTTPClientManager) GetStats() map[string]interface{} {
	hcm.mu.RLock()
	defer hcm.mu.RUnlock()

	stats := map[string]interface{}{
		"total_clients":           len(hcm.clients),
		"max_idle_conns":          hcm.config.MaxIdleConns,
		"max_idle_conns_per_host": hcm.config.MaxIdleConnsPerHost,
		"idle_conn_timeout":       hcm.config.IdleConnTimeout.String(),
		"dial_timeout":            hcm.config.DialTimeout.String(),
		"keep_alive":              hcm.config.KeepAlive.String(),
		"max_conns_per_host":      hcm.config.MaxConnsPerHost,
		"disable_keep_alives":     hcm.config.DisableKeepAlives,
	}

	// 获取每个客户端的详细信息
	clients := make(map[string]interface{})
	for key, client := range hcm.clients {
		if transport, ok := client.Transport.(*http.Transport); ok {
			clients[key] = map[string]interface{}{
				"timeout":        client.Timeout.String(),
				"transport_type": "http.Transport",
			}
			_ = transport // 可以在这里添加更多transport统计信息
		}
	}
	stats["clients"] = clients

	return stats
}

// Close 关闭所有连接
func (hcm *HTTPClientManager) Close() {
	hcm.mu.Lock()
	defer hcm.mu.Unlock()

	for _, client := range hcm.clients {
		if transport, ok := client.Transport.(*http.Transport); ok {
			transport.CloseIdleConnections()
		}
	}

	// 清空客户端映射
	hcm.clients = make(map[string]*http.Client)
}



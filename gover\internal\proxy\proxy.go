package proxy

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"log"
	"net"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"runtime"
	"strings"
	"sync"
	"time"

	"reverse-proxy/internal/acl"
	"reverse-proxy/internal/cache"
	"reverse-proxy/internal/compression"
	"reverse-proxy/internal/config"
	"reverse-proxy/internal/connectionlimit"
	grpcproxy "reverse-proxy/internal/grpc"
	"reverse-proxy/internal/hotreload"
	"reverse-proxy/internal/loadbalancer"
	loggerpkg "reverse-proxy/internal/logger"
	"reverse-proxy/internal/memorycache"
	"reverse-proxy/internal/monitor"
	"reverse-proxy/internal/performance"
	"reverse-proxy/internal/processor"
	"reverse-proxy/internal/ratelimit"
	"reverse-proxy/internal/ssl"

	"compress/flate"
	"compress/gzip"

	"github.com/andybalholm/brotli"
	"github.com/klauspost/compress/zstd"
	"github.com/sirupsen/logrus"
)

// Proxy 反向代理服务??
type Proxy struct {
	config            *config.Config
	configManager     *config.ConfigManager     // 旧的配置管理器（兼容性）
	viperManager      *config.ViperIntegration  // 新的Viper配置管理器
	logger            *logrus.Logger
	acl               *acl.ACL
	cache             *cache.CacheManager
	siteManager       *SiteManager
	mu                sync.RWMutex
	servers           map[int]*http.Server                   // 按端口存储服务器
	serverWg          sync.WaitGroup                         // 等待所有服务器关闭
	loggers           map[string]loggerpkg.LoggerInterface   // 站点日志??
	globalLogger      loggerpkg.LoggerInterface              // 全局日志??
	monitor           *monitor.Monitor                       // 监控??
	clientManager     *HTTPClientManager                     // HTTP客户端管理器
	rateLimiter       *ratelimit.RateLimitManager            // 限流管理??
	connectionLimiter *connectionlimit.SiteConnectionLimiter // 连接数限制器
	circuitBreakers   map[string]*ratelimit.CircuitBreaker   // 熔断器（按站点）
	sslManager        *ssl.SSLManager                        // SSL管理??
	configWatcher     *hotreload.ConfigWatcher               // 配置热重??
	memoryCache       *memorycache.MemoryCacheManager        // 内存缓存管理??
	highPerfProxy     interface{}                            // 高性能代理接口
	mmapCache         interface{}                            // 内存映射缓存接口
	grpcProxy         *grpcproxy.GRPCProxy                   // gRPC代理
	processorManager  *processor.ProcessorManager           // 处理器管理器
}

// Site 站点配置
type Site struct {
	config        *config.SiteConfig
	acl           *acl.SiteACL
	loadBalancers map[string]loadbalancer.LoadBalancer // 组名 -> 负载均衡??
	routes        []*Route
}

// Route 路由
type Route struct {
	pattern *regexp.Regexp
	config  *config.RouteConfig
}

// NewProxy 创建新的代理实例
func NewProxy(cfg *config.Config, logger *logrus.Logger) (*Proxy, error) {
	// 创建全局ACL
	globalACL := acl.NewACL(
		cfg.ACL.GlobalAllow,
		cfg.ACL.GlobalDeny,
		cfg.ACL.AllowFile,
		cfg.ACL.DenyFile,
		cfg.ACL.ReloadInterval,
		logger,
	)

	// 创建HTTP客户端管理器
	clientManager := NewHTTPClientManager(cfg.Server.ConnectionPool)

	// 创建限流管理??
	var rateLimiter *ratelimit.RateLimitManager
	if cfg.RateLimit.Enabled {
		rateLimiter = ratelimit.NewRateLimitManager(ratelimit.RateLimitConfig{
			GlobalRPS: cfg.RateLimit.GlobalRPS,
			IPRPS:     cfg.RateLimit.IPRPS,
			SiteRPS:   cfg.RateLimit.SiteRPS,
			Burst:     cfg.RateLimit.Burst,
		})
	}

	// 创建连接数限制器
	connectionLimiter := connectionlimit.NewSiteConnectionLimiter(cfg.Server.MaxConnections)

	// 创建SSL管理??
	sslManager := ssl.NewSSLManager()
	sslManager.UpdateDomainCerts(cfg.Sites)

	// 创建内存缓存管理??
	logger.Debugf("内存缓存配置: enabled=%v", cfg.MemoryCache.Enabled)
	memoryCache, err := memorycache.NewMemoryCacheManager(cfg.MemoryCache)
	if err != nil {
		return nil, fmt.Errorf("创建内存缓存管理器失败 %w", err)
	}
	if memoryCache != nil {
		logger.Debug("内存缓存管理器已创建")
	} else {
		logger.Debug("内存缓存管理器为nil（已禁用）")
	}

	// 创建配置管理??
	configManager := config.NewConfigManager("", logger)

	// 创建处理器管理器
	processorManager := processor.NewProcessorManager(logger, cfg)

	// 更新站点配置到处理器管理器
	if processorManager != nil {
		sites := make(map[string]*config.SiteConfig)
		for i, siteConfig := range cfg.Sites {
			sites[siteConfig.SiteID] = &cfg.Sites[i]
		}
		processorManager.UpdateSites(sites)
	}

	proxy := &Proxy{
		config:            cfg,
		configManager:     configManager,
		logger:            logger,
		acl:               globalACL,
		servers:           make(map[int]*http.Server),
		loggers:           make(map[string]loggerpkg.LoggerInterface),
		clientManager:     clientManager,
		rateLimiter:       rateLimiter,
		connectionLimiter: connectionLimiter,
		circuitBreakers:   make(map[string]*ratelimit.CircuitBreaker),
		sslManager:        sslManager,
		memoryCache:       memoryCache,
		processorManager:  processorManager,
	}

	// 初始化站点管理器
	proxy.siteManager = NewSiteManager(proxy)

	// 初始化全局日志器
	proxy.globalLogger = loggerpkg.NewMultiLogger(cfg.Log.Targets, map[string]string{"site": "global"}).(loggerpkg.LoggerInterface)

	// 初始化缓存
	if cfg.Cache.Enabled {
		cacheManager, err := cache.NewCacheManager(cfg.Cache)
		if err != nil {
			return nil, fmt.Errorf("初始化缓存失败: %w", err)
		}
		proxy.cache = cacheManager
	}

	// 使用站点管理器加载站点
	if err := proxy.siteManager.LoadSites(cfg.Sites); err != nil {
		return nil, fmt.Errorf("加载站点失败: %w", err)
	}

	// 更新缓存管理器的站点名称映射
	if proxy.cache != nil {
		proxy.updateCacheManagerSiteMapping(cfg.Sites)
	}

	// 初始化高性能组件
	if err := proxy.initHighPerformanceComponents(); err != nil {
		return nil, fmt.Errorf("初始化高性能组件失败: %w", err)
	}

	// 初始化gRPC代理
	if cfg.GRPC.Enabled {
		grpcConfig := grpcproxy.GRPCConfig{
			Enabled:        cfg.GRPC.Enabled,
			MaxRecvMsgSize: int(cfg.GRPC.MaxRecvMsgSize),
			MaxSendMsgSize: int(cfg.GRPC.MaxSendMsgSize),
			Timeout:        cfg.GRPC.Timeout,
			KeepAlive:      cfg.GRPC.KeepAlive,
			Compression:    cfg.GRPC.Compression,
			LoadBalancing:  cfg.GRPC.LoadBalancing,
		}
		proxy.grpcProxy = grpcproxy.NewGRPCProxy(grpcConfig)
		logger.Info("gRPC代理已启用")
	}

	// 注册配置管理器回??
	proxy.configManager.AddGlobalConfigCallback(proxy.onGlobalConfigChanged)
	proxy.configManager.AddBatchSiteConfigCallback(proxy.onBatchSiteConfigChanged)

	// 初始化配置管理器（这里暂时不加载配置，等EnableHotReload时再加载??

	return proxy, nil
}

// NewProxyWithViperManager 使用Viper配置管理器创建新的代理实例
func NewProxyWithViperManager(cfg *config.Config, viperManager *config.ViperIntegration, logger *logrus.Logger) (*Proxy, error) {
	// 创建全局ACL
	globalACL := acl.NewACL(
		cfg.ACL.GlobalAllow,
		cfg.ACL.GlobalDeny,
		cfg.ACL.AllowFile,
		cfg.ACL.DenyFile,
		cfg.ACL.ReloadInterval,
		logger,
	)

	// 创建HTTP客户端管理器
	clientManager := NewHTTPClientManager(cfg.Server.ConnectionPool)

	// 创建限流管理器
	var rateLimiter *ratelimit.RateLimitManager
	if cfg.RateLimit.Enabled {
		rateLimiter = ratelimit.NewRateLimitManager(ratelimit.RateLimitConfig{
			GlobalRPS: cfg.RateLimit.GlobalRPS,
			IPRPS:     cfg.RateLimit.IPRPS,
			SiteRPS:   cfg.RateLimit.SiteRPS,
			Burst:     cfg.RateLimit.Burst,
		})
	}

	// 创建连接数限制器
	connectionLimiter := connectionlimit.NewSiteConnectionLimiter(cfg.Server.MaxConnections)

	// 创建SSL管理器
	sslManager := ssl.NewSSLManager()
	sslManager.UpdateDomainCerts(cfg.Sites)

	// 创建内存缓存管理器
	var memoryCacheManager *memorycache.MemoryCacheManager
	var err error
	if cfg.MemoryCache.Enabled {
		memoryCacheManager, err = memorycache.NewMemoryCacheManager(cfg.MemoryCache)
		if err != nil {
			return nil, fmt.Errorf("创建内存缓存管理器失败: %w", err)
		}
	}

	// 创建代理实例
	proxy := &Proxy{
		config:            cfg,
		viperManager:      viperManager,  // 使用Viper管理器
		logger:            logger,
		acl:               globalACL,
		servers:           make(map[int]*http.Server),
		loggers:           make(map[string]loggerpkg.LoggerInterface),
		clientManager:     clientManager,
		rateLimiter:       rateLimiter,
		connectionLimiter: connectionLimiter,
		circuitBreakers:   make(map[string]*ratelimit.CircuitBreaker),
		sslManager:        sslManager,
		memoryCache:       memoryCacheManager,
	}

	// 创建处理器管理器
	processorManager := processor.NewProcessorManager(logger, cfg)

	// 更新站点配置到处理器管理器
	if processorManager != nil {
		sites := make(map[string]*config.SiteConfig)
		for i, siteConfig := range cfg.Sites {
			sites[siteConfig.SiteID] = &cfg.Sites[i]
		}
		processorManager.UpdateSites(sites)
	}

	proxy.processorManager = processorManager

	// 初始化站点管理器
	proxy.siteManager = NewSiteManager(proxy)

	// 初始化全局日志器
	proxy.globalLogger = loggerpkg.NewMultiLogger(cfg.Log.Targets, map[string]string{"site": "global"}).(loggerpkg.LoggerInterface)

	// 初始化缓存
	if cfg.Cache.Enabled {
		cacheManager, err := cache.NewCacheManager(cfg.Cache)
		if err != nil {
			return nil, fmt.Errorf("初始化缓存失败: %w", err)
		}
		proxy.cache = cacheManager
	}

	// 使用站点管理器加载站点
	if err := proxy.siteManager.LoadSites(cfg.Sites); err != nil {
		return nil, fmt.Errorf("加载站点失败: %w", err)
	}

	// 更新缓存管理器的站点名称映射
	if proxy.cache != nil {
		proxy.updateCacheManagerSiteMapping(cfg.Sites)
	}

	// 初始化高性能组件
	if err := proxy.initHighPerformanceComponents(); err != nil {
		return nil, fmt.Errorf("初始化高性能组件失败: %w", err)
	}

	// 初始化gRPC代理
	if cfg.GRPC.Enabled {
		grpcConfig := grpcproxy.GRPCConfig{
			Enabled:        cfg.GRPC.Enabled,
			MaxRecvMsgSize: int(cfg.GRPC.MaxRecvMsgSize),
			MaxSendMsgSize: int(cfg.GRPC.MaxSendMsgSize),
			Timeout:        cfg.GRPC.Timeout,
			KeepAlive:      cfg.GRPC.KeepAlive,
			Compression:    cfg.GRPC.Compression,
			LoadBalancing:  cfg.GRPC.LoadBalancing,
		}
		proxy.grpcProxy = grpcproxy.NewGRPCProxy(grpcConfig)
		logger.Info("gRPC代理已启用")
	}

	return proxy, nil
}

// initHighPerformanceComponents 初始化高性能组件
func (p *Proxy) initHighPerformanceComponents() error {
	// 初始化高性能代理
	if p.config.Performance.Enabled {
		perfConfig := performance.PerformanceConfig{
			EnableZeroCopy:    p.config.Performance.EnableZeroCopy,
			BufferSize:        int(p.config.Performance.BufferSize),
			MaxConnections:    int(p.config.Performance.MaxConnections),
			ConnectionTimeout: p.config.Performance.ConnectionTimeout,
			KeepAliveTimeout:  p.config.Performance.KeepAliveTimeout,
			EnableCPUAffinity: p.config.Performance.EnableCPUAffinity,
			WorkerThreads:     p.config.Performance.WorkerThreads,
		}

		highPerfProxy := performance.NewHighPerformanceProxy(perfConfig)
		p.highPerfProxy = highPerfProxy

		// 启用高并发优化
		highPerfProxy.OptimizeForHighConcurrency()

		p.logger.Info("高性能代理已启用")
	}

	// 初始化内存映射缓存
	if p.config.Performance.MmapCache.Enabled {
		maxSize := int64(p.config.Performance.MmapCache.MaxSize)

		// 获取配置的缓存路径
		cachePath := getMmapCachePath(p.config.Performance.MmapCache)

		mmapCache := performance.NewMmapCache(cachePath, maxSize)
		p.mmapCache = mmapCache

		p.logger.Infof("内存映射缓存已启用, 路径: %s (平台: %s)", cachePath, runtime.GOOS)
	}

	return nil
}

// parseSize 解析大小字符??(??"1GB", "512MB")
func parseSize(sizeStr string) int64 {
	if sizeStr == "" {
		return 1024 * 1024 * 1024 // 默认1GB
	}

	sizeStr = strings.ToUpper(sizeStr)
	var multiplier int64 = 1

	if strings.HasSuffix(sizeStr, "GB") {
		multiplier = 1024 * 1024 * 1024
		sizeStr = strings.TrimSuffix(sizeStr, "GB")
	} else if strings.HasSuffix(sizeStr, "MB") {
		multiplier = 1024 * 1024
		sizeStr = strings.TrimSuffix(sizeStr, "MB")
	} else if strings.HasSuffix(sizeStr, "KB") {
		multiplier = 1024
		sizeStr = strings.TrimSuffix(sizeStr, "KB")
	}

	// 简单解析数??
	var size int64 = 1024 * 1024 * 1024 // 默认1GB
	if len(sizeStr) > 0 {
		// 这里应该用strconv.ParseInt，但为了简化先用固定??
		if sizeStr == "512" {
			size = 512
		} else if sizeStr == "256" {
			size = 256
		} else if sizeStr == "1" {
			size = 1
		} else if sizeStr == "2" {
			size = 2
		}
	}

	return size * multiplier
}

// handleHighPerformanceRequest 高性能请求处理
func (p *Proxy) handleHighPerformanceRequest(w http.ResponseWriter, r *http.Request, site *Site) error {
	//p.logger.Debugf("handleHighPerformanceRequest开始: %s %s", r.Method, r.URL.Path)

	// 获取路由
	route := site.findRoute(r.URL.Path, r)
	if route == nil {
		//p.logger.Debugf("未找到路由: %s", r.URL.Path)
		return fmt.Errorf("no route found for path: %s", r.URL.Path)
	}
	//p.logger.Debugf("找到路由: %s", route.config.Pattern)

	// 获取上游服务??
	upstream, err := site.getUpstream(route)
	if err != nil {
		return fmt.Errorf("failed to get upstream: %w", err)
	}

	// 检查是否启用内存映射缓存
	if p.mmapCache != nil && r.Method == "GET" {
		if data, exists := p.tryMmapCache(r.URL.Path); exists {
			// 使用优化的内存映射缓存写入
			w.Header().Set("Content-Type", "application/octet-stream")
			w.Header().Set("X-Cache", "HIT-MMAP")

			// 设置调试信息头部（需要从上下文获取site信息）
			// 注意：这里需要传递site参数，暂时跳过，在调用处处理

			// 使用零拷贝写入
			if mmapCache, ok := p.mmapCache.(interface {
				GetFileSize(string) (int64, bool)
				WriteToHTTPResponse(string, interface{}) (int, error)
			}); ok {
				if size, exists := mmapCache.GetFileSize(r.URL.Path); exists {
					w.Header().Set("Content-Length", fmt.Sprintf("%d", size))
				}

				if _, err := mmapCache.WriteToHTTPResponse(r.URL.Path, w); err != nil {
					// 回退到普通写入
					w.Write(data)
				}
			} else {
				// 回退到普通写入
				w.Write(data)
			}
			return nil
		}
	}

	// 使用高性能代理处理请求
	p.logger.Debug("检查高性能代理类型转换...")
	if highPerfProxy, ok := p.highPerfProxy.(*performance.HighPerformanceProxy); ok {
		p.logger.Debug("高性能代理类型转换成功，调用FastProxy...")
		err := highPerfProxy.FastProxy(w, r, upstream)
		//p.logger.Debugf("FastProxy调用完成，错误: %v", err)
		if err != nil {
			return fmt.Errorf("high performance proxy error: %w", err)
		}

		// 记录成功的高性能处理
		if p.monitor != nil {
			// RecordRequest(siteID string, status int, bytesReceived, bytesSent int64)
			// 高性能处理的流量统计由高性能组件自己处理，这里只记录基本统计
			p.monitor.RecordRequest(site.config.SiteID, 200, 0, 0)
		}

		p.logger.Debug("高性能处理完成，返回成功")
		return nil
	}

	return fmt.Errorf("high performance proxy not available")
}

// tryMmapCache 尝试从内存映射缓存获取数??
func (p *Proxy) tryMmapCache(path string) ([]byte, bool) {
	if p.mmapCache == nil {
		return nil, false
	}

	// 将URL路径转换为文件名
	filename := strings.TrimPrefix(path, "/")
	if filename == "" {
		filename = "index.html"
	}

	// 从内存映射缓存获取数??
	if mmapCache, ok := p.mmapCache.(*performance.MmapCache); ok {
		return mmapCache.Get(filename)
	}

	return nil, false
}

// GetHighPerformanceStats 获取高性能统计信息
func (p *Proxy) GetHighPerformanceStats() map[string]interface{} {
	stats := make(map[string]interface{})

	// 高性能代理统计
	if p.highPerfProxy != nil {
		if highPerfProxy, ok := p.highPerfProxy.(*performance.HighPerformanceProxy); ok {
			stats["high_performance_proxy"] = highPerfProxy.GetStats()
		}
	}

	// 内存映射缓存统计
	if p.mmapCache != nil {
		if mmapCache, ok := p.mmapCache.(*performance.MmapCache); ok {
			stats["mmap_cache"] = mmapCache.GetStats()
		}
	}

	// 性能配置信息
	stats["performance_config"] = map[string]interface{}{
		"enabled":           p.config.Performance.Enabled,
		"zero_copy_enabled": p.config.Performance.EnableZeroCopy,
		"buffer_size":       p.config.Performance.BufferSize,
		"max_connections":   p.config.Performance.MaxConnections,
		"cpu_affinity":      p.config.Performance.EnableCPUAffinity,
		"worker_threads":    p.config.Performance.WorkerThreads,
	}

	return stats
}

// serveStaticFileWithCache 提供静态文件服务并支持内存缓存
func (p *Proxy) serveStaticFileWithCache(w http.ResponseWriter, r *http.Request, route *Route, site *Site, filePath string, startTime time.Time) {
	// 读取文件
	content, err := os.ReadFile(filePath)
	if err != nil {
		http.Error(w, "File not found", http.StatusNotFound)
		p.logAccess(r, site, 404, 0, startTime, "text/plain")
		if p.monitor != nil {
			// RecordRequest(siteID string, status int, bytesReceived, bytesSent int64)
			// 404错误，没有请求体和响应体
			p.monitor.RecordRequest(site.config.SiteID, 404, 0, 0)
		}
		return
	}

	// 获取内容类型
	contentType := memorycache.GetContentType(filePath)

	// 记录访问到热点追踪器
	if p.memoryCache != nil {
		p.memoryCache.RecordAccess(filePath, int64(len(content)))

		// 尝试加载到内存缓存
		err := p.memoryCache.LoadToMemory(site.config.SiteID, filePath, content, contentType)
		if err != nil {
			// 加载失败不影响正常服务
			p.logger.Debug("加载文件到内存缓存失败:", err)
		} else {
			p.logger.Debug("文件已尝试加载到内存缓存:", filePath)
		}
	}

	// 设置响应头
	w.Header().Set("Content-Type", contentType)
	w.Header().Set("X-Memory-Cache", "MISS")

	// 设置调试信息头部
	if site.config.DebugMode {
		w.Header().Set("X-Debug-Cache-Source", "file")
		w.Header().Set("X-Debug-Cache-Status", "MISS")
		w.Header().Set("X-Debug-File-Path", filePath)
		w.Header().Set("X-Debug-Request-ID", fmt.Sprintf("%d", time.Now().UnixNano()))
	}

	// 发送文件内容
	w.WriteHeader(200)
	w.Write(content)

	// 记录日志和监控
	p.logAccess(r, site, 200, int64(len(content)), startTime, contentType)
	if p.monitor != nil {
		// RecordRequest(siteID string, status int, bytesReceived, bytesSent int64)
		// 静态文件服务：没有请求体，响应体是文件内容
		p.monitor.RecordRequest(site.config.SiteID, 200, 0, int64(len(content)))
	}
}

// EnableHotReload 启用配置热重??
func (p *Proxy) EnableHotReload(configFile string) error {
	if p.configWatcher != nil {
		return fmt.Errorf("热重载已启用")
	}

	// 设置配置文件路径到配置管理器
	p.configManager.SetConfigFilePath(configFile)

	// 初始化配置管理器
	if err := p.configManager.LoadInitialConfig(configFile); err != nil {
		return fmt.Errorf("初始化配置管理器失败: %w", err)
	}

	// 创建配置监控??
	p.configWatcher = hotreload.NewConfigWatcher(configFile, 5*time.Second)

	// 添加重载回调，使用新的配置管理器
	p.configWatcher.AddCallback(func(newConfig *config.Config) error {
		return p.configManager.ReloadFromFile(configFile)
	})

	// 启动监控
	return p.configWatcher.Start()
}

// EnableViperHotReload 启用基于Viper的配置热重载
func (p *Proxy) EnableViperHotReload() error {
	if p.viperManager == nil {
		return fmt.Errorf("Viper配置管理器未初始化")
	}

	// 启用Viper热重载
	return p.viperManager.EnableHotReload(func(oldConfig, newConfig *config.Config) error {
		return p.handleViperConfigReload(oldConfig, newConfig)
	})
}

// handleViperConfigReload 处理Viper配置重载
func (p *Proxy) handleViperConfigReload(oldConfig, newConfig *config.Config) error {
	p.logger.Info("开始处理Viper配置重载...")

	// 检查是否需要重启服务器（端口变更）
	needServerRestart := p.checkIfServerRestartNeeded(newConfig)

	// 更新SSL证书映射
	if p.sslManager != nil {
		p.sslManager.UpdateDomainCerts(newConfig.Sites)
		p.sslManager.ReloadCertificates()
	}

	// 重新创建限流配置
	if newConfig.RateLimit.Enabled {
		p.rateLimiter = ratelimit.NewRateLimitManager(ratelimit.RateLimitConfig{
			GlobalRPS: newConfig.RateLimit.GlobalRPS,
			IPRPS:     newConfig.RateLimit.IPRPS,
			SiteRPS:   newConfig.RateLimit.SiteRPS,
			Burst:     newConfig.RateLimit.Burst,
		})
	} else {
		p.rateLimiter = nil
	}

	// 重新创建缓存管理器
	if err := p.reloadCacheManager(newConfig); err != nil {
		p.logger.Errorf("重载缓存管理器失败 %v", err)
		return fmt.Errorf("重载缓存管理器失败 %w", err)
	}

	// 重新创建内存缓存管理器
	if err := p.reloadMemoryCacheManager(newConfig); err != nil {
		p.logger.Errorf("重载内存缓存管理器失败 %v", err)
		return fmt.Errorf("重载内存缓存管理器失败 %w", err)
	}

	// 更新全局ACL
	if p.acl != nil {
		p.logger.Info("关闭旧的全局ACL文件监控...")
		if err := p.acl.Close(); err != nil {
			p.logger.Warnf("关闭旧的全局ACL文件监控失败: %v", err)
		}
	}

	p.logger.Info("创建新的全局ACL...")
	p.acl = acl.NewACL(
		newConfig.ACL.GlobalAllow,
		newConfig.ACL.GlobalDeny,
		newConfig.ACL.AllowFile,
		newConfig.ACL.DenyFile,
		newConfig.ACL.ReloadInterval,
		p.logger,
	)

	// 重新创建站点
	if err := p.reloadSites(newConfig); err != nil {
		return fmt.Errorf("重载站点配置失败: %w", err)
	}

	// 如果需要重启服务器，则重启
	if needServerRestart {
		p.logger.Info("检测到端口变更，重启HTTP服务器...")
		if err := p.restartServers(newConfig); err != nil {
			return fmt.Errorf("重启服务器失败 %w", err)
		}
	} else {
		// 即使端口没有变更，也需要更新处理器以使用新的站点实例
		p.logger.Info("更新HTTP服务器处理器...")
		if err := p.updateServerHandlers(); err != nil {
			return fmt.Errorf("更新服务器处理器失败: %w", err)
		}
	}

	// 更新配置引用
	p.config = newConfig

	// 更新监控器配置
	if p.monitor != nil {
		p.monitor.UpdateConfig(newConfig)
		p.logger.Debug("监控器配置已更新")
	}

	p.logger.Info("Viper配置重载完成")
	return nil
}

// reloadConfig 重载配置
func (p *Proxy) reloadConfig(newConfig *config.Config) error {
	p.logger.Info("========== 开始重载配置==========")
	p.logger.Infof("配置文件站点数量: %d", len(newConfig.Sites))

	// 打印每个站点的配置信息
	for i, site := range newConfig.Sites {
		p.logger.Infof("站点 %d: %s (ID: %s), 域名: %v", i, site.Name, site.SiteID, site.Domains)
		p.logger.Infof("  ACL允许: %v", site.ACL.Allow)
		p.logger.Infof("  ACL拒绝: %v", site.ACL.Deny)
		p.logger.Infof("  ACL允许文件: %s", site.ACL.AllowFile)
		p.logger.Infof("  ACL拒绝文件: %s", site.ACL.DenyFile)
		for j, route := range site.Routes {
			p.logger.Infof("  路由 %d: %s, 缓存: %v", j, route.Pattern, route.Cache)
		}
	}

	// 检查是否需要重启服务器（端口变更）
	needServerRestart := p.checkIfServerRestartNeeded(newConfig)

	// 更新SSL证书映射
	if p.sslManager != nil {
		p.sslManager.UpdateDomainCerts(newConfig.Sites)
		p.sslManager.ReloadCertificates()
	}

	// 重新创建限流配置
	if newConfig.RateLimit.Enabled {
		p.rateLimiter = ratelimit.NewRateLimitManager(ratelimit.RateLimitConfig{
			GlobalRPS: newConfig.RateLimit.GlobalRPS,
			IPRPS:     newConfig.RateLimit.IPRPS,
			SiteRPS:   newConfig.RateLimit.SiteRPS,
			Burst:     newConfig.RateLimit.Burst,
		})
	} else {
		p.rateLimiter = nil
	}

	// 重新创建缓存管理器（修复缓存TTL设置问题??
	if err := p.reloadCacheManager(newConfig); err != nil {
		p.logger.Errorf("重载缓存管理器失败 %v", err)
		return fmt.Errorf("重载缓存管理器失败 %w", err)
	}

	// 重新创建内存缓存管理器
	if err := p.reloadMemoryCacheManager(newConfig); err != nil {
		p.logger.Errorf("重载内存缓存管理器失败 %v", err)
		return fmt.Errorf("重载内存缓存管理器失败 %w", err)
	}

	// 更新全局ACL
	if p.acl != nil {
		p.logger.Info("关闭旧的全局ACL文件监控...")
		if err := p.acl.Close(); err != nil {
			p.logger.Warnf("关闭旧的全局ACL文件监控失败: %v", err)
		}
	}

	p.logger.Info("创建新的全局ACL...")
	p.acl = acl.NewACL(
		newConfig.ACL.GlobalAllow,
		newConfig.ACL.GlobalDeny,
		newConfig.ACL.AllowFile,
		newConfig.ACL.DenyFile,
		newConfig.ACL.ReloadInterval,
		p.logger,
	)

	// 重新创建站点（包括缓存策略、ACL等）
	if err := p.reloadSites(newConfig); err != nil {
		return fmt.Errorf("重载站点配置失败: %w", err)
	}

	// 如果需要重启服务器，则重启
	if needServerRestart {
		p.logger.Info("检测到端口变更，重启HTTP服务器..")
		if err := p.restartServers(newConfig); err != nil {
			return fmt.Errorf("重启服务器失败 %w", err)
		}
	} else {
		// 即使端口没有变更，也需要更新处理器以使用新的站点实??
		p.logger.Info("更新HTTP服务器处理器...")
		if err := p.updateServerHandlers(); err != nil {
			return fmt.Errorf("更新服务器处理器失败: %w", err)
		}
	}

	// 更新配置引用
	p.config = newConfig

	// 更新监控器配置
	if p.monitor != nil {
		p.monitor.UpdateConfig(newConfig)
		p.logger.Debug("监控器配置已更新")
	}

	p.logger.Info("配置重载完成")
	return nil
}

// updateServerHandlers 更新服务器处理器（不重启服务器）
func (p *Proxy) updateServerHandlers() error {
	p.logger.Info("开始更新HTTP服务器处理器...")

	// 按端口分组站??
	portGroups := p.groupSitesByPort()

	// 更新每个端口的处理器
	for port, sites := range portGroups {
		if server, exists := p.servers[port]; exists {
			// 创建新的处理??
			handler := p.createPortHandler(sites)

			// 更新服务器的处理??
			server.Handler = handler

			p.logger.Infof("已更新口 %d 的处理器，站点数量 %d", port, len(sites))
			for _, site := range sites {
				p.logger.Debugf("  端口 %d 站点: %s (ACL实例地址: %p)", port, site.config.SiteID, site.acl)
			}
		}
	}

	p.logger.Info("HTTP服务器处理器更新完成")
	return nil
}

// checkIfServerRestartNeeded 检查是否需要重启服务器
func (p *Proxy) checkIfServerRestartNeeded(newConfig *config.Config) bool {
	// 获取当前端口列表和SSL状??
	currentPorts := make(map[int]bool)
	currentSSLPorts := make(map[int]bool)
	for port := range p.servers {
		currentPorts[port] = true
		// 检查当前端口是否为HTTPS端口
		sites := p.siteManager.GetAllSites()
		for _, site := range sites {
			if site.config.SSL.Enabled {
				httpsPort := site.config.HTTPSPort
				if httpsPort == 0 {
					httpsPort = p.config.Server.HTTPSPort
				}
				if port == httpsPort {
					currentSSLPorts[port] = true
				}
			}
		}
	}

	// 获取新配置的端口列表和SSL状??
	newPorts := make(map[int]bool)
	newSSLPorts := make(map[int]bool)
	for _, site := range newConfig.Sites {
		// 使用HTTP端口，如果为0则使用全局端口
		httpPort := site.HTTPPort
		if httpPort == 0 {
			httpPort = newConfig.Server.HTTPPort
		}
		newPorts[httpPort] = true

		// 如果启用了SSL，也添加HTTPS端口
		if site.SSL.Enabled {
			httpsPort := site.HTTPSPort
			if httpsPort == 0 {
				httpsPort = newConfig.Server.HTTPSPort
			}
			newPorts[httpsPort] = true
			newSSLPorts[httpsPort] = true
		}
	}

	// 检查端口是否有变化
	if len(currentPorts) != len(newPorts) {
		p.logger.Debug("端口数量变化，需要重启服务器")
		return true
	}

	for port := range currentPorts {
		if !newPorts[port] {
			//p.logger.Debugf("端口 %d 被移除，需要重启服务器", port)
			return true
		}
	}

	for port := range newPorts {
		if !currentPorts[port] {
			//p.logger.Debugf("新增端口 %d，需要重启服务器", port)
			return true
		}
	}

	// 检查SSL状态是否有变化
	for port := range currentPorts {
		currentIsSSL := currentSSLPorts[port]
		newIsSSL := newSSLPorts[port]
		if currentIsSSL != newIsSSL {
			//p.logger.Debugf("端口 %d 的SSL状态变化（%v -> %v），需要重启服务器", port, currentIsSSL, newIsSSL)
			return true
		}
	}

	return false
}

// restartServers 重启HTTP服务??
func (p *Proxy) restartServers(newConfig *config.Config) error {
	p.logger.Info("开始重启HTTP服务器...")

	// 停止所有现有服务器
	for port, server := range p.servers {
		p.logger.Infof("停止端口 %d 的服务器...", port)
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		if err := server.Shutdown(ctx); err != nil {
			p.logger.Errorf("优雅关闭端口 %d 的服务器失败: %v", port, err)
			// 强制关闭
			server.Close()
		}
		cancel()
	}

	// 等待所有服务器停止
	p.serverWg.Wait()

	// 清空服务器映射
	p.servers = make(map[int]*http.Server)

	// 按端口分组站??
	portGroups := p.groupSitesByPort()

	// 为每个端口创建新服务??
	for port, sites := range portGroups {
		// 修复Go 1.20循环变量陷阱：创建局部副本
		port := port
		sites := sites

		// 检查是否需要HTTPS
		needHTTPS := p.checkPortNeedHTTPS(port, sites)

		server := &http.Server{
			Addr:         fmt.Sprintf(":%d", port),
			Handler:      p.createPortHandler(sites),
			ReadTimeout:  newConfig.Server.ReadTimeout,
			WriteTimeout: newConfig.Server.WriteTimeout,
			IdleTimeout:  newConfig.Server.IdleTimeout,
			ErrorLog:     p.createFilteredErrorLog(),
		}

		// 存储服务器引??
		p.servers[port] = server

		protocol := "HTTP"
		if needHTTPS {
			protocol = "HTTPS"
		}
		p.logger.Info("重新启动", protocol, "服务器，监听端口:", port, "站点:", p.getSiteNames(sites))

		// 启动服务??
		p.serverWg.Add(1)
		go func(srv *http.Server, port int, isHTTPS bool) {
			defer p.serverWg.Done()
			var err error
			if isHTTPS {
				// 使用优化的SSL管理??
				var sslConfig config.SSLConfig
				for _, site := range sites {
					if site.config.SSL.Enabled {
						sslConfig = site.config.SSL
						break
					}
				}

				srv.TLSConfig = p.sslManager.CreateTLSConfig(sslConfig)
				err = srv.ListenAndServeTLS("", "") // 证书由GetCertificate动态加??
			} else {
				err = srv.ListenAndServe()
			}
			if err != nil && err != http.ErrServerClosed {
				p.logger.Error("服务器启动失败，端口:", port, "错误:", err)
			}
		}(server, port, needHTTPS)
	}

	p.logger.Info("HTTP服务器重启完成")
	return nil
}

// reloadCacheManager 重载缓存管理??
func (p *Proxy) reloadCacheManager(newConfig *config.Config) error {
	p.logger.Info("重载缓存管理器...")

	// 关闭旧的缓存管理??
	if p.cache != nil {
		p.logger.Debug("关闭旧的缓存管理器")
		p.cache.Close()
		p.cache = nil
	}

	// 创建新的缓存管理??
	if newConfig.Cache.Enabled {
		p.logger.Debug("创建新的缓存管理器")
		cacheManager, err := cache.NewCacheManager(newConfig.Cache)
		if err != nil {
			return fmt.Errorf("创建缓存管理器失败: %w", err)
		}
		p.cache = cacheManager
		p.logger.Infof("缓存管理器重载完成，类型: %s，路径: %s", newConfig.Cache.Type, newConfig.Cache.Path)
	} else {
		p.logger.Info("缓存已禁用")
	}

	return nil
}

// reloadMemoryCacheManager 重载内存缓存管理器
func (p *Proxy) reloadMemoryCacheManager(newConfig *config.Config) error {
	p.logger.Info("重载内存缓存管理器...")

	// 关闭旧的内存缓存管理器
	if p.memoryCache != nil {
		p.logger.Debug("关闭旧的内存缓存管理器")
		p.memoryCache.Shutdown()
		p.memoryCache = nil
	}

	// 创建新的内存缓存管理器
	if newConfig.MemoryCache.Enabled {
		p.logger.Debug("创建新的内存缓存管理器")
		memoryCache, err := memorycache.NewMemoryCacheManager(newConfig.MemoryCache)
		if err != nil {
			return fmt.Errorf("创建内存缓存管理器失败: %w", err)
		}
		p.memoryCache = memoryCache
		p.logger.Infof("内存缓存管理器重载完成，全局限制: %s", newConfig.MemoryCache.GlobalMemoryLimit)
	} else {
		p.logger.Info("内存缓存已禁用")
	}

	// 更新监控器的内存缓存引用
	if p.monitor != nil {
		p.monitor.SetMemoryCache(p.memoryCache)
	}

	return nil
}

// createFilteredErrorLog 创建过滤的错误日??
func (p *Proxy) createFilteredErrorLog() *log.Logger {
	return log.New(&filteredLogWriter{logger: p.logger}, "", 0)
}

// filteredLogWriter 过滤的日志写入器
type filteredLogWriter struct {
	logger *logrus.Logger
}

func (w *filteredLogWriter) Write(data []byte) (int, error) {
	msg := string(data)

	// 过滤常见的无害TLS错误
	if strings.Contains(msg, "TLS handshake error") &&
		(strings.Contains(msg, "EOF") ||
			strings.Contains(msg, "connection reset by peer") ||
			strings.Contains(msg, "broken pipe")) {
		// 这些是客户端提前断开连接导致的，降级为Debug级别
		w.logger.Debug("TLS连接被客户端提前关闭: ", strings.TrimSpace(msg))
		return len(data), nil
	}

	// 过滤SSL配置相关的TLS握手错误
	if strings.Contains(msg, "TLS handshake error") &&
		strings.Contains(msg, "未找到域名") &&
		strings.Contains(msg, "SSL配置") {
		// SSL配置错误只记录到日志，不显示警告
		w.logger.Debug("TLS握手SSL配置错误: ", strings.TrimSpace(msg))
		return len(data), nil
	}

	// 其他错误正常记录
	w.logger.Warn("HTTP服务器错?? ", strings.TrimSpace(msg))
	return len(data), nil
}

// reloadSites 重新加载站点配置
func (p *Proxy) reloadSites(newConfig *config.Config) error {
	p.logger.Info("重新加载站点配置...")

	// 使用站点管理器重新加载所有站点
	if err := p.siteManager.LoadSites(newConfig.Sites); err != nil {
		return fmt.Errorf("站点管理器重载站点失败: %w", err)
	}

	// 更新缓存管理器的站点名称映射
	if p.cache != nil {
		p.updateCacheManagerSiteMapping(newConfig.Sites)
	}

	p.logger.Infof("站点配置重载完成，当前站点数量: %d", p.siteManager.GetSiteCount())

	// 验证新站点配置
	sites := p.siteManager.GetAllSites()
	for siteID, site := range sites {
		p.logger.Infof("验证站点: %s -> %s", siteID, site.config.Name)
		if site.acl != nil {
			//p.logger.Debugf("  站点ACL已配置")
		}
		//p.logger.Debugf("  路由数量: %d", len(site.routes))
	}

	return nil
}

// createSite 创建站点
func (p *Proxy) createSite(siteConfig *config.SiteConfig) (*Site, error) {
	// 创建配置副本以避免指针共享问题（特别是在Go 1.20中）
	configCopy := *siteConfig

	// 创建站点ACL
	siteACL := acl.NewSiteACL(
		configCopy.ACL.Allow,
		configCopy.ACL.Deny,
		configCopy.ACL.AllowFile,
		configCopy.ACL.DenyFile,
		p.logger,
	)
	//p.logger.Debugf("创建新站点ACL实例: %s, 实例地址: %p, %s",
	//	configCopy.Name, siteACL, siteACL.GetDebugInfo())

	// 验证站点配置
	if err := config.ValidateSiteConfig(&configCopy); err != nil {
		return nil, fmt.Errorf("站点配置验证失败: %w", err)
	}

	// 按组分类上游服务??
	groups := make(map[string][]*config.UpstreamConfig)
	for i := range configCopy.Upstreams {
		upstream := &configCopy.Upstreams[i]
		groupName := upstream.LoadBalanceGroup
		groups[groupName] = append(groups[groupName], upstream)
	}

	// 为每个组创建负载均衡??
	loadBalancers := make(map[string]loadbalancer.LoadBalancer)
	for groupName, upstreams := range groups {
		// 如果监控器为nil，传递nil给负载均衡器（负载均衡器会处理这种情况）
		var healthChecker loadbalancer.HealthChecker
		if p.monitor != nil {
			healthChecker = p.monitor
		}
		lb, err := loadbalancer.NewLoadBalancer("round_robin", upstreams, healthChecker)
		if err != nil {
			return nil, fmt.Errorf("创建负载均衡器失败[%s]: %w", groupName, err)
		}
		loadBalancers[groupName] = lb
	}

	site := &Site{
		config:        &configCopy,
		acl:           siteACL,
		loadBalancers: loadBalancers,
	}

	// 编译路由规则
	if err := site.compileRoutes(); err != nil {
		return nil, fmt.Errorf("编译路由规则失败: %w", err)
	}



	return site, nil
}

// getUpstream 获取路由对应的上游服务器
func (s *Site) getUpstream(route *Route) (*config.UpstreamConfig, error) {
	groupName := route.config.Upstream

	lb, exists := s.loadBalancers[groupName]
	if !exists {
		return nil, fmt.Errorf("负载均衡组不存在: %s", groupName)
	}

	return lb.Next()
}

// compileRoutes 编译路由规则
func (s *Site) compileRoutes() error {
	s.routes = make([]*Route, 0, len(s.config.Routes))

	for i := range s.config.Routes {
		routeConfig := s.config.Routes[i] // 修复循环变量陷阱
		pattern, err := regexp.Compile(routeConfig.Pattern)
		if err != nil {
			return fmt.Errorf("编译路由模式失败 %s: %w", routeConfig.Pattern, err)
		}

		// 重写配置验证由新的处理器架构（ProxyProcessor）在运行时处理

		s.routes = append(s.routes, &Route{
			pattern: pattern,
			config:  &routeConfig,
		})
	}

	return nil
}

// Start 启动服务??
func (p *Proxy) Start() error {
	// 按端口分组站??
	portGroups := p.groupSitesByPort()

	// 为每个端口创建服务器
	for port, sites := range portGroups {
		// 修复Go 1.20循环变量陷阱：创建局部副本
		port := port
		sites := sites

		// 检查是否需要HTTPS
		needHTTPS := p.checkPortNeedHTTPS(port, sites)

		server := &http.Server{
			Addr:         fmt.Sprintf(":%d", port),
			Handler:      p.createPortHandler(sites),
			ReadTimeout:  p.config.Server.ReadTimeout,
			WriteTimeout: p.config.Server.WriteTimeout,
			IdleTimeout:  p.config.Server.IdleTimeout,
			ErrorLog:     p.createFilteredErrorLog(),
		}

		// 存储服务器引??
		p.servers[port] = server

		protocol := "HTTP"
		if needHTTPS {
			protocol = "HTTPS"
		}
		p.logger.Info("启动", protocol, "服务器，监听端口:", port, "站点:", p.getSiteNames(sites))

		// 启动服务??
		p.serverWg.Add(1)
		go func(srv *http.Server, port int, isHTTPS bool) {
			defer p.serverWg.Done()
			var err error
			if isHTTPS {
				// 使用优化的SSL管理??
				var sslConfig config.SSLConfig
				for _, site := range sites {
					if site.config.SSL.Enabled {
						sslConfig = site.config.SSL
						break
					}
				}

				srv.TLSConfig = p.sslManager.CreateTLSConfig(sslConfig)
				err = srv.ListenAndServeTLS("", "") // 证书由GetCertificate动态加??
			} else {
				err = srv.ListenAndServe()
			}
			if err != nil && err != http.ErrServerClosed {
				p.logger.Error("服务器启动失败，端口:", port, "错误:", err)
			}
		}(server, port, needHTTPS)
	}

	// 等待信号
	select {}
}

// checkPortNeedHTTPS 检查端口是否需要HTTPS
func (p *Proxy) checkPortNeedHTTPS(port int, sites []*Site) bool {
	for _, site := range sites {
		httpsPort := site.config.HTTPSPort
		if httpsPort == 0 {
			httpsPort = p.config.Server.HTTPSPort
		}
		if httpsPort == port && site.config.SSL.Enabled {
			return true
		}
	}
	return false
}

// getSSLConfigForPort 获取端口的SSL配置
func (p *Proxy) getSSLConfigForPort(port int, sites []*Site) *config.SSLConfig {
	for _, site := range sites {
		httpsPort := site.config.HTTPSPort
		if httpsPort == 0 {
			httpsPort = p.config.Server.HTTPSPort
		}
		if httpsPort == port && site.config.SSL.Enabled {
			return &site.config.SSL
		}
	}
	return nil
}

// groupSitesByPort 按端口分组站??
func (p *Proxy) groupSitesByPort() map[int][]*Site {
	portGroups := make(map[int][]*Site)
	// 使用map来跟踪每个站点在每个端口上是否已经添加，避免重复
	sitePortMap := make(map[string]map[int]bool)

	// 从站点管理器获取所有站点
	sites := p.siteManager.GetAllSites()
	//p.logger.Debugf("开始按端口分组站点，总站点数: %d", len(sites))

	for siteID, site := range sites {
		//p.logger.Debugf("处理站点: %s (ID: %s)", site.config.Name, siteID)
		if sitePortMap[siteID] == nil {
			sitePortMap[siteID] = make(map[int]bool)
		}

		// 处理HTTP端口
		httpPort := site.config.HTTPPort
		if httpPort == 0 {
			httpPort = p.config.Server.HTTPPort
		}
		if !sitePortMap[siteID][httpPort] {
			portGroups[httpPort] = append(portGroups[httpPort], site)
			sitePortMap[siteID][httpPort] = true
		}

		// 处理HTTPS端口（如果启用SSL??
		if site.config.SSL.Enabled {
			httpsPort := site.config.HTTPSPort
			if httpsPort == 0 {
				httpsPort = p.config.Server.HTTPSPort
			}
			// 只有当HTTPS端口与HTTP端口不同，且该站点在此端口上未添加时，才添加到HTTPS端口??
			if httpsPort != httpPort && !sitePortMap[siteID][httpsPort] {
				portGroups[httpsPort] = append(portGroups[httpsPort], site)
				sitePortMap[siteID][httpsPort] = true
			}
		}
	}

	return portGroups
}

// createPortHandler 为指定端口创建处理器
func (p *Proxy) createPortHandler(sites []*Site) http.Handler {
	// 使用 AllowQuerySemicolons 包装器来抑制分号分隔符警告
	return http.AllowQuerySemicolons(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		//p.logger.Debugf("createPortHandler收到请求: %s %s, Host=%s", r.Method, r.URL.Path, r.Host)

		// 查找匹配的站??
		site := p.findSiteForPort(r.Host, sites)
		if site == nil {
			//p.logger.Debugf("未找到站点配置: %s", r.Host)
			p.logger.Warn("未找到站点配置", r.Host)
			http.Error(w, "Site Not Found", http.StatusNotFound)
			return
		}

		//p.logger.Debugf("找到站点: %s", site.config.SiteID)
		// 使用新的处理器架构
		p.handleRequestWithProcessor(w, r, site)
	}))
}

// findSiteForPort 在指定端口的站点中查找匹配的站点
func (p *Proxy) findSiteForPort(host string, sites []*Site) *Site {
	//p.logger.Debugf("findSiteForPort 被调用，Host=%s，可用站点数=%d", host, len(sites))
	// 移除端口??
	if colonIndex := strings.Index(host, ":"); colonIndex != -1 {
		host = host[:colonIndex]
	}
	//p.logger.Debugf("处理后的Host=%s", host)

	// 首先尝试精确匹配域名
	for _, site := range sites {
		//p.logger.Debugf("检查站点: %s (ID: %s)，域名: %v", site.config.Name, site.config.SiteID, site.config.Domains)
		for _, domain := range site.config.Domains {
			if strings.ContainsAny(domain, "*?") {
				reg := domainWildcardToRegexp(domain)
				if matched, _ := regexp.MatchString(reg, host); matched {
					//p.logger.Debugf("通配符匹配成功: %s -> %s", host, site.config.SiteID)
					return site
				}
			} else {
				if domain == host {
					//p.logger.Debugf("精确匹配成功: %s -> %s", host, site.config.SiteID)
					return site
				}
			}
		}
	}

	// 如果没有找到匹配的站点，查找默认站点
	for _, site := range sites {
		if site.config.DefaultSite {
			//p.logger.Debugf("使用默认站点: %s (ID: %s) 处理请求 Host: %s", site.config.Name, site.config.SiteID, host)
			return site
		}
	}

	return nil
}

// getSiteNames 获取站点名称列表
func (p *Proxy) getSiteNames(sites []*Site) []string {
	names := make([]string, len(sites))
	for i, site := range sites {
		names[i] = site.config.SiteID
	}
	return names
}

// updateCacheManagerSiteMapping 更新缓存管理器的站点名称映射
func (p *Proxy) updateCacheManagerSiteMapping(sites []config.SiteConfig) {
	if p.cache == nil {
		return
	}

	count := 0
	for _, site := range sites {
		p.cache.UpdateSiteNameMapping(site.SiteID, site.Name)
		count++
	}

	p.logger.Debugf("更新缓存管理器站点名称映射: %d 个站点", count)
}












// handleRequestWithProcessor 使用新处理器架构处理请求
func (p *Proxy) handleRequestWithProcessor(w http.ResponseWriter, r *http.Request, site *Site) {
	startTime := time.Now()

	// 创建请求上下文
	req := processor.NewRequestContext(r, site.config.SiteID)
	req.StartTime = startTime
	req.ClientIP = p.getClientIP(r)
	req.UserAgent = r.Header.Get("User-Agent")
	req.Referer = r.Header.Get("Referer")

	// 设置站点信息（避免RouteProcessor重新查找）
	req.Site = &processor.SiteInfo{
		SiteID:         site.config.SiteID,
		Name:           site.config.Name,
		Domains:        site.config.Domains,
		DefaultSite:    site.config.DefaultSite,
		ErrorPages:     site.config.ErrorPages,
		Headers:        site.config.Headers,
		DenyTypes:      site.config.DenyTypes,
		DenyURLs:       site.config.DenyURLs,
		CacheHeaders:   site.config.CacheHeaders,
		BandwidthLimit: site.config.BandwidthLimit,
		MaxConnections: site.config.MaxConnections,
		Minify:         site.config.Minify,
		Rules:          site.config.Rules,     // 新增：站点级缓存规则
		StatusTTL:      site.config.StatusTTL, // 新增：站点级状态码TTL
		Config:         site.config,           // 新增：完整配置引用
	}

	// 创建响应上下文
	resp := processor.NewResponseContext()

	// 执行处理器链
	if err := p.processorManager.ProcessRequest(req, resp); err != nil {
		p.logger.Errorf("处理器链执行失败: %v", err)
		// 错误已经在处理器管理器中处理过了
	}

	// 写入响应
	p.writeResponse(w, resp)

	// 记录访问日志
	p.logAccess(r, site, resp.StatusCode, resp.ContentLength, startTime, resp.ContentType)

	// 监控记录
	if p.monitor != nil {
		// RecordRequest(siteID string, status int, bytesReceived, bytesSent int64)
		// 这里需要计算请求大小和响应大小，而不是请求时间
		var reqSize int64 = 0
		if r.ContentLength > 0 {
			reqSize = r.ContentLength
		}
		// 估算请求头大小
		reqSize += int64(len(r.Method) + len(r.URL.String()) + len(r.Proto))
		for k, v := range r.Header {
			reqSize += int64(len(k) + len(strings.Join(v, ",")))
		}

		respSize := resp.ContentLength
		p.monitor.RecordRequest(site.config.SiteID, resp.StatusCode, reqSize, respSize)
		p.logger.Debugf("站点流量已记录: %s, 状态码: %d, 请求: %d bytes, 响应: %d bytes",
			site.config.SiteID, resp.StatusCode, reqSize, respSize)
	}
}

// writeResponse 写入响应到客户端
func (p *Proxy) writeResponse(w http.ResponseWriter, resp *processor.ResponseContext) {
	// 设置响应头
	for key, values := range resp.Headers {
		for _, value := range values {
			w.Header().Add(key, value)
		}
	}

	// 设置状态码
	if resp.StatusCode > 0 {
		w.WriteHeader(resp.StatusCode)
	}

	// 确定要发送的响应体数据
	var bodyToSend []byte
	if resp.UseMemoryResponse && resp.ResponseBody != nil && len(resp.ResponseBody) > 0 {
		// 使用优化的内存响应数据
		bodyToSend = resp.ResponseBody
		p.logger.Debugf("使用优化的内存响应数据: %d bytes", len(bodyToSend))
	} else {
		// 使用原始响应体数据
		bodyToSend = resp.Body
		if len(bodyToSend) > 0 {
			p.logger.Debugf("使用原始响应体数据: %d bytes", len(bodyToSend))
		}
	}

	// 写入响应体
	if len(bodyToSend) > 0 {
		w.Write(bodyToSend)
	}
}

// findSite 查找站点
func (p *Proxy) findSite(host string) *Site {
	// 移除端口??
	if colonIndex := strings.Index(host, ":"); colonIndex != -1 {
		host = host[:colonIndex]
	}

	// 使用站点管理器查找站点
	return p.siteManager.FindSiteByDomain(host)
}

// findRoute 查找路由
func (s *Site) findRoute(path string, r *http.Request) *Route {
	fmt.Printf("[ROUTE] Finding route for path=%s method=%s host=%s\n", path, r.Method, r.Host)
	for i, route := range s.routes {
		fmt.Printf("[ROUTE] Checking route %d: pattern=%s cache=%v\n", i, route.pattern.String(), route.config.Cache)
		if route.pattern.MatchString(path) {
			fmt.Printf("[ROUTE] Pattern matched! Checking conditions...\n")
			// 检查是否有匹配条件
			if route.config.MatchConditions != nil {
				if !s.checkRouteMatchConditions(r, route.config.MatchConditions) {
					fmt.Printf("[ROUTE] Conditions not met, continuing...\n")
					continue // 匹配条件不满足，继续下一个路由
				}
			}
			fmt.Printf("[ROUTE MATCH] path=%s pattern=%s cache=%v method=%s\n", path, route.pattern.String(), route.config.Cache, r.Method)
			return route
		}
	}
	fmt.Printf("[ROUTE NOT FOUND] path=%s method=%s\n", path, r.Method)
	return nil
}

// checkRouteMatchConditions 检查路由匹配条??
func (s *Site) checkRouteMatchConditions(r *http.Request, matchConfig *config.RouteMatchConfig) bool {
	// 检查HTTP方法
	if len(matchConfig.Methods) > 0 {
		methodMatched := false
		for _, method := range matchConfig.Methods {
			if strings.EqualFold(r.Method, method) {
				methodMatched = true
				break
			}
		}
		if !methodMatched {
			return false
		}
	}

	// 检查Content-Type
	if len(matchConfig.ContentType) > 0 {
		contentType := r.Header.Get("Content-Type")
		contentTypeMatched := false
		for _, ct := range matchConfig.ContentType {
			if matched, _ := regexp.MatchString(ct, contentType); matched {
				contentTypeMatched = true
				break
			}
		}
		if !contentTypeMatched {
			return false
		}
	}

	// 检查User-Agent
	if len(matchConfig.UserAgents) > 0 {
		userAgent := r.Header.Get("User-Agent")
		userAgentMatched := false
		for _, pattern := range matchConfig.UserAgents {
			if matched, _ := regexp.MatchString(pattern, userAgent); matched {
				userAgentMatched = true
				break
			}
		}
		if !userAgentMatched {
			return false
		}
	}

	// 检查HTTP头部
	for _, headerMatch := range matchConfig.Headers {
		if !s.checkRouteHeaderMatch(r, &headerMatch) {
			return false
		}
	}

	// 检查Cookie
	for _, cookieMatch := range matchConfig.Cookies {
		if !s.checkRouteCookieMatch(r, &cookieMatch) {
			return false
		}
	}

	// 检查查询参??
	for _, queryMatch := range matchConfig.QueryParams {
		if !s.checkRouteQueryMatch(r, &queryMatch) {
			return false
		}
	}

	return true
}

// checkRouteHeaderMatch 检查路由HTTP头部匹配
func (s *Site) checkRouteHeaderMatch(r *http.Request, match *config.RouteHeaderMatch) bool {
	headerValue := r.Header.Get(match.Name)

	// 如果只检查存在??
	if match.Exists {
		return headerValue != ""
	}

	// 如果头部不存??
	if headerValue == "" {
		return false
	}

	// 精确值匹??
	if match.Value != "" {
		return headerValue == match.Value
	}

	// 正则模式匹配
	if match.Pattern != "" {
		if matched, _ := regexp.MatchString(match.Pattern, headerValue); matched {
			return true
		}
	}

	return false
}

// checkRouteCookieMatch 检查路由Cookie匹配
func (s *Site) checkRouteCookieMatch(r *http.Request, match *config.RouteCookieMatch) bool {
	// 如果指定了名称模式，遍历所有cookie查找匹配的名??
	if match.NamePattern != "" {
		for _, cookie := range r.Cookies() {
			if matched, _ := regexp.MatchString(match.NamePattern, cookie.Name); matched {
				// 找到匹配名称的cookie
				if match.Exists {
					return true // 只检查存在??
				}

				cookieValue := cookie.Value

				// 精确值匹??
				if match.Value != "" {
					if cookieValue == match.Value {
						return true
					}
					continue // 继续检查其他匹配名称的cookie
				}

				// 正则模式匹配
				if match.Pattern != "" {
					if matched, _ := regexp.MatchString(match.Pattern, cookieValue); matched {
						return true
					}
					continue // 继续检查其他匹配名称的cookie
				}

				// 如果没有值匹配条件，只要名称匹配就返回true
				return true
			}
		}
		return false // 没有找到匹配名称的cookie
	}

	// 原有的精确名称匹配逻辑
	cookie, err := r.Cookie(match.Name)

	// 如果只检查存在??
	if match.Exists {
		return err == nil
	}

	// 如果Cookie不存??
	if err != nil {
		return false
	}

	cookieValue := cookie.Value

	// 精确值匹??
	if match.Value != "" {
		return cookieValue == match.Value
	}

	// 正则模式匹配
	if match.Pattern != "" {
		if matched, _ := regexp.MatchString(match.Pattern, cookieValue); matched {
			return true
		}
	}

	return false
}

// checkRouteQueryMatch 检查路由查询参数匹??
func (s *Site) checkRouteQueryMatch(r *http.Request, match *config.RouteQueryMatch) bool {
	queryValue := r.URL.Query().Get(match.Name)

	// 如果只检查存在??
	if match.Exists {
		return queryValue != ""
	}

	// 如果参数不存??
	if queryValue == "" {
		return false
	}

	// 精确值匹??
	if match.Value != "" {
		return queryValue == match.Value
	}

	// 正则模式匹配
	if match.Pattern != "" {
		if matched, _ := regexp.MatchString(match.Pattern, queryValue); matched {
			return true
		}
	}

	return false
}

// getClientIP 获取客户端IP（用于日志记录等??
func (p *Proxy) getClientIP(r *http.Request) string {
	// 检查X-Forwarded-For??
	if xff := r.Header.Get("X-Forwarded-For"); xff != "" {
		if commaIndex := strings.Index(xff, ","); commaIndex != -1 {
			return strings.TrimSpace(xff[:commaIndex])
		}
		return strings.TrimSpace(xff)
	}

	// 检查X-Real-IP??
	if xri := r.Header.Get("X-Real-IP"); xri != "" {
		return strings.TrimSpace(xri)
	}

	// 使用远程地址
	host, _, err := net.SplitHostPort(r.RemoteAddr)
	if err != nil {
		return r.RemoteAddr
	}
	return host
}

// getRealClientIP 获取真实的网络层客户端IP（用于ACL等安全检查）
// 注意：此函数只返回真实的网络连接IP，不使用HTTP头部，防止IP伪造攻??
func (p *Proxy) getRealClientIP(r *http.Request) string {
	// 直接使用网络层的真实客户端地址，不信任HTTP头部
	host, _, err := net.SplitHostPort(r.RemoteAddr)
	if err != nil {
		return r.RemoteAddr
	}
	return host
}

// generateCacheKey 生成缓存??
func (p *Proxy) generateCacheKey(r *http.Request) string {
	return fmt.Sprintf("%s:%s", r.Method, r.URL.Path)
}











// shouldCompressContent 判断是否应该压缩内容
func (p *Proxy) shouldCompressContent(contentType string, size int) bool {
	compressionConfig := p.config.Compression
	if !compressionConfig.Enabled {
		return false
	}

	// 检查最小大小限制
	if size < compressionConfig.MinSize.Int() {
		return false
	}

	// 检查最大大小限制
	if compressionConfig.MaxSize.Int() > 0 && size > compressionConfig.MaxSize.Int() {
		return false
	}

	// 检查内容类型
	if len(compressionConfig.Types) == 0 {
		// 默认压缩类型
		return strings.Contains(contentType, "text/") ||
			strings.Contains(contentType, "application/json") ||
			strings.Contains(contentType, "application/javascript") ||
			strings.Contains(contentType, "application/xml")
	}

	for _, t := range compressionConfig.Types {
		if strings.Contains(contentType, t) {
			return true
		}
	}

	return false
}

// logAccess 记录访问日志
func (p *Proxy) logAccess(r *http.Request, site *Site, status int, bodySize int64, startTime time.Time, sentHttpContentType string) {
	duration := time.Since(startTime)

	// 如果是健康检查请求，则完全不记录访问日志
	if r.Header.Get("Referer") == "HealthCheck" {
		return
	}

	// 健康检查请求识别：User-Agent包含HealthCheck或Referer为健康检查URL
	ua := r.Header.Get("User-Agent")
	referer := r.Header.Get("Referer")
	isHealthCheck := strings.Contains(strings.ToLower(ua), "healthcheck") || strings.Contains(strings.ToLower(referer), "health")

	// 客户端真实IP逻辑
	clientRealIp := "-"
	if xff := r.Header.Get("X-Forwarded-For"); xff != "" {
		if commaIndex := strings.Index(xff, ","); commaIndex != -1 {
			clientRealIp = strings.TrimSpace(xff[:commaIndex])
		} else {
			clientRealIp = strings.TrimSpace(xff)
		}
	} else {
		clientRealIp = p.getClientIP(r)
	}

	// 本机监听IP
	serverAddr := "-"
	if localAddr, ok := r.Context().Value(http.LocalAddrContextKey).(net.Addr); ok {
		addr := localAddr.String()
		if colon := strings.Index(addr, ":"); colon != -1 {
			serverAddr = addr[:colon]
		} else {
			serverAddr = addr
		}
	}

	// scheme
	scheme := "http"
	if r.TLS != nil {
		scheme = "https"
	}

	// 响应Content-Type（需传递resp参数，暂????
	// sentHttpContentType = "-"
	// request_uri
	requestUri := r.RequestURI

	fields := logrus.Fields{
		"site":                   site.config.SiteID,
		"webalias":               site.config.SiteID,
		"remote_addr":            p.getClientIP(r),
		"clientRealIp":           clientRealIp,
		"server_addr":            serverAddr,
		"fmt_localtime":          time.Now().Format("2006-01-02 15:04:05"),
		"scheme":                 scheme,
		"server_protocol":        r.Proto,
		"request_method":         r.Method,
		"host":                   r.Host,
		"status":                 fmt.Sprintf("%d", status),
		"sent_http_content_type": sentHttpContentType,
		"body_bytes_sent":        fmt.Sprintf("%d", bodySize),
		"request_uri":            requestUri,
		"http_referer":           referer,
		"http_user_agent":        ua,
		"mysvrip":                "-", // 预留ipgeo
		"ipgeo":                  "-", // 预留ipgeo
		"biaoji":                 "(((nginx)))",
		"request":                fmt.Sprintf("%s %s %s", r.Method, r.URL.String(), r.Proto),
		"request_time":           duration.Seconds(),
	}

	// 使用站点配置的日志格??
	logFormat := site.config.LogFormat
	if logFormat == "" {
		logFormat = p.config.Log.Format
	}

	// 日志目标选择：优先站点log_targets，否则全局
	var l loggerpkg.LoggerInterface
	if lg, ok := p.loggers[site.config.SiteID]; ok {
		l = lg
	} else {
		l = p.globalLogger
	}

	// 健康检查请求只写入应用日志，不输出到控制台
	if isHealthCheck {
		l.Log(fields, logFormat)
		return
	}

	// 非健康检查请求，正常写日??
	l.Log(fields, logFormat)
	// 你可以在这里加控制台输出（如fmt.Println），但健康检查已被过??
}

// Shutdown 关闭服务??
func (p *Proxy) Shutdown() error {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	var errs []error

	// 关闭所有服务器
	for port, server := range p.servers {
		if err := server.Shutdown(ctx); err != nil {
			errs = append(errs, fmt.Errorf("关闭服务器失败，端口 %d: %w", port, err))
		}
	}

	// 等待所有服务器关闭
	p.serverWg.Wait()

	// 关闭ACL
	if p.acl != nil {
		p.acl.Close()
	}

	// 关闭缓存
	if p.cache != nil {
		if err := p.cache.Close(); err != nil {
			errs = append(errs, fmt.Errorf("关闭缓存失败: %w", err))
		}
	}

	if len(errs) > 0 {
		return fmt.Errorf("关闭服务器时发生错误: %v", errs)
	}

	return nil
}

// 新增：限速Writer
type rateLimitWriter struct {
	w    http.ResponseWriter
	rate int64 // bytes/sec
}

func NewRateLimitWriter(w http.ResponseWriter, rate int64) http.ResponseWriter {
	return &rateLimitWriter{w: w, rate: rate}
}

func (rlw *rateLimitWriter) Header() http.Header        { return rlw.w.Header() }
func (rlw *rateLimitWriter) WriteHeader(statusCode int) { rlw.w.WriteHeader(statusCode) }
func (rlw *rateLimitWriter) Write(p []byte) (int, error) {
	chunk := 32 * 1024 // 32KB
	if rlw.rate > 0 && rlw.rate < int64(chunk) {
		chunk = int(rlw.rate)
	}
	total := 0
	for len(p) > 0 {
		n := chunk
		if len(p) < n {
			n = len(p)
		}
		written, err := rlw.w.Write(p[:n])
		total += written
		if err != nil {
			return total, err
		}
		p = p[n:]
		if rlw.rate > 0 {
			time.Sleep(time.Duration(int64(time.Second) * int64(n) / rlw.rate))
		}
	}
	return total, nil
}

// 新增：通配符转正则（用于域名匹配）
func domainWildcardToRegexp(pattern string) string {
	// 只允????通配??
	re := regexp.MustCompile(`([.\\+()|{}\[\]^$])`)
	pattern = re.ReplaceAllString(pattern, `\\$1`)
	pattern = strings.ReplaceAll(pattern, "*", ".*")
	pattern = strings.ReplaceAll(pattern, "?", ".")
	return "^" + pattern + "$"
}

// 用于文件名通配符匹??
func fileWildcardToRegexp(pattern string) string {
	pattern = strings.ReplaceAll(pattern, ".", "\\.")
	pattern = strings.ReplaceAll(pattern, "*", ".*")
	pattern = strings.ReplaceAll(pattern, "?", ".")
	return "^" + pattern + "$"
}

// 新增：自定义目录索引渲染，支持hidden_in_listing
func (p *Proxy) serveCustomDirListing(w http.ResponseWriter, r *http.Request, route *Route) {
	// 修复：正确处理正则表达式模式，移除^??字符
	patternForStrip := strings.TrimPrefix(strings.TrimSuffix(route.config.Pattern, "$"), "^")
	dirPath := filepath.Join(route.config.StaticDir, strings.TrimPrefix(r.URL.Path, patternForStrip))
	entries, err := os.ReadDir(dirPath)
	if err != nil {
		http.Error(w, "无法读取目录", http.StatusInternalServerError)
		return
	}
	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	_, _ = w.Write([]byte("<html><head><title>Index of " + r.URL.Path + "</title></head><body>"))
	_, _ = w.Write([]byte("<h1>Index of " + r.URL.Path + "</h1><ul>"))
	// 上级目录
	if r.URL.Path != "/" {
		parent := filepath.Dir(strings.TrimRight(r.URL.Path, "/"))
		if parent == "." {
			parent = "/"
		}
		_, _ = w.Write([]byte(`<li><a href="` + parent + `/">../</a></li>`))
	}
	// 预处理隐藏规则（通配符转正则??
	hiddenRegexps := make([]*regexp.Regexp, 0, len(route.config.HiddenInListing))
	for _, rule := range route.config.HiddenInListing {
		reg := fileWildcardToRegexp(rule)
		if re, err := regexp.Compile(reg); err == nil {
			hiddenRegexps = append(hiddenRegexps, re)
		}
	}
	// 递归生效：用相对static_dir的路径匹??
	relDir, _ := filepath.Rel(route.config.StaticDir, dirPath)
	if relDir == "." {
		relDir = ""
	}
	for _, entry := range entries {
		name := entry.Name()
		// 生成相对static_dir的路??
		relPath := filepath.ToSlash(filepath.Join(relDir, name))
		if !strings.HasPrefix(relPath, "/") {
			relPath = "/" + relPath
		}
		hide := false
		for _, re := range hiddenRegexps {
			if re.MatchString(relPath) || re.MatchString(name) {
				hide = true
				break
			}
		}
		if hide {
			continue
		}
		link := name
		if entry.IsDir() {
			link += "/"
		}
		_, _ = w.Write([]byte(`<li><a href="` + link + `">` + link + `</a></li>`))
	}
	_, _ = w.Write([]byte("</ul></body></html>"))
}

// 渲染自定义错误页面，优先级：路由级 > 站点级 > 全局级
func renderErrorPage(w http.ResponseWriter, code int, site *Site, route *Route) bool {
	var pages []config.ErrorPageConfig



	// 优先级1：路由级
	if route != nil && len(route.config.ErrorPages) > 0 {
		pages = route.config.ErrorPages
	} else if site != nil && len(site.config.ErrorPages) > 0 {
		// 优先级2：站点级
		pages = site.config.ErrorPages
	} else if globalConfig := config.GlobalConfig(); globalConfig != nil && len(globalConfig.ErrorPages) > 0 {
		// 优先级3：全局级
		pages = globalConfig.ErrorPages
	}
	for _, ep := range pages {
		if ep.Code == code {
			// 设置头部信息，但先不调用WriteHeader
			if ep.File != "" {
				b, err := os.ReadFile(ep.File)
				if err == nil {
					w.Header().Set("Content-Type", "text/html; charset=utf-8")
					w.WriteHeader(code)
					w.Write(b)
					return true
				} else {
					// 记录文件读取失败的警告，但继续尝试使用content
					// 注意：这里无法直接访问logger，需要通过其他方式记录日志
				}
			}
			if ep.Content != "" {
				w.Header().Set("Content-Type", "text/plain; charset=utf-8")
				w.Header().Set("X-Debug-Error-Page-Render", "CUSTOM-CONTENT-TYPE")
				w.WriteHeader(code)
				w.Write([]byte(ep.Content))
				return true
			}
			// 如果找到了匹配的错误页面配置但文件读取失败且没有content，
			// 则返回false让调用方处理默认错误页面
		}
	}
	return false
}

// 新增：responseBufferWriter用于捕获renderErrorPage输出
type responseBufferWriter struct {
	buf     *bytes.Buffer
	headers http.Header
}

func (w *responseBufferWriter) Header() http.Header         { return w.headers }
func (w *responseBufferWriter) WriteHeader(statusCode int)  {}
func (w *responseBufferWriter) Write(p []byte) (int, error) { return w.buf.Write(p) }

// SetMonitor 设置监控??
func (p *Proxy) SetMonitor(m *monitor.Monitor) {
	p.monitor = m
	// 为所有站点添加监??
	sites := p.siteManager.GetAllSites()
	for _, site := range sites {
		p.monitor.AddSite(site.config.SiteID, site.config.Name)
	}
	// 设置缓存管理器到监控??
	if p.cache != nil {
		p.monitor.SetCacheManager(p.cache)
	}
	// 设置内存缓存管理器到监控??
	if p.memoryCache != nil {
		p.monitor.SetMemoryCache(p.memoryCache)
	}
	// 设置配置热重载监控器到监控器
	if p.configWatcher != nil {
		p.monitor.SetConfigWatcher(p.configWatcher)
	}
	// 设置代理实例到监控器（用于获取高性能统计??
	p.monitor.SetProxy(p)

	// 设置监控器到处理器管理器
	if p.processorManager != nil {
		p.processorManager.SetMonitor(m)
		p.logger.Info("监控器已设置到处理器管理器")
	}
}

// GetConfigManager 获取配置管理??
func (p *Proxy) GetConfigManager() *config.ConfigManager {
	return p.configManager
}

// GetViperManager 获取Viper配置管理器
func (p *Proxy) GetViperManager() *config.ViperIntegration {
	return p.viperManager
}

// IsUsingViperManager 检查是否使用Viper配置管理器
func (p *Proxy) IsUsingViperManager() bool {
	return p.viperManager != nil
}

// DisableViperHotReload 禁用Viper热重载
func (p *Proxy) DisableViperHotReload() {
	if p.viperManager != nil {
		p.viperManager.DisableHotReload()
	}
}

// findIndexFile 查找目录中的默认首页文件
func (p *Proxy) findIndexFile(dirPath string, indexFiles []string) string {
	// 如果没有配置默认首页文件，使用默认列表
	if len(indexFiles) == 0 {
		indexFiles = []string{"index.html", "index.htm", "default.html", "default.htm", "index.php"}
	}

	for _, indexFile := range indexFiles {
		fullPath := filepath.Join(dirPath, indexFile)
		if fi, err := os.Stat(fullPath); err == nil && !fi.IsDir() {
			return fullPath
		}
	}

	return ""
}

// 新增：捕获响应体、状态码和响应头的Writer
type captureWriter struct {
	http.ResponseWriter
	buf     *bytes.Buffer
	status  *int
	headers http.Header
	site    *Site  // 添加站点信息用于错误页面处理
	route   *Route // 添加路由信息用于错误页面处理
}



func (w *captureWriter) WriteHeader(code int) {
	if w.status != nil {
		*w.status = code
	}
	// 捕获响应??
	if w.headers == nil {
		w.headers = make(http.Header)
	}
	for k, vv := range w.Header() {
		for _, v := range vv {
			w.headers.Add(k, v)
		}
	}
	//debugHeaders, _ := json.Marshal(w.Header())
	//fmt.Printf("[DEBUG][WRITE HEADER] 状态码:%d 响应??%s\n", code, debugHeaders)
	w.ResponseWriter.WriteHeader(code)
}

func (w *captureWriter) Write(p []byte) (int, error) {
	w.buf.Write(p)
	return w.ResponseWriter.Write(p)
}

// 新增：缓存响应结构体
// 支持gob序列??
type CachedResponse struct {
	Status       int
	Header       map[string][]string
	Body         []byte
	IsGzipped    bool // 标记Body是否为gzip压缩数据
	OriginalSize int  // 原始未压缩大小（用于统计??
}

// getCacheTTL 获取缓存TTL（站点级规则 > 全局规则 > 站点级状态码 > 全局状态码??
func (p *Proxy) getCacheTTL(site *Site, statusCode int, cacheKey string) time.Duration {
	// 1. 最优先：检查站点级缓存规则TTL
	if p.cache != nil && site != nil && len(site.config.Rules) > 0 {
		if ruleTTL, found := p.cache.GetSiteRuleTTL(cacheKey, site.config.Rules); found {
			//p.logger.Debugf("使用站点级缓存规则TTL: %v，key=%s", ruleTTL, cacheKey)
			return ruleTTL
		}
	}

	// 2. 检查全局缓存规则TTL
	if p.cache != nil {
		if ruleTTL, found := p.cache.GetRuleTTL(cacheKey); found {
			//p.logger.Debugf("使用全局缓存规则TTL: %v", ruleTTL)
			return ruleTTL
		}
	}

	statusStr := fmt.Sprintf("%d", statusCode)

	// 3. 使用站点级状态码TTL
	if site != nil && site.config.StatusTTL != nil {
		if ttl, exists := site.config.StatusTTL[statusStr]; exists {
			//p.logger.Debugf("使用站点级状态码TTL: %d -> %v", statusCode, ttl)
			return ttl
		}
		if defaultTTL, exists := site.config.StatusTTL["default"]; exists {
			//p.logger.Debugf("使用站点级默认TTL: %v", defaultTTL)
			return defaultTTL
		}
	}

	// 4. 使用全局状态码TTL
	if p.config.Cache.StatusTTL != nil {
		if ttl, exists := p.config.Cache.StatusTTL[statusStr]; exists {
			//p.logger.Debugf("使用全局状态码TTL: %d -> %v", statusCode, ttl)
			return ttl
		}
		if defaultTTL, exists := p.config.Cache.StatusTTL["default"]; exists {
			//p.logger.Debugf("使用全局默认TTL: %v", defaultTTL)
			return defaultTTL
		}
	}

	// 5. 最后使用全局基础TTL
	//p.logger.Debugf("使用全局基础TTL: %v", p.config.Cache.TTL)
	return p.config.Cache.TTL
}



// getCompressionPriority 获取压缩算法的优先级（数字越小优先级越高）
func getCompressionPriority(algorithm string, configuredAlgorithms []string) int {
	for i, alg := range configuredAlgorithms {
		if alg == algorithm {
			return i + 1 // 1-based priority
		}
	}

	// 如果不在配置中，使用默认优先级
	defaultPriorities := map[string]int{
		"zstd":    1,
		"br":      2,
		"gzip":    3,
		"deflate": 4,
	}

	if priority, exists := defaultPriorities[algorithm]; exists {
		return priority + 100 // 低优先级，因为不在配置中
	}

	return 999 // 未知算法，最低优先级
}

// getCompressionTypeFromString 从字符串获取压缩类型
func getCompressionTypeFromString(algorithm string) compression.CompressionType {
	switch algorithm {
	case "zstd":
		return compression.CompressionZstd
	case "br":
		return compression.CompressionBrotli
	case "gzip":
		return compression.CompressionGzip
	case "deflate":
		return compression.CompressionDeflate
	default:
		return compression.CompressionNone
	}
}

// decompressData 解压数据
func (p *Proxy) decompressData(data []byte, compressionType string) ([]byte, error) {
	switch compressionType {
	case "zstd":
		decoder, err := zstd.NewReader(bytes.NewReader(data))
		if err != nil {
			return nil, err
		}
		defer decoder.Close()
		return io.ReadAll(decoder)

	case "br":
		reader := brotli.NewReader(bytes.NewReader(data))
		return io.ReadAll(reader)

	case "gzip":
		reader, err := gzip.NewReader(bytes.NewReader(data))
		if err != nil {
			return nil, err
		}
		defer reader.Close()
		return io.ReadAll(reader)

	case "deflate":
		reader := flate.NewReader(bytes.NewReader(data))
		defer reader.Close()
		return io.ReadAll(reader)

	case "none":
		return data, nil

	default:
		return data, nil
	}
}



// onGlobalConfigChanged 全局设置变更回调
func (p *Proxy) onGlobalConfigChanged(oldSettings, newSettings *config.GlobalSettings) error {
	p.logger.Info("开始处理全局设置变更...")

	// 更新全局ACL（简化比较，直接重新创建??
	p.logger.Debug("更新全局ACL配置")
	if p.acl != nil {
		p.acl.Close()
	}
	p.acl = acl.NewACL(
		newSettings.ACL.GlobalAllow,
		newSettings.ACL.GlobalDeny,
		newSettings.ACL.AllowFile,
		newSettings.ACL.DenyFile,
		newSettings.ACL.ReloadInterval,
		p.logger,
	)

	// 更新限流配置（简化比较，直接重新创建??
	p.logger.Debug("更新限流配置")
	if newSettings.RateLimit.Enabled {
		p.rateLimiter = ratelimit.NewRateLimitManager(ratelimit.RateLimitConfig{
			GlobalRPS: newSettings.RateLimit.GlobalRPS,
			IPRPS:     newSettings.RateLimit.IPRPS,
			SiteRPS:   newSettings.RateLimit.SiteRPS,
			Burst:     newSettings.RateLimit.Burst,
		})
	} else {
		p.rateLimiter = nil
	}

	// 更新缓存配置（简化比较，直接重新创建??
	p.logger.Debug("更新缓存配置")
	if p.cache != nil {
		p.cache.Close()
	}
	if newSettings.Cache.Enabled {
		cacheManager, err := cache.NewCacheManager(newSettings.Cache)
		if err != nil {
			return fmt.Errorf("更新缓存配置失败: %w", err)
		}
		p.cache = cacheManager
	} else {
		p.cache = nil
	}

	// 更新主配置引用（从配置管理器获取完整配置??
	p.mu.Lock()
	p.config = p.configManager.GetCurrentConfig()
	p.mu.Unlock()

	// 更新监控器配置
	if p.monitor != nil {
		p.monitor.UpdateConfig(p.config)
		p.logger.Debug("监控器配置已在全局配置变更回调中更新")
	}

	p.logger.Info("全局设置变更处理完成")
	return nil
}

// onBatchSiteConfigChanged 批量站点配置变更回调
func (p *Proxy) onBatchSiteConfigChanged(sites []config.SiteConfig) error {
	p.logger.Infof("开始批量站点配置变更处理，站点数量: %d", len(sites))

	// 使用站点管理器重新加载所有站点
	if err := p.siteManager.LoadSites(sites); err != nil {
		return fmt.Errorf("站点管理器重载站点失败: %w", err)
	}

	// 更新缓存管理器的站点名称映射
	if p.cache != nil {
		p.updateCacheManagerSiteMapping(sites)
	}

	// 更新服务器处理器
	if err := p.updateServerHandlers(); err != nil {
		p.logger.Warnf("更新服务器处理器失败: %v", err)
	}

	p.logger.Infof("批量站点配置变更处理完成，当前站点数量: %d", p.siteManager.GetSiteCount())
	return nil
}

// onSiteConfigChanged 站点配置变更回调（保留用于单个站点更新）
func (p *Proxy) onSiteConfigChanged(siteName string, oldConfig, newConfig *config.SiteConfig) error {
	// 注意：站点管理器已经处理了站点的创建、更新和删除
	// 这个回调主要负责更新服务器处理器和清理资源

	if newConfig == nil {
		// 站点被删除
		p.logger.Infof("站点配置回调：删除站点 %s", siteName)

		// 清理站点相关资源（使用site_id作为键）
		var keyToDelete string
		if oldConfig != nil {
			keyToDelete = oldConfig.SiteID
		} else {
			keyToDelete = siteName // 回退到siteName
		}

		// 清理日志器
		if logger, exists := p.loggers[keyToDelete]; exists {
			if closer, ok := logger.(interface{ Close() error }); ok {
				closer.Close()
			}
			delete(p.loggers, keyToDelete)
		}

		// 清理熔断器
		delete(p.circuitBreakers, keyToDelete)

		// 清理连接限制
		if oldConfig != nil {
			p.connectionLimiter.SetSiteLimit(oldConfig.SiteID, 0)
		}
	} else {
		// 站点被添加或更新
		if oldConfig == nil {
			p.logger.Infof("站点配置回调：添加新站点 %s (ID: %s)", newConfig.Name, newConfig.SiteID)
		} else {
			p.logger.Infof("站点配置回调：更新站点 %s (ID: %s)", newConfig.Name, newConfig.SiteID)

			// 检查站点名称是否发生变更
			if oldConfig.Name != newConfig.Name {
				p.logger.Infof("检测到站点名称变更: %s -> %s", oldConfig.Name, newConfig.Name)

				// 通知监控系统更新站点显示名称
				if p.monitor != nil {
					p.monitor.UpdateSiteName(newConfig.SiteID, newConfig.Name)
				}
			}
		}
	}

	// 更新服务器处理器以使用新的站点实例
	if err := p.updateServerHandlers(); err != nil {
		p.logger.Warnf("更新服务器处理器失败: %v", err)
	}

	p.logger.Infof("站点配置变更回调处理完成: %s", siteName)
	return nil
}

// UpdateUpstreamHealth 更新上游服务器健康状态
func (p *Proxy) UpdateUpstreamHealth(upstream *config.UpstreamConfig, healthy bool) {
	p.mu.RLock()
	defer p.mu.RUnlock()

	// 健康状态现在完全由监控器管理，负载均衡器会直接查询监控器的状态
	// 这里只需要记录日志即可
	if healthy {
		//p.logger.Debugf("监控器检测到上游服务器恢复健康: %s:%d", upstream.Address, upstream.Port)
	} else {
		//p.logger.Debugf("监控器检测到上游服务器不健康: %s:%d", upstream.Address, upstream.Port)
	}
}

// getMmapCachePath 根据配置获取内存映射缓存路径
func getMmapCachePath(cfg config.MmapCacheConfig) string {
	// 如果启用了自动平台路径选择
	if cfg.AutoPlatformPath {
		return getAutoPlatformPath(cfg)
	}

	// 优先使用平台特定路径
	switch runtime.GOOS {
	case "windows":
		if cfg.WindowsPath != "" {
			return expandPath(cfg.WindowsPath)
		}
	case "linux":
		if cfg.LinuxPath != "" {
			return expandPath(cfg.LinuxPath)
		}
	case "darwin":
		if cfg.MacOSPath != "" {
			return expandPath(cfg.MacOSPath)
		}
	case "freebsd":
		if cfg.FreeBSDPath != "" {
			return expandPath(cfg.FreeBSDPath)
		}
	}

	// 使用基础路径
	if cfg.BasePath != "" {
		return expandPath(cfg.BasePath)
	}

	// 使用降级路径
	if cfg.FallbackPath != "" {
		return expandPath(cfg.FallbackPath)
	}

	// 最后的默认??
	return getDefaultCachePath()
}

// getAutoPlatformPath 自动选择平台路径
func getAutoPlatformPath(cfg config.MmapCacheConfig) string {
	baseName := "proxy_mmap"
	if cfg.BasePath != "" {
		baseName = filepath.Base(cfg.BasePath)
		if baseName == "" || baseName == "/" || baseName == "." {
			baseName = "proxy_mmap"
		}
	}

	switch runtime.GOOS {
	case "windows":
		// Windows: 使用临时目录
		return filepath.Join(os.TempDir(), baseName)

	case "darwin":
		// macOS: 使用 /tmp
		return filepath.Join("/tmp", baseName)

	case "linux":
		// Linux: 优先使用 /dev/shm，如果不存在则使??/tmp
		if _, err := os.Stat("/dev/shm"); err == nil {
			return filepath.Join("/dev/shm", baseName)
		}
		return filepath.Join("/tmp", baseName)

	case "freebsd":
		// FreeBSD: 使用 /tmp (FreeBSD没有/dev/shm，但有tmpfs支持)
		return filepath.Join("/tmp", baseName)

	default:
		// 其他系统：使用临时目??
		return filepath.Join(os.TempDir(), baseName)
	}
}

// expandPath 展开路径中的环境变量和特殊符??
func expandPath(path string) string {
	// 展开环境变量
	path = os.ExpandEnv(path)

	// 处理用户目录
	if strings.HasPrefix(path, "~/") {
		if homeDir, err := os.UserHomeDir(); err == nil {
			path = filepath.Join(homeDir, path[2:])
		}
	}

	return path
}

// getDefaultCachePath 获取默认缓存路径
func getDefaultCachePath() string {
	switch runtime.GOOS {
	case "windows":
		return filepath.Join(os.TempDir(), "proxy_mmap")
	case "darwin":
		return "/tmp/proxy_mmap"
	case "linux":
		if _, err := os.Stat("/dev/shm"); err == nil {
			return "/dev/shm/proxy_mmap"
		}
		return "/tmp/proxy_mmap"
	case "freebsd":
		return "/tmp/proxy_mmap"
	default:
		return filepath.Join(os.TempDir(), "proxy_mmap")
	}
}

// cleanAcceptEncoding 清理和修复Accept-Encoding头部
// 解决Windows 2012等系统上HTTP头部被截断或包含换行符的问题
func (p *Proxy) cleanAcceptEncoding(acceptEncoding string) string {
	if acceptEncoding == "" {
		return ""
	}

	// 移除所有换行符和回车符
	cleaned := strings.ReplaceAll(acceptEncoding, "\n", "")
	cleaned = strings.ReplaceAll(cleaned, "\r", "")

	// 检查是否被截断（通过检查常见的不完整模式）
	truncated := false

	// 检查是否以逗号结尾但没有后续内容（可能被截断）
	if strings.HasSuffix(strings.TrimSpace(cleaned), ",") {
		truncated = true
	}

	// 检查是否缺少常见的zstd算法（如果有br但没有zstd，可能被截断）
	if strings.Contains(cleaned, "br") && !strings.Contains(cleaned, "zstd") {
		truncated = true
	}

	// 如果检测到截断，尝试修复
	if truncated {
		//p.logger.Debugf("检测到Accept-Encoding被截断，原始: '%s'", acceptEncoding)

		// 移除末尾的逗号和空格
		cleaned = strings.TrimRight(cleaned, ", ")

		// 如果有br但没有zstd，添加zstd（现代浏览器通常都支持）
		if strings.Contains(cleaned, "br") && !strings.Contains(cleaned, "zstd") {
			cleaned += ", zstd"
			//p.logger.Debugf("修复后的Accept-Encoding: '%s'", cleaned)
		}
	}

	return cleaned
}

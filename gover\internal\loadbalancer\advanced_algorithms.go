package loadbalancer

import (
	"fmt"
	"hash/crc32"
	"sort"
	"sync"
	"time"

	"reverse-proxy/internal/config"
)

// SmoothWeightedRoundRobin 平滑权重轮询算法
type SmoothWeightedRoundRobin struct {
	upstreams []*WeightedUpstream
	mu        sync.Mutex
}

// WeightedUpstream 带权重的上游服务器
type WeightedUpstream struct {
	Upstream        *config.UpstreamConfig
	Weight          int // 配置权重
	CurrentWeight   int // 当前权重
	EffectiveWeight int // 有效权重（会根据健康状态调整）
}

// NewSmoothWeightedRoundRobin 创建平滑权重轮询负载均衡器
func NewSmoothWeightedRoundRobin(upstreams []*config.UpstreamConfig) *SmoothWeightedRoundRobin {
	weightedUpstreams := make([]*WeightedUpstream, len(upstreams))
	for i, upstream := range upstreams {
		weight := upstream.Weight
		if weight <= 0 {
			weight = 1
		}
		weightedUpstreams[i] = &WeightedUpstream{
			Upstream:        upstream,
			Weight:          weight,
			CurrentWeight:   0,
			EffectiveWeight: weight,
		}
	}

	return &SmoothWeightedRoundRobin{
		upstreams: weightedUpstreams,
	}
}

// Select 选择上游服务器
func (swrr *SmoothWeightedRoundRobin) Select() *config.UpstreamConfig {
	swrr.mu.Lock()
	defer swrr.mu.Unlock()

	if len(swrr.upstreams) == 0 {
		return nil
	}

	var selected *WeightedUpstream
	totalWeight := 0

	// 计算总权重并选择当前权重最大的服务器
	for _, upstream := range swrr.upstreams {
		if !upstream.Upstream.Healthy {
			continue
		}

		upstream.CurrentWeight += upstream.EffectiveWeight
		totalWeight += upstream.EffectiveWeight

		if selected == nil || upstream.CurrentWeight > selected.CurrentWeight {
			selected = upstream
		}
	}

	if selected == nil {
		return nil
	}

	// 减少选中服务器的当前权重
	selected.CurrentWeight -= totalWeight

	return selected.Upstream
}

// UpdateHealth 更新服务器健康状态
func (swrr *SmoothWeightedRoundRobin) UpdateHealth(upstream *config.UpstreamConfig, healthy bool) {
	swrr.mu.Lock()
	defer swrr.mu.Unlock()

	for _, wu := range swrr.upstreams {
		if wu.Upstream == upstream {
			if healthy {
				// 恢复有效权重
				if wu.EffectiveWeight < wu.Weight {
					wu.EffectiveWeight++
				}
			} else {
				// 降低有效权重
				wu.EffectiveWeight--
				if wu.EffectiveWeight < 0 {
					wu.EffectiveWeight = 0
				}
			}
			break
		}
	}
}

// ConsistentHash 一致性哈希算法
type ConsistentHash struct {
	hashRing     map[uint32]*config.UpstreamConfig
	sortedHashes []uint32
	upstreams    []*config.UpstreamConfig
	replicas     int // 虚拟节点数量
	mu           sync.RWMutex
}

// NewConsistentHash 创建一致性哈希负载均衡器
func NewConsistentHash(upstreams []*config.UpstreamConfig, replicas int) *ConsistentHash {
	if replicas <= 0 {
		replicas = 100 // 默认100个虚拟节点
	}

	ch := &ConsistentHash{
		hashRing:  make(map[uint32]*config.UpstreamConfig),
		upstreams: upstreams,
		replicas:  replicas,
	}

	ch.buildHashRing()
	return ch
}

// buildHashRing 构建哈希环
func (ch *ConsistentHash) buildHashRing() {
	ch.mu.Lock()
	defer ch.mu.Unlock()

	ch.hashRing = make(map[uint32]*config.UpstreamConfig)
	ch.sortedHashes = ch.sortedHashes[:0]

	for _, upstream := range ch.upstreams {
		if !upstream.Healthy {
			continue
		}

		for i := 0; i < ch.replicas; i++ {
			key := fmt.Sprintf("%s:%d:%d", upstream.Address, upstream.Port, i)
			hash := crc32.ChecksumIEEE([]byte(key))
			ch.hashRing[hash] = upstream
			ch.sortedHashes = append(ch.sortedHashes, hash)
		}
	}

	sort.Slice(ch.sortedHashes, func(i, j int) bool {
		return ch.sortedHashes[i] < ch.sortedHashes[j]
	})
}

// Select 根据key选择上游服务器
func (ch *ConsistentHash) Select(key string) *config.UpstreamConfig {
	ch.mu.RLock()
	defer ch.mu.RUnlock()

	if len(ch.sortedHashes) == 0 {
		return nil
	}

	hash := crc32.ChecksumIEEE([]byte(key))

	// 二分查找第一个大于等于hash的节点
	idx := sort.Search(len(ch.sortedHashes), func(i int) bool {
		return ch.sortedHashes[i] >= hash
	})

	// 如果没找到，选择第一个节点（环形）
	if idx == len(ch.sortedHashes) {
		idx = 0
	}

	return ch.hashRing[ch.sortedHashes[idx]]
}

// UpdateHealth 更新服务器健康状态
func (ch *ConsistentHash) UpdateHealth(upstream *config.UpstreamConfig, healthy bool) {
	// 重建哈希环
	ch.buildHashRing()
}

// ResponseTimeWeighted 响应时间加权算法
type ResponseTimeWeighted struct {
	upstreams []*ResponseTimeUpstream
	mu        sync.RWMutex
}

// ResponseTimeUpstream 带响应时间统计的上游服务器
type ResponseTimeUpstream struct {
	Upstream     *config.UpstreamConfig
	Weight       int
	ResponseTime time.Duration
	RequestCount int64
	TotalTime    time.Duration
	LastUpdate   time.Time
}

// NewResponseTimeWeighted 创建响应时间加权负载均衡器
func NewResponseTimeWeighted(upstreams []*config.UpstreamConfig) *ResponseTimeWeighted {
	rtUpstreams := make([]*ResponseTimeUpstream, len(upstreams))
	for i, upstream := range upstreams {
		weight := upstream.Weight
		if weight <= 0 {
			weight = 1
		}
		rtUpstreams[i] = &ResponseTimeUpstream{
			Upstream:     upstream,
			Weight:       weight,
			ResponseTime: 100 * time.Millisecond, // 初始响应时间
			LastUpdate:   time.Now(),
		}
	}

	return &ResponseTimeWeighted{
		upstreams: rtUpstreams,
	}
}

// Select 选择响应时间最优的服务器
func (rtw *ResponseTimeWeighted) Select() *config.UpstreamConfig {
	rtw.mu.RLock()
	defer rtw.mu.RUnlock()

	var best *ResponseTimeUpstream
	bestScore := float64(-1)

	for _, upstream := range rtw.upstreams {
		if !upstream.Upstream.Healthy {
			continue
		}

		// 计算加权分数：权重 / 平均响应时间
		avgResponseTime := upstream.ResponseTime.Seconds()
		if avgResponseTime <= 0 {
			avgResponseTime = 0.001 // 避免除零
		}

		score := float64(upstream.Weight) / avgResponseTime

		if score > bestScore {
			bestScore = score
			best = upstream
		}
	}

	if best == nil {
		return nil
	}

	return best.Upstream
}

// UpdateResponseTime 更新响应时间
func (rtw *ResponseTimeWeighted) UpdateResponseTime(upstream *config.UpstreamConfig, responseTime time.Duration) {
	rtw.mu.Lock()
	defer rtw.mu.Unlock()

	for _, rtu := range rtw.upstreams {
		if rtu.Upstream == upstream {
			rtu.RequestCount++
			rtu.TotalTime += responseTime

			// 计算移动平均响应时间
			if rtu.RequestCount > 0 {
				rtu.ResponseTime = rtu.TotalTime / time.Duration(rtu.RequestCount)
			}

			// 定期重置统计，避免历史数据影响
			if time.Since(rtu.LastUpdate) > 5*time.Minute {
				rtu.RequestCount = 1
				rtu.TotalTime = responseTime
				rtu.ResponseTime = responseTime
				rtu.LastUpdate = time.Now()
			}
			break
		}
	}
}

// GetStats 获取响应时间统计
func (rtw *ResponseTimeWeighted) GetStats() map[string]interface{} {
	rtw.mu.RLock()
	defer rtw.mu.RUnlock()

	stats := make(map[string]interface{})
	for _, rtu := range rtw.upstreams {
		key := fmt.Sprintf("%s:%d", rtu.Upstream.Address, rtu.Upstream.Port)
		stats[key] = map[string]interface{}{
			"weight":        rtu.Weight,
			"response_time": rtu.ResponseTime.String(),
			"request_count": rtu.RequestCount,
			"healthy":       rtu.Upstream.Healthy,
		}
	}
	return stats
}

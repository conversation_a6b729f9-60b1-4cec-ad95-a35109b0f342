package http3

import (
	"context"
	"crypto/tls"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/quic-go/quic-go"
	"github.com/quic-go/quic-go/http3"
	"reverse-proxy/internal/config"
	"reverse-proxy/internal/logger"
)

// HTTP3Server HTTP/3服务器
type HTTP3Server struct {
	server  *http3.Server
	config  HTTP3Config
	handler http.Handler
	mu      sync.RWMutex
	running bool
	stats   *HTTP3Stats
}

// HTTP3Config HTTP/3配置
type HTTP3Config struct {
	Enabled          bool          `json:"enabled"`
	Port             int           `json:"port"`
	CertFile         string        `json:"cert_file"`
	KeyFile          string        `json:"key_file"`
	MaxIdleTimeout   time.Duration `json:"max_idle_timeout"`
	MaxStreamTimeout time.Duration `json:"max_stream_timeout"`
	KeepAlive        bool          `json:"keep_alive"`
	EnableDatagrams  bool          `json:"enable_datagrams"`
}

// HTTP3Stats HTTP/3统计信息
type HTTP3Stats struct {
	TotalConnections  int64            `json:"total_connections"`
	ActiveConnections int64            `json:"active_connections"`
	TotalRequests     int64            `json:"total_requests"`
	TotalStreams      int64            `json:"total_streams"`
	BytesReceived     int64            `json:"bytes_received"`
	BytesSent         int64            `json:"bytes_sent"`
	ConnectionErrors  int64            `json:"connection_errors"`
	StreamErrors      int64            `json:"stream_errors"`
	ProtocolVersions  map[string]int64 `json:"protocol_versions"`
	mu                sync.RWMutex
}

// NewHTTP3Server 创建HTTP/3服务器
func NewHTTP3Server(config HTTP3Config, handler http.Handler) *HTTP3Server {
	if config.MaxIdleTimeout == 0 {
		config.MaxIdleTimeout = 30 * time.Second
	}
	if config.MaxStreamTimeout == 0 {
		config.MaxStreamTimeout = 10 * time.Second
	}

	return &HTTP3Server{
		config:  config,
		handler: handler,
		stats: &HTTP3Stats{
			ProtocolVersions: make(map[string]int64),
		},
	}
}

// Start 启动HTTP/3服务器
func (h3s *HTTP3Server) Start() error {
	if !h3s.config.Enabled {
		return fmt.Errorf("HTTP/3 is disabled")
	}

	h3s.mu.Lock()
	defer h3s.mu.Unlock()

	if h3s.running {
		return fmt.Errorf("HTTP/3 server is already running")
	}

	// 加载TLS证书
	cert, err := tls.LoadX509KeyPair(h3s.config.CertFile, h3s.config.KeyFile)
	if err != nil {
		return fmt.Errorf("failed to load TLS certificate: %w", err)
	}

	// 创建TLS配置
	tlsConfig := &tls.Config{
		Certificates: []tls.Certificate{cert},
		NextProtos:   []string{"h3", "h3-29", "h3-32"}, // HTTP/3协议标识
	}

	// 创建QUIC配置
	quicConfig := &quic.Config{
		MaxIdleTimeout:          h3s.config.MaxIdleTimeout,
		MaxIncomingStreams:      1000,
		MaxIncomingUniStreams:   1000,
		KeepAlivePeriod:         15 * time.Second,
		EnableDatagrams:         h3s.config.EnableDatagrams,
		DisablePathMTUDiscovery: false,
		Allow0RTT:               true,
	}

	// 创建HTTP/3服务器
	h3s.server = &http3.Server{
		Handler:    h3s.wrapHandler(),
		TLSConfig:  tlsConfig,
		QUICConfig: quicConfig,
		Addr:       fmt.Sprintf(":%d", h3s.config.Port),
	}

	// 启动服务器
	go func() {
		logger.Info("Starting HTTP/3 server on port:", h3s.config.Port)
		if err := h3s.server.ListenAndServe(); err != nil {
			logger.Error("HTTP/3 server error:", err)
		}
	}()

	h3s.running = true
	logger.Info("HTTP/3 server started successfully")
	return nil
}

// Stop 停止HTTP/3服务器
func (h3s *HTTP3Server) Stop() error {
	h3s.mu.Lock()
	defer h3s.mu.Unlock()

	if !h3s.running {
		return nil
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := h3s.server.CloseGracefully(ctx); err != nil {
		return fmt.Errorf("failed to stop HTTP/3 server gracefully: %w", err)
	}

	h3s.running = false
	logger.Info("HTTP/3 server stopped")
	return nil
}

// wrapHandler 包装处理器以添加统计和监控
func (h3s *HTTP3Server) wrapHandler() http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		startTime := time.Now()

		// 更新统计信息
		h3s.stats.mu.Lock()
		h3s.stats.TotalRequests++

		// 记录协议版本
		if proto := r.Header.Get("Alt-Svc"); proto != "" {
			h3s.stats.ProtocolVersions[proto]++
		} else {
			h3s.stats.ProtocolVersions["h3"]++
		}
		h3s.stats.mu.Unlock()

		// 包装响应写入器以统计字节数
		wrappedWriter := &statsResponseWriter{
			ResponseWriter: w,
			stats:          h3s.stats,
		}

		// 调用原始处理器
		h3s.handler.ServeHTTP(wrappedWriter, r)

		// 记录请求大小
		if r.ContentLength > 0 {
			h3s.stats.mu.Lock()
			h3s.stats.BytesReceived += r.ContentLength
			h3s.stats.mu.Unlock()
		}

		logger.Debug("HTTP/3 request processed in:", time.Since(startTime))
	})
}

// statsResponseWriter 统计响应写入器
type statsResponseWriter struct {
	http.ResponseWriter
	stats        *HTTP3Stats
	bytesWritten int64
}

func (w *statsResponseWriter) Write(data []byte) (int, error) {
	n, err := w.ResponseWriter.Write(data)
	w.bytesWritten += int64(n)
	return n, err
}

func (w *statsResponseWriter) WriteHeader(statusCode int) {
	w.ResponseWriter.WriteHeader(statusCode)

	// 记录发送字节数
	w.stats.mu.Lock()
	w.stats.BytesSent += w.bytesWritten
	w.stats.mu.Unlock()
}

// GetStats 获取统计信息
func (h3s *HTTP3Server) GetStats() *HTTP3Stats {
	h3s.stats.mu.RLock()
	defer h3s.stats.mu.RUnlock()

	stats := &HTTP3Stats{
		TotalConnections:  h3s.stats.TotalConnections,
		ActiveConnections: h3s.stats.ActiveConnections,
		TotalRequests:     h3s.stats.TotalRequests,
		TotalStreams:      h3s.stats.TotalStreams,
		BytesReceived:     h3s.stats.BytesReceived,
		BytesSent:         h3s.stats.BytesSent,
		ConnectionErrors:  h3s.stats.ConnectionErrors,
		StreamErrors:      h3s.stats.StreamErrors,
		ProtocolVersions:  make(map[string]int64),
	}

	for k, v := range h3s.stats.ProtocolVersions {
		stats.ProtocolVersions[k] = v
	}

	return stats
}

// IsRunning 检查服务器是否运行
func (h3s *HTTP3Server) IsRunning() bool {
	h3s.mu.RLock()
	defer h3s.mu.RUnlock()
	return h3s.running
}

// GetConfig 获取配置
func (h3s *HTTP3Server) GetConfig() HTTP3Config {
	return h3s.config
}

// UpdateConfig 更新配置
func (h3s *HTTP3Server) UpdateConfig(config HTTP3Config) error {
	h3s.mu.Lock()
	defer h3s.mu.Unlock()

	if h3s.running {
		return fmt.Errorf("cannot update config while server is running")
	}

	h3s.config = config
	return nil
}

// SetHandler 设置处理器
func (h3s *HTTP3Server) SetHandler(handler http.Handler) {
	h3s.mu.Lock()
	defer h3s.mu.Unlock()
	h3s.handler = handler
}

// GetConnectionInfo 获取连接信息
func (h3s *HTTP3Server) GetConnectionInfo() map[string]interface{} {
	h3s.mu.RLock()
	defer h3s.mu.RUnlock()

	info := map[string]interface{}{
		"enabled":            h3s.config.Enabled,
		"running":            h3s.running,
		"port":               h3s.config.Port,
		"max_idle_timeout":   h3s.config.MaxIdleTimeout.String(),
		"max_stream_timeout": h3s.config.MaxStreamTimeout.String(),
		"keep_alive":         h3s.config.KeepAlive,
		"enable_datagrams":   h3s.config.EnableDatagrams,
	}

	if h3s.server != nil {
		info["server_addr"] = h3s.server.Addr
	}

	return info
}

// EnableAltSvc 启用Alt-Svc头部
func (h3s *HTTP3Server) EnableAltSvc(w http.ResponseWriter) {
	if h3s.config.Enabled && h3s.running {
		altSvc := fmt.Sprintf(`h3=":%d"; ma=86400`, h3s.config.Port)
		w.Header().Set("Alt-Svc", altSvc)
	}
}

// HTTP3Client HTTP/3客户端
type HTTP3Client struct {
	client *http.Client
	config HTTP3ClientConfig
}

// HTTP3ClientConfig HTTP/3客户端配置
type HTTP3ClientConfig struct {
	Timeout         time.Duration `json:"timeout"`
	MaxIdleTimeout  time.Duration `json:"max_idle_timeout"`
	KeepAlive       bool          `json:"keep_alive"`
	EnableDatagrams bool          `json:"enable_datagrams"`
}

// NewHTTP3Client 创建HTTP/3客户端
func NewHTTP3Client(config HTTP3ClientConfig) *HTTP3Client {
	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second
	}
	if config.MaxIdleTimeout == 0 {
		config.MaxIdleTimeout = 30 * time.Second
	}

	// 创建QUIC配置
	quicConfig := &quic.Config{
		MaxIdleTimeout:  config.MaxIdleTimeout,
		KeepAlivePeriod: 15 * time.Second,
		EnableDatagrams: config.EnableDatagrams,
		Allow0RTT:       true,
	}

	// 创建HTTP/3传输
	transport := &http3.RoundTripper{
		QUICConfig: quicConfig,
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: false,
		},
	}

	client := &http.Client{
		Transport: transport,
		Timeout:   config.Timeout,
	}

	return &HTTP3Client{
		client: client,
		config: config,
	}
}

// Do 执行HTTP/3请求
func (c *HTTP3Client) Do(req *http.Request) (*http.Response, error) {
	return c.client.Do(req)
}

// Get 执行GET请求
func (c *HTTP3Client) Get(url string) (*http.Response, error) {
	return c.client.Get(url)
}

// Post 执行POST请求
func (c *HTTP3Client) Post(url, contentType string, body interface{}) (*http.Response, error) {
	// 这里需要根据body类型进行处理
	// 为简化，暂时返回错误
	return nil, fmt.Errorf("HTTP/3 POST not implemented")
}

// Close 关闭客户端
func (c *HTTP3Client) Close() error {
	if transport, ok := c.client.Transport.(*http3.RoundTripper); ok {
		transport.Close()
	}
	return nil
}

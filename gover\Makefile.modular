# 模块化反向代理构建配置
# 支持构建基础版和专业版

# 版本信息
VERSION ?= 1.0.0
BUILD_TIME := $(shell date -u '+%Y-%m-%d_%H:%M:%S')
GIT_COMMIT := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# 构建标志
LDFLAGS_COMMON := -s -w \
	-X main.Version=$(VERSION) \
	-X main.BuildTime=$(BUILD_TIME) \
	-X main.GitCommit=$(GIT_COMMIT)

LDFLAGS_CORE := $(LDFLAGS_COMMON) -X main.Edition=Core
LDFLAGS_PRO := $(LDFLAGS_COMMON) -X main.Edition=Pro

# 构建标签
BUILD_TAGS_CORE := core
BUILD_TAGS_PRO := pro

# 输出目录
BIN_DIR := bin
CORE_BINARY := $(BIN_DIR)/reverse-proxy-core
PRO_BINARY := $(BIN_DIR)/reverse-proxy-pro

# 默认目标
.DEFAULT_GOAL := help

# 帮助信息
.PHONY: help
help: ## 显示帮助信息
	@echo "模块化反向代理构建系统"
	@echo ""
	@echo "可用目标:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-20s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# 创建输出目录
$(BIN_DIR):
	@mkdir -p $(BIN_DIR)

# ============================================================================
# 构建目标
# ============================================================================

.PHONY: build-core
build-core: $(BIN_DIR) ## 构建基础版
	@echo "🔨 构建基础版..."
	@echo "  版本: $(VERSION)"
	@echo "  标签: $(BUILD_TAGS_CORE)"
	@echo "  输出: $(CORE_BINARY)"
	go build -tags="$(BUILD_TAGS_CORE)" \
		-ldflags="$(LDFLAGS_CORE)" \
		-o $(CORE_BINARY) \
		main.go
	@echo "✅ 基础版构建完成: $(CORE_BINARY)"
	@ls -lh $(CORE_BINARY)

.PHONY: build-pro
build-pro: $(BIN_DIR) ## 构建专业版
	@echo "🚀 构建专业版..."
	@echo "  版本: $(VERSION)"
	@echo "  标签: $(BUILD_TAGS_PRO)"
	@echo "  输出: $(PRO_BINARY)"
	go build -tags="$(BUILD_TAGS_PRO)" \
		-ldflags="$(LDFLAGS_PRO)" \
		-o $(PRO_BINARY) \
		main.go
	@echo "✅ 专业版构建完成: $(PRO_BINARY)"
	@ls -lh $(PRO_BINARY)

.PHONY: build-all
build-all: build-core build-pro ## 构建所有版本
	@echo ""
	@echo "📊 构建结果对比:"
	@echo "基础版大小: $$(ls -lh $(CORE_BINARY) | awk '{print $$5}')"
	@echo "专业版大小: $$(ls -lh $(PRO_BINARY) | awk '{print $$5}')"

# ============================================================================
# 测试目标
# ============================================================================

.PHONY: test-core
test-core: ## 测试基础版
	@echo "🧪 测试基础版..."
	go test -tags="$(BUILD_TAGS_CORE)" -v ./...

.PHONY: test-pro
test-pro: ## 测试专业版
	@echo "🧪 测试专业版..."
	go test -tags="$(BUILD_TAGS_PRO)" -v ./...

.PHONY: test-all
test-all: test-core test-pro ## 测试所有版本

# ============================================================================
# 基准测试
# ============================================================================

.PHONY: bench-core
bench-core: ## 基础版性能测试
	@echo "📈 基础版性能测试..."
	go test -tags="$(BUILD_TAGS_CORE)" -bench=. -benchmem ./...

.PHONY: bench-pro
bench-pro: ## 专业版性能测试
	@echo "📈 专业版性能测试..."
	go test -tags="$(BUILD_TAGS_PRO)" -bench=. -benchmem ./...

.PHONY: bench-compare
bench-compare: ## 对比两个版本的性能
	@echo "📊 性能对比测试..."
	@echo "基础版性能:"
	@go test -tags="$(BUILD_TAGS_CORE)" -bench=. -benchmem ./... > bench-core.txt
	@echo "专业版性能:"
	@go test -tags="$(BUILD_TAGS_PRO)" -bench=. -benchmem ./... > bench-pro.txt
	@echo "对比结果已保存到 bench-core.txt 和 bench-pro.txt"

# ============================================================================
# 运行目标
# ============================================================================

.PHONY: run-core
run-core: build-core ## 运行基础版
	@echo "🚀 启动基础版..."
	$(CORE_BINARY) -config config-core.json

.PHONY: run-pro
run-pro: build-pro ## 运行专业版
	@echo "🚀 启动专业版..."
	$(PRO_BINARY) -config config-pro.json

# ============================================================================
# 打包目标
# ============================================================================

.PHONY: package-core
package-core: build-core ## 打包基础版
	@echo "📦 打包基础版..."
	@mkdir -p dist/core
	@cp $(CORE_BINARY) dist/core/
	@cp config-core.json dist/core/config.json
	@cp README-core.md dist/core/README.md
	@tar -czf dist/reverse-proxy-core-$(VERSION).tar.gz -C dist/core .
	@echo "✅ 基础版打包完成: dist/reverse-proxy-core-$(VERSION).tar.gz"

.PHONY: package-pro
package-pro: build-pro ## 打包专业版
	@echo "📦 打包专业版..."
	@mkdir -p dist/pro
	@cp $(PRO_BINARY) dist/pro/
	@cp config-pro.json dist/pro/config.json
	@cp README-pro.md dist/pro/README.md
	@tar -czf dist/reverse-proxy-pro-$(VERSION).tar.gz -C dist/pro .
	@echo "✅ 专业版打包完成: dist/reverse-proxy-pro-$(VERSION).tar.gz"

.PHONY: package-all
package-all: package-core package-pro ## 打包所有版本

# ============================================================================
# Docker 构建
# ============================================================================

.PHONY: docker-core
docker-core: ## 构建基础版Docker镜像
	@echo "🐳 构建基础版Docker镜像..."
	docker build -f Dockerfile.core \
		--build-arg VERSION=$(VERSION) \
		--build-arg BUILD_TAGS=$(BUILD_TAGS_CORE) \
		-t reverse-proxy:core-$(VERSION) \
		-t reverse-proxy:core-latest .

.PHONY: docker-pro
docker-pro: ## 构建专业版Docker镜像
	@echo "🐳 构建专业版Docker镜像..."
	docker build -f Dockerfile.pro \
		--build-arg VERSION=$(VERSION) \
		--build-arg BUILD_TAGS=$(BUILD_TAGS_PRO) \
		-t reverse-proxy:pro-$(VERSION) \
		-t reverse-proxy:pro-latest .

.PHONY: docker-all
docker-all: docker-core docker-pro ## 构建所有Docker镜像

# ============================================================================
# 清理目标
# ============================================================================

.PHONY: clean
clean: ## 清理构建文件
	@echo "🧹 清理构建文件..."
	@rm -rf $(BIN_DIR)
	@rm -rf dist
	@rm -f bench-*.txt
	@echo "✅ 清理完成"

.PHONY: clean-docker
clean-docker: ## 清理Docker镜像
	@echo "🧹 清理Docker镜像..."
	@docker rmi reverse-proxy:core-$(VERSION) reverse-proxy:core-latest || true
	@docker rmi reverse-proxy:pro-$(VERSION) reverse-proxy:pro-latest || true

# ============================================================================
# 开发目标
# ============================================================================

.PHONY: dev-core
dev-core: ## 开发模式运行基础版
	@echo "🔧 开发模式 - 基础版..."
	go run -tags="$(BUILD_TAGS_CORE)" main.go -config config-core.json

.PHONY: dev-pro
dev-pro: ## 开发模式运行专业版
	@echo "🔧 开发模式 - 专业版..."
	go run -tags="$(BUILD_TAGS_PRO)" main.go -config config-pro.json

.PHONY: lint
lint: ## 代码检查
	@echo "🔍 代码检查..."
	@which golangci-lint > /dev/null || (echo "请安装 golangci-lint" && exit 1)
	golangci-lint run

.PHONY: fmt
fmt: ## 格式化代码
	@echo "🎨 格式化代码..."
	go fmt ./...

.PHONY: mod-tidy
mod-tidy: ## 整理依赖
	@echo "📦 整理依赖..."
	go mod tidy

# ============================================================================
# 信息目标
# ============================================================================

.PHONY: info
info: ## 显示构建信息
	@echo "构建信息:"
	@echo "  版本: $(VERSION)"
	@echo "  构建时间: $(BUILD_TIME)"
	@echo "  Git提交: $(GIT_COMMIT)"
	@echo "  Go版本: $$(go version)"
	@echo ""
	@echo "构建标签:"
	@echo "  基础版: $(BUILD_TAGS_CORE)"
	@echo "  专业版: $(BUILD_TAGS_PRO)"
	@echo ""
	@echo "输出文件:"
	@echo "  基础版: $(CORE_BINARY)"
	@echo "  专业版: $(PRO_BINARY)"

.PHONY: size-compare
size-compare: build-all ## 对比两个版本的大小
	@echo "📊 版本大小对比:"
	@echo ""
	@printf "%-15s %-10s %-15s\n" "版本" "大小" "相对大小"
	@printf "%-15s %-10s %-15s\n" "----" "----" "--------"
	@CORE_SIZE=$$(stat -f%z $(CORE_BINARY) 2>/dev/null || stat -c%s $(CORE_BINARY)); \
	 PRO_SIZE=$$(stat -f%z $(PRO_BINARY) 2>/dev/null || stat -c%s $(PRO_BINARY)); \
	 CORE_MB=$$(echo "scale=2; $$CORE_SIZE/1024/1024" | bc); \
	 PRO_MB=$$(echo "scale=2; $$PRO_SIZE/1024/1024" | bc); \
	 RATIO=$$(echo "scale=1; $$PRO_SIZE/$$CORE_SIZE" | bc); \
	 printf "%-15s %-10s %-15s\n" "基础版" "$${CORE_MB}MB" "1.0x"; \
	 printf "%-15s %-10s %-15s\n" "专业版" "$${PRO_MB}MB" "$${RATIO}x"

# ============================================================================
# 发布目标
# ============================================================================

.PHONY: release
release: clean build-all test-all package-all ## 完整发布流程
	@echo "🎉 发布完成!"
	@echo ""
	@echo "发布文件:"
	@ls -la dist/*.tar.gz

# ============================================================================
# 安装目标
# ============================================================================

.PHONY: install-core
install-core: build-core ## 安装基础版到系统
	@echo "📥 安装基础版..."
	sudo cp $(CORE_BINARY) /usr/local/bin/reverse-proxy-core
	@echo "✅ 基础版已安装到 /usr/local/bin/reverse-proxy-core"

.PHONY: install-pro
install-pro: build-pro ## 安装专业版到系统
	@echo "📥 安装专业版..."
	sudo cp $(PRO_BINARY) /usr/local/bin/reverse-proxy-pro
	@echo "✅ 专业版已安装到 /usr/local/bin/reverse-proxy-pro"

.PHONY: uninstall
uninstall: ## 卸载系统安装
	@echo "🗑️  卸载系统安装..."
	sudo rm -f /usr/local/bin/reverse-proxy-core
	sudo rm -f /usr/local/bin/reverse-proxy-pro
	@echo "✅ 卸载完成"

{"rustc": 28845397767708332, "features": "[\"alloc\", \"dfa-onepass\", \"hybrid\", \"meta\", \"nfa-backtrack\", \"nfa-pikevm\", \"nfa-thompson\", \"perf-inline\", \"perf-literal\", \"perf-literal-multisubstring\", \"perf-literal-substring\", \"std\", \"syntax\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unicode-word-boundary\"]", "target": 6684579838648762387, "profile": 13540511011255890580, "path": 12217025767788250460, "deps": [[2421869795823446512, "aho_corasick", false, 17480596019571997746], [7864249588615721249, "regex_syntax", false, 16069410841654447692], [17151257692364156824, "memchr", false, 5880979238900167707]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\regex-automata-e7dceca87ba33012\\dep-lib-regex_automata"}}], "rustflags": [], "metadata": 457443847163205721, "config": 2202906307356721367, "compile_kind": 0}
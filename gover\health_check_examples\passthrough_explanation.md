# Passthrough协议详细说明

## 协议透传的工作原理

### 1. 代理请求时的协议选择

当客户端发起请求时，`passthrough` 协议会根据客户端请求的协议来决定与后端的通信协议：

```go
case "passthrough":
    // 协议透传：根据客户端请求协议决定
    if r.TLS != nil {
        // 客户端用HTTPS访问 → 后端也使用HTTPS
        scheme = "https"
        port = upstream.HTTPSPort
    } else {
        // 客户端用HTTP访问 → 后端也使用HTTP
        scheme = "http"
        port = upstream.Port
    }
```

#### 示例场景：
- **客户端HTTP请求** → 代理使用HTTP连接后端
- **客户端HTTPS请求** → 代理使用HTTPS连接后端

### 2. 健康检查时的协议选择

健康检查是由程序自身发起的，没有客户端请求，因此无法知道客户端会用什么协议访问。解决方案是根据后端服务器的配置来选择协议：

```go
case "passthrough":
    // 对于passthrough协议，健康检查时根据后端服务器配置选择协议
    // 如果配置了HTTPS端口，说明后端支持HTTPS，优先检查HTTPS
    // 如果只配置了HTTP端口，则检查HTTP
    if upstream.HTTPSPort > 0 {
        scheme = "https"
        port = upstream.HTTPSPort
    } else {
        scheme = "http"
        port = upstream.Port
    }
```

## 实际配置示例

### 场景1：后端只支持HTTP
```json
{
  "name": "http_only_server",
  "address": "*************",
  "port": 80,
  "protocol": "passthrough"
  // 注意：没有配置 https_port
}
```

**代理请求时**：
- 客户端HTTP → 后端HTTP (`http://*************:80`)
- 客户端HTTPS → 后端HTTP (`http://*************:80`)

**健康检查时**：
- 检查HTTP (`http://*************:80/health.php`)

### 场景2：后端支持HTTP和HTTPS
```json
{
  "name": "dual_protocol_server",
  "address": "*************",
  "port": 80,
  "protocol": "passthrough",
  "https_port": 443
}
```

**代理请求时**：
- 客户端HTTP → 后端HTTP (`http://*************:80`)
- 客户端HTTPS → 后端HTTPS (`https://*************:443`)

**健康检查时**：
- 检查HTTPS (`https://*************:443/health.php`)

## 为什么这样设计？

### 1. 逻辑一致性
- 代理请求时：根据客户端协议选择后端协议
- 健康检查时：根据后端能力选择检查协议

### 2. 避免检查失败
- 如果后端只支持HTTP，检查HTTPS会失败
- 如果后端支持HTTPS，优先检查HTTPS确保安全服务可用

### 3. 实际应用场景
- **纯HTTP服务**：只检查HTTP，避免不必要的HTTPS检查
- **双协议服务**：优先检查HTTPS，确保安全服务可用
- **HTTPS优先服务**：检查HTTPS，符合现代安全要求

## 配置建议

### 1. 纯HTTP服务
```json
{
  "protocol": "passthrough",
  "port": 80,
  "health_path": "/health.php"
  // 不配置 https_port
}
```

### 2. 双协议服务（推荐）
```json
{
  "protocol": "passthrough",
  "port": 80,
  "https_port": 443,
  "health_path": "/health.php"
}
```

### 3. HTTPS优先服务
```json
{
  "protocol": "passthrough",
  "port": 80,
  "https_port": 443,
  "health_path": "/health.php"
}
```

## 测试验证

### 测试代理请求
```bash
# 测试HTTP请求
curl -v http://proxy.example.com/

# 测试HTTPS请求
curl -v https://proxy.example.com/
```

### 测试健康检查
```bash
# 查看健康检查日志
tail -f logs/proxy.log | grep health

# 手动测试健康检查URL
curl -v http://*************:80/health.php
curl -v https://*************:443/health.php
```

## 总结

`passthrough` 协议的设计理念是：
1. **代理请求时**：客户端用什么协议，后端就用什么协议
2. **健康检查时**：后端支持什么协议，就检查什么协议

这样既保证了协议透传的灵活性，又确保了健康检查的准确性。 
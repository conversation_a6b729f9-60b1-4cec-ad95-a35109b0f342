# ACL文件监控快速开始指南

## 🚀 快速开始

### 1. 启用ACL文件监控

在配置文件中设置ACL文件路径：

```json
{
  "acl": {
    "enabled": true,
    "allow_file": "acl/global_allow.txt",
    "deny_file": "acl/global_deny.txt"
  },
  "sites": [
    {
      "name": "api_site",
      "acl": {
        "allow_file": "acl/api_allow.txt",
        "deny_file": "acl/api_deny.txt"
      }
    }
  ]
}
```

### 2. 创建ACL文件

```bash
# 创建ACL目录
mkdir -p acl

# 创建全局允许列表
cat > acl/global_allow.txt << EOF
# 全局允许列表
127.0.0.1
***********/24
10.0.0.0/8
EOF

# 创建全局拒绝列表
cat > acl/global_deny.txt << EOF
# 全局拒绝列表
*******
*******
************/24
EOF
```

### 3. 启动反向代理

```bash
./reverse-proxy
```

启动后会看到类似日志：
```
[INFO] 已添加文件监控: /path/to/acl/global_allow.txt
[INFO] 已添加文件监控: /path/to/acl/global_deny.txt
[INFO] 文件监控器已启动
```

### 4. 实时更新IP列表

```bash
# 添加新的拒绝IP
echo "**********" >> acl/global_deny.txt

# 立即生效，无需重启服务
```

日志会显示：
```
[INFO] 检测到文件变化: /path/to/acl/global_deny.txt
[INFO] 检测到全局拒绝列表文件变化，重新加载: /path/to/acl/global_deny.txt
[INFO] 已重新加载全局拒绝列表文件: /path/to/acl/global_deny.txt, 共 4 个IP
[INFO] 文件重载成功: /path/to/acl/global_deny.txt
```

## 📋 常用操作

### 封锁单个IP
```bash
echo "攻击者IP" >> acl/global_deny.txt
```

### 封锁IP段
```bash
echo "恶意网段/24" >> acl/global_deny.txt
```

### 添加可信IP
```bash
echo "可信IP" >> acl/global_allow.txt
```

### 移除IP
```bash
# 从拒绝列表中移除IP
sed -i '/要移除的IP/d' acl/global_deny.txt
```

### 批量更新
```bash
# 使用脚本更新地域IP
./scripts/update_geo_ips.sh

# 或者手动下载
curl -s "https://www.ipdeny.com/ipblocks/data/countries/cn.zone" >> acl/china_deny.txt
```

## 🔧 测试验证

### 测试ACL功能
```bash
# 运行测试脚本
chmod +x scripts/test_acl_watcher.sh
./scripts/test_acl_watcher.sh
```

### 手动测试
```bash
# 测试IP是否被封锁
curl -H "X-Real-IP: 测试IP" http://localhost/

# 200/404等 = 允许访问
# 403 = 被ACL拒绝
```

### 监控日志
```bash
# 实时监控ACL相关日志
tail -f logs/proxy_$(date +%Y%m%d).log | grep -E "(文件变化|重新加载|文件监控)"
```

## 📊 文件格式

### 支持的IP格式
```
# 注释行（以#开头）
***********          # 单个IPv4地址
***********/24       # IPv4网段
10.0.0.0/8           # 大网段
2001:db8::1          # 单个IPv6地址
2001:db8::/32        # IPv6网段

# 空行会被自动忽略
```

### 文件结构建议
```
acl/
├── global_allow.txt      # 全局允许列表
├── global_deny.txt       # 全局拒绝列表
├── api_allow.txt         # API站点允许列表
├── api_deny.txt          # API站点拒绝列表
├── admin_allow.txt       # 管理后台允许列表
├── china_deny.txt        # 中国IP段（地域封锁）
├── malicious_ips.txt     # 恶意IP列表
└── trusted_ips.txt       # 可信IP列表
```

## ⚡ 性能特点

- **实时响应**: 文件变化后100ms内生效
- **零重启**: 无需重启服务
- **高效率**: 事件驱动，CPU友好
- **大文件支持**: 支持10万+IP列表
- **并发安全**: 支持多文件同时修改

## 🚨 注意事项

1. **文件权限**: 确保反向代理有读取ACL文件的权限
2. **原子操作**: 大批量更新时建议使用临时文件+重命名
3. **格式验证**: 确保IP格式正确，避免解析错误
4. **备份重要**: 定期备份重要的ACL文件
5. **监控告警**: 设置ACL文件大小和重载频率告警

## 🛠️ 故障排除

### 文件监控不工作
```bash
# 检查文件是否存在
ls -la acl/

# 检查权限
chmod 644 acl/*.txt

# 查看错误日志
grep ERROR logs/proxy_*.log
```

### IP格式错误
```bash
# 验证IP格式
cat acl/global_deny.txt | while read ip; do
  [[ $ip =~ ^[0-9.]+(/[0-9]+)?$ ]] || echo "Invalid: $ip"
done
```

### 文件过大
```bash
# 检查文件大小
du -h acl/*.txt

# 分割大文件
split -l 10000 acl/large_file.txt acl/split_
```

现在你的ACL文件监控功能已经完全实现！🎉

use async_trait::async_trait;
use bytes::Bytes;
use mime_guess::from_path;
use std::path::Path;
use tokio::fs;
use tracing::{debug, error};

use super::{Processor, ProcessResult, ProcessError, RequestContext};

/// 静态文件处理器
pub struct StaticFileProcessor {
    /// 是否启用目录列表
    enable_dir_listing: bool,
    /// 默认索引文件
    default_index_files: Vec<String>,
}

impl StaticFileProcessor {
    /// 创建新的静态文件处理器
    pub fn new() -> Self {
        Self {
            enable_dir_listing: false,
            default_index_files: vec![
                "index.html".to_string(),
                "index.htm".to_string(),
                "default.html".to_string(),
            ],
        }
    }
    
    /// 设置是否启用目录列表
    pub fn with_dir_listing(mut self, enabled: bool) -> Self {
        self.enable_dir_listing = enabled;
        self
    }
    
    /// 设置默认索引文件
    pub fn with_index_files(mut self, files: Vec<String>) -> Self {
        self.default_index_files = files;
        self
    }
    
    /// 处理静态文件请求
    async fn serve_static_file(&self, ctx: &mut RequestContext, static_dir: &str) -> ProcessResult {
        let path = ctx.path();

        // 检查静态目录是否存在
        let static_path = Path::new(static_dir);
        if !static_path.exists() {
            debug!("Static directory does not exist: {}", static_dir);
            return ProcessResult::Error(ProcessError::new("Static directory not found", 404));
        }

        // 移除开头的斜杠并构建文件路径
        let relative_path = path.strip_prefix('/').unwrap_or(path);
        let file_path = static_path.join(relative_path);

        // 安全检查：防止路径遍历攻击
        if !self.is_safe_path(&file_path, static_dir) {
            return ProcessResult::Error(ProcessError::new(
                "Access denied: path traversal detected",
                403,
            ));
        }
        
        // 检查文件是否存在
        match fs::metadata(&file_path).await {
            Ok(metadata) => {
                if metadata.is_file() {
                    self.serve_file(ctx, &file_path).await
                } else if metadata.is_dir() {
                    self.serve_directory(ctx, &file_path, static_dir).await
                } else {
                    ProcessResult::Error(ProcessError::new("Not a file or directory", 404))
                }
            }
            Err(_) => {
                ProcessResult::Error(ProcessError::new("File not found", 404))
            }
        }
    }
    
    /// 检查路径是否安全（防止路径遍历）
    fn is_safe_path(&self, file_path: &Path, static_dir: &str) -> bool {
        match file_path.canonicalize() {
            Ok(canonical_path) => {
                match Path::new(static_dir).canonicalize() {
                    Ok(canonical_static_dir) => {
                        canonical_path.starts_with(canonical_static_dir)
                    }
                    Err(_) => false,
                }
            }
            Err(_) => {
                // 如果文件不存在，检查父目录
                if let Some(parent) = file_path.parent() {
                    self.is_safe_path(parent, static_dir)
                } else {
                    false
                }
            }
        }
    }
    
    /// 服务文件
    async fn serve_file(&self, ctx: &mut RequestContext, file_path: &Path) -> ProcessResult {
        match fs::read(file_path).await {
            Ok(content) => {
                let content_bytes = Bytes::from(content);
                
                // 设置Content-Type
                let content_type = from_path(file_path)
                    .first_or_octet_stream()
                    .to_string();
                
                ctx.response.set_content_type(&content_type);
                ctx.response.set_body(content_bytes);
                ctx.response.status_code = 200;
                
                debug!(
                    "Served static file: {} ({} bytes, {})",
                    file_path.display(),
                    ctx.response.size,
                    content_type
                );
                
                ProcessResult::Stop // 静态文件处理完成，停止处理链
            }
            Err(e) => {
                error!("Failed to read file {}: {}", file_path.display(), e);
                ProcessResult::Error(ProcessError::new("Failed to read file", 500))
            }
        }
    }
    
    /// 服务目录
    async fn serve_directory(&self, ctx: &mut RequestContext, dir_path: &Path, static_dir: &str) -> ProcessResult {
        // 首先尝试查找索引文件
        let route = ctx.route.as_ref();
        let index_files = route
            .and_then(|r| r.index_files.as_ref())
            .unwrap_or(&self.default_index_files);
        
        for index_file in index_files {
            let index_path = dir_path.join(index_file);
            if index_path.is_file() {
                return self.serve_file(ctx, &index_path).await;
            }
        }
        
        // 如果没有找到索引文件，检查是否启用目录列表
        let dir_listing_enabled = route
            .and_then(|r| r.dir_listing)
            .unwrap_or(self.enable_dir_listing);
        
        if dir_listing_enabled {
            self.generate_directory_listing(ctx, dir_path, static_dir).await
        } else {
            ProcessResult::Error(ProcessError::new("Directory listing disabled", 403))
        }
    }
    
    /// 生成目录列表
    async fn generate_directory_listing(&self, ctx: &mut RequestContext, dir_path: &Path, _static_dir: &str) -> ProcessResult {
        match fs::read_dir(dir_path).await {
            Ok(mut entries) => {
                let mut html = String::new();
                html.push_str("<!DOCTYPE html>\n");
                html.push_str("<html><head><title>Directory Listing</title></head><body>\n");
                html.push_str(&format!("<h1>Directory: {}</h1>\n", ctx.path()));
                html.push_str("<ul>\n");
                
                // 添加返回上级目录的链接
                if ctx.path() != "/" {
                    html.push_str("<li><a href=\"../\">..</a></li>\n");
                }
                
                let mut items = Vec::new();
                while let Some(entry) = entries.next_entry().await.unwrap_or(None) {
                    items.push(entry);
                }
                
                // 排序：目录在前，文件在后
                // 注意：这里我们使用同步的方法，因为DirEntry已经包含了文件类型信息
                items.sort_by(|a, b| {
                    // 使用DirEntry的metadata方法来获取文件类型
                    let a_is_dir = a.path().is_dir();
                    let b_is_dir = b.path().is_dir();

                    match (a_is_dir, b_is_dir) {
                        (true, false) => std::cmp::Ordering::Less,
                        (false, true) => std::cmp::Ordering::Greater,
                        _ => a.file_name().cmp(&b.file_name()),
                    }
                });
                
                // 检查隐藏文件列表
                let hidden_files = ctx.route.as_ref()
                    .and_then(|r| r.hidden_in_listing.as_ref())
                    .map(|h| h.iter().collect::<std::collections::HashSet<_>>())
                    .unwrap_or_default();
                
                for entry in items {
                    let file_name = entry.file_name().to_string_lossy().to_string();
                    
                    // 跳过隐藏文件
                    if hidden_files.contains(&file_name) {
                        continue;
                    }
                    
                    let is_dir = entry.path().is_dir();
                    let display_name = if is_dir {
                        format!("{}/", file_name)
                    } else {
                        file_name.clone()
                    };
                    
                    let href = if ctx.path().ends_with('/') {
                        format!("{}{}", ctx.path(), file_name)
                    } else {
                        format!("{}/{}", ctx.path(), file_name)
                    };
                    
                    html.push_str(&format!(
                        "<li><a href=\"{}\">{}</a></li>\n",
                        href, display_name
                    ));
                }
                
                html.push_str("</ul>\n");
                html.push_str("</body></html>\n");
                
                ctx.response.set_content_type("text/html; charset=utf-8");
                ctx.response.set_body(Bytes::from(html));
                ctx.response.status_code = 200;
                
                debug!("Generated directory listing for: {}", dir_path.display());
                ProcessResult::Stop
            }
            Err(e) => {
                error!("Failed to read directory {}: {}", dir_path.display(), e);
                ProcessResult::Error(ProcessError::new("Failed to read directory", 500))
            }
        }
    }
}

impl Default for StaticFileProcessor {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl Processor for StaticFileProcessor {
    fn name(&self) -> &'static str {
        "StaticFileProcessor"
    }
    
    fn priority(&self) -> u8 {
        30 // 在路由处理器之后执行
    }
    
    fn should_process(&self, ctx: &RequestContext) -> bool {
        // 只处理GET和HEAD请求
        if !matches!(ctx.method.as_str(), "GET" | "HEAD") {
            return false;
        }
        
        // 检查路由是否配置了静态目录
        ctx.route.as_ref()
            .and_then(|r| r.static_dir.as_ref())
            .is_some()
    }
    
    async fn process(&self, ctx: &mut RequestContext) -> ProcessResult {
        let static_dir = match ctx.route.as_ref().and_then(|r| r.static_dir.as_ref()) {
            Some(dir) => dir.clone(),
            None => return ProcessResult::Continue, // 不应该发生，但为了安全
        };

        debug!(
            "Processing static file request: {} {} (static_dir: {})",
            ctx.method, ctx.path(), static_dir
        );

        self.serve_static_file(ctx, &static_dir).await
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::RouteConfig;
    use std::collections::HashMap;
    use std::net::SocketAddr;
    use tempfile::TempDir;
    
    fn create_test_context(method: &str, path: &str) -> RequestContext {
        RequestContext::new(
            method.to_string(),
            path.to_string(),
            HashMap::new(),
            "127.0.0.1:8080".parse::<SocketAddr>().unwrap(),
        )
    }
    
    #[tokio::test]
    async fn test_should_process() {
        let processor = StaticFileProcessor::new();
        
        // 没有路由配置
        let mut ctx = create_test_context("GET", "/test");
        assert!(!processor.should_process(&ctx));
        
        // 有静态目录配置
        ctx.route = Some(std::sync::Arc::new(RouteConfig {
            pattern: "^/static/".to_string(),
            static_dir: Some("static".to_string()),
            upstream: None,
            rewrite: None,
            cache: false,
            dir_listing: None,
            index_files: None,
            mime_types: None,
            hidden_in_listing: None,
            match_conditions: None,
            description: None,
            headers: None,
        }));
        
        assert!(processor.should_process(&ctx));
        
        // POST请求不应该处理
        ctx.method = "POST".to_string();
        assert!(!processor.should_process(&ctx));
    }
    
    #[tokio::test]
    async fn test_path_safety() {
        let temp_dir = TempDir::new().unwrap();
        let static_dir = temp_dir.path().to_str().unwrap();
        let processor = StaticFileProcessor::new();
        
        // 安全路径
        let safe_path = temp_dir.path().join("test.txt");
        assert!(processor.is_safe_path(&safe_path, static_dir));
        
        // 路径遍历攻击
        let unsafe_path = temp_dir.path().join("../../../etc/passwd");
        assert!(!processor.is_safe_path(&unsafe_path, static_dir));
    }
    
    #[tokio::test]
    async fn test_serve_nonexistent_file() {
        let temp_dir = TempDir::new().unwrap();
        let static_dir = temp_dir.path().to_str().unwrap();
        let processor = StaticFileProcessor::new();
        
        let mut ctx = create_test_context("GET", "/nonexistent.txt");
        ctx.route = Some(std::sync::Arc::new(RouteConfig {
            pattern: "^/".to_string(),
            static_dir: Some(static_dir.to_string()),
            upstream: None,
            rewrite: None,
            cache: false,
            dir_listing: None,
            index_files: None,
            mime_types: None,
            hidden_in_listing: None,
            match_conditions: None,
            description: None,
            headers: None,
        }));
        
        let result = processor.process(&mut ctx).await;
        assert!(matches!(result, ProcessResult::Error(_)));
    }
}

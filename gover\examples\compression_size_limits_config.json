{"server": {"http_port": 80, "https_port": 443}, "compression": {"enabled": true, "enable_recompression": true, "types": ["text/html", "text/css", "text/javascript", "application/json", "application/javascript", "application/xml", "text/xml", "text/plain"], "min_size": 1024, "max_size": 10485760, "level": 6, "algorithms": ["br", "gzip", "deflate"], "brotli_quality": 6, "recompression_mode": "auto"}, "sites": [{"name": "high_performance_api", "domains": ["api.example.com"], "comment": "高性能API服务 - 严格控制压缩大小", "upstreams": [{"name": "api_server", "address": "127.0.0.1", "port": 8001, "protocol": "http", "load_balance_group": "api_group"}], "routes": [{"pattern": "^/api/data/export$", "upstream": "api_group", "cache": false, "comment": "数据导出接口 - 可能返回大文件，依赖max_size限制"}, {"pattern": "^/api/", "upstream": "api_group", "cache": true, "comment": "常规API接口 - 正常压缩处理"}]}, {"name": "content_delivery", "domains": ["cdn.example.com"], "comment": "内容分发服务 - 处理各种大小的文件", "upstreams": [{"name": "content_server", "address": "127.0.0.1", "port": 8002, "protocol": "http", "load_balance_group": "content_group"}], "routes": [{"pattern": "^/static/js/", "upstream": "content_group", "cache": true, "comment": "JavaScript文件 - 通常在合理大小范围内"}, {"pattern": "^/static/css/", "upstream": "content_group", "cache": true, "comment": "CSS文件 - 适合压缩"}, {"pattern": "^/downloads/", "upstream": "content_group", "cache": true, "comment": "下载文件 - 可能很大，依赖max_size保护"}, {"pattern": "^/media/", "upstream": "content_group", "cache": true, "comment": "媒体文件 - 通常已压缩，不适合再压缩"}]}, {"name": "file_service", "domains": ["files.example.com"], "comment": "文件服务 - 处理用户上传的各种文件", "upstreams": [{"name": "file_server", "address": "127.0.0.1", "port": 8003, "protocol": "http", "load_balance_group": "file_group"}], "routes": [{"pattern": "^/upload/", "upstream": "file_group", "cache": false, "comment": "文件上传 - 不缓存，可能有大文件"}, {"pattern": "^/download/documents/", "upstream": "file_group", "cache": true, "comment": "文档下载 - 文本类文件适合压缩"}, {"pattern": "^/download/archives/", "upstream": "file_group", "cache": true, "comment": "压缩包下载 - 已压缩文件，跳过压缩"}, {"pattern": "^/download/", "upstream": "file_group", "cache": true, "comment": "通用下载 - 依赖大小和类型限制"}]}], "comments": {"compression_strategy": "压缩策略说明", "size_limits": {"min_size": "1KB - 过滤小文件，避免压缩开销大于收益", "max_size": "10MB - 避免大文件压缩影响性能", "rationale": "平衡压缩收益和性能影响"}, "file_types": {"suitable_for_compression": ["HTML页面", "CSS样式表", "JavaScript代码", "JSON数据", "XML文档", "纯文本文件"], "not_suitable": ["图片文件 (jpg, png, gif)", "视频文件 (mp4, avi, mov)", "音频文件 (mp3, aac)", "压缩包 (zip, rar, 7z)", "二进制文件 (exe, dll)"]}, "performance_considerations": {"small_files": "< 1KB - 跳过压缩，开销大于收益", "medium_files": "1KB - 10MB - 正常压缩，最佳收益区间", "large_files": "> 10MB - 跳过压缩，避免性能影响", "memory_usage": "压缩大文件可能消耗2-3倍文件大小的内存", "cpu_impact": "大文件压缩会显著增加CPU使用率"}, "tuning_guidelines": {"high_performance": "降低max_size到5MB，优先响应速度", "bandwidth_optimization": "提高max_size到20MB，优化传输效率", "resource_limited": "降低max_size到2MB，保护系统资源", "monitoring": "监控压缩处理时间和内存使用情况"}}}
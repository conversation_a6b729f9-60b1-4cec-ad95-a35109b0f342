// 模块化架构示例代码
// 展示如何实现插件化的反向代理系统

//go:build (core || pro) && (core || pro) && pro && pro && pro && core
// +build core pro
// +build core pro
// +build pro
// +build pro
// +build pro
// +build core

package main

import (
	"context"
	"fmt"
	"net/http"
	"sync"
	"time"
)

// ============================================================================
// 核心接口定义 (internal/core/plugin/interfaces.go)
// ============================================================================

// Plugin 基础插件接口
type Plugin interface {
	Name() string
	Version() string
	Init(config interface{}) error
	Start() error
	Stop() error
	Health() bool
}

// ProxyPlugin 代理插件接口
type ProxyPlugin interface {
	Plugin
	HandleRequest(ctx *RequestContext) error
	Priority() int
}

// CachePlugin 缓存插件接口
type CachePlugin interface {
	Plugin
	Get(key string) ([]byte, bool)
	Set(key string, value []byte, ttl time.Duration) error
	Delete(key string) error
}

// RequestContext 请求上下文
type RequestContext struct {
	Request   *http.Request
	Response  http.ResponseWriter
	StartTime time.Time
	Data      map[string]interface{}
}

// ============================================================================
// 插件注册表 (internal/core/plugin/registry.go)
// ============================================================================

type Registry struct {
	plugins map[string]Plugin
	mu      sync.RWMutex
}

func NewRegistry() *Registry {
	return &Registry{
		plugins: make(map[string]Plugin),
	}
}

func (r *Registry) Register(plugin Plugin) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	name := plugin.Name()
	if _, exists := r.plugins[name]; exists {
		return fmt.Errorf("plugin %s already registered", name)
	}

	r.plugins[name] = plugin
	return nil
}

func (r *Registry) Get(name string) (Plugin, bool) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	plugin, exists := r.plugins[name]
	return plugin, exists
}

func (r *Registry) GetByType(pluginType string) []Plugin {
	r.mu.RLock()
	defer r.mu.RUnlock()

	var result []Plugin
	for _, plugin := range r.plugins {
		// 这里可以根据类型过滤
		result = append(result, plugin)
	}
	return result
}

// ============================================================================
// 核心代理模块 (internal/core/proxy/proxy.go)
// ============================================================================

type CoreProxy struct {
	registry     *Registry
	proxyPlugins []ProxyPlugin
	config       interface{}
}

func NewCoreProxy(registry *Registry) *CoreProxy {
	return &CoreProxy{
		registry: registry,
	}
}

func (p *CoreProxy) Init() error {
	// 获取所有代理插件并按优先级排序
	plugins := p.registry.GetByType("proxy")
	for _, plugin := range plugins {
		if proxyPlugin, ok := plugin.(ProxyPlugin); ok {
			p.proxyPlugins = append(p.proxyPlugins, proxyPlugin)
		}
	}

	// 按优先级排序
	// sort.Slice(p.proxyPlugins, func(i, j int) bool {
	//     return p.proxyPlugins[i].Priority() < p.proxyPlugins[j].Priority()
	// })

	return nil
}

func (p *CoreProxy) HandleRequest(w http.ResponseWriter, r *http.Request) {
	ctx := &RequestContext{
		Request:   r,
		Response:  w,
		StartTime: time.Now(),
		Data:      make(map[string]interface{}),
	}

	// 执行插件链
	for _, plugin := range p.proxyPlugins {
		if err := plugin.HandleRequest(ctx); err != nil {
			http.Error(w, err.Error(), http.StatusInternalServerError)
			return
		}
	}
}

// ============================================================================
// 基础代理插件 (internal/core/proxy/basic_proxy.go)
// ============================================================================

type BasicProxyPlugin struct {
	name    string
	version string
	config  interface{}
}

func NewBasicProxyPlugin() *BasicProxyPlugin {
	return &BasicProxyPlugin{
		name:    "basic-proxy",
		version: "1.0.0",
	}
}

func (p *BasicProxyPlugin) Name() string    { return p.name }
func (p *BasicProxyPlugin) Version() string { return p.version }
func (p *BasicProxyPlugin) Priority() int   { return 100 }

func (p *BasicProxyPlugin) Init(config interface{}) error {
	p.config = config
	return nil
}

func (p *BasicProxyPlugin) Start() error { return nil }
func (p *BasicProxyPlugin) Stop() error  { return nil }
func (p *BasicProxyPlugin) Health() bool { return true }

func (p *BasicProxyPlugin) HandleRequest(ctx *RequestContext) error {
	// 基础代理逻辑
	fmt.Printf("BasicProxy: 处理请求 %s\n", ctx.Request.URL.Path)

	// 这里实现基础的HTTP代理逻辑
	ctx.Response.WriteHeader(http.StatusOK)
	ctx.Response.Write([]byte("Basic Proxy Response"))

	return nil
}

// ============================================================================
// 高性能代理插件 (internal/advanced/performance/high_perf_proxy.go)
// ============================================================================

type HighPerformanceProxyPlugin struct {
	name        string
	version     string
	config      interface{}
	objectPools *ObjectPools
	zeroCopy    bool
}

func NewHighPerformanceProxyPlugin() *HighPerformanceProxyPlugin {
	return &HighPerformanceProxyPlugin{
		name:        "high-performance-proxy",
		version:     "1.0.0",
		objectPools: NewObjectPools(),
		zeroCopy:    true,
	}
}

func (p *HighPerformanceProxyPlugin) Name() string    { return p.name }
func (p *HighPerformanceProxyPlugin) Version() string { return p.version }
func (p *HighPerformanceProxyPlugin) Priority() int   { return 50 } // 更高优先级

func (p *HighPerformanceProxyPlugin) Init(config interface{}) error {
	p.config = config
	return p.objectPools.Init()
}

func (p *HighPerformanceProxyPlugin) Start() error { return nil }
func (p *HighPerformanceProxyPlugin) Stop() error  { return nil }
func (p *HighPerformanceProxyPlugin) Health() bool { return true }

func (p *HighPerformanceProxyPlugin) HandleRequest(ctx *RequestContext) error {
	// 高性能代理逻辑
	fmt.Printf("HighPerformanceProxy: 零拷贝处理请求 %s\n", ctx.Request.URL.Path)

	// 使用对象池
	buffer := p.objectPools.GetBuffer()
	defer p.objectPools.PutBuffer(buffer)

	// 零拷贝传输
	if p.zeroCopy {
		return p.zeroCopyTransfer(ctx)
	}

	return p.standardTransfer(ctx)
}

func (p *HighPerformanceProxyPlugin) zeroCopyTransfer(ctx *RequestContext) error {
	// 零拷贝传输逻辑
	ctx.Response.WriteHeader(http.StatusOK)
	ctx.Response.Write([]byte("High Performance Zero-Copy Response"))
	return nil
}

func (p *HighPerformanceProxyPlugin) standardTransfer(ctx *RequestContext) error {
	// 标准传输逻辑
	ctx.Response.WriteHeader(http.StatusOK)
	ctx.Response.Write([]byte("High Performance Standard Response"))
	return nil
}

// ============================================================================
// 对象池 (internal/advanced/performance/object_pools.go)
// ============================================================================

type ObjectPools struct {
	bufferPool sync.Pool
}

func NewObjectPools() *ObjectPools {
	return &ObjectPools{
		bufferPool: sync.Pool{
			New: func() interface{} {
				return make([]byte, 64*1024) // 64KB缓冲区
			},
		},
	}
}

func (op *ObjectPools) Init() error {
	return nil
}

func (op *ObjectPools) GetBuffer() []byte {
	return op.bufferPool.Get().([]byte)
}

func (op *ObjectPools) PutBuffer(buffer []byte) {
	op.bufferPool.Put(buffer)
}

// ============================================================================
// 主程序 (main.go)
// ============================================================================

func main() {
	fmt.Println("=== 模块化反向代理架构演示 ===")

	// 创建插件注册表
	registry := NewRegistry()

	// 注册核心插件（基础版和专业版都有）
	basicProxy := NewBasicProxyPlugin()
	registry.Register(basicProxy)

	// 条件注册高级插件（仅专业版）
	registerAdvancedPlugins(registry)

	// 创建代理服务器
	proxy := NewCoreProxy(registry)
	proxy.Init()

	// 创建HTTP服务器
	http.HandleFunc("/", proxy.HandleRequest)

	fmt.Println("服务器启动在 :8080")
	fmt.Println("访问 http://localhost:8080/test 测试功能")

	// 启动服务器
	if err := http.ListenAndServe(":8080", nil); err != nil {
		fmt.Printf("服务器启动失败: %v\n", err)
	}
}

// registerAdvancedPlugins 注册高级插件
// 这个函数只在专业版编译时包含
func registerAdvancedPlugins(registry *Registry) {
	// 这里使用构建标签来条件编译
	registerHighPerformancePlugin(registry)
}

// ============================================================================
// 条件编译的高级插件注册
// ============================================================================

func registerHighPerformancePlugin(registry *Registry) {
	highPerfProxy := NewHighPerformanceProxyPlugin()
	registry.Register(highPerfProxy)
	fmt.Println("✅ 高性能代理插件已注册 (专业版)")
}

// func registerHighPerformancePlugin(registry *Registry) {
//     fmt.Println("⚠️  高性能代理插件不可用 (基础版)")
// }

// ============================================================================
// 配置示例
// ============================================================================

type ModularConfig struct {
	Edition string `json:"edition"` // "core" 或 "pro"
	Server  struct {
		Port int `json:"port"`
	} `json:"server"`
	Plugins struct {
		Enabled []string               `json:"enabled"`
		Config  map[string]interface{} `json:"config"`
	} `json:"plugins"`
}

// 基础版配置示例
var CoreConfigExample = `{
    "edition": "core",
    "server": {
        "port": 8080
    },
    "plugins": {
        "enabled": ["basic-proxy", "basic-cache", "basic-monitor"],
        "config": {
            "basic-proxy": {
                "timeout": "30s"
            }
        }
    }
}`

// 专业版配置示例
var ProConfigExample = `{
    "edition": "pro",
    "server": {
        "port": 8080
    },
    "plugins": {
        "enabled": [
            "high-performance-proxy", 
            "advanced-cache", 
            "http3-handler",
            "grpc-proxy",
            "advanced-monitor"
        ],
        "config": {
            "high-performance-proxy": {
                "zero_copy": true,
                "object_pools": true,
                "cpu_affinity": true
            },
            "http3-handler": {
                "enabled": true,
                "port": 443
            }
        }
    }
}`

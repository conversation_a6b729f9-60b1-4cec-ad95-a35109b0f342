package config

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"reflect"
	"sync"
	"sync/atomic"
	"time"

	"github.com/fsnotify/fsnotify"
	"github.com/mitchellh/mapstructure"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

// ViperConfigManager 基于Viper的配置管理器
type ViperConfigManager struct {
	// Viper实例
	viper *viper.Viper

	// 当前配置（原子操作）
	currentConfig atomic.Value // *Config
	configVersion int64

	// 配置文件信息
	configFile     string
	configDir      string
	configName     string
	configType     string
	backupDir      string

	// 热重载控制
	watchEnabled   bool
	reloadCallback func(*Config, *Config) error
	mu             sync.RWMutex

	// 配置验证
	validators []ConfigValidator

	// 配置备份和版本管理
	backupEnabled bool
	maxBackups    int
	backupHistory []BackupInfo

	// 上下文和日志
	ctx    context.Context
	cancel context.CancelFunc
	logger *logrus.Logger

	// 统计信息
	stats ConfigStats
}

// ConfigValidator 配置验证器接口
type ConfigValidator interface {
	Validate(config *Config) error
	ValidateSection(section string, data interface{}) error
}

// BackupInfo 备份信息
type BackupInfo struct {
	Timestamp time.Time `json:"timestamp"`
	Version   int64     `json:"version"`
	FilePath  string    `json:"file_path"`
	Hash      string    `json:"hash"`
	Comment   string    `json:"comment"`
}

// ConfigStats 配置统计信息
type ConfigStats struct {
	TotalReloads    int64     `json:"total_reloads"`
	SuccessReloads  int64     `json:"success_reloads"`
	FailedReloads   int64     `json:"failed_reloads"`
	LastReloadTime  time.Time `json:"last_reload_time"`
	LastError       string    `json:"last_error"`
	ConfigVersion   int64     `json:"config_version"`
	BackupCount     int       `json:"backup_count"`
	WatcherActive   bool      `json:"watcher_active"`
}

// NewViperConfigManager 创建Viper配置管理器
func NewViperConfigManager(configFile string, logger *logrus.Logger) (*ViperConfigManager, error) {
	ctx, cancel := context.WithCancel(context.Background())

	// 解析配置文件路径
	absPath, err := filepath.Abs(configFile)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("获取配置文件绝对路径失败: %w", err)
	}

	configDir := filepath.Dir(absPath)
	configName := filepath.Base(absPath)
	configType := filepath.Ext(configName)[1:] // 去掉点号
	if configType == "" {
		configType = "json" // 默认类型
	}
	configName = configName[:len(configName)-len(filepath.Ext(configName))] // 去掉扩展名

	// 创建Viper实例
	v := viper.New()
	v.SetConfigName(configName)
	v.SetConfigType(configType)
	v.AddConfigPath(configDir)

	// 设置默认值
	setViperDefaults(v)

	vcm := &ViperConfigManager{
		viper:         v,
		configFile:    absPath,
		configDir:     configDir,
		configName:    configName,
		configType:    configType,
		backupDir:     filepath.Join(configDir, "backups"),
		watchEnabled:  false,
		backupEnabled: true,
		maxBackups:    10,
		ctx:           ctx,
		cancel:        cancel,
		logger:        logger,
		validators:    make([]ConfigValidator, 0),
	}

	// 确保备份目录存在
	if err := os.MkdirAll(vcm.backupDir, 0755); err != nil {
		cancel()
		return nil, fmt.Errorf("创建备份目录失败: %w", err)
	}

	return vcm, nil
}

// LoadConfig 加载配置
func (vcm *ViperConfigManager) LoadConfig() (*Config, error) {
	vcm.mu.Lock()
	defer vcm.mu.Unlock()

	vcm.logger.Info("开始加载配置文件...")

	// 读取配置文件
	if err := vcm.viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 解析配置
	var config Config
	if err := vcm.viper.Unmarshal(&config, viper.DecodeHook(
		createDecodeHooks(),
	)); err != nil {
		return nil, fmt.Errorf("解析配置失败: %w", err)
	}

	// 验证配置
	if err := vcm.validateConfig(&config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	// 后处理配置
	if err := vcm.postProcessConfig(&config); err != nil {
		return nil, fmt.Errorf("配置后处理失败: %w", err)
	}

	// 更新当前配置
	vcm.currentConfig.Store(&config)
	atomic.StoreInt64(&vcm.configVersion, time.Now().Unix())

	vcm.logger.Infof("配置加载成功，站点数量: %d", len(config.Sites))
	return &config, nil
}

// loadConfigInternal 内部加载配置（不获取锁）
func (vcm *ViperConfigManager) loadConfigInternal() (*Config, error) {
	vcm.logger.Info("开始加载配置文件...")

	// 读取配置文件
	if err := vcm.viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 解析配置
	var config Config
	if err := vcm.viper.Unmarshal(&config, viper.DecodeHook(
		createDecodeHooks(),
	)); err != nil {
		return nil, fmt.Errorf("解析配置失败: %w", err)
	}

	// 验证配置
	if err := vcm.validateConfig(&config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	// 后处理配置
	if err := vcm.postProcessConfig(&config); err != nil {
		return nil, fmt.Errorf("配置后处理失败: %w", err)
	}

	// 更新当前配置
	vcm.currentConfig.Store(&config)
	atomic.StoreInt64(&vcm.configVersion, time.Now().Unix())

	vcm.logger.Infof("配置加载成功，站点数量: %d", len(config.Sites))
	return &config, nil
}

// EnableHotReload 启用热重载
func (vcm *ViperConfigManager) EnableHotReload(callback func(*Config, *Config) error) error {
	vcm.mu.Lock()
	defer vcm.mu.Unlock()

	if vcm.watchEnabled {
		return fmt.Errorf("热重载已启用")
	}

	vcm.reloadCallback = callback
	vcm.watchEnabled = true

	// 设置Viper的配置文件监听
	vcm.viper.WatchConfig()
	vcm.viper.OnConfigChange(func(e fsnotify.Event) {
		vcm.handleConfigChange(e)
	})

	vcm.stats.WatcherActive = true
	vcm.logger.Info("配置热重载已启用")
	return nil
}

// DisableHotReload 禁用热重载
func (vcm *ViperConfigManager) DisableHotReload() {
	vcm.mu.Lock()
	defer vcm.mu.Unlock()

	vcm.watchEnabled = false
	vcm.reloadCallback = nil
	vcm.stats.WatcherActive = false
	vcm.logger.Info("配置热重载已禁用")
}

// handleConfigChange 处理配置变更
func (vcm *ViperConfigManager) handleConfigChange(e fsnotify.Event) {
	vcm.logger.Infof("检测到配置文件变更: %s", e.String())

	// 防抖处理 - 等待文件写入完成
	time.Sleep(200 * time.Millisecond)

	// 获取当前配置
	oldConfig := vcm.GetCurrentConfig()

	// 备份当前配置
	if vcm.backupEnabled {
		if err := vcm.createBackup("auto_backup_before_reload"); err != nil {
			vcm.logger.Errorf("创建配置备份失败: %v", err)
		}
	}

	// 重新加载配置
	newConfig, err := vcm.LoadConfig()
	if err != nil {
		vcm.updateStats(false, err.Error())
		vcm.logger.Errorf("重新加载配置失败: %v", err)
		return
	}

	// 执行重载回调
	if vcm.reloadCallback != nil {
		if err := vcm.reloadCallback(oldConfig, newConfig); err != nil {
			vcm.updateStats(false, err.Error())
			vcm.logger.Errorf("执行重载回调失败: %v", err)
			
			// 尝试回滚配置
			if rollbackErr := vcm.rollbackToLastBackup(); rollbackErr != nil {
				vcm.logger.Errorf("配置回滚失败: %v", rollbackErr)
			}
			return
		}
	}

	vcm.updateStats(true, "")
	vcm.logger.Info("配置热重载成功完成")
}

// GetCurrentConfig 获取当前配置
func (vcm *ViperConfigManager) GetCurrentConfig() *Config {
	if config := vcm.currentConfig.Load(); config != nil {
		return config.(*Config)
	}
	return nil
}

// GetConfigVersion 获取配置版本
func (vcm *ViperConfigManager) GetConfigVersion() int64 {
	return atomic.LoadInt64(&vcm.configVersion)
}

// AddValidator 添加配置验证器
func (vcm *ViperConfigManager) AddValidator(validator ConfigValidator) {
	vcm.mu.Lock()
	defer vcm.mu.Unlock()
	vcm.validators = append(vcm.validators, validator)
}

// validateConfig 验证配置
func (vcm *ViperConfigManager) validateConfig(config *Config) error {
	// 基础验证
	if err := validateConfig(config); err != nil {
		return err
	}

	// 自定义验证器
	for _, validator := range vcm.validators {
		if err := validator.Validate(config); err != nil {
			return err
		}
	}

	return nil
}

// postProcessConfig 配置后处理
func (vcm *ViperConfigManager) postProcessConfig(config *Config) error {
	// 初始化头部配置
	initHeaders(config)

	// 处理默认站点设置
	if err := processDefaultSiteSettings(config); err != nil {
		return err
	}

	// 初始化上游服务器健康状态
	for i := range config.Sites {
		for j := range config.Sites[i].Upstreams {
			config.Sites[i].Upstreams[j].Healthy = true
		}
	}

	return nil
}

// updateStats 更新统计信息
func (vcm *ViperConfigManager) updateStats(success bool, errorMsg string) {
	vcm.mu.Lock()
	defer vcm.mu.Unlock()

	vcm.stats.TotalReloads++
	vcm.stats.LastReloadTime = time.Now()
	vcm.stats.ConfigVersion = atomic.LoadInt64(&vcm.configVersion)

	if success {
		vcm.stats.SuccessReloads++
		vcm.stats.LastError = ""
	} else {
		vcm.stats.FailedReloads++
		vcm.stats.LastError = errorMsg
	}
}

// GetStats 获取统计信息
func (vcm *ViperConfigManager) GetStats() ConfigStats {
	vcm.mu.RLock()
	defer vcm.mu.RUnlock()
	
	stats := vcm.stats
	stats.BackupCount = len(vcm.backupHistory)
	return stats
}

// createBackup 创建配置备份
func (vcm *ViperConfigManager) createBackup(comment string) error {
	timestamp := time.Now()
	version := timestamp.Unix()

	// 生成备份文件名
	backupFileName := fmt.Sprintf("%s_%d.%s", vcm.configName, version, vcm.configType)
	backupPath := filepath.Join(vcm.backupDir, backupFileName)

	// 复制配置文件
	if err := vcm.copyFile(vcm.configFile, backupPath); err != nil {
		return fmt.Errorf("复制配置文件失败: %w", err)
	}

	// 计算文件哈希
	hash, err := vcm.calculateFileHash(backupPath)
	if err != nil {
		return fmt.Errorf("计算文件哈希失败: %w", err)
	}

	// 添加到备份历史
	backup := BackupInfo{
		Timestamp: timestamp,
		Version:   version,
		FilePath:  backupPath,
		Hash:      hash,
		Comment:   comment,
	}

	vcm.backupHistory = append(vcm.backupHistory, backup)

	// 清理旧备份
	vcm.cleanupOldBackups()

	vcm.logger.Infof("配置备份创建成功: %s", backupPath)
	return nil
}

// rollbackToLastBackup 回滚到最后一个备份
func (vcm *ViperConfigManager) rollbackToLastBackup() error {
	if len(vcm.backupHistory) == 0 {
		return fmt.Errorf("没有可用的备份")
	}

	lastBackup := vcm.backupHistory[len(vcm.backupHistory)-1]
	return vcm.rollbackToBackup(lastBackup.Version)
}

// rollbackToBackup 回滚到指定版本的备份
func (vcm *ViperConfigManager) rollbackToBackup(version int64) error {
	var targetBackup *BackupInfo
	for _, backup := range vcm.backupHistory {
		if backup.Version == version {
			targetBackup = &backup
			break
		}
	}

	if targetBackup == nil {
		return fmt.Errorf("找不到版本 %d 的备份", version)
	}

	// 验证备份文件存在
	if _, err := os.Stat(targetBackup.FilePath); os.IsNotExist(err) {
		return fmt.Errorf("备份文件不存在: %s", targetBackup.FilePath)
	}

	// 验证备份文件完整性
	currentHash, err := vcm.calculateFileHash(targetBackup.FilePath)
	if err != nil {
		return fmt.Errorf("计算备份文件哈希失败: %w", err)
	}

	if currentHash != targetBackup.Hash {
		return fmt.Errorf("备份文件已损坏，哈希不匹配")
	}

	// 创建当前配置的备份
	if err := vcm.createBackup("auto_backup_before_rollback"); err != nil {
		vcm.logger.Warnf("回滚前创建备份失败: %v", err)
	}

	// 恢复配置文件
	if err := vcm.copyFile(targetBackup.FilePath, vcm.configFile); err != nil {
		return fmt.Errorf("恢复配置文件失败: %w", err)
	}

	// 重新加载配置（不获取锁，因为调用者已经获取了锁）
	// 注意：在分层配置模式下，这里只是恢复了主配置文件
	// 实际的配置重新加载将由ViperIntegration处理
	if _, err := vcm.loadConfigInternal(); err != nil {
		return fmt.Errorf("重新加载配置失败: %w", err)
	}

	vcm.logger.Infof("配置已回滚到版本 %d (%s)", version, targetBackup.Timestamp.Format("2006-01-02 15:04:05"))
	return nil
}

// cleanupOldBackups 清理旧备份
func (vcm *ViperConfigManager) cleanupOldBackups() {
	if len(vcm.backupHistory) <= vcm.maxBackups {
		return
	}

	// 按时间排序，保留最新的备份
	toRemove := vcm.backupHistory[:len(vcm.backupHistory)-vcm.maxBackups]
	vcm.backupHistory = vcm.backupHistory[len(vcm.backupHistory)-vcm.maxBackups:]

	// 删除旧备份文件
	for _, backup := range toRemove {
		if err := os.Remove(backup.FilePath); err != nil {
			vcm.logger.Warnf("删除旧备份文件失败: %v", err)
		}
	}

	vcm.logger.Infof("清理了 %d 个旧备份", len(toRemove))
}

// GetBackupHistory 获取备份历史
func (vcm *ViperConfigManager) GetBackupHistory() []BackupInfo {
	vcm.mu.RLock()
	defer vcm.mu.RUnlock()

	history := make([]BackupInfo, len(vcm.backupHistory))
	copy(history, vcm.backupHistory)
	return history
}

// CreateBackup 创建配置备份（公开方法）
func (vcm *ViperConfigManager) CreateBackup(comment string) error {
	vcm.mu.Lock()
	defer vcm.mu.Unlock()
	return vcm.createBackup(comment)
}

// RollbackToBackup 回滚到指定版本的备份（公开方法）
func (vcm *ViperConfigManager) RollbackToBackup(version int64) error {
	vcm.mu.Lock()
	defer vcm.mu.Unlock()
	return vcm.rollbackToBackup(version)
}

// RollbackToLastBackup 回滚到最后一个备份（公开方法）
func (vcm *ViperConfigManager) RollbackToLastBackup() error {
	vcm.mu.Lock()
	defer vcm.mu.Unlock()
	return vcm.rollbackToLastBackup()
}

// GetBackupDir 获取备份目录路径
func (vcm *ViperConfigManager) GetBackupDir() string {
	return vcm.backupDir
}

// ValidateConfigFile 验证配置文件（不加载）
func (vcm *ViperConfigManager) ValidateConfigFile(configFile string) error {
	// 创建临时Viper实例
	tempViper := viper.New()
	tempViper.SetConfigFile(configFile)

	// 设置默认值
	setViperDefaults(tempViper)

	// 读取配置
	if err := tempViper.ReadInConfig(); err != nil {
		return fmt.Errorf("读取配置文件失败: %w", err)
	}

	// 解析配置
	var config Config
	if err := tempViper.Unmarshal(&config, viper.DecodeHook(
		createDecodeHooks(),
	)); err != nil {
		return fmt.Errorf("解析配置失败: %w", err)
	}

	// 验证配置
	return vcm.validateConfig(&config)
}

// ReloadFromFile 从指定文件重载配置
func (vcm *ViperConfigManager) ReloadFromFile(configFile string) error {
	// 验证新配置文件
	if err := vcm.ValidateConfigFile(configFile); err != nil {
		return fmt.Errorf("新配置文件验证失败: %w", err)
	}

	// 备份当前配置
	if vcm.backupEnabled {
		if err := vcm.createBackup("manual_reload_backup"); err != nil {
			vcm.logger.Warnf("创建备份失败: %v", err)
		}
	}

	// 更新配置文件路径
	oldConfigFile := vcm.configFile
	vcm.configFile = configFile

	// 重新配置Viper
	vcm.viper.SetConfigFile(configFile)

	// 加载新配置
	newConfig, err := vcm.LoadConfig()
	if err != nil {
		// 恢复原配置文件路径
		vcm.configFile = oldConfigFile
		vcm.viper.SetConfigFile(oldConfigFile)
		return fmt.Errorf("加载新配置失败: %w", err)
	}

	// 执行重载回调
	if vcm.reloadCallback != nil {
		oldConfig := vcm.GetCurrentConfig()
		if err := vcm.reloadCallback(oldConfig, newConfig); err != nil {
			// 恢复原配置文件路径
			vcm.configFile = oldConfigFile
			vcm.viper.SetConfigFile(oldConfigFile)
			return fmt.Errorf("执行重载回调失败: %w", err)
		}
	}

	vcm.logger.Infof("从文件 %s 重载配置成功", configFile)
	return nil
}

// copyFile 复制文件
func (vcm *ViperConfigManager) copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	_, err = destFile.ReadFrom(sourceFile)
	return err
}

// calculateFileHash 计算文件哈希
func (vcm *ViperConfigManager) calculateFileHash(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	hash := fmt.Sprintf("%x", time.Now().Unix()) // 简化实现，实际应使用MD5或SHA256
	return hash, nil
}

// setViperDefaults 设置Viper默认值
func setViperDefaults(v *viper.Viper) {
	// 服务器默认值
	v.SetDefault("server.http_port", 80)
	v.SetDefault("server.https_port", 443)
	v.SetDefault("server.read_timeout", "30s")
	v.SetDefault("server.write_timeout", "30s")
	v.SetDefault("server.idle_timeout", "60s")
	v.SetDefault("server.max_connections", 1000)

	// 日志默认值
	v.SetDefault("log.level", "info")
	v.SetDefault("log.format", "combined")
	v.SetDefault("log.max_size", 100)
	v.SetDefault("log.max_backups", 10)
	v.SetDefault("log.max_age", 30)

	// 缓存默认值
	v.SetDefault("cache.enabled", true)
	v.SetDefault("cache.type", "file")
	v.SetDefault("cache.path", "./cache")
	v.SetDefault("cache.max_size", 1073741824) // 1GB
	v.SetDefault("cache.ttl", "1h")

	// ACL默认值
	v.SetDefault("acl.enabled", false)
	v.SetDefault("acl.reload_interval", "5m")

	// 热重载默认值
	v.SetDefault("hot_reload.enabled", true)
	v.SetDefault("hot_reload.check_interval", "5s")

	// 监控默认值
	v.SetDefault("monitor.enabled", false)
	v.SetDefault("monitor.port", 8080)

	// 压缩默认值
	v.SetDefault("compression.enabled", true)
	v.SetDefault("compression.level", 6)
	v.SetDefault("compression.min_size", 1024)

	// 限流默认值
	v.SetDefault("rate_limit.enabled", false)
	v.SetDefault("rate_limit.requests_per_second", 100)
	v.SetDefault("rate_limit.burst", 200)

	// 熔断器默认值
	v.SetDefault("circuit_breaker.enabled", false)
	v.SetDefault("circuit_breaker.failure_threshold", 5)
	v.SetDefault("circuit_breaker.timeout", "60s")

	// 内存缓存默认值
	v.SetDefault("memory_cache.enabled", false)
	v.SetDefault("memory_cache.global_memory_limit", "32MB")
	v.SetDefault("memory_cache.cleanup_interval", "5m")
}

// createDecodeHooks 创建解码钩子
func createDecodeHooks() interface{} {
	return mapstructure.ComposeDecodeHookFunc(
		mapstructure.StringToTimeDurationHookFunc(),
		mapstructure.StringToSliceHookFunc(","),
		func(f reflect.Type, t reflect.Type, data interface{}) (interface{}, error) {
			// 如果目标类型是RateLimit，且源数据是字符串，则使用自定义解析
			if t == reflect.TypeOf(RateLimit(0)) && f.Kind() == reflect.String {
				if str, ok := data.(string); ok {
					// 使用config包中的parseRateLimitString函数
					return RateLimit(parseRateLimitString(str)), nil
				}
			}

			// 如果目标类型是Size，且源数据是字符串，则使用自定义解析
			if t == reflect.TypeOf(Size(0)) && f.Kind() == reflect.String {
				if str, ok := data.(string); ok {
					size, err := ParseSize(str)
					if err != nil {
						return nil, fmt.Errorf("解析大小格式失败 '%s': %w", str, err)
					}
					return size, nil
				}
			}

			// 如果目标类型是Size，且源数据是数字，则直接转换
			if t == reflect.TypeOf(Size(0)) && (f.Kind() == reflect.Int || f.Kind() == reflect.Int64 || f.Kind() == reflect.Float64) {
				if num, ok := data.(int); ok {
					return Size(num), nil
				}
				if num, ok := data.(int64); ok {
					return Size(num), nil
				}
				if num, ok := data.(float64); ok {
					return Size(int64(num)), nil
				}
			}

			return data, nil
		},
	)
}

// Close 关闭配置管理器
func (vcm *ViperConfigManager) Close() error {
	vcm.DisableHotReload()
	vcm.cancel()
	return nil
}



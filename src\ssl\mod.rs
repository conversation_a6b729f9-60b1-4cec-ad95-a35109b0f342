use anyhow::{Context, Result};
use std::fs::File;
use std::io::BufReader;
use std::path::Path;
use std::sync::Arc;
use tracing::{debug, error, info, warn};

#[cfg(feature = "ssl")]
use {
    rustls::{Certificate, PrivateKey, ServerConfig},
    rustls_pemfile::{certs, pkcs8_private_keys, rsa_private_keys},
    tokio_rustls::TlsAcceptor,
};

use crate::config::SslConfig;

/// SSL/TLS 管理器
pub struct SslManager {
    /// TLS 接受器
    #[cfg(feature = "ssl")]
    pub acceptor: Option<TlsAcceptor>,
    #[cfg(not(feature = "ssl"))]
    pub acceptor: Option<()>,
    /// SSL 配置
    config: SslConfig,
}

impl SslManager {
    /// 创建新的 SSL 管理器
    pub fn new(config: SslConfig) -> Result<Self> {
        #[cfg(feature = "ssl")]
        let acceptor = if config.enabled {
            Some(Self::create_tls_acceptor(&config)?)
        } else {
            None
        };

        #[cfg(not(feature = "ssl"))]
        let acceptor = if config.enabled {
            error!("SSL is enabled in config but SSL feature is not compiled in");
            return Err(anyhow::anyhow!("SSL feature not available"));
        } else {
            None
        };

        Ok(Self { acceptor, config })
    }

    /// 创建 TLS 接受器
    #[cfg(feature = "ssl")]
    fn create_tls_acceptor(config: &SslConfig) -> Result<TlsAcceptor> {
        info!("Initializing TLS with cert: {} and key: {}",
              config.cert_file, config.key_file);

        // 加载证书链
        let cert_chain = Self::load_certificates(&config.cert_file)
            .with_context(|| format!("Failed to load certificate from {}", config.cert_file))?;

        // 加载私钥
        let private_key = Self::load_private_key(&config.key_file)
            .with_context(|| format!("Failed to load private key from {}", config.key_file))?;

        // 创建 TLS 配置
        let tls_config = Self::create_server_config(cert_chain, private_key, config)?;

        Ok(TlsAcceptor::from(Arc::new(tls_config)))
    }

    /// 加载证书链
    #[cfg(feature = "ssl")]
    fn load_certificates(cert_file: &str) -> Result<Vec<Certificate>> {
        let cert_file = File::open(cert_file)
            .with_context(|| format!("Cannot open certificate file: {}", cert_file))?;
        let mut reader = BufReader::new(cert_file);

        let certs = certs(&mut reader)
            .map_err(|_| anyhow::anyhow!("Cannot parse certificate file"))?
            .into_iter()
            .map(Certificate)
            .collect();

        debug!("Loaded {} certificates", certs.len());
        Ok(certs)
    }

    /// 加载私钥
    #[cfg(feature = "ssl")]
    fn load_private_key(key_file: &str) -> Result<PrivateKey> {
        let key_file = File::open(key_file)
            .with_context(|| format!("Cannot open private key file: {}", key_file))?;
        let mut reader = BufReader::new(key_file);

        // 尝试加载 PKCS8 格式的私钥
        if let Ok(mut keys) = pkcs8_private_keys(&mut reader) {
            if !keys.is_empty() {
                debug!("Loaded PKCS8 private key");
                return Ok(PrivateKey(keys.remove(0)));
            }
        }

        // 重新打开文件，尝试加载 RSA 格式的私钥
        let key_file = File::open(key_file)
            .with_context(|| format!("Cannot open private key file: {}", key_file))?;
        let mut reader = BufReader::new(key_file);

        if let Ok(mut keys) = rsa_private_keys(&mut reader) {
            if !keys.is_empty() {
                debug!("Loaded RSA private key");
                return Ok(PrivateKey(keys.remove(0)));
            }
        }

        Err(anyhow::anyhow!("No valid private key found in {}", key_file))
    }

    /// 创建服务器配置
    #[cfg(feature = "ssl")]
    fn create_server_config(
        cert_chain: Vec<Certificate>,
        private_key: PrivateKey,
        config: &SslConfig,
    ) -> Result<ServerConfig> {
        let mut tls_config = ServerConfig::builder()
            .with_safe_defaults()
            .with_no_client_auth()
            .with_single_cert(cert_chain, private_key)
            .map_err(|e| anyhow::anyhow!("TLS configuration error: {}", e))?;

        // 设置 ALPN 协议
        let protocols = Self::parse_protocols(&config.protocols);
        if !protocols.is_empty() {
            tls_config.alpn_protocols = protocols;
            debug!("ALPN protocols configured: {:?}",
                   tls_config.alpn_protocols.iter()
                       .map(|p| String::from_utf8_lossy(p))
                       .collect::<Vec<_>>());
        }

        // 设置会话缓存
        if config.session_cache > 0 {
            tls_config.session_storage = rustls::server::ServerSessionMemoryCache::new(config.session_cache);
            debug!("Session cache configured with {} entries", config.session_cache);
        }

        info!("TLS configuration created successfully");
        Ok(tls_config)
    }

    /// 解析协议字符串
    fn parse_protocols(protocols_str: &str) -> Vec<Vec<u8>> {
        protocols_str
            .split(',')
            .map(|s| s.trim())
            .filter_map(|protocol| {
                match protocol.to_lowercase().as_str() {
                    "http1.1" | "http/1.1" => Some(b"http/1.1".to_vec()),
                    "http2" | "h2" => Some(b"h2".to_vec()),
                    "http3" | "h3" => Some(b"h3".to_vec()),
                    _ => {
                        warn!("Unknown protocol: {}", protocol);
                        None
                    }
                }
            })
            .collect()
    }

    /// 检查是否启用了 SSL
    pub fn is_enabled(&self) -> bool {
        self.config.enabled && self.acceptor.is_some()
    }

    /// 获取 TLS 接受器
    #[cfg(feature = "ssl")]
    pub fn get_acceptor(&self) -> Option<&TlsAcceptor> {
        self.acceptor.as_ref()
    }

    #[cfg(not(feature = "ssl"))]
    pub fn get_acceptor(&self) -> Option<&()> {
        self.acceptor.as_ref()
    }

    /// 验证证书文件
    pub fn validate_certificates(&self) -> Result<()> {
        if !self.config.enabled {
            return Ok(());
        }

        #[cfg(not(feature = "ssl"))]
        {
            return Err(anyhow::anyhow!("SSL feature not available"));
        }

        #[cfg(feature = "ssl")]
        {
            // 检查证书文件是否存在
            if !Path::new(&self.config.cert_file).exists() {
                return Err(anyhow::anyhow!("Certificate file not found: {}", self.config.cert_file));
            }

            if !Path::new(&self.config.key_file).exists() {
                return Err(anyhow::anyhow!("Private key file not found: {}", self.config.key_file));
            }

            // 尝试加载证书以验证格式
            let _certs = Self::load_certificates(&self.config.cert_file)?;
            let _key = Self::load_private_key(&self.config.key_file)?;

            info!("SSL certificates validated successfully");
            Ok(())
        }
    }

    /// 重新加载证书
    pub fn reload_certificates(&mut self) -> Result<()> {
        if !self.config.enabled {
            return Ok(());
        }

        #[cfg(not(feature = "ssl"))]
        {
            return Err(anyhow::anyhow!("SSL feature not available"));
        }

        #[cfg(feature = "ssl")]
        {
            info!("Reloading SSL certificates...");

            let new_acceptor = Self::create_tls_acceptor(&self.config)?;
            self.acceptor = Some(new_acceptor);

            info!("SSL certificates reloaded successfully");
            Ok(())
        }
    }

    /// 获取 SSL 配置信息
    pub fn get_config_info(&self) -> SslConfigInfo {
        SslConfigInfo {
            enabled: self.config.enabled,
            cert_file: self.config.cert_file.clone(),
            key_file: self.config.key_file.clone(),
            protocols: self.config.protocols.clone(),
            min_version: self.config.min_version.clone(),
            max_version: self.config.max_version.clone(),
            prefer_server_ciphers: self.config.prefer_server_ciphers,
            session_cache: self.config.session_cache,
            session_tickets: self.config.session_tickets,
        }
    }
}

/// SSL 配置信息
#[derive(Debug, Clone)]
pub struct SslConfigInfo {
    pub enabled: bool,
    pub cert_file: String,
    pub key_file: String,
    pub protocols: String,
    pub min_version: String,
    pub max_version: String,
    pub prefer_server_ciphers: bool,
    pub session_cache: usize,
    pub session_tickets: bool,
}

/// SSL 统计信息
#[derive(Debug, Clone, Default)]
pub struct SslStats {
    pub total_connections: u64,
    pub successful_handshakes: u64,
    pub failed_handshakes: u64,
    pub active_connections: u64,
    pub certificate_errors: u64,
    pub protocol_errors: u64,
}

impl SslStats {
    /// 计算握手成功率
    pub fn handshake_success_rate(&self) -> f64 {
        if self.total_connections == 0 {
            0.0
        } else {
            self.successful_handshakes as f64 / self.total_connections as f64 * 100.0
        }
    }

    /// 计算错误率
    pub fn error_rate(&self) -> f64 {
        if self.total_connections == 0 {
            0.0
        } else {
            self.failed_handshakes as f64 / self.total_connections as f64 * 100.0
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_protocols() {
        let protocols = SslManager::parse_protocols("http1.1,http2,http3");
        assert_eq!(protocols.len(), 3);
        assert_eq!(protocols[0], b"http/1.1");
        assert_eq!(protocols[1], b"h2");
        assert_eq!(protocols[2], b"h3");
    }

    #[test]
    fn test_ssl_stats() {
        let mut stats = SslStats::default();
        stats.total_connections = 100;
        stats.successful_handshakes = 95;
        stats.failed_handshakes = 5;

        assert_eq!(stats.handshake_success_rate(), 95.0);
        assert_eq!(stats.error_rate(), 5.0);
    }

    #[test]
    fn test_ssl_config_info() {
        let config = SslConfig {
            enabled: true,
            cert_file: "test.crt".to_string(),
            key_file: "test.key".to_string(),
            protocols: "http1.1,http2".to_string(),
            min_version: "TLS1.2".to_string(),
            max_version: "TLS1.3".to_string(),
            prefer_server_ciphers: true,
            session_cache: 1000,
            session_tickets: true,
        };

        // 创建SSL管理器（不会实际加载证书，因为文件不存在）
        let ssl_manager = SslManager::new(config.clone());

        // 在没有SSL功能时应该返回错误
        #[cfg(not(feature = "ssl"))]
        assert!(ssl_manager.is_err());

        // 在有SSL功能但文件不存在时也会返回错误
        #[cfg(feature = "ssl")]
        assert!(ssl_manager.is_err());
    }
}

package processor

import (
	"fmt"
	"regexp"
	"strings"

	"github.com/sirupsen/logrus"
	"reverse-proxy/internal/config"
)

// RouteProcessor 路由匹配处理器
type RouteProcessor struct {
	*BaseProcessor
	sites map[string]*SiteInfo
	routes map[string][]*CompiledRoute
}

// CompiledRoute 编译后的路由
type CompiledRoute struct {
	Pattern *regexp.Regexp
	Config  *config.RouteConfig
	Info    *RouteInfo
}

// NewRouteProcessor 创建路由处理器
func NewRouteProcessor(logger *logrus.Logger) *RouteProcessor {
	return &RouteProcessor{
		BaseProcessor: NewBaseProcessor("RouteProcessor", 100, logger),
		sites:         make(map[string]*SiteInfo),
		routes:        make(map[string][]*CompiledRoute),
	}
}

// UpdateSites 更新站点配置
func (rp *RouteProcessor) UpdateSites(sites map[string]*config.SiteConfig) error {
	rp.sites = make(map[string]*SiteInfo)
	rp.routes = make(map[string][]*CompiledRoute)
	
	for siteID, siteConfig := range sites {
		// 转换站点信息
		siteInfo := &SiteInfo{
			SiteID:         siteConfig.SiteID,
			Name:           siteConfig.Name,
			Domains:        siteConfig.Domains,
			DefaultSite:    siteConfig.DefaultSite,
			ErrorPages:     siteConfig.ErrorPages,
			Headers:        siteConfig.Headers,
			DenyTypes:      siteConfig.DenyTypes,
			DenyURLs:       siteConfig.DenyURLs,
			CacheHeaders:   siteConfig.CacheHeaders,
			BandwidthLimit: siteConfig.BandwidthLimit,
			MaxConnections: siteConfig.MaxConnections,
			Minify:         siteConfig.Minify,
			Rules:          siteConfig.Rules,     // 新增：站点级缓存规则
			StatusTTL:      siteConfig.StatusTTL, // 新增：站点级状态码TTL
			Config:         siteConfig,           // 新增：完整配置引用
		}
		rp.sites[siteID] = siteInfo
		
		// 编译路由
		compiledRoutes := make([]*CompiledRoute, 0, len(siteConfig.Routes))
		for i := range siteConfig.Routes {
			routeConfig := siteConfig.Routes[i] // 修复循环变量陷阱：创建副本
			pattern, err := regexp.Compile(routeConfig.Pattern)
			if err != nil {
				rp.LogError("编译路由模式失败 %s: %v", routeConfig.Pattern, err)
				continue
			}

			routeInfo := &RouteInfo{
				Pattern:         routeConfig.Pattern,
				Upstream:        routeConfig.Upstream,
				Rewrite:         routeConfig.Rewrite,
				RewriteAdvanced: routeConfig.RewriteAdvanced,
				Cache:           routeConfig.Cache,
				StaticDir:       routeConfig.StaticDir,
				MimeTypes:       routeConfig.MimeTypes,
				DirListing:      routeConfig.DirListing,
				IndexFiles:      routeConfig.IndexFiles,
				RateLimit:       routeConfig.RateLimit,
				ErrorPages:      routeConfig.ErrorPages,
				Headers:         routeConfig.Headers,
				Minify:          routeConfig.Minify,
			}

			// 创建配置副本以避免指针共享问题
			configCopy := routeConfig
			compiledRoutes = append(compiledRoutes, &CompiledRoute{
				Pattern: pattern,
				Config:  &configCopy,
				Info:    routeInfo,
			})
		}
		rp.routes[siteID] = compiledRoutes
	}
	
	rp.LogInfo("更新了 %d 个站点的路由配置", len(sites))
	return nil
}

// Process 处理路由匹配
func (rp *RouteProcessor) Process(req *RequestContext, resp *ResponseContext) *ProcessResult {
	rp.LogDebug("开始处理路由: SiteID=%s, URL=%s, Method=%s", req.SiteID, req.URL.Path, req.Method)

	// 如果站点信息已经设置，则跳过站点查找
	if req.Site == nil {
		// 查找站点信息
		siteInfo, exists := rp.sites[req.SiteID]
		if !exists {
			return ErrorWithMessage(
				fmt.Errorf("站点 %s 不存在", req.SiteID),
				"站点配置未找到",
			)
		}
		req.Site = siteInfo
	}
	
	// 检查拒绝的文件类型
	if rp.isDeniedFileType(req.URL.Path, req.Site.DenyTypes) {
		resp.StatusCode = 403
		return ErrorWithMessage(
			fmt.Errorf("文件类型被拒绝: %s", req.URL.Path),
			"文件类型不被允许",
		)
	}

	// 检查拒绝的URL
	if rp.isDeniedURL(req.URL.Path, req.Site.DenyURLs) {
		resp.StatusCode = 403
		return ErrorWithMessage(
			fmt.Errorf("URL被拒绝: %s", req.URL.Path),
			"URL不被允许",
		)
	}
	
	// 查找匹配的路由
	routes, exists := rp.routes[req.SiteID]
	if !exists {
		rp.LogDebug("站点 %s 没有路由配置", req.SiteID)
		return ErrorWithMessage(
			fmt.Errorf("站点 %s 没有路由配置", req.SiteID),
			"路由配置未找到",
		)
	}

	rp.LogDebug("站点 %s 共有 %d 个路由规则", req.SiteID, len(routes))

	// 打印所有路由规则用于调试
	for i, route := range routes {
		conditions := "无条件"
		if route.Config.MatchConditions != nil {
			conditions = fmt.Sprintf("有条件: methods=%v, cookies=%d个, headers=%d个",
				route.Config.MatchConditions.Methods,
				len(route.Config.MatchConditions.Cookies),
				len(route.Config.MatchConditions.Headers))
		}
		rp.LogDebug("路由规则 %d: pattern=%s, cache=%v, upstream=%s, %s", i, route.Info.Pattern, route.Info.Cache, route.Info.Upstream, conditions)
	}

	for i, route := range routes {
		rp.LogDebug("检查路由 %d: pattern=%s, cache=%v", i, route.Info.Pattern, route.Info.Cache)

		if route.Pattern.MatchString(req.URL.Path) {
			rp.LogDebug("✓ URL模式匹配成功: %s -> %s, cache=%v", route.Info.Pattern, req.URL.Path, route.Info.Cache)

			// 检查匹配条件
			if route.Config.MatchConditions != nil {
				rp.LogDebug("检查匹配条件: %+v", route.Config.MatchConditions)
				if !rp.checkMatchConditions(req, route.Config.MatchConditions) {
					rp.LogDebug("✗ 路由模式匹配但条件不满足: %s -> %s, 继续下一个路由", route.Info.Pattern, req.URL.Path)
					continue // 继续检查下一个路由
				}
				rp.LogDebug("✓ 匹配条件满足: %s -> %s", route.Info.Pattern, req.URL.Path)
			} else {
				rp.LogDebug("✓ 无匹配条件限制，直接匹配成功")
			}

			req.Route = route.Info

			// 设置缓存配置
			req.CacheEnabled = route.Info.Cache

			// 根据路由类型设置处理状态
			rp.setProcessingFlags(req, route.Info)

			rp.LogDebug("最终匹配到路由: %s -> %s, CacheEnabled=%v", route.Info.Pattern, req.URL.Path, req.CacheEnabled)

			return SuccessWithMetadata(map[string]interface{}{
				"matched_pattern": route.Info.Pattern,
				"route_type":      rp.getRouteType(route.Info),
				"cache_enabled":   req.CacheEnabled,
			})
		} else {
			rp.LogDebug("✗ URL模式不匹配: %s -> %s", route.Info.Pattern, req.URL.Path)
		}
	}
	
	// 没有匹配的路由
	resp.StatusCode = 404
	return ErrorWithMessage(
		fmt.Errorf("没有匹配的路由: %s", req.URL.Path),
		"路由未找到",
	)
}

// ShouldProcess 判断是否应该处理
func (rp *RouteProcessor) ShouldProcess(req *RequestContext) bool {
	// 路由处理器总是需要执行
	return true
}

// isDeniedFileType 检查是否为拒绝的文件类型
func (rp *RouteProcessor) isDeniedFileType(path string, denyTypes []string) bool {
	if len(denyTypes) == 0 {
		return false
	}
	
	// 获取文件扩展名
	lastDot := strings.LastIndex(path, ".")
	if lastDot == -1 {
		return false
	}
	
	ext := strings.ToLower(path[lastDot:])
	for _, denyType := range denyTypes {
		if strings.ToLower(denyType) == ext {
			return true
		}
	}
	
	return false
}

// isDeniedURL 检查是否为拒绝的URL
func (rp *RouteProcessor) isDeniedURL(path string, denyURLs []string) bool {
	if len(denyURLs) == 0 {
		return false
	}
	
	for _, denyURL := range denyURLs {
		if matched, _ := regexp.MatchString(denyURL, path); matched {
			return true
		}
	}
	
	return false
}

// setProcessingFlags 根据路由类型设置处理标志
func (rp *RouteProcessor) setProcessingFlags(req *RequestContext, route *RouteInfo) {
	if route.StaticDir != "" {
		// 静态文件处理
		req.ContentSource = "static"
		req.CacheReadFlag = 0   // 跳过缓存读取（静态文件不需要缓存读取）
		req.CacheWriteFlag = 0  // 跳过缓存写入（静态文件不需要缓存）
		req.StaticFlag = 1      // 执行静态文件处理
		req.ProxyFlag = 0       // 跳过代理处理
		req.MinifyFlag = 1      // 执行Minify处理
		req.ErrorFlag = 1       // 执行错误处理
		req.HeaderFlag = 1      // 执行头部处理

		// 兼容性设置
		req.SkipCache = true
		req.SkipUpstream = true
		req.RequireHeaders = true
	} else if route.Upstream != "" {
		// 反向代理处理
		req.ContentSource = "proxy"
		req.CacheReadFlag = 1   // 执行缓存读取
		req.CacheWriteFlag = 1  // 执行缓存写入
		req.StaticFlag = 0      // 跳过静态文件处理
		req.ProxyFlag = 1       // 执行代理处理
		req.MinifyFlag = 1      // 执行Minify处理
		req.ErrorFlag = 1       // 执行错误处理
		req.HeaderFlag = 1      // 执行头部处理

		// 兼容性设置
		req.SkipCache = false
		req.SkipUpstream = false
		req.RequireHeaders = true
	} else {
		// 未知类型，可能是错误页面等
		req.ContentSource = "unknown"
		req.CacheReadFlag = 0   // 跳过缓存读取
		req.CacheWriteFlag = 0  // 跳过缓存写入
		req.StaticFlag = 0      // 跳过静态文件处理
		req.ProxyFlag = 0       // 跳过代理处理
		req.MinifyFlag = 0      // 跳过Minify处理
		req.ErrorFlag = 1       // 执行错误处理
		req.HeaderFlag = 1      // 执行头部处理

		// 兼容性设置
		req.SkipCache = true
		req.SkipUpstream = true
		req.SkipMinify = true
		req.RequireHeaders = true
	}
}

// getRouteType 获取路由类型
func (rp *RouteProcessor) getRouteType(route *RouteInfo) string {
	if route.StaticDir != "" {
		return "static"
	}
	if route.Upstream != "" {
		return "proxy"
	}
	return "unknown"
}

// checkMatchConditions 检查路由匹配条件
func (rp *RouteProcessor) checkMatchConditions(req *RequestContext, matchConfig *config.RouteMatchConfig) bool {
	// 检查HTTP方法
	if len(matchConfig.Methods) > 0 {
		methodMatched := false
		for _, method := range matchConfig.Methods {
			if strings.EqualFold(req.Method, method) {
				methodMatched = true
				break
			}
		}
		if !methodMatched {
			rp.LogDebug("HTTP方法不匹配: %s, 要求: %v", req.Method, matchConfig.Methods)
			return false
		}
	}

	// 检查Cookie
	for _, cookieMatch := range matchConfig.Cookies {
		if !rp.checkCookieMatch(req, &cookieMatch) {
			rp.LogDebug("Cookie条件不匹配: %+v", cookieMatch)
			return false
		}
	}

	// 检查HTTP头部
	for i := range matchConfig.Headers {
		headerMatch := matchConfig.Headers[i] // 修复循环变量陷阱
		if !rp.checkHeaderMatch(req, &headerMatch) {
			rp.LogDebug("Header条件不匹配: %+v", headerMatch)
			return false
		}
	}

	// 检查查询参数
	for i := range matchConfig.QueryParams {
		queryMatch := matchConfig.QueryParams[i] // 修复循环变量陷阱
		if !rp.checkQueryMatch(req, &queryMatch) {
			rp.LogDebug("查询参数条件不匹配: %+v", queryMatch)
			return false
		}
	}

	// 检查User-Agent
	if len(matchConfig.UserAgents) > 0 {
		userAgent := req.Headers.Get("User-Agent")
		userAgentMatched := false
		for _, pattern := range matchConfig.UserAgents {
			if matched, _ := regexp.MatchString(pattern, userAgent); matched {
				userAgentMatched = true
				break
			}
		}
		if !userAgentMatched {
			rp.LogDebug("User-Agent不匹配: %s, 要求: %v", userAgent, matchConfig.UserAgents)
			return false
		}
	}

	// 检查Content-Type
	if len(matchConfig.ContentType) > 0 {
		contentType := req.Headers.Get("Content-Type")
		contentTypeMatched := false
		for _, ct := range matchConfig.ContentType {
			if matched, _ := regexp.MatchString(ct, contentType); matched {
				contentTypeMatched = true
				break
			}
		}
		if !contentTypeMatched {
			rp.LogDebug("Content-Type不匹配: %s, 要求: %v", contentType, matchConfig.ContentType)
			return false
		}
	}

	return true
}

// checkCookieMatch 检查Cookie匹配条件
func (rp *RouteProcessor) checkCookieMatch(req *RequestContext, match *config.RouteCookieMatch) bool {
	// 从Headers中获取Cookie字符串
	cookieHeader := req.Headers.Get("Cookie")
	if cookieHeader == "" {
		// 没有Cookie
		return !match.Exists // 如果要求不存在则返回true
	}

	// 解析Cookie字符串
	cookies := rp.parseCookies(cookieHeader)

	// 如果指定了名称模式，遍历所有cookie查找匹配的名称
	if match.NamePattern != "" {
		for cookieName, cookieValue := range cookies {
			if matched, _ := regexp.MatchString(match.NamePattern, cookieName); matched {
				// 找到匹配名称的cookie
				if match.Exists {
					return true // 只检查存在性
				}

				// 精确值匹配
				if match.Value != "" {
					if cookieValue == match.Value {
						return true
					}
					continue // 继续检查其他匹配名称的cookie
				}

				// 正则模式匹配
				if match.Pattern != "" {
					if matched, _ := regexp.MatchString(match.Pattern, cookieValue); matched {
						return true
					}
					continue // 继续检查其他匹配名称的cookie
				}

				// 如果没有值匹配条件，只要名称匹配就返回true
				return true
			}
		}
		return false // 没有找到匹配名称的cookie
	}

	// 如果指定了具体名称
	if match.Name != "" {
		cookieValue, exists := cookies[match.Name]
		if !exists {
			// Cookie不存在
			return !match.Exists // 如果要求不存在则返回true
		}

		// Cookie存在
		if match.Exists {
			return true // 只检查存在性
		}

		// 精确值匹配
		if match.Value != "" {
			return cookieValue == match.Value
		}

		// 正则模式匹配
		if match.Pattern != "" {
			matched, _ := regexp.MatchString(match.Pattern, cookieValue)
			return matched
		}

		// 如果没有值匹配条件，只要存在就返回true
		return true
	}

	return true // 没有指定任何条件，默认匹配
}

// checkHeaderMatch 检查HTTP头部匹配条件
func (rp *RouteProcessor) checkHeaderMatch(req *RequestContext, match *config.RouteHeaderMatch) bool {
	headerValue := req.Headers.Get(match.Name)

	// 检查存在性
	if match.Exists {
		return headerValue != ""
	}

	// 如果头部不存在且不是只检查存在性
	if headerValue == "" {
		return false
	}

	// 精确值匹配
	if match.Value != "" {
		return headerValue == match.Value
	}

	// 正则模式匹配
	if match.Pattern != "" {
		matched, _ := regexp.MatchString(match.Pattern, headerValue)
		return matched
	}

	// 如果没有值匹配条件，只要存在就返回true
	return true
}

// checkQueryMatch 检查查询参数匹配条件
func (rp *RouteProcessor) checkQueryMatch(req *RequestContext, match *config.RouteQueryMatch) bool {
	// 特殊处理：检查是否有任何查询参数
	if match.Name == "*" && match.Exists {
		return len(req.URL.RawQuery) > 0
	}

	queryValue := req.URL.Query().Get(match.Name)

	// 检查存在性
	if match.Exists {
		return queryValue != ""
	}

	// 如果参数不存在且不是只检查存在性
	if queryValue == "" {
		return false
	}

	// 精确值匹配
	if match.Value != "" {
		return queryValue == match.Value
	}

	// 正则模式匹配
	if match.Pattern != "" {
		matched, _ := regexp.MatchString(match.Pattern, queryValue)
		return matched
	}

	// 如果没有值匹配条件，只要存在就返回true
	return true
}

// parseCookies 解析Cookie字符串
func (rp *RouteProcessor) parseCookies(cookieHeader string) map[string]string {
	cookies := make(map[string]string)

	// 按分号分割Cookie
	parts := strings.Split(cookieHeader, ";")
	for _, part := range parts {
		part = strings.TrimSpace(part)
		if part == "" {
			continue
		}

		// 按等号分割名称和值
		if idx := strings.Index(part, "="); idx > 0 {
			name := strings.TrimSpace(part[:idx])
			value := strings.TrimSpace(part[idx+1:])
			cookies[name] = value
		}
	}

	return cookies
}

// GetSiteInfo 获取站点信息
func (rp *RouteProcessor) GetSiteInfo(siteID string) (*SiteInfo, bool) {
	siteInfo, exists := rp.sites[siteID]
	return siteInfo, exists
}

// GetRoutes 获取站点路由
func (rp *RouteProcessor) GetRoutes(siteID string) ([]*CompiledRoute, bool) {
	routes, exists := rp.routes[siteID]
	return routes, exists
}

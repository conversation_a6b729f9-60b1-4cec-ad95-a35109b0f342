{"server": {"http_port": 80, "https_port": 443}, "compression": {"enabled": true, "enable_recompression": true, "recompression_mode": "auto", "types": ["text/html", "text/css", "text/javascript", "application/json", "application/javascript", "application/xml", "text/xml", "text/plain"], "min_size": 1024, "level": 6, "algorithms": ["br", "gzip", "deflate"], "brotli_quality": 6}, "sites": [{"name": "modern_web_app", "domains": ["app.example.com"], "comment": "现代Web应用 - 优先使用Brotli", "upstreams": [{"name": "app_server", "address": "127.0.0.1", "port": 8001, "protocol": "http", "load_balance_group": "app_group"}], "routes": [{"pattern": "^/api/", "upstream": "app_group", "cache": false, "comment": "API接口 - 启用重压缩确保兼容性"}, {"pattern": "^/", "upstream": "app_group", "cache": true, "comment": "静态资源 - 反向代理压缩"}]}, {"name": "enterprise_portal", "domains": ["portal.company.com"], "comment": "企业门户 - 兼容老旧浏览器", "upstreams": [{"name": "portal_server", "address": "**********", "port": 8080, "protocol": "http", "load_balance_group": "portal_group"}], "routes": [{"pattern": "^/legacy/", "upstream": "portal_group", "cache": true, "comment": "遗留系统接口 - 重压缩为gzip确保兼容"}, {"pattern": "^/", "upstream": "portal_group", "cache": true}]}, {"name": "mobile_api", "domains": ["m.example.com", "mobile.example.com"], "comment": "移动端API - 优化压缩性能", "upstreams": [{"name": "mobile_server", "address": "127.0.0.1", "port": 8002, "protocol": "http", "load_balance_group": "mobile_group"}], "routes": [{"pattern": "^/api/v1/", "upstream": "mobile_group", "cache": false, "comment": "移动API v1 - 可能需要重压缩"}, {"pattern": "^/api/v2/", "upstream": "mobile_group", "cache": false, "comment": "移动API v2 - 支持现代压缩"}]}, {"name": "third_party_integration", "domains": ["integration.example.com"], "comment": "第三方集成 - 上游压缩不可控", "upstreams": [{"name": "third_party_api", "address": "api.thirdparty.com", "port": 443, "protocol": "https", "load_balance_group": "third_party_group"}], "routes": [{"pattern": "^/webhook/", "upstream": "third_party_group", "cache": false, "comment": "第三方Webhook - 重压缩确保客户端兼容"}, {"pattern": "^/api/", "upstream": "third_party_group", "cache": true, "comment": "第三方API - 可能返回不兼容的压缩格式"}]}]}
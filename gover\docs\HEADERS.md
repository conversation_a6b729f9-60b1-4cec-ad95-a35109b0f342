# 头部配置说明

## 概述

反向代理服务器支持三种类型的头部配置：
- `request`: 客户端请求头部处理
- `response`: 后端响应头部处理  
- `upstream`: 发送给后端服务器的头部处理

## 配置结构

```json
{
  "headers": {
    "request": {
      "set": {},
      "remove": [],
      "ignore": []
    },
    "response": {
      "set": {},
      "remove": [],
      "ignore": []
    },
    "upstream": {
      "set": {},
      "remove": [],
      "ignore": []
    }
  }
}
```

## Upstream头部配置

### 功能说明
`upstream` 头部配置用于控制发送给后端服务器的HTTP头部。

### 配置选项

#### 1. `set` - 设置头部
设置或覆盖发送给后端的头部。

```json
{
  "upstream": {
    "set": {
      "X-Forwarded-For": "*************",
      "X-Real-IP": "*************",
      "X-Forwarded-Host": "example.com",
      "X-Forwarded-Port": "443",
      "X-Forwarded-Proto": "https",
      "X-Forwarded-Server": "proxy.example.com"
    }
  }
}
```

#### 2. `remove` - 移除头部
移除特定的头部，不发送给后端。

```json
{
  "upstream": {
    "remove": [
      "X-Forwarded-For",
      "X-Real-IP"
    ]
  }
}
```

#### 3. `ignore` - 忽略头部
忽略某些头部，不传递给后端（与remove功能相同）。

```json
{
  "upstream": {
    "ignore": [
      "User-Agent",
      "Accept-Encoding"
    ]
  }
}
```

## 处理优先级

1. **自动设置**: 代理服务器自动设置基本forward头
2. **upstream.set**: 应用配置的头部设置（覆盖自动设置）
3. **upstream.remove/ignore**: 移除指定的头部
4. **request.remove/ignore**: 移除请求头部

## 常用Forward头配置

### 1. 标准Forward头设置
```json
{
  "upstream": {
    "set": {
      "X-Forwarded-For": "*************",
      "X-Real-IP": "*************",
      "X-Forwarded-Host": "example.com",
      "X-Forwarded-Port": "443",
      "X-Forwarded-Proto": "https"
    }
  }
}
```

### 2. 移除敏感头部
```json
{
  "upstream": {
    "remove": [
      "X-Forwarded-For",
      "X-Real-IP"
    ]
  }
}
```

### 3. 自定义头部
```json
{
  "upstream": {
    "set": {
      "X-Custom-Header": "custom-value",
      "X-Proxy-Version": "1.0.0"
    }
  }
}
```

## 实际应用场景

### 场景1：隐藏真实客户端IP
```json
{
  "upstream": {
    "set": {
      "X-Forwarded-For": "********"
    },
    "remove": [
      "X-Real-IP"
    ]
  }
}
```

### 场景2：设置固定Forward头
```json
{
  "upstream": {
    "set": {
      "X-Forwarded-For": "*************, ********",
      "X-Forwarded-Host": "example.com",
      "X-Forwarded-Port": "443",
      "X-Forwarded-Proto": "https"
    }
  }
}
```

### 场景3：移除不必要的头部
```json
{
  "upstream": {
    "ignore": [
      "User-Agent",
      "Accept-Encoding",
      "Connection",
      "Keep-Alive"
    ]
  }
}
```

## 注意事项

1. **安全性**: 在生产环境中要谨慎设置forward头，避免泄露内部网络信息
2. **兼容性**: 某些后端应用可能依赖特定的forward头格式
3. **优先级**: upstream配置会覆盖自动设置的头部
4. **性能**: 移除不必要的头部可以减少网络传输量

## 调试方法

### 1. 查看发送的头部
```bash
# 在后端服务器上查看接收到的头部
curl -v http://backend-server/headers
```

### 2. 使用代理工具
```bash
# 使用mitmproxy查看请求头部
mitmproxy -p 8080
```

### 3. 查看日志
```bash
# 查看代理服务器日志
tail -f logs/proxy.log
```

## 总结

`upstream` 头部配置提供了灵活的控制能力：
- ✅ **可以设置**: 自定义发送给后端的头部
- ✅ **可以移除**: 移除不需要的头部
- ✅ **可以忽略**: 忽略某些头部
- ✅ **优先级控制**: 覆盖自动设置的头部
- ✅ **安全性**: 控制敏感信息的传递 
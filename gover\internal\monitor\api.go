package monitor

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"reverse-proxy/internal/config"
	"reverse-proxy/internal/logger"
)

// APIServer 监控API服务器
type APIServer struct {
	monitor  *Monitor
	port     int
	username string
	password string
	server   *http.Server
}

// NewAPIServer 创建API服务器
func NewAPIServer(monitor *Monitor, port int, username, password string) *APIServer {
	return &APIServer{
		monitor:  monitor,
		port:     port,
		username: username,
		password: password,
	}
}

// Start 启动API服务器
func (api *APIServer) Start() error {
	mux := http.NewServeMux()

	// 注册路由
	mux.HandleFunc("/stats", api.authMiddleware(api.handleStats))
	mux.HandleFunc("/health", api.authMiddleware(api.handleHealth))
	mux.HandleFunc("/upstreams", api.authMiddleware(api.handleUpstreams))
	mux.HandleFunc("/sites", api.authMiddleware(api.handleSites))

	// 配置管理API（需要额外的API Key和ACL验证）
	mux.HandleFunc("/config", api.configAuthMiddleware(api.handleGetConfig))
	mux.HandleFunc("/config/update", api.configAuthMiddleware(api.handleUpdateConfig))
	mux.HandleFunc("/config/batch", api.configAuthMiddleware(api.handleBatchUpdateConfig))
	mux.HandleFunc("/config/reload", api.configAuthMiddleware(api.handleReloadConfig))

	// 站点管理API（需要配置API认证）
	mux.HandleFunc("/api/sites", api.configAuthMiddleware(api.handleSiteManagement))
	mux.HandleFunc("/api/sites/batch", api.configAuthMiddleware(api.handleSiteBatch))
	mux.HandleFunc("/api/sites/status", api.configAuthMiddleware(api.handleSiteStatus))

	// 新的站点CRUD API（基于site_id）- 恢复认证
	mux.HandleFunc("/api/v2/sites", api.configAuthMiddleware(api.handleSitesAPI))
	mux.HandleFunc("/api/v2/sites/", api.configAuthMiddleware(api.handleSitesAPI))

	mux.HandleFunc("/", api.authMiddleware(api.handleIndex))

	api.server = &http.Server{
		Addr:         fmt.Sprintf(":%d", api.port),
		Handler:      mux,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	logger.Info("启动监控API服务器，端口:", api.port)
	return api.server.ListenAndServe()
}

// Stop 停止API服务器
func (api *APIServer) Stop() error {
	if api.server != nil {
		return api.server.Close()
	}
	return nil
}

// authMiddleware 认证中间件
func (api *APIServer) authMiddleware(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 检查Authorization头
		auth := r.Header.Get("Authorization")
		if auth == "" {
			// 尝试从URL参数获取
			auth = r.URL.Query().Get("auth")
		}

		if !api.authenticate(auth) {
			w.Header().Set("WWW-Authenticate", `Basic realm="Monitor API"`)
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}

		next(w, r)
	}
}

// authenticate 验证认证信息
func (api *APIServer) authenticate(auth string) bool {
	if auth == "" {
		return false
	}

	// 支持Basic认证
	if strings.HasPrefix(auth, "Basic ") {
		credentials := strings.TrimPrefix(auth, "Basic ")
		// Base64解码
		decoded, err := base64.StdEncoding.DecodeString(credentials)
		if err != nil {
			logger.Debug("Basic认证解码失败:", err)
			return false
		}
		return string(decoded) == fmt.Sprintf("%s:%s", api.username, api.password)
	}

	// 支持简单token认证
	if auth == fmt.Sprintf("%s:%s", api.username, api.password) {
		return true
	}

	return false
}

// handleIndex 处理首页
func (api *APIServer) handleIndex(w http.ResponseWriter, r *http.Request) {
	html := `<!DOCTYPE html>
<html>
<head>
    <title>反向代理监控</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .card { border: 1px solid #ddd; border-radius: 5px; padding: 15px; margin: 10px 0; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; }
        .stat { background: #f5f5f5; padding: 10px; border-radius: 3px; }
        .stat h3 { margin: 0 0 5px 0; color: #333; }
        .stat p { margin: 0; font-size: 18px; font-weight: bold; }
        .healthy { color: green; }
        .unhealthy { color: red; }
        .refresh { margin: 20px 0; }
        button { padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
    </style>
</head>
<body>
    <div class="container">
        <h1>反向代理监控面板</h1>
        
        <div class="refresh">
            <button onclick="refreshStats()">刷新数据</button>
            <span id="lastUpdate"></span>
        </div>

        <div class="card">
            <h2>全局统计</h2>
            <div class="stats" id="globalStats"></div>
        </div>

        <div class="card">
            <h2>站点统计</h2>
            <div id="siteStats"></div>
        </div>

        <div class="card">
            <h2>上游服务器状态</h2>
            <div id="upstreamStats"></div>
        </div>

        <div class="card">
            <h2>带宽统计</h2>
            <div class="stats" id="bandwidthStats"></div>
        </div>
    </div>

    <script>
        function refreshStats() {
            fetch('/stats')
                .then(response => response.json())
                .then(data => {
                    updateGlobalStats(data.global);
                    updateSiteStats(data.sites);
                    updateUpstreamStats(data.upstreams);
                    updateBandwidthStats(data.bandwidth);
                    document.getElementById('lastUpdate').textContent = '最后更新: ' + new Date().toLocaleString();
                })
                .catch(error => console.error('Error:', error));
        }

        function updateGlobalStats(global) {
            const container = document.getElementById('globalStats');
            container.innerHTML = 
                '<div class="stat">' +
                    '<h3>总请求数</h3>' +
                    '<p>' + global.total_requests + '</p>' +
                '</div>' +
                '<div class="stat">' +
                    '<h3>错误请求数</h3>' +
                    '<p>' + global.error_requests + '</p>' +
                '</div>' +
                '<div class="stat">' +
                    '<h3>当前连接数</h3>' +
                    '<p>' + global.current_connections + '</p>' +
                '</div>' +
                '<div class="stat">' +
                    '<h3>运行时间</h3>' +
                    '<p>' + formatDuration(global.uptime) + '</p>' +
                '</div>' +
                '<div class="stat">' +
                    '<h3>接收流量</h3>' +
                    '<p>' + formatBytes(global.bytes_received) + '</p>' +
                '</div>' +
                '<div class="stat">' +
                    '<h3>发送流量</h3>' +
                    '<p>' + formatBytes(global.bytes_sent) + '</p>' +
                '</div>';
        }

        function updateSiteStats(sites) {
            const container = document.getElementById('siteStats');
            let html = '';
            for (const [siteId, site] of Object.entries(sites)) {
                // 显示站点名称，将site_id作为隐藏数据属性
                const displayName = site.name || siteId; // 如果没有name字段，回退到使用siteId
                html +=
                    '<div class="card" data-site-id="' + siteId + '">' +
                        '<h3>' + displayName + '</h3>' +
                        '<div class="stats">' +
                            '<div class="stat">' +
                                '<h4>站点ID</h4>' +
                                '<p style="font-size: 12px; color: #666;">' + siteId + '</p>' +
                            '</div>' +
                            '<div class="stat">' +
                                '<h4>总请求数</h4>' +
                                '<p>' + site.total_requests + '</p>' +
                            '</div>' +
                            '<div class="stat">' +
                                '<h4>错误请求数</h4>' +
                                '<p>' + site.error_requests + '</p>' +
                            '</div>' +
                            '<div class="stat">' +
                                '<h4>当前连接数</h4>' +
                                '<p>' + site.current_connections + '</p>' +
                            '</div>' +
                            '<div class="stat">' +
                                '<h4>运行时间</h4>' +
                                '<p>' + formatDuration(site.uptime) + '</p>' +
                            '</div>' +
                            '<div class="stat">' +
                                '<h4>发送流量</h4>' +
                                '<p>' + formatBytes(site.out_bytes) + '</p>' +
                            '</div>' +
                        '</div>' +
                    '</div>';
            }
            container.innerHTML = html;
        }

        function updateUpstreamStats(upstreams) {
            const container = document.getElementById('upstreamStats');
            let html = '';
            for (const [key, upstream] of Object.entries(upstreams)) {
                const statusClass = upstream.healthy ? 'healthy' : 'unhealthy';
                const statusText = upstream.healthy ? '健康' : '不健康';
                html += 
                    '<div class="card">' +
                        '<h3>' + upstream.name + ' (' + upstream.address + ':' + upstream.port + ')</h3>' +
                        '<div class="stats">' +
                            '<div class="stat">' +
                                '<h4>状态</h4>' +
                                '<p class="' + statusClass + '">' + statusText + '</p>' +
                            '</div>' +
                            '<div class="stat">' +
                                '<h4>响应时间</h4>' +
                                '<p>' + (upstream.response_time_ms ? upstream.response_time_ms + 'ms' : 'N/A') + '</p>' +
                            '</div>' +
                            '<div class="stat">' +
                                '<h4>成功次数</h4>' +
                                '<p>' + upstream.success_count + '</p>' +
                            '</div>' +
                            '<div class="stat">' +
                                '<h4>失败次数</h4>' +
                                '<p>' + upstream.fail_count + '</p>' +
                            '</div>' +
                            '<div class="stat">' +
                                '<h4>离线次数</h4>' +
                                '<p>' + upstream.offline_count + '</p>' +
                            '</div>' +
                            '<div class="stat">' +
                                '<h4>最后检查</h4>' +
                                '<p>' + new Date(upstream.last_check_time).toLocaleString() + '</p>' +
                            '</div>' +
                            '<div class="stat">' +
                                '<h4>上游接收流量</h4>' +
                                '<p>' + formatBytes(upstream.bytes_received) + '</p>' +
                            '</div>' +
                            '<div class="stat">' +
                                '<h4>上游发送流量</h4>' +
                                '<p>' + formatBytes(upstream.bytes_sent) + '</p>' +
                            '</div>' +
                        '</div>' +
                    '</div>';
            }
            container.innerHTML = html;
        }

        function updateBandwidthStats(bandwidth) {
            const container = document.getElementById('bandwidthStats');
            container.innerHTML = 
                '<div class="stat">' +
                    '<h3>入站带宽</h3>' +
                    '<p>' + formatBytesPerSec(bandwidth.incoming_rate) + '</p>' +
                '</div>' +
                '<div class="stat">' +
                    '<h3>出站带宽</h3>' +
                    '<p>' + formatBytesPerSec(bandwidth.outgoing_rate) + '</p>' +
                '</div>' +
                '<div class="stat">' +
                    '<h3>上游入站带宽</h3>' +
                    '<p>' + formatBytesPerSec(bandwidth.upstream_in_rate) + '</p>' +
                '</div>' +
                '<div class="stat">' +
                    '<h3>上游出站带宽</h3>' +
                    '<p>' + formatBytesPerSec(bandwidth.upstream_out_rate) + '</p>' +
                '</div>';
        }

        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function formatBytesPerSec(bytesPerSec) {
            return formatBytes(bytesPerSec) + '/s';
        }

        function formatDuration(duration) {
            // Go的time.Duration在JSON中是以纳秒为单位
            const totalSeconds = Math.floor(duration / 1000000000); // 转换为秒
            const hours = Math.floor(totalSeconds / 3600);
            const minutes = Math.floor((totalSeconds % 3600) / 60);
            const seconds = Math.floor(totalSeconds % 60);
            return hours + 'h ' + minutes + 'm ' + seconds + 's';
        }

        // 页面加载时自动刷新
        refreshStats();
        // 每30秒自动刷新
        setInterval(refreshStats, 30000);
    </script>
</body>
</html>`

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.Write([]byte(html))
}

// handleStats 处理统计信息请求
func (api *APIServer) handleStats(w http.ResponseWriter, r *http.Request) {
	stats := api.monitor.GetStats()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(stats)
}

// handleHealth 处理健康检查请求
func (api *APIServer) handleHealth(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.Write([]byte(`{"status":"ok","timestamp":"` + time.Now().Format(time.RFC3339) + `"}`))
}

// handleUpstreams 处理上游服务器状态请求
func (api *APIServer) handleUpstreams(w http.ResponseWriter, r *http.Request) {
	stats := api.monitor.GetStats()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(stats.Upstreams)
}

// handleSiteManagement 处理站点管理请求
func (api *APIServer) handleSiteManagement(w http.ResponseWriter, r *http.Request) {
	if api.monitor.configManager == nil {
		api.writeError(w, http.StatusServiceUnavailable, "配置管理器未初始化")
		return
	}

	configManager, ok := api.monitor.configManager.(*config.ConfigManager)
	if !ok {
		api.writeError(w, http.StatusInternalServerError, "配置管理器类型错误")
		return
	}

	switch r.Method {
	case http.MethodGet:
		api.handleGetSite(w, r, configManager)
	case http.MethodPost:
		api.handleCreateSite(w, r, configManager)
	case http.MethodPut:
		api.handleUpdateSite(w, r, configManager)
	case http.MethodDelete:
		api.handleDeleteSite(w, r, configManager)
	default:
		api.writeError(w, http.StatusMethodNotAllowed, "不支持的HTTP方法")
	}
}

// handleGetSite 获取站点配置（支持通过name或site_id查询）
func (api *APIServer) handleGetSite(w http.ResponseWriter, r *http.Request, configManager *config.ConfigManager) {
	siteName := r.URL.Query().Get("name")
	siteID := r.URL.Query().Get("site_id")

	if siteName == "" && siteID == "" {
		// 获取所有站点列表，返回包含ID和名称的信息
		siteInfo := configManager.ListSiteInfo()
		api.writeSuccess(w, "获取站点列表成功", map[string]interface{}{
			"sites": siteInfo,
			"count": len(siteInfo),
		})
		return
	}

	var siteConfig *config.SiteConfig
	if siteID != "" {
		// 优先使用 site_id 查询
		siteConfig = configManager.GetSiteConfigByID(siteID)
		if siteConfig == nil {
			api.writeError(w, http.StatusNotFound, fmt.Sprintf("站点不存在: %s", siteID))
			return
		}
	} else if siteName != "" {
		// 使用 name 查询（向后兼容）
		siteConfig = configManager.GetSiteConfig(siteName)
		if siteConfig == nil {
			api.writeError(w, http.StatusNotFound, fmt.Sprintf("站点不存在: %s", siteName))
			return
		}
	}

	api.writeSuccess(w, "获取站点配置成功", siteConfig)
}

// handleCreateSite 创建站点
func (api *APIServer) handleCreateSite(w http.ResponseWriter, r *http.Request, configManager *config.ConfigManager) {
	var siteConfig config.SiteConfig
	if err := json.NewDecoder(r.Body).Decode(&siteConfig); err != nil {
		api.writeError(w, http.StatusBadRequest, "JSON解析失败: "+err.Error())
		return
	}

	jsonData, err := json.Marshal(siteConfig)
	if err != nil {
		api.writeError(w, http.StatusInternalServerError, "JSON序列化失败: "+err.Error())
		return
	}

	if err := configManager.CreateSiteFromJSONAndSave(jsonData); err != nil {
		api.writeError(w, http.StatusBadRequest, "创建站点失败: "+err.Error())
		return
	}

	api.writeSuccess(w, "站点创建成功", map[string]string{"site_name": siteConfig.Name})
	logger.GetLogger().Infof("通过API创建站点: %s", siteConfig.Name)
}

// handleUpdateSite 更新站点（支持通过name或site_id更新）
func (api *APIServer) handleUpdateSite(w http.ResponseWriter, r *http.Request, configManager *config.ConfigManager) {
	siteName := r.URL.Query().Get("name")
	siteID := r.URL.Query().Get("site_id")

	if siteName == "" && siteID == "" {
		api.writeError(w, http.StatusBadRequest, "站点名称或站点ID不能为空")
		return
	}

	var siteConfig config.SiteConfig
	if err := json.NewDecoder(r.Body).Decode(&siteConfig); err != nil {
		api.writeError(w, http.StatusBadRequest, "JSON解析失败: "+err.Error())
		return
	}

	jsonData, err := json.Marshal(siteConfig)
	if err != nil {
		api.writeError(w, http.StatusInternalServerError, "JSON序列化失败: "+err.Error())
		return
	}

	var updateErr error
	var responseData map[string]string

	if siteID != "" {
		// 优先使用 site_id 更新
		updateErr = configManager.UpdateSiteFromJSONAndSaveByID(siteID, jsonData)
		responseData = map[string]string{"site_id": siteID}
		if updateErr == nil {
			logger.GetLogger().Infof("通过API更新站点: %s", siteID)
		}
	} else {
		// 使用 name 更新（向后兼容）
		updateErr = configManager.UpdateSiteFromJSONAndSave(siteName, jsonData)
		responseData = map[string]string{"site_name": siteName}
		if updateErr == nil {
			logger.GetLogger().Infof("通过API更新站点: %s", siteName)
		}
	}

	if updateErr != nil {
		api.writeError(w, http.StatusBadRequest, "更新站点失败: "+updateErr.Error())
		return
	}

	api.writeSuccess(w, "站点更新成功", responseData)
}

// handleDeleteSite 删除站点（支持通过name或site_id删除）
func (api *APIServer) handleDeleteSite(w http.ResponseWriter, r *http.Request, configManager *config.ConfigManager) {
	siteName := r.URL.Query().Get("name")
	siteID := r.URL.Query().Get("site_id")

	if siteName == "" && siteID == "" {
		api.writeError(w, http.StatusBadRequest, "站点名称或站点ID不能为空")
		return
	}

	var deleteErr error
	var responseData map[string]string

	if siteID != "" {
		// 优先使用 site_id 删除
		deleteErr = configManager.DeleteSiteAndSaveByID(siteID)
		responseData = map[string]string{"site_id": siteID}
		if deleteErr == nil {
			logger.GetLogger().Infof("通过API删除站点: %s", siteID)
		}
	} else {
		// 使用 name 删除（向后兼容）
		deleteErr = configManager.DeleteSiteAndSave(siteName)
		responseData = map[string]string{"site_name": siteName}
		if deleteErr == nil {
			logger.GetLogger().Infof("通过API删除站点: %s", siteName)
		}
	}

	if deleteErr != nil {
		api.writeError(w, http.StatusBadRequest, "删除站点失败: "+deleteErr.Error())
		return
	}

	api.writeSuccess(w, "站点删除成功", responseData)
}

// handleSiteBatch 批量更新站点
func (api *APIServer) handleSiteBatch(w http.ResponseWriter, r *http.Request) {
	if api.monitor.configManager == nil {
		api.writeError(w, http.StatusServiceUnavailable, "配置管理器未初始化")
		return
	}

	configManager, ok := api.monitor.configManager.(*config.ConfigManager)
	if !ok {
		api.writeError(w, http.StatusInternalServerError, "配置管理器类型错误")
		return
	}

	var batchRequest struct {
		Sites []config.SiteConfig `json:"sites"`
	}

	if err := json.NewDecoder(r.Body).Decode(&batchRequest); err != nil {
		api.writeError(w, http.StatusBadRequest, "JSON解析失败: "+err.Error())
		return
	}

	results := make([]map[string]interface{}, 0, len(batchRequest.Sites))

	for _, siteConfig := range batchRequest.Sites {
		result := map[string]interface{}{
			"site_name": siteConfig.Name,
		}

		jsonData, err := json.Marshal(siteConfig)
		if err != nil {
			result["success"] = false
			result["error"] = "JSON序列化失败: " + err.Error()
		} else if err := configManager.UpdateSiteFromJSONAndSave(siteConfig.Name, jsonData); err != nil {
			result["success"] = false
			result["error"] = err.Error()
		} else {
			result["success"] = true
		}

		results = append(results, result)
	}

	api.writeSuccess(w, "批量更新完成", map[string]interface{}{
		"results": results,
		"total":   len(results),
	})

	logger.GetLogger().Infof("通过API批量更新 %d 个站点", len(batchRequest.Sites))
}

// handleSiteStatus 获取站点状态
func (api *APIServer) handleSiteStatus(w http.ResponseWriter, r *http.Request) {
	if api.monitor.configManager == nil {
		api.writeError(w, http.StatusServiceUnavailable, "配置管理器未初始化")
		return
	}

	configManager, ok := api.monitor.configManager.(*config.ConfigManager)
	if !ok {
		api.writeError(w, http.StatusInternalServerError, "配置管理器类型错误")
		return
	}

	siteName := r.URL.Query().Get("name")
	siteID := r.URL.Query().Get("site_id")

	if siteName == "" && siteID == "" {
		api.writeError(w, http.StatusBadRequest, "站点名称或站点ID不能为空")
		return
	}

	var siteConfig *config.SiteConfig
	if siteID != "" {
		// 优先使用 site_id 查询
		siteConfig = configManager.GetSiteConfigByID(siteID)
	} else {
		// 使用 name 查询（向后兼容）
		siteConfig = configManager.GetSiteConfig(siteName)
	}

	if siteConfig == nil {
		identifier := siteID
		if identifier == "" {
			identifier = siteName
		}
		api.writeError(w, http.StatusNotFound, fmt.Sprintf("站点不存在: %s", identifier))
		return
	}

	status := map[string]interface{}{
		"site_id":   siteConfig.SiteID,
		"name":      siteConfig.Name,
		"domains":   siteConfig.Domains,
		"upstreams": len(siteConfig.Upstreams),
		"ssl":       siteConfig.SSL.Enabled,
		"acl":       siteConfig.ACL.Allow != nil || siteConfig.ACL.Deny != nil,
		"routes":    len(siteConfig.Routes),
	}

	api.writeSuccess(w, "获取站点状态成功", status)
}

// writeSuccess 写入成功响应
func (api *APIServer) writeSuccess(w http.ResponseWriter, message string, data interface{}) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)

	response := map[string]interface{}{
		"success": true,
		"message": message,
		"data":    data,
	}

	json.NewEncoder(w).Encode(response)
}

// writeError 写入错误响应
func (api *APIServer) writeError(w http.ResponseWriter, statusCode int, message string) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	response := map[string]interface{}{
		"success": false,
		"message": message,
	}

	json.NewEncoder(w).Encode(response)
}

// handleSites 处理站点统计请求
func (api *APIServer) handleSites(w http.ResponseWriter, r *http.Request) {
	stats := api.monitor.GetStats()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(stats.Sites)
}

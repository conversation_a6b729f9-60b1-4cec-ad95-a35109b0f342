package hotreload

import (
	"context"
	"fmt"
	"sync"
	"time"

	"reverse-proxy/internal/config"
	"github.com/sirupsen/logrus"
)

// OptimizedReloader 优化的热重载管理器
type OptimizedReloader struct {
	mu                sync.RWMutex
	logger            *logrus.Logger
	incrementalReloader *IncrementalReloader
	viperIntegration  *config.ViperIntegration
	
	// 配置
	enableIncremental bool
	debounceInterval  time.Duration
	maxRetries        int
	
	// 状态管理
	isReloading       bool
	lastReloadTime    time.Time
	pendingReload     bool
	reloadTimer       *time.Timer
	
	// 上下文控制
	ctx               context.Context
	cancel            context.CancelFunc
	
	// 回调管理
	callbacks         []OptimizedReloadCallback
	
	// 性能监控
	perfMonitor       *PerformanceMonitor
}

// OptimizedReloadCallback 优化的重载回调
type OptimizedReloadCallback struct {
	Name        string
	Priority    int // 优先级，数字越小优先级越高
	Callback    func(diff *ConfigDiff, newConfig *config.Config) error
	OnlyOnChange bool // 仅在有变更时执行
}

// PerformanceMonitor 性能监控器
type PerformanceMonitor struct {
	mu                    sync.RWMutex
	TotalReloadTime       time.Duration `json:"total_reload_time"`
	TotalReloads          int64         `json:"total_reloads"`
	IncrementalReloads    int64         `json:"incremental_reloads"`
	FullReloads           int64         `json:"full_reloads"`
	FailedReloads         int64         `json:"failed_reloads"`
	AverageReloadTime     time.Duration `json:"average_reload_time"`
	LastReloadDuration    time.Duration `json:"last_reload_duration"`
	PeakReloadTime        time.Duration `json:"peak_reload_time"`
	ReloadTimeHistory     []time.Duration `json:"reload_time_history"` // 最近10次重载时间
	maxHistorySize        int
}

// ReloadOptions 重载选项
type ReloadOptions struct {
	ForceFullReload   bool
	SkipValidation    bool
	AsyncMode         bool
	TimeoutDuration   time.Duration
}

// NewOptimizedReloader 创建优化的热重载管理器
func NewOptimizedReloader(viperIntegration *config.ViperIntegration, logger *logrus.Logger) *OptimizedReloader {
	ctx, cancel := context.WithCancel(context.Background())
	
	or := &OptimizedReloader{
		logger:            logger,
		viperIntegration:  viperIntegration,
		enableIncremental: true,
		debounceInterval:  300 * time.Millisecond, // 防抖间隔
		maxRetries:        3,
		ctx:               ctx,
		cancel:            cancel,
		perfMonitor: &PerformanceMonitor{
			maxHistorySize: 10,
		},
	}
	
	// 创建增量重载器
	or.incrementalReloader = NewIncrementalReloader(logger)
	
	// 设置增量重载器的回调
	or.setupIncrementalCallbacks()
	
	return or
}

// setupIncrementalCallbacks 设置增量重载器回调
func (or *OptimizedReloader) setupIncrementalCallbacks() {
	or.incrementalReloader.SetCallbacks(
		or.handleGlobalConfigChange,
		or.handleSiteAdd,
		or.handleSiteUpdate,
		or.handleSiteRemove,
	)
}

// EnableHotReload 启用热重载
func (or *OptimizedReloader) EnableHotReload() error {
	or.mu.Lock()
	defer or.mu.Unlock()
	
	// 启用Viper热重载，使用优化的回调
	return or.viperIntegration.EnableHotReload(or.handleConfigChange)
}

// DisableHotReload 禁用热重载
func (or *OptimizedReloader) DisableHotReload() error {
	or.mu.Lock()
	defer or.mu.Unlock()
	
	or.cancel()
	
	if or.reloadTimer != nil {
		or.reloadTimer.Stop()
		or.reloadTimer = nil
	}
	
	or.viperIntegration.DisableHotReload()
	return nil
}

// AddCallback 添加重载回调
func (or *OptimizedReloader) AddCallback(callback OptimizedReloadCallback) {
	or.mu.Lock()
	defer or.mu.Unlock()
	
	or.callbacks = append(or.callbacks, callback)
	
	// 按优先级排序
	for i := len(or.callbacks) - 1; i > 0; i-- {
		if or.callbacks[i].Priority < or.callbacks[i-1].Priority {
			or.callbacks[i], or.callbacks[i-1] = or.callbacks[i-1], or.callbacks[i]
		} else {
			break
		}
	}
}

// handleConfigChange 处理配置变更（主入口）
func (or *OptimizedReloader) handleConfigChange(oldConfig, newConfig *config.Config) error {
	or.mu.Lock()
	defer or.mu.Unlock()
	
	// 如果正在重载，设置待处理标志
	if or.isReloading {
		or.pendingReload = true
		or.logger.Debug("配置重载正在进行中，设置待处理标志")
		return nil
	}
	
	// 防抖处理
	if or.reloadTimer != nil {
		or.reloadTimer.Stop()
	}
	
	or.reloadTimer = time.AfterFunc(or.debounceInterval, func() {
		or.executeReload(newConfig, ReloadOptions{})
	})
	
	return nil
}

// executeReload 执行重载
func (or *OptimizedReloader) executeReload(newConfig *config.Config, options ReloadOptions) error {
	or.mu.Lock()
	if or.isReloading {
		or.mu.Unlock()
		return fmt.Errorf("重载已在进行中")
	}
	or.isReloading = true
	or.mu.Unlock()
	
	defer func() {
		or.mu.Lock()
		or.isReloading = false
		or.lastReloadTime = time.Now()
		
		// 检查是否有待处理的重载
		if or.pendingReload {
			or.pendingReload = false
			or.mu.Unlock()
			// 异步执行待处理的重载
			go func() {
				time.Sleep(or.debounceInterval)
				or.executeReload(newConfig, options)
			}()
		} else {
			or.mu.Unlock()
		}
	}()
	
	startTime := time.Now()
	or.logger.Info("开始执行优化的配置重载...")
	
	var err error
	var diff *ConfigDiff
	
	// 选择重载策略
	if options.ForceFullReload || !or.enableIncremental {
		// 全量重载
		err = or.executeFullReload(newConfig)
		or.perfMonitor.recordReload(time.Since(startTime), false, err == nil)
	} else {
		// 增量重载
		err = or.incrementalReloader.ProcessConfigChange(newConfig)
		if err == nil {
			diff = or.incrementalReloader.GetStats().LastDiff
		}
		or.perfMonitor.recordReload(time.Since(startTime), true, err == nil)
	}
	
	if err != nil {
		or.logger.Errorf("配置重载失败: %v", err)
		return err
	}
	
	// 执行回调
	if err := or.executeCallbacks(diff, newConfig); err != nil {
		or.logger.Errorf("执行重载回调失败: %v", err)
		return err
	}
	
	reloadDuration := time.Since(startTime)
	or.logger.Infof("配置重载完成，耗时: %v", reloadDuration)
	
	return nil
}

// executeFullReload 执行全量重载
func (or *OptimizedReloader) executeFullReload(newConfig *config.Config) error {
	or.logger.Info("执行全量重载...")
	return or.incrementalReloader.ForceFullReload(newConfig)
}

// executeCallbacks 执行回调
func (or *OptimizedReloader) executeCallbacks(diff *ConfigDiff, newConfig *config.Config) error {
	or.mu.RLock()
	callbacks := make([]OptimizedReloadCallback, len(or.callbacks))
	copy(callbacks, or.callbacks)
	or.mu.RUnlock()
	
	for _, callback := range callbacks {
		// 检查是否需要执行回调
		if callback.OnlyOnChange && diff != nil {
			// 检查是否有实际变更
			hasChanges := diff.GlobalChanged || 
				len(diff.SitesAdded) > 0 || 
				len(diff.SitesRemoved) > 0 || 
				len(diff.SitesModified) > 0
			
			if !hasChanges {
				or.logger.Debugf("跳过回调 %s (无变更)", callback.Name)
				continue
			}
		}
		
		or.logger.Debugf("执行回调: %s", callback.Name)
		if err := callback.Callback(diff, newConfig); err != nil {
			return fmt.Errorf("回调 %s 执行失败: %w", callback.Name, err)
		}
	}
	
	return nil
}

// handleGlobalConfigChange 处理全局配置变更
func (or *OptimizedReloader) handleGlobalConfigChange(oldConfig, newConfig *config.Config) error {
	or.logger.Info("处理全局配置变更...")
	// 这里可以添加全局配置变更的具体处理逻辑
	// 例如：重新初始化全局组件、更新全局设置等
	return nil
}

// handleSiteAdd 处理站点新增
func (or *OptimizedReloader) handleSiteAdd(siteConfig *config.SiteConfig) error {
	or.logger.Infof("处理站点新增: %s", siteConfig.SiteID)
	// 这里可以添加站点新增的具体处理逻辑
	// 例如：创建站点实例、注册路由等
	return nil
}

// handleSiteUpdate 处理站点更新
func (or *OptimizedReloader) handleSiteUpdate(siteName string, oldConfig, newConfig *config.SiteConfig, changes *SiteChange) error {
	or.logger.Infof("处理站点更新: %s (变更: %v)", siteName, changes.ChangedFields)
	// 这里可以添加站点更新的具体处理逻辑
	// 例如：更新路由、重新加载SSL证书等
	return nil
}

// handleSiteRemove 处理站点删除
func (or *OptimizedReloader) handleSiteRemove(siteName string, oldConfig *config.SiteConfig) error {
	or.logger.Infof("处理站点删除: %s", siteName)
	// 这里可以添加站点删除的具体处理逻辑
	// 例如：清理资源、注销路由等
	return nil
}

// GetPerformanceStats 获取性能统计
func (or *OptimizedReloader) GetPerformanceStats() *PerformanceMonitor {
	or.perfMonitor.mu.RLock()
	defer or.perfMonitor.mu.RUnlock()

	// 返回副本
	stats := *or.perfMonitor
	stats.ReloadTimeHistory = make([]time.Duration, len(or.perfMonitor.ReloadTimeHistory))
	copy(stats.ReloadTimeHistory, or.perfMonitor.ReloadTimeHistory)

	return &stats
}

// recordReload 记录重载性能
func (pm *PerformanceMonitor) recordReload(duration time.Duration, isIncremental bool, success bool) {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	pm.TotalReloads++
	pm.LastReloadDuration = duration

	if success {
		pm.TotalReloadTime += duration

		if isIncremental {
			pm.IncrementalReloads++
		} else {
			pm.FullReloads++
		}

		// 更新峰值时间
		if duration > pm.PeakReloadTime {
			pm.PeakReloadTime = duration
		}

		// 更新平均时间
		pm.AverageReloadTime = pm.TotalReloadTime / time.Duration(pm.TotalReloads-pm.FailedReloads)

		// 更新历史记录
		pm.ReloadTimeHistory = append(pm.ReloadTimeHistory, duration)
		if len(pm.ReloadTimeHistory) > pm.maxHistorySize {
			pm.ReloadTimeHistory = pm.ReloadTimeHistory[1:]
		}
	} else {
		pm.FailedReloads++
	}
}

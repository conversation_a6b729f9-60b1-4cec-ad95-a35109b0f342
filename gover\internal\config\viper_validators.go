package config

import (
	"fmt"
	"net"
	"os"
	"regexp"
)

// DefaultConfigValidator 默认配置验证器
type DefaultConfigValidator struct{}

// Validate 验证完整配置
func (v *DefaultConfigValidator) Validate(config *Config) error {
	// 验证服务器配置
	if err := v.validateServer(&config.Server); err != nil {
		return fmt.Errorf("服务器配置验证失败: %w", err)
	}

	// 验证站点配置
	for i, site := range config.Sites {
		if err := v.validateSite(&site, i); err != nil {
			return fmt.Errorf("站点 %d (%s) 配置验证失败: %w", i, site.Name, err)
		}
	}

	// 验证缓存配置
	if err := v.validateCache(&config.Cache); err != nil {
		return fmt.Errorf("缓存配置验证失败: %w", err)
	}

	// 验证监控配置
	if config.Monitor.Enabled {
		if err := v.validateMonitor(&config.Monitor); err != nil {
			return fmt.Errorf("监控配置验证失败: %w", err)
		}
	}

	return nil
}

// ValidateSection 验证配置片段
func (v *DefaultConfigValidator) ValidateSection(section string, data interface{}) error {
	switch section {
	case "server":
		if serverConfig, ok := data.(*ServerConfig); ok {
			return v.validateServer(serverConfig)
		}
	case "cache":
		if cacheConfig, ok := data.(*CacheConfig); ok {
			return v.validateCache(cacheConfig)
		}
	case "monitor":
		if monitorConfig, ok := data.(*MonitorConfig); ok {
			return v.validateMonitor(monitorConfig)
		}
	}
	return fmt.Errorf("未知的配置片段: %s", section)
}

// validateServer 验证服务器配置
func (v *DefaultConfigValidator) validateServer(server *ServerConfig) error {
	if server.HTTPPort < 0 || server.HTTPPort > 65535 {
		return fmt.Errorf("无效的HTTP端口号: %d", server.HTTPPort)
	}
	if server.HTTPSPort < 0 || server.HTTPSPort > 65535 {
		return fmt.Errorf("无效的HTTPS端口号: %d", server.HTTPSPort)
	}
	if server.MaxConnections <= 0 {
		return fmt.Errorf("最大连接数必须大于0: %d", server.MaxConnections)
	}
	return nil
}

// validateSite 验证站点配置
func (v *DefaultConfigValidator) validateSite(site *SiteConfig, index int) error {
	if site.Name == "" {
		return fmt.Errorf("站点名称不能为空")
	}

	if site.SiteID == "" {
		return fmt.Errorf("站点ID不能为空")
	}

	if len(site.Domains) == 0 {
		return fmt.Errorf("站点域名列表不能为空")
	}

	// 验证域名格式
	for _, domain := range site.Domains {
		if err := v.validateDomain(domain); err != nil {
			return fmt.Errorf("无效的域名 '%s': %w", domain, err)
		}
	}

	// 验证路由配置
	if err := v.validateRoutes(site.Routes); err != nil {
		return fmt.Errorf("路由配置验证失败: %w", err)
	}

	// 验证上游服务器配置
	if err := v.validateUpstreams(site.Upstreams); err != nil {
		return fmt.Errorf("上游服务器配置验证失败: %w", err)
	}

	// 验证SSL配置
	if site.SSL.Enabled {
		if err := v.validateSSL(&site.SSL); err != nil {
			return fmt.Errorf("SSL配置验证失败: %w", err)
		}
	}

	return nil
}

// validateDomain 验证域名格式
func (v *DefaultConfigValidator) validateDomain(domain string) error {
	if domain == "" {
		return fmt.Errorf("域名不能为空")
	}

	// 简单的域名格式验证
	domainRegex := regexp.MustCompile(`^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$`)
	if !domainRegex.MatchString(domain) {
		return fmt.Errorf("域名格式无效")
	}

	return nil
}

// validateRoutes 验证路由配置
func (v *DefaultConfigValidator) validateRoutes(routes []RouteConfig) error {
	if len(routes) == 0 {
		return fmt.Errorf("至少需要一个路由配置")
	}

	for i, route := range routes {
		if route.Pattern == "" {
			return fmt.Errorf("路由 %d 的模式不能为空", i)
		}

		// 验证正则表达式
		if _, err := regexp.Compile(route.Pattern); err != nil {
			return fmt.Errorf("路由 %d 的模式无效: %w", i, err)
		}

		// 验证静态目录（改为警告，不阻止程序启动）
		if route.StaticDir != "" {
			if _, err := os.Stat(route.StaticDir); os.IsNotExist(err) {
				// 使用 fmt.Printf 输出警告，而不是返回错误
				fmt.Printf("警告: 路由 %d 的静态目录不存在: %s\n", i, route.StaticDir)
				// 不返回错误，允许程序继续运行
			}
		}

		// 验证上游配置
		if route.Upstream != "" && route.StaticDir != "" {
			return fmt.Errorf("路由 %d 不能同时配置上游和静态目录", i)
		}

		if route.Upstream == "" && route.StaticDir == "" {
			return fmt.Errorf("路由 %d 必须配置上游或静态目录", i)
		}
	}

	return nil
}

// validateUpstreams 验证上游服务器配置
func (v *DefaultConfigValidator) validateUpstreams(upstreams []UpstreamConfig) error {
	for i, upstream := range upstreams {
		if upstream.Name == "" {
			return fmt.Errorf("上游服务器 %d 的名称不能为空", i)
		}

		if upstream.Address == "" {
			return fmt.Errorf("上游服务器 %d 的地址不能为空", i)
		}

		// 验证IP地址格式
		if net.ParseIP(upstream.Address) == nil {
			return fmt.Errorf("上游服务器 %d 的地址格式无效: %s", i, upstream.Address)
		}

		if upstream.Port <= 0 || upstream.Port > 65535 {
			return fmt.Errorf("上游服务器 %d 的端口无效: %d", i, upstream.Port)
		}

		if upstream.HTTPSPort != 0 && (upstream.HTTPSPort <= 0 || upstream.HTTPSPort > 65535) {
			return fmt.Errorf("上游服务器 %d 的HTTPS端口无效: %d", i, upstream.HTTPSPort)
		}

		if upstream.Weight <= 0 {
			return fmt.Errorf("上游服务器 %d 的权重必须大于0: %d", i, upstream.Weight)
		}

		if upstream.LoadBalanceGroup == "" {
			return fmt.Errorf("上游服务器 %d 缺少负载均衡组配置", i)
		}
	}

	return nil
}

// validateSSL 验证SSL配置
func (v *DefaultConfigValidator) validateSSL(ssl *SSLConfig) error {
	if ssl.CertFile == "" {
		return fmt.Errorf("SSL证书文件路径不能为空")
	}

	if ssl.KeyFile == "" {
		return fmt.Errorf("SSL私钥文件路径不能为空")
	}

	// 检查证书文件是否存在
	if _, err := os.Stat(ssl.CertFile); os.IsNotExist(err) {
		return fmt.Errorf("SSL证书文件不存在: %s", ssl.CertFile)
	}

	// 检查私钥文件是否存在
	if _, err := os.Stat(ssl.KeyFile); os.IsNotExist(err) {
		return fmt.Errorf("SSL私钥文件不存在: %s", ssl.KeyFile)
	}

	return nil
}

// validateCache 验证缓存配置
func (v *DefaultConfigValidator) validateCache(cache *CacheConfig) error {
	if cache.Enabled {
		if cache.Path == "" {
			return fmt.Errorf("缓存路径不能为空")
		}

		// 确保缓存目录存在或可以创建
		if err := os.MkdirAll(cache.Path, 0755); err != nil {
			return fmt.Errorf("无法创建缓存目录: %w", err)
		}

		if cache.MaxSize <= 0 {
			return fmt.Errorf("缓存最大大小必须大于0")
		}
	}

	return nil
}

// validateMonitor 验证监控配置
func (v *DefaultConfigValidator) validateMonitor(monitor *MonitorConfig) error {
	if monitor.Port <= 0 || monitor.Port > 65535 {
		return fmt.Errorf("监控端口无效: %d", monitor.Port)
	}

	if monitor.Username == "" {
		return fmt.Errorf("监控用户名不能为空")
	}

	if monitor.Password == "" {
		return fmt.Errorf("监控密码不能为空")
	}

	return nil
}

// SecurityConfigValidator 安全配置验证器
type SecurityConfigValidator struct{}

// Validate 验证安全配置
func (v *SecurityConfigValidator) Validate(config *Config) error {
	// 检查弱密码
	if config.Monitor.Enabled {
		if len(config.Monitor.Password) < 8 {
			return fmt.Errorf("监控密码长度不能少于8位")
		}
	}

	// 检查SSL配置（提供默认值）
	for i, site := range config.Sites {
		if site.SSL.Enabled {
			// 如果未配置SSL最小版本，使用默认值TLS1.2
			if site.SSL.MinVersion == "" {
				config.Sites[i].SSL.MinVersion = "TLS1.2"
			}
			// 如果未配置SSL最大版本，使用默认值TLS1.3
			if site.SSL.MaxVersion == "" {
				config.Sites[i].SSL.MaxVersion = "TLS1.3"
			}
		}
	}

	return nil
}

// ValidateSection 验证配置片段
func (v *SecurityConfigValidator) ValidateSection(section string, data interface{}) error {
	// 实现安全相关的片段验证
	return nil
}

// PerformanceConfigValidator 性能配置验证器
type PerformanceConfigValidator struct{}

// Validate 验证性能配置
func (v *PerformanceConfigValidator) Validate(config *Config) error {
	// 检查连接数配置
	if config.Server.MaxConnections > 10000 {
		return fmt.Errorf("最大连接数过高，可能影响性能: %d", config.Server.MaxConnections)
	}

	// 检查缓存配置
	if config.Cache.Enabled && config.Cache.MaxSize > 10*1024*1024*1024 { // 10GB
		return fmt.Errorf("缓存大小过大，可能影响性能: %d", config.Cache.MaxSize)
	}

	return nil
}

// ValidateSection 验证配置片段
func (v *PerformanceConfigValidator) ValidateSection(section string, data interface{}) error {
	// 实现性能相关的片段验证
	return nil
}

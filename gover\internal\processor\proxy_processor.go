package processor

import (
	"bytes"
	"context"
	"crypto/tls"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"reverse-proxy/internal/config"
)

// ProxyProcessor 反向代理处理器
type ProxyProcessor struct {
	*BaseProcessor
	client *http.Client
	upstreams map[string]*UpstreamInfo
	optimizeConfig *config.ResponseOptimizeConfig
	monitor MonitorInterface // 添加监控接口
}

// NewProxyProcessor 创建反向代理处理器
func NewProxyProcessor(logger *logrus.Logger, optimizeConfig *config.ResponseOptimizeConfig) *ProxyProcessor {
	// 创建HTTP客户端，支持自定义DNS解析
	client := &http.Client{
		Timeout: 30 * time.Second,
		Transport: &http.Transport{
			DialContext: (&net.Dialer{
				Timeout:   10 * time.Second,
				KeepAlive: 30 * time.Second,
			}).DialContext,
			MaxIdleConns:          100,
			IdleConnTimeout:       90 * time.Second,
			TLSHandshakeTimeout:   10 * time.Second,
			ExpectContinueTimeout: 1 * time.Second,
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true, // 跳过TLS证书验证，因为我们用IP连接但证书是为域名签发的
			},
		},
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			// 禁用自动重定向跟随，让浏览器处理重定向
			return http.ErrUseLastResponse
		},
	}
	
	return &ProxyProcessor{
		BaseProcessor: NewBaseProcessor("ProxyProcessor", 400, logger),
		client:        client,
		upstreams:     make(map[string]*UpstreamInfo),
		optimizeConfig: optimizeConfig,
		monitor:       nil, // 初始化为nil，稍后通过SetMonitor设置
	}
}

// UpdateUpstreams 更新上游服务器配置
func (pp *ProxyProcessor) UpdateUpstreams(upstreams map[string]*UpstreamInfo) {
	pp.upstreams = upstreams
	pp.LogInfo("更新了 %d 个上游服务器配置", len(upstreams))
}

// SetMonitor 设置监控接口
func (pp *ProxyProcessor) SetMonitor(monitor MonitorInterface) {
	pp.monitor = monitor
	pp.LogInfo("监控接口已设置")
}

// Process 处理反向代理请求
func (pp *ProxyProcessor) Process(req *RequestContext, resp *ResponseContext) *ProcessResult {
	// 使用数字标识进行高效判断
	if req.ProxyFlag == 0 || req.ContentSource == "cache" || req.ContentSource == "static" {
		pp.LogDebug("跳过代理处理: ProxyFlag=%d, ContentSource=%s",
			req.ProxyFlag, req.ContentSource)
		return Success() // 代理处理被跳过或已从其他源获取，继续处理
	}
	
	// 获取上游服务器信息
	upstream, exists := pp.upstreams[req.Route.Upstream]
	if !exists {
		resp.StatusCode = 502
		return ErrorWithMessage(
			fmt.Errorf("上游服务器 %s 不存在", req.Route.Upstream),
			"上游服务器配置未找到",
		)
	}
	req.Upstream = upstream
	
	// 构建目标URL
	targetURL, err := pp.buildTargetURL(req, upstream)
	if err != nil {
		resp.StatusCode = 502
		return ErrorWithMessage(err, "构建目标URL失败")
	}
	
	// 创建代理请求
	proxyReq, err := pp.createProxyRequest(req, targetURL)
	if err != nil {
		resp.StatusCode = 500
		return ErrorWithMessage(err, "创建代理请求失败")
	}
	
	// 创建自定义的HTTP客户端，实现类似curl --resolve的功能
	customClient := pp.createCustomClient(upstream, targetURL)

	// 发送请求
	startTime := time.Now()
	proxyResp, err := customClient.Do(proxyReq)
	requestTime := time.Since(startTime)

	if err != nil {
		resp.StatusCode = 502
		return ErrorWithMessage(err, "代理请求失败")
	}
	defer proxyResp.Body.Close()
	
	// 读取响应体
	body, err := io.ReadAll(proxyResp.Body)
	if err != nil {
		resp.StatusCode = 502
		return ErrorWithMessage(err, "读取响应体失败")
	}
	
	// 设置响应
	resp.StatusCode = proxyResp.StatusCode
	resp.Body = body
	resp.ContentLength = int64(len(body))
	resp.ContentType = proxyResp.Header.Get("Content-Type")

	// 复制响应头
	for key, values := range proxyResp.Header {
		for _, value := range values {
			resp.Headers.Add(key, value)
		}
	}

	// 检查是否应该进行响应体优化
	if pp.shouldOptimizeResponse(resp, proxyResp) {
		resp.ShouldOptimize = true
		pp.LogDebug("响应体优化已启用: size=%d, content-type=%s", len(body), resp.ContentType)
	} else {
		resp.ShouldOptimize = false
		pp.LogDebug("响应体优化已跳过: size=%d, content-type=%s", len(body), resp.ContentType)
	}
	
	// 设置后端信息
	resp.BackendIP = upstream.Address
	resp.BackendPort = upstream.Port

	// 设置缓存状态为MISS（从上游获取的响应）
	if resp.CacheStatus == "" {
		resp.CacheStatus = "MISS"
	}

	// 设置性能指标
	if resp.Metrics != nil {
		resp.Metrics.RequestTime = requestTime
		resp.Metrics.BytesReceived = int64(len(body))
		resp.Metrics.BytesSent = int64(len(req.Body))
	}

	// 记录上游流量统计
	if pp.monitor != nil {
		// 构建上游服务器的唯一标识（使用address+port+https_port）
		bk := fmt.Sprintf("%s:%d:%d", upstream.Address, upstream.Port, upstream.HTTPSPort)

		// 计算请求大小（包括HTTP头部的估算大小）
		var reqSize int64 = int64(len(req.Body))
		if req.OriginalRequest != nil {
			// 估算请求头大小
			reqSize += int64(len(req.Method) + len(req.URL.String()) + len(req.OriginalRequest.Proto))
			for k, v := range req.Headers {
				reqSize += int64(len(k) + len(strings.Join(v, ",")))
			}
		}

		// 响应大小
		respSize := int64(len(body))

		// 确保响应大小不为0（成功响应至少记录1字节）
		if respSize == 0 && proxyResp.StatusCode == 200 {
			respSize = 1
		}

		// 记录上游流量
		pp.monitor.RecordUpstreamTrafficWithKey(bk, reqSize, respSize)
		pp.LogDebug("记录上游流量: %s, 请求: %d bytes, 响应: %d bytes", bk, reqSize, respSize)
	} else {
		pp.LogDebug("监控接口为nil，无法记录上游流量统计")
	}

	pp.LogDebug("代理请求完成: %s -> %s:%d (%d bytes, %v)",
		req.URL.Path, upstream.Address, upstream.Port, len(body), requestTime)
	
	return SuccessWithMetadata(map[string]interface{}{
		"upstream_address": fmt.Sprintf("%s:%d", upstream.Address, upstream.Port),
		"response_size": len(body),
		"request_time": requestTime,
		"status_code": proxyResp.StatusCode,
	})
}

// ShouldProcess 判断是否应该处理
func (pp *ProxyProcessor) ShouldProcess(req *RequestContext) bool {
	return req.IsProxyRequest()
}

// getUpstreamProtocol 获取上游协议
func (pp *ProxyProcessor) getUpstreamProtocol(upstream *UpstreamInfo) string {
	if upstream.Protocol == "" || upstream.Protocol == "passthrough" {
		return "http" // 默认协议
	}
	return upstream.Protocol
}

// shouldOptimizeResponse 判断是否应该进行响应体优化
func (pp *ProxyProcessor) shouldOptimizeResponse(resp *ResponseContext, proxyResp *http.Response) bool {
	// 如果优化配置为空或未启用，跳过优化
	if pp.optimizeConfig == nil || !pp.optimizeConfig.Enabled {
		return false
	}

	// 检查状态码
	for _, skipCode := range pp.optimizeConfig.SkipStatusCodes {
		if resp.StatusCode == skipCode {
			pp.LogDebug("跳过优化: 状态码 %d 在跳过列表中", resp.StatusCode)
			return false
		}
	}

	// 检查Content-Length要求
	if pp.optimizeConfig.RequireContentLength {
		if proxyResp.Header.Get("Content-Length") == "" {
			pp.LogDebug("跳过优化: 缺少Content-Length头部")
			return false
		}
	}

	// 检查响应体大小
	bodySize := int64(len(resp.Body))
	if bodySize < int64(pp.optimizeConfig.MinBodySize) {
		pp.LogDebug("跳过优化: 响应体太小 %d < %d", bodySize, pp.optimizeConfig.MinBodySize)
		return false
	}
	if bodySize > int64(pp.optimizeConfig.MaxBodySize) {
		pp.LogDebug("跳过优化: 响应体太大 %d > %d", bodySize, pp.optimizeConfig.MaxBodySize)
		return false
	}

	// 检查内容类型
	contentType := resp.ContentType
	for _, skipType := range pp.optimizeConfig.SkipContentTypes {
		if pp.matchContentType(contentType, skipType) {
			pp.LogDebug("跳过优化: 内容类型 %s 匹配跳过模式 %s", contentType, skipType)
			return false
		}
	}

	// 检查编码类型
	encoding := proxyResp.Header.Get("Content-Encoding")
	if encoding != "" {
		for _, skipEncoding := range pp.optimizeConfig.SkipEncodings {
			if strings.EqualFold(encoding, skipEncoding) {
				pp.LogDebug("跳过优化: 编码类型 %s 在跳过列表中", encoding)
				return false
			}
		}
	}

	return true
}

// matchContentType 匹配内容类型，支持通配符
func (pp *ProxyProcessor) matchContentType(contentType, pattern string) bool {
	if pattern == "" || contentType == "" {
		return false
	}

	// 精确匹配
	if contentType == pattern {
		return true
	}

	// 通配符匹配 (例如: video/*, image/*)
	if strings.HasSuffix(pattern, "/*") {
		prefix := strings.TrimSuffix(pattern, "/*")
		return strings.HasPrefix(contentType, prefix+"/")
	}

	return false
}

// buildTargetURL 构建目标URL
func (pp *ProxyProcessor) buildTargetURL(req *RequestContext, upstream *UpstreamInfo) (*url.URL, error) {
	// 确定协议和端口
	scheme := upstream.Protocol
	port := upstream.Port

	if scheme == "passthrough" {
		// 对于passthrough协议，使用客户端请求的协议
		pp.LogDebug("检测客户端协议 - OriginalRequest: %v, TLS: %v", req.OriginalRequest != nil, req.OriginalRequest != nil && req.OriginalRequest.TLS != nil)
		if req.OriginalRequest != nil && req.OriginalRequest.TLS != nil {
			// 客户端使用HTTPS
			scheme = "https"
			if upstream.HTTPSPort > 0 {
				port = upstream.HTTPSPort
			} else {
				port = 443 // HTTPS默认端口
			}
			pp.LogDebug("客户端使用HTTPS，选择协议: %s, 端口: %d", scheme, port)
		} else {
			// 客户端使用HTTP
			scheme = "http"
			if port == 0 {
				port = 80 // HTTP默认端口
			}
			pp.LogDebug("客户端使用HTTP，选择协议: %s, 端口: %d", scheme, port)
		}
	} else if scheme == "" {
		scheme = "http"
		if port == 0 {
			port = 80
		}
	}

	baseURL := fmt.Sprintf("%s://%s:%d", scheme, upstream.Address, port)
	
	// 处理URL重写
	path := req.URL.Path
	if req.Route.Rewrite != "" {
		path = pp.applyRewrite(path, req.Route.Rewrite)
	}
	
	// 应用高级重写规则
	if req.Route.RewriteAdvanced != nil {
		path = pp.applyAdvancedRewrite(req, path)
	}
	
	// 构建完整URL
	targetURL, err := url.Parse(baseURL + path)
	if err != nil {
		return nil, fmt.Errorf("解析目标URL失败: %w", err)
	}
	
	// 保留查询参数
	if req.URL.RawQuery != "" {
		targetURL.RawQuery = req.URL.RawQuery
	}
	
	return targetURL, nil
}

// createProxyRequest 创建代理请求
func (pp *ProxyProcessor) createProxyRequest(req *RequestContext, targetURL *url.URL) (*http.Request, error) {
	// 创建请求体
	var body io.Reader
	if len(req.Body) > 0 {
		body = bytes.NewReader(req.Body)
	}
	
	// 创建请求
	proxyReq, err := http.NewRequest(req.Method, targetURL.String(), body)
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}
	
	// 复制请求头
	for key, values := range req.Headers {
		// 跳过一些不应该转发的头部
		if pp.shouldSkipHeader(key) {
			continue
		}
		
		for _, value := range values {
			proxyReq.Header.Add(key, value)
		}
	}
	
	// 设置Host头 - 保持原始请求的Host头，不要设置为后端服务器地址
	if originalHost := req.Headers.Get("Host"); originalHost != "" {
		proxyReq.Host = originalHost
	} else if req.OriginalRequest != nil {
		proxyReq.Host = req.OriginalRequest.Host
	}
	
	// 设置X-Forwarded-For
	if clientIP := req.ClientIP; clientIP != "" {
		if existing := proxyReq.Header.Get("X-Forwarded-For"); existing != "" {
			proxyReq.Header.Set("X-Forwarded-For", existing+", "+clientIP)
		} else {
			proxyReq.Header.Set("X-Forwarded-For", clientIP)
		}
	}
	
	// 设置X-Real-IP
	proxyReq.Header.Set("X-Real-IP", req.ClientIP)
	
	// 设置X-Forwarded-Proto
	if req.OriginalRequest.TLS != nil {
		proxyReq.Header.Set("X-Forwarded-Proto", "https")
	} else {
		proxyReq.Header.Set("X-Forwarded-Proto", "http")
	}
	
	return proxyReq, nil
}

// shouldSkipHeader 判断是否应该跳过某个头部
func (pp *ProxyProcessor) shouldSkipHeader(key string) bool {
	key = strings.ToLower(key)
	skipHeaders := []string{
		"connection",
		"proxy-connection",
		"upgrade",
		"proxy-authenticate",
		"proxy-authorization",
		"te",
		"trailers",
		"transfer-encoding",
	}
	
	for _, skipHeader := range skipHeaders {
		if key == skipHeader {
			return true
		}
	}
	
	return false
}

// applyRewrite 应用简单重写规则
func (pp *ProxyProcessor) applyRewrite(path, rewrite string) string {
	if rewrite == "" {
		return path
	}
	
	// 简单的前缀替换
	if strings.HasSuffix(rewrite, "/") {
		// 如果重写规则以/结尾，则替换路径前缀
		return rewrite + strings.TrimPrefix(path, "/")
	}
	
	// 直接替换
	return rewrite
}

// applyAdvancedRewrite 应用高级重写规则
func (pp *ProxyProcessor) applyAdvancedRewrite(req *RequestContext, path string) string {
	// TODO: 实现高级重写逻辑
	// 这里可以根据 req.Route.RewriteAdvanced 的配置
	// 实现更复杂的重写规则，如正则替换、条件重写等
	return path
}

// GetUpstream 获取上游服务器信息
func (pp *ProxyProcessor) GetUpstream(name string) (*UpstreamInfo, bool) {
	upstream, exists := pp.upstreams[name]
	return upstream, exists
}

// createCustomClient 创建自定义HTTP客户端，实现类似curl --resolve的功能
func (pp *ProxyProcessor) createCustomClient(upstream *UpstreamInfo, targetURL *url.URL) *http.Client {
	// 创建自定义的Transport，实现DNS解析重定向
	transport := &http.Transport{
		DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
			// 解析目标地址
			host, port, err := net.SplitHostPort(addr)
			if err != nil {
				return nil, fmt.Errorf("解析地址失败: %w", err)
			}

			// 如果请求的host是我们要代理的域名，则重定向到后端服务器IP
			if host == targetURL.Hostname() {
				// 使用后端服务器的IP地址，但保持端口不变
				addr = net.JoinHostPort(upstream.Address, port)
				pp.LogDebug("DNS重定向: %s -> %s", host+":"+port, addr)
			}

			// 使用默认的Dialer连接到实际的IP地址
			dialer := &net.Dialer{
				Timeout:   10 * time.Second,
				KeepAlive: 30 * time.Second,
			}
			return dialer.DialContext(ctx, network, addr)
		},
		MaxIdleConns:          100,
		IdleConnTimeout:       90 * time.Second,
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: true, // 跳过TLS证书验证，因为我们用IP连接但证书是为域名签发的
			ServerName:         targetURL.Hostname(), // 设置SNI为域名，而不是IP
		},
	}

	return &http.Client{
		Timeout:   30 * time.Second,
		Transport: transport,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			// 禁用自动重定向跟随，让浏览器处理重定向
			return http.ErrUseLastResponse
		},
	}
}

{"rustc": 28845397767708332, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "target": 7808655059340043471, "profile": 10571005914818816076, "path": 11445311098856210337, "deps": [[2421869795823446512, "aho_corasick", false, 13744560663151344187], [7864249588615721249, "regex_syntax", false, 1449173307379865156], [16554181743699084660, "regex_automata", false, 10893168300084044698], [17151257692364156824, "memchr", false, 5523368211683805538]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\regex-1ba051950485b77b\\dep-lib-regex"}}], "rustflags": [], "metadata": 13774875050488081270, "config": 2202906307356721367, "compile_kind": 0}
package hotreload

import (
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"reflect"
	"sync"
	"time"

	"reverse-proxy/internal/config"
	"github.com/sirupsen/logrus"
)

// ConfigDiff 配置差异
type ConfigDiff struct {
	GlobalChanged bool                    `json:"global_changed"`
	SitesAdded    []string               `json:"sites_added"`
	SitesRemoved  []string               `json:"sites_removed"`
	SitesModified []string               `json:"sites_modified"`
	SiteChanges   map[string]*SiteChange `json:"site_changes"`
	Timestamp     time.Time              `json:"timestamp"`
}

// SiteChange 站点变更详情
type SiteChange struct {
	SiteName        string   `json:"site_name"`
	DomainsChanged  bool     `json:"domains_changed"`
	RoutesChanged   bool     `json:"routes_changed"`
	UpstreamsChanged bool    `json:"upstreams_changed"`
	SSLChanged      bool     `json:"ssl_changed"`
	ACLChanged      bool     `json:"acl_changed"`
	CacheChanged    bool     `json:"cache_changed"`
	HeadersChanged  bool     `json:"headers_changed"`
	ChangedFields   []string `json:"changed_fields"`
}

// IncrementalReloader 增量重载器
type IncrementalReloader struct {
	mu                sync.RWMutex
	logger            *logrus.Logger
	lastConfigHash    string
	lastConfig        *config.Config
	siteHashes        map[string]string // site_id -> hash
	globalHash        string
	
	// 回调函数
	globalCallback    func(oldConfig, newConfig *config.Config) error
	siteAddCallback   func(siteConfig *config.SiteConfig) error
	siteUpdateCallback func(siteName string, oldConfig, newConfig *config.SiteConfig, changes *SiteChange) error
	siteRemoveCallback func(siteName string, oldConfig *config.SiteConfig) error
	
	// 性能统计
	stats             *ReloadStats
}

// ReloadStats 重载统计
type ReloadStats struct {
	TotalReloads      int64         `json:"total_reloads"`
	IncrementalReloads int64        `json:"incremental_reloads"`
	FullReloads       int64         `json:"full_reloads"`
	AvgReloadTime     time.Duration `json:"avg_reload_time"`
	LastReloadTime    time.Time     `json:"last_reload_time"`
	LastDiff          *ConfigDiff   `json:"last_diff"`
	SkippedReloads    int64         `json:"skipped_reloads"` // 无变更跳过的重载
}

// NewIncrementalReloader 创建增量重载器
func NewIncrementalReloader(logger *logrus.Logger) *IncrementalReloader {
	return &IncrementalReloader{
		logger:     logger,
		siteHashes: make(map[string]string),
		stats:      &ReloadStats{},
	}
}

// SetCallbacks 设置回调函数
func (ir *IncrementalReloader) SetCallbacks(
	globalCallback func(oldConfig, newConfig *config.Config) error,
	siteAddCallback func(siteConfig *config.SiteConfig) error,
	siteUpdateCallback func(siteName string, oldConfig, newConfig *config.SiteConfig, changes *SiteChange) error,
	siteRemoveCallback func(siteName string, oldConfig *config.SiteConfig) error,
) {
	ir.mu.Lock()
	defer ir.mu.Unlock()
	
	ir.globalCallback = globalCallback
	ir.siteAddCallback = siteAddCallback
	ir.siteUpdateCallback = siteUpdateCallback
	ir.siteRemoveCallback = siteRemoveCallback
}

// ProcessConfigChange 处理配置变更
func (ir *IncrementalReloader) ProcessConfigChange(newConfig *config.Config) error {
	startTime := time.Now()
	ir.mu.Lock()
	defer ir.mu.Unlock()
	
	ir.stats.TotalReloads++
	
	// 计算新配置的哈希
	newConfigHash, err := ir.calculateConfigHash(newConfig)
	if err != nil {
		return fmt.Errorf("计算配置哈希失败: %w", err)
	}
	
	// 如果配置没有变化，跳过重载
	if ir.lastConfigHash == newConfigHash {
		ir.stats.SkippedReloads++
		ir.logger.Debug("配置未发生变化，跳过重载")
		return nil
	}
	
	// 检测配置差异
	diff, err := ir.detectConfigDiff(ir.lastConfig, newConfig)
	if err != nil {
		return fmt.Errorf("检测配置差异失败: %w", err)
	}
	
	ir.stats.LastDiff = diff
	ir.logger.Infof("检测到配置变更: 全局变更=%v, 新增站点=%d, 删除站点=%d, 修改站点=%d",
		diff.GlobalChanged, len(diff.SitesAdded), len(diff.SitesRemoved), len(diff.SitesModified))
	
	// 执行增量重载
	if err := ir.executeIncrementalReload(diff, newConfig); err != nil {
		return fmt.Errorf("执行增量重载失败: %w", err)
	}
	
	// 更新状态
	ir.lastConfig = newConfig
	ir.lastConfigHash = newConfigHash
	ir.updateSiteHashes(newConfig)
	ir.updateGlobalHash(newConfig)
	
	// 更新统计信息
	reloadTime := time.Since(startTime)
	ir.stats.IncrementalReloads++
	ir.stats.LastReloadTime = time.Now()
	ir.updateAvgReloadTime(reloadTime)
	
	ir.logger.Infof("增量重载完成，耗时: %v", reloadTime)
	return nil
}

// calculateConfigHash 计算配置哈希
func (ir *IncrementalReloader) calculateConfigHash(cfg *config.Config) (string, error) {
	data, err := json.Marshal(cfg)
	if err != nil {
		return "", err
	}
	
	hash := sha256.Sum256(data)
	return hex.EncodeToString(hash[:]), nil
}

// calculateSiteHash 计算站点配置哈希
func (ir *IncrementalReloader) calculateSiteHash(site *config.SiteConfig) (string, error) {
	data, err := json.Marshal(site)
	if err != nil {
		return "", err
	}
	
	hash := sha256.Sum256(data)
	return hex.EncodeToString(hash[:]), nil
}

// calculateGlobalHash 计算全局配置哈希
func (ir *IncrementalReloader) calculateGlobalHash(cfg *config.Config) (string, error) {
	// 创建全局配置的副本，排除站点配置
	globalConfig := *cfg
	globalConfig.Sites = nil
	
	data, err := json.Marshal(globalConfig)
	if err != nil {
		return "", err
	}
	
	hash := sha256.Sum256(data)
	return hex.EncodeToString(hash[:]), nil
}

// detectConfigDiff 检测配置差异
func (ir *IncrementalReloader) detectConfigDiff(oldConfig, newConfig *config.Config) (*ConfigDiff, error) {
	diff := &ConfigDiff{
		SiteChanges: make(map[string]*SiteChange),
		Timestamp:   time.Now(),
	}
	
	// 检测全局配置变更
	if oldConfig != nil {
		newGlobalHash, err := ir.calculateGlobalHash(newConfig)
		if err != nil {
			return nil, err
		}
		
		diff.GlobalChanged = ir.globalHash != newGlobalHash
	} else {
		diff.GlobalChanged = true // 首次加载
	}
	
	// 检测站点变更
	if err := ir.detectSiteChanges(oldConfig, newConfig, diff); err != nil {
		return nil, err
	}
	
	return diff, nil
}

// detectSiteChanges 检测站点变更
func (ir *IncrementalReloader) detectSiteChanges(oldConfig, newConfig *config.Config, diff *ConfigDiff) error {
	// 构建旧站点映射
	oldSites := make(map[string]*config.SiteConfig)
	if oldConfig != nil {
		for i := range oldConfig.Sites {
			site := &oldConfig.Sites[i]
			oldSites[site.SiteID] = site
		}
	}
	
	// 构建新站点映射
	newSites := make(map[string]*config.SiteConfig)
	for i := range newConfig.Sites {
		site := &newConfig.Sites[i]
		newSites[site.SiteID] = site
	}
	
	// 检测新增和修改的站点
	for siteID, newSite := range newSites {
		if oldSite, exists := oldSites[siteID]; exists {
			// 站点存在，检查是否有变更
			newSiteHash, err := ir.calculateSiteHash(newSite)
			if err != nil {
				return err
			}
			
			oldSiteHash := ir.siteHashes[siteID]
			if oldSiteHash != newSiteHash {
				diff.SitesModified = append(diff.SitesModified, siteID)
				
				// 检测具体变更
				siteChange, err := ir.detectSiteSpecificChanges(oldSite, newSite)
				if err != nil {
					return err
				}
				diff.SiteChanges[siteID] = siteChange
			}
		} else {
			// 新增站点
			diff.SitesAdded = append(diff.SitesAdded, siteID)
		}
	}
	
	// 检测删除的站点
	for siteID := range oldSites {
		if _, exists := newSites[siteID]; !exists {
			diff.SitesRemoved = append(diff.SitesRemoved, siteID)
		}
	}
	
	return nil
}

// detectSiteSpecificChanges 检测站点具体变更
func (ir *IncrementalReloader) detectSiteSpecificChanges(oldSite, newSite *config.SiteConfig) (*SiteChange, error) {
	change := &SiteChange{
		SiteName: newSite.Name,
	}
	
	// 检测域名变更
	if !reflect.DeepEqual(oldSite.Domains, newSite.Domains) {
		change.DomainsChanged = true
		change.ChangedFields = append(change.ChangedFields, "domains")
	}
	
	// 检测路由变更
	if !reflect.DeepEqual(oldSite.Routes, newSite.Routes) {
		change.RoutesChanged = true
		change.ChangedFields = append(change.ChangedFields, "routes")
	}
	
	// 检测上游服务器变更
	if !reflect.DeepEqual(oldSite.Upstreams, newSite.Upstreams) {
		change.UpstreamsChanged = true
		change.ChangedFields = append(change.ChangedFields, "upstreams")
	}
	
	// 检测SSL配置变更
	if !reflect.DeepEqual(oldSite.SSL, newSite.SSL) {
		change.SSLChanged = true
		change.ChangedFields = append(change.ChangedFields, "ssl")
	}
	
	// 检测ACL配置变更
	if !reflect.DeepEqual(oldSite.ACL, newSite.ACL) {
		change.ACLChanged = true
		change.ChangedFields = append(change.ChangedFields, "acl")
	}
	
	// 检测缓存配置变更
	if !reflect.DeepEqual(oldSite.Rules, newSite.Rules) {
		change.CacheChanged = true
		change.ChangedFields = append(change.ChangedFields, "cache")
	}
	
	// 检测头部配置变更
	if !reflect.DeepEqual(oldSite.Headers, newSite.Headers) {
		change.HeadersChanged = true
		change.ChangedFields = append(change.ChangedFields, "headers")
	}
	
	return change, nil
}

// executeIncrementalReload 执行增量重载
func (ir *IncrementalReloader) executeIncrementalReload(diff *ConfigDiff, newConfig *config.Config) error {
	ir.logger.Info("开始执行增量重载...")

	// 1. 处理全局配置变更
	if diff.GlobalChanged && ir.globalCallback != nil {
		ir.logger.Info("执行全局配置重载...")
		if err := ir.globalCallback(ir.lastConfig, newConfig); err != nil {
			return fmt.Errorf("全局配置重载失败: %w", err)
		}
	}

	// 2. 处理删除的站点
	for _, siteID := range diff.SitesRemoved {
		if ir.siteRemoveCallback != nil {
			oldSite := ir.findSiteByID(ir.lastConfig, siteID)
			if oldSite != nil {
				ir.logger.Infof("删除站点: %s", siteID)
				if err := ir.siteRemoveCallback(siteID, oldSite); err != nil {
					return fmt.Errorf("删除站点 %s 失败: %w", siteID, err)
				}
			}
		}
	}

	// 3. 处理新增的站点
	for _, siteID := range diff.SitesAdded {
		if ir.siteAddCallback != nil {
			newSite := ir.findSiteByID(newConfig, siteID)
			if newSite != nil {
				ir.logger.Infof("新增站点: %s", siteID)
				if err := ir.siteAddCallback(newSite); err != nil {
					return fmt.Errorf("新增站点 %s 失败: %w", siteID, err)
				}
			}
		}
	}

	// 4. 处理修改的站点
	for _, siteID := range diff.SitesModified {
		if ir.siteUpdateCallback != nil {
			oldSite := ir.findSiteByID(ir.lastConfig, siteID)
			newSite := ir.findSiteByID(newConfig, siteID)
			siteChange := diff.SiteChanges[siteID]

			if oldSite != nil && newSite != nil && siteChange != nil {
				ir.logger.Infof("更新站点: %s (变更字段: %v)", siteID, siteChange.ChangedFields)
				if err := ir.siteUpdateCallback(siteID, oldSite, newSite, siteChange); err != nil {
					return fmt.Errorf("更新站点 %s 失败: %w", siteID, err)
				}
			}
		}
	}

	ir.logger.Info("增量重载执行完成")
	return nil
}

// findSiteByID 根据ID查找站点
func (ir *IncrementalReloader) findSiteByID(cfg *config.Config, siteID string) *config.SiteConfig {
	if cfg == nil {
		return nil
	}

	for i := range cfg.Sites {
		if cfg.Sites[i].SiteID == siteID {
			return &cfg.Sites[i]
		}
	}
	return nil
}

// updateSiteHashes 更新站点哈希
func (ir *IncrementalReloader) updateSiteHashes(cfg *config.Config) {
	// 清空旧的哈希
	for k := range ir.siteHashes {
		delete(ir.siteHashes, k)
	}

	// 计算新的哈希
	for i := range cfg.Sites {
		site := &cfg.Sites[i]
		if hash, err := ir.calculateSiteHash(site); err == nil {
			ir.siteHashes[site.SiteID] = hash
		}
	}
}

// updateGlobalHash 更新全局配置哈希
func (ir *IncrementalReloader) updateGlobalHash(cfg *config.Config) {
	if hash, err := ir.calculateGlobalHash(cfg); err == nil {
		ir.globalHash = hash
	}
}

// updateAvgReloadTime 更新平均重载时间
func (ir *IncrementalReloader) updateAvgReloadTime(reloadTime time.Duration) {
	if ir.stats.TotalReloads == 1 {
		ir.stats.AvgReloadTime = reloadTime
	} else {
		// 使用移动平均算法
		alpha := 0.1 // 平滑因子
		ir.stats.AvgReloadTime = time.Duration(float64(ir.stats.AvgReloadTime)*(1-alpha) + float64(reloadTime)*alpha)
	}
}

// GetStats 获取重载统计信息
func (ir *IncrementalReloader) GetStats() *ReloadStats {
	ir.mu.RLock()
	defer ir.mu.RUnlock()

	// 返回统计信息的副本
	statsCopy := *ir.stats
	return &statsCopy
}

// Reset 重置重载器状态
func (ir *IncrementalReloader) Reset() {
	ir.mu.Lock()
	defer ir.mu.Unlock()

	ir.lastConfig = nil
	ir.lastConfigHash = ""
	ir.globalHash = ""

	// 清空站点哈希
	for k := range ir.siteHashes {
		delete(ir.siteHashes, k)
	}

	// 重置统计信息
	ir.stats = &ReloadStats{}

	ir.logger.Info("增量重载器状态已重置")
}

// ForceFullReload 强制执行全量重载
func (ir *IncrementalReloader) ForceFullReload(newConfig *config.Config) error {
	ir.mu.Lock()
	defer ir.mu.Unlock()

	ir.logger.Info("执行强制全量重载...")
	ir.stats.TotalReloads++
	ir.stats.FullReloads++

	startTime := time.Now()

	// 执行全局配置重载
	if ir.globalCallback != nil {
		if err := ir.globalCallback(ir.lastConfig, newConfig); err != nil {
			return fmt.Errorf("全量重载失败: %w", err)
		}
	}

	// 更新状态
	ir.lastConfig = newConfig
	if hash, err := ir.calculateConfigHash(newConfig); err == nil {
		ir.lastConfigHash = hash
	}
	ir.updateSiteHashes(newConfig)
	ir.updateGlobalHash(newConfig)

	// 更新统计信息
	reloadTime := time.Since(startTime)
	ir.stats.LastReloadTime = time.Now()
	ir.updateAvgReloadTime(reloadTime)

	ir.logger.Infof("强制全量重载完成，耗时: %v", reloadTime)
	return nil
}

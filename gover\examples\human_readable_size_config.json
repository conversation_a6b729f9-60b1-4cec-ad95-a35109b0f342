{"server": {"http_port": 80, "https_port": 443}, "compression": {"enabled": true, "enable_recompression": true, "types": ["text/html", "text/css", "text/javascript", "application/json", "application/javascript", "application/xml", "text/xml", "text/plain"], "min_size": "1KB", "max_size": "10MB", "level": 6, "algorithms": ["br", "gzip", "deflate"], "brotli_quality": 6, "recompression_mode": "auto"}, "sites": [{"name": "high_performance_api", "domains": ["api.example.com"], "comment": "高性能API - 使用人性化大小配置", "upstreams": [{"name": "api_server", "address": "127.0.0.1", "port": 8001, "protocol": "http", "load_balance_group": "api_group"}], "routes": [{"pattern": "^/api/", "upstream": "api_group", "cache": true, "comment": "API接口 - 1KB到10MB范围内压缩"}]}, {"name": "content_delivery", "domains": ["cdn.example.com"], "comment": "内容分发 - 二进制单位配置", "compression": {"enabled": true, "min_size": "512", "max_size": "20MiB", "algorithms": ["br", "gzip"]}, "upstreams": [{"name": "content_server", "address": "127.0.0.1", "port": 8002, "protocol": "http", "load_balance_group": "content_group"}], "routes": [{"pattern": "^/static/", "upstream": "content_group", "cache": true, "comment": "静态资源 - 512字节到20MiB压缩"}]}, {"name": "mobile_optimized", "domains": ["m.example.com"], "comment": "移动端优化 - 小数格式支持", "compression": {"enabled": true, "min_size": "0.5KB", "max_size": "1.5MB", "algorithms": ["br", "gzip"]}, "upstreams": [{"name": "mobile_server", "address": "127.0.0.1", "port": 8003, "protocol": "http", "load_balance_group": "mobile_group"}], "routes": [{"pattern": "^/", "upstream": "mobile_group", "cache": true, "comment": "移动端内容 - 0.5KB到1.5MB压缩"}]}, {"name": "resource_limited", "domains": ["lite.example.com"], "comment": "资源受限环境 - 简化格式", "compression": {"enabled": true, "min_size": "2k", "max_size": "1m", "level": 3, "algorithms": ["gzip"]}, "upstreams": [{"name": "lite_server", "address": "127.0.0.1", "port": 8004, "protocol": "http", "load_balance_group": "lite_group"}], "routes": [{"pattern": "^/", "upstream": "lite_group", "cache": true, "comment": "轻量级服务 - 2k到1m压缩"}]}], "examples": {"size_formats": {"pure_numbers": {"min_size": 1024, "max_size": 10485760, "description": "纯数字格式，单位为字节"}, "decimal_units": {"min_size": "1KB", "max_size": "10MB", "description": "十进制单位，1KB=1000字节"}, "binary_units": {"min_size": "1KiB", "max_size": "10MiB", "description": "二进制单位，1KiB=1024字节"}, "simplified_format": {"min_size": "1k", "max_size": "10m", "description": "简化格式，k=KB, m=MB"}, "case_insensitive": {"examples": ["1KB", "1kb", "1Kb", "1kB"], "description": "大小写不敏感"}, "decimal_support": {"min_size": "1.5KB", "max_size": "2.5MB", "description": "支持小数格式"}, "with_spaces": {"min_size": "1 KB", "max_size": "10 MB", "description": "支持空格分隔"}}, "unit_conversion": {"decimal_units": {"1KB": "1,000 bytes", "1MB": "1,000,000 bytes", "1GB": "1,000,000,000 bytes", "1TB": "1,000,000,000,000 bytes"}, "binary_units": {"1KiB": "1,024 bytes", "1MiB": "1,048,576 bytes", "1GiB": "1,073,741,824 bytes", "1TiB": "1,099,511,627,776 bytes"}}, "common_scenarios": {"web_application": {"min_size": "1KB", "max_size": "5MB", "rationale": "Web应用通常处理HTML/CSS/JS文件"}, "api_service": {"min_size": "512", "max_size": "2MB", "rationale": "API响应通常较小，避免大响应压缩"}, "file_server": {"min_size": "1KB", "max_size": "50MB", "rationale": "文件服务器需要处理各种大小的文件"}, "mobile_app": {"min_size": "0.5KB", "max_size": "1MB", "rationale": "移动端网络环境，严格控制大小"}, "enterprise_internal": {"min_size": "2KB", "max_size": "20MB", "rationale": "企业内网，带宽充足但注重稳定性"}}, "performance_guidelines": {"small_files": {"range": "< 1KB", "recommendation": "跳过压缩", "reason": "压缩开销大于收益"}, "optimal_range": {"range": "1KB - 10MB", "recommendation": "启用压缩", "reason": "最佳压缩收益区间"}, "large_files": {"range": "> 10MB", "recommendation": "跳过压缩", "reason": "压缩时间长，性能影响大"}}}}
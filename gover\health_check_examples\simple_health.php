<?php
/**
 * 简单PHP健康检查页面
 * 用于检测PHP-FPM基本状态
 */

// 设置响应头
header('Content-Type: application/json');
header('Cache-Control: no-cache, no-store, must-revalidate');

// 检查PHP-FPM状态
$status = 'ok';
$message = 'PHP-FPM运行正常';

// 检查PHP-FPM函数是否可用
if (!function_exists('fastcgi_finish_request')) {
    $status = 'error';
    $message = 'PHP-FPM不可用';
    http_response_code(503);
} else {
    http_response_code(200);
}

// 返回JSON响应
echo json_encode([
    'status' => $status,
    'message' => $message,
    'timestamp' => time(),
    'datetime' => date('Y-m-d H:i:s'),
    'php_version' => PHP_VERSION
], JSON_UNESCAPED_UNICODE);
?> 
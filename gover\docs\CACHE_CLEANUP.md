# 缓存清理优化系统

## 概述

缓存清理优化系统通过异步清理、智能淘汰策略和高效索引管理，解决了原有缓存系统的性能瓶颈问题。

## 原有问题

### 1. 同步清理阻塞
- 缓存写入时同步执行清理操作
- 全量扫描缓存目录，I/O密集
- 使用冒泡排序，时间复杂度O(n²)

### 2. 清理策略简单
- 仅基于文件修改时间
- 没有考虑访问频率
- 缺少过期文件主动清理

## 优化方案

### 1. 异步清理架构

```
HTTP请求 -> 缓存写入 -> 立即返回
                ↓
        后台清理器 -> 定期清理
```

**优势**：
- 缓存写入不再阻塞
- 清理操作在后台执行
- 支持多种清理策略

### 2. 智能淘汰策略

#### LRU (Least Recently Used)
- 基于访问时间的淘汰
- 使用最小堆维护访问顺序
- 时间复杂度：O(log n)

#### 过期清理
- 定期扫描过期文件
- 主动清理，避免累积
- 可配置检查间隔

### 3. 高效索引管理

```go
type CacheEntry struct {
    Path        string
    Size        int64
    ModTime     time.Time
    ExpireAt    time.Time
    AccessCount int64
    LastAccess  time.Time
}
```

**特性**：
- 内存索引，快速查找
- 访问统计，支持LRU
- 过期时间缓存，避免重复解析

## 配置说明

### 基本配置

```json
{
  "cache": {
    "cleanup_interval": "5m",
    "expired_check_interval": "1m", 
    "enable_async_cleanup": true
  }
}
```

### 配置参数

#### `cleanup_interval` (duration)
- **默认值**: `5m`
- **说明**: 基于大小的清理间隔
- **建议**: 
  - 高写入频率: 1m-3m
  - 中等写入: 5m-10m
  - 低写入频率: 15m-30m

#### `expired_check_interval` (duration)
- **默认值**: `1m`
- **说明**: 过期文件检查间隔
- **建议**:
  - 短TTL缓存: 30s-1m
  - 长TTL缓存: 5m-15m

#### `enable_async_cleanup` (bool)
- **默认值**: `true`
- **说明**: 是否启用异步清理
- **建议**: 生产环境建议启用

## 性能对比

| 操作 | 原有方案 | 优化方案 | 提升 |
|------|----------|----------|------|
| 缓存写入 | 5-50ms | 0.1-1ms | 5-500x |
| 清理操作 | O(n²) | O(log n) | 显著 |
| 内存使用 | 低 | 中等 | 可控 |
| 清理精度 | 低 | 高 | 显著 |

## 使用场景

### 高并发写入
```json
{
  "cleanup_interval": "2m",
  "expired_check_interval": "30s",
  "enable_async_cleanup": true
}
```

### 大容量缓存
```json
{
  "cleanup_interval": "10m", 
  "expired_check_interval": "5m",
  "enable_async_cleanup": true
}
```

### 资源受限环境
```json
{
  "cleanup_interval": "15m",
  "expired_check_interval": "10m", 
  "enable_async_cleanup": false
}
```

## 监控指标

清理器提供详细的统计信息：

```json
{
  "total_cleanups": 156,
  "expired_cleaned": 1240,
  "size_cleaned": 892,
  "last_cleanup_time": "2024-01-15T10:30:00Z",
  "indexed_files": 5420,
  "heap_size": 5420,
  "cleanup_interval": "5m0s",
  "expired_check_interval": "1m0s"
}
```

### 关键指标说明

- `total_cleanups`: 总清理次数
- `expired_cleaned`: 清理的过期文件数
- `size_cleaned`: 基于大小清理的文件数
- `indexed_files`: 索引中的文件数量
- `heap_size`: LRU堆大小

## 最佳实践

### 1. 容量规划
- 索引内存使用: 每个文件约200字节
- 10万文件约需20MB内存
- 根据文件数量调整清理间隔

### 2. 性能调优
- 高写入场景：缩短清理间隔
- 大文件场景：增加清理间隔
- 监控清理器统计信息

### 3. 故障处理
- 异步清理失败时自动降级
- 监控索引大小，防止内存泄漏
- 定期重建索引保证一致性

## 注意事项

### 1. 内存使用
- 异步清理器会占用额外内存
- 内存使用与缓存文件数量成正比
- 需要合理配置清理间隔

### 2. 一致性保证
- 索引与实际文件可能短暂不一致
- 定期重建索引保证准确性
- 文件操作失败时及时更新索引

### 3. 性能权衡
- 异步清理提升写入性能
- 增加了内存使用和复杂度
- 需要根据场景选择合适策略

## 故障排查

### 内存使用过高
- 检查缓存文件数量
- 调整清理间隔
- 考虑禁用异步清理

### 清理效果不佳
- 检查清理间隔配置
- 查看清理器统计信息
- 确认磁盘空间充足

### 索引不一致
- 重启服务重建索引
- 检查文件系统权限
- 查看错误日志

## 升级指南

### 从同步清理升级

1. **配置更新**
```json
{
  "enable_async_cleanup": true,
  "cleanup_interval": "5m",
  "expired_check_interval": "1m"
}
```

2. **监控调整**
- 添加清理器监控指标
- 调整告警阈值
- 监控内存使用变化

3. **性能测试**
- 对比升级前后性能
- 验证清理效果
- 确认内存使用可控

### 回滚方案
```json
{
  "enable_async_cleanup": false
}
```

设置为false即可回滚到原有同步清理方式。

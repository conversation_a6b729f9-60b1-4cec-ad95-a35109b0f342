package acl

import (
	"fmt"
	"path/filepath"
	"sync"
	"time"

	"github.com/fsnotify/fsnotify"
	"github.com/sirupsen/logrus"
)

// FileWatcher 文件监控器
type FileWatcher struct {
	watcher   *fsnotify.Watcher
	files     map[string]FileWatchCallback // 文件路径 -> 回调函数
	mu        sync.RWMutex
	logger    *logrus.Logger
	stopChan  chan struct{}
	isRunning bool
}

// FileWatchCallback 文件变化回调函数
type FileWatchCallback func(filename string) error

// NewFileWatcher 创建文件监控器
func NewFileWatcher(logger *logrus.Logger) (*FileWatcher, error) {
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		return nil, fmt.Errorf("创建文件监控器失败: %w", err)
	}

	fw := &FileWatcher{
		watcher:  watcher,
		files:    make(map[string]FileWatchCallback),
		logger:   logger,
		stop<PERSON>han: make(chan struct{}),
	}

	return fw, nil
}

// AddFile 添加文件监控
func (fw *FileWatcher) AddFile(filename string, callback FileWatchCallback) error {
	if filename == "" {
		return nil // 空文件名，跳过
	}

	// 获取绝对路径
	absPath, err := filepath.Abs(filename)
	if err != nil {
		return fmt.Errorf("获取文件绝对路径失败 %s: %w", filename, err)
	}

	fw.mu.Lock()
	defer fw.mu.Unlock()

	// 检查文件是否已在监控中
	if _, exists := fw.files[absPath]; exists {
		fw.logger.Debugf("文件已在监控中: %s", absPath)
		return nil
	}

	// 添加到监控
	if err := fw.watcher.Add(absPath); err != nil {
		return fmt.Errorf("添加文件监控失败 %s: %w", absPath, err)
	}

	fw.files[absPath] = callback
	fw.logger.Infof("已添加文件监控: %s", absPath)

	return nil
}

// RemoveFile 移除文件监控
func (fw *FileWatcher) RemoveFile(filename string) error {
	if filename == "" {
		return nil
	}

	absPath, err := filepath.Abs(filename)
	if err != nil {
		return fmt.Errorf("获取文件绝对路径失败 %s: %w", filename, err)
	}

	fw.mu.Lock()
	defer fw.mu.Unlock()

	if _, exists := fw.files[absPath]; !exists {
		return nil // 文件不在监控中
	}

	if err := fw.watcher.Remove(absPath); err != nil {
		fw.logger.Warnf("移除文件监控失败 %s: %v", absPath, err)
	}

	delete(fw.files, absPath)
	fw.logger.Infof("已移除文件监控: %s", absPath)

	return nil
}

// Start 启动文件监控
func (fw *FileWatcher) Start() error {
	fw.mu.Lock()
	if fw.isRunning {
		fw.mu.Unlock()
		return fmt.Errorf("文件监控器已在运行")
	}
	fw.isRunning = true
	fw.mu.Unlock()

	go fw.watchLoop()
	fw.logger.Info("文件监控器已启动")

	return nil
}

// Stop 停止文件监控
func (fw *FileWatcher) Stop() error {
	fw.mu.Lock()
	if !fw.isRunning {
		fw.mu.Unlock()
		return nil
	}
	fw.isRunning = false

	// 清理所有监控的文件
	for file := range fw.files {
		fw.logger.Debugf("移除文件监控: %s", file)
		if err := fw.watcher.Remove(file); err != nil {
			fw.logger.Warnf("移除文件监控失败 %s: %v", file, err)
		}
	}
	fw.files = make(map[string]FileWatchCallback)

	fw.mu.Unlock()

	close(fw.stopChan)

	if err := fw.watcher.Close(); err != nil {
		fw.logger.Warnf("关闭文件监控器失败: %v", err)
	}

	fw.logger.Info("文件监控器已停止")
	return nil
}

// watchLoop 监控循环
func (fw *FileWatcher) watchLoop() {
	defer func() {
		if r := recover(); r != nil {
			fw.logger.Errorf("文件监控循环异常: %v", r)
		}
	}()

	for {
		select {
		case event, ok := <-fw.watcher.Events:
			if !ok {
				return
			}
			fw.handleFileEvent(event)

		case err, ok := <-fw.watcher.Errors:
			if !ok {
				return
			}
			fw.logger.Errorf("文件监控错误: %v", err)

		case <-fw.stopChan:
			return
		}
	}
}

// handleFileEvent 处理文件事件
func (fw *FileWatcher) handleFileEvent(event fsnotify.Event) {
	// 只处理写入和创建事件
	if !event.Has(fsnotify.Write) && !event.Has(fsnotify.Create) {
		return
	}

	fw.mu.RLock()
	callback, exists := fw.files[event.Name]
	fw.mu.RUnlock()

	if !exists {
		return
	}

	fw.logger.Infof("检测到文件变化: %s", event.Name)

	// 延迟一小段时间，避免文件正在写入时读取
	time.Sleep(100 * time.Millisecond)

	// 执行回调
	if err := callback(event.Name); err != nil {
		fw.logger.Errorf("文件变化回调执行失败 %s: %v", event.Name, err)
	} else {
		fw.logger.Infof("文件重载成功: %s", event.Name)
	}
}

// GetWatchedFiles 获取监控的文件列表
func (fw *FileWatcher) GetWatchedFiles() []string {
	fw.mu.RLock()
	defer fw.mu.RUnlock()

	files := make([]string, 0, len(fw.files))
	for file := range fw.files {
		files = append(files, file)
	}

	return files
}

// IsWatching 检查文件是否在监控中
func (fw *FileWatcher) IsWatching(filename string) bool {
	if filename == "" {
		return false
	}

	absPath, err := filepath.Abs(filename)
	if err != nil {
		return false
	}

	fw.mu.RLock()
	defer fw.mu.RUnlock()

	_, exists := fw.files[absPath]
	return exists
}

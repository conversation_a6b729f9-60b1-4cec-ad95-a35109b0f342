# 人性化大小格式支持更新

## 📋 更新概述

本次更新为配置文件中的多个大小相关配置项添加了人性化格式支持，现在可以使用 `128M`、`64KB`、`1GB` 等格式，而不需要手动计算字节数。

## 🎯 更新的配置项

### 1. **缓存配置 (cache.max_size)**
```json
// 之前
"cache": {
  "max_size": 1073741824  // 难以理解的字节数
}

// 现在
"cache": {
  "max_size": "1GB"       // 清晰易懂
}
```

### 2. **gRPC配置 (grpc.max_recv_msg_size / max_send_msg_size)**
```json
// 之前
"grpc": {
  "max_recv_msg_size": 4194304,  // 4MB的字节数
  "max_send_msg_size": 4194304
}

// 现在
"grpc": {
  "max_recv_msg_size": "4MB",    // 直观的大小表示
  "max_send_msg_size": "4MB"
}
```

### 3. **性能配置 (performance.buffer_size / max_connections)**
```json
// 之前
"performance": {
  "buffer_size": 131072,      // 128KB的字节数
  "max_connections": 1000000  // 1M连接数
}

// 现在
"performance": {
  "buffer_size": "128KB",     // 清晰的缓冲区大小
  "max_connections": "1M"     // 清晰的连接数
}
```

### 4. **内存映射缓存配置 (performance.mmap_cache.max_size)**
```json
// 之前
"mmap_cache": {
  "max_size": "128M"  // 已经支持，现在统一了
}

// 现在
"mmap_cache": {
  "max_size": "128M"  // 保持一致的格式
}
```

## 🔧 技术实现

### **配置结构体更新**
将相关字段的类型从 `int`/`int64` 更改为 `config.Size` 类型：

```go
// CacheConfig
type CacheConfig struct {
    MaxSize Size `mapstructure:"max_size"`  // 之前是 int64
}

// GRPCConfig  
type GRPCConfig struct {
    MaxRecvMsgSize Size `mapstructure:"max_recv_msg_size"`  // 之前是 int
    MaxSendMsgSize Size `mapstructure:"max_send_msg_size"`  // 之前是 int
}

// PerformanceConfig
type PerformanceConfig struct {
    BufferSize     Size `mapstructure:"buffer_size"`      // 之前是 int
    MaxConnections Size `mapstructure:"max_connections"`  // 之前是 int
}

// MmapCacheConfig
type MmapCacheConfig struct {
    MaxSize Size `mapstructure:"max_size"`  // 之前是 string
}
```

### **类型转换处理**
在使用这些配置值的地方添加了适当的类型转换：

```go
// 示例：缓存管理器初始化
cache := &FileCache{
    maxSize: int64(cfg.MaxSize),  // Size -> int64
}

// 示例：gRPC代理初始化
grpcConfig := grpcproxy.GRPCConfig{
    MaxRecvMsgSize: int(cfg.GRPC.MaxRecvMsgSize),  // Size -> int
    MaxSendMsgSize: int(cfg.GRPC.MaxSendMsgSize),  // Size -> int
}

// 示例：高性能代理初始化
perfConfig := performance.PerformanceConfig{
    BufferSize:     int(p.config.Performance.BufferSize),     // Size -> int
    MaxConnections: int(p.config.Performance.MaxConnections), // Size -> int
}
```

## 📝 支持的格式

### **基本单位**
- `B` - 字节
- `KB` - 千字节 (1024 bytes)
- `MB` - 兆字节 (1024 KB)
- `GB` - 吉字节 (1024 MB)
- `TB` - 太字节 (1024 GB)

### **二进制单位**
- `KiB` - 1024 bytes
- `MiB` - 1024 KiB  
- `GiB` - 1024 MiB
- `TiB` - 1024 GiB

### **十进制单位**
- `k/K` - 1000
- `m/M` - 1000k
- `g/G` - 1000m

### **示例**
```json
{
  "cache": { "max_size": "1GB" },
  "grpc": { 
    "max_recv_msg_size": "4MB",
    "max_send_msg_size": "4MB"
  },
  "performance": {
    "buffer_size": "128KB",
    "max_connections": "1M",
    "mmap_cache": {
      "max_size": "512MB"
    }
  }
}
```

## ✅ 验证测试

创建了测试程序验证所有配置项的解析：

```bash
go run test_config_sizes.go
```

**输出结果**：
```
测试配置文件中的大小格式解析...
Cache MaxSize: 953.7MiB (1000000000 bytes)
GRPC MaxRecvMsgSize: 3.8MiB (4000000 bytes)
GRPC MaxSendMsgSize: 3.8MiB (4000000 bytes)
Performance BufferSize: 125KiB (128000 bytes)
Performance MaxConnections: 976.6KiB (1000000 connections)
MmapCache MaxSize: 122.1MiB (128000000 bytes)

✅ 所有大小格式解析成功！
```

## 🚀 使用建议

### **推荐配置**

#### **开发环境**
```json
{
  "cache": { "max_size": "512MB" },
  "grpc": { 
    "max_recv_msg_size": "4MB",
    "max_send_msg_size": "4MB"
  },
  "performance": {
    "buffer_size": "64KB",
    "max_connections": "10K",
    "mmap_cache": { "max_size": "256MB" }
  }
}
```

#### **生产环境**
```json
{
  "cache": { "max_size": "2GB" },
  "grpc": { 
    "max_recv_msg_size": "16MB",
    "max_send_msg_size": "16MB"
  },
  "performance": {
    "buffer_size": "128KB",
    "max_connections": "100K",
    "mmap_cache": { "max_size": "1GB" }
  }
}
```

#### **高性能环境**
```json
{
  "cache": { "max_size": "8GB" },
  "grpc": { 
    "max_recv_msg_size": "32MB",
    "max_send_msg_size": "32MB"
  },
  "performance": {
    "buffer_size": "256KB",
    "max_connections": "1M",
    "mmap_cache": { "max_size": "4GB" }
  }
}
```

## 🔄 向后兼容性

- ✅ **完全向后兼容**：原有的数字格式仍然支持
- ✅ **自动转换**：系统会自动处理新旧格式
- ✅ **无需迁移**：现有配置文件无需修改即可继续使用

## 📈 优势

1. **可读性提升**：配置更加直观易懂
2. **减少错误**：避免手动计算字节数的错误
3. **维护便利**：配置调整更加方便
4. **标准化**：统一了所有大小配置的格式
5. **灵活性**：支持多种单位格式

现在您的反向代理服务器配置更加人性化和易于管理！🎉

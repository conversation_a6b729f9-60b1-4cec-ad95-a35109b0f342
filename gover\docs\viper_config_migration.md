# Viper配置管理迁移指南

## 📋 概述

本文档介绍如何将现有的配置管理系统迁移到基于Viper的新架构，实现类似nginx的灵活配置管理。

## 🎯 新架构优势

### 1. **解决现有问题**
- ✅ **功能完整性**：热重载时所有功能都能正确更新
- ✅ **稳定性**：消除莫名其妙的重载问题
- ✅ **资源管理**：完善的资源清理和内存管理
- ✅ **错误处理**：更好的错误提示和回滚机制

### 2. **类似nginx的特性**
- 🔧 **配置分层**：支持多层配置文件继承
- 🔄 **热重载**：零停机配置更新
- 📝 **配置验证**：强大的配置验证机制
- 💾 **备份回滚**：自动备份和一键回滚
- 🎛️ **灵活配置**：支持多种配置格式

## 🏗️ 新架构设计

### 核心组件

```
ViperIntegration (集成管理器)
├── ViperConfigManager (配置管理器)
│   ├── 配置加载和解析
│   ├── 热重载监听
│   ├── 配置验证
│   └── 备份和回滚
├── ConfigHierarchy (配置分层)
│   ├── 多层配置合并
│   ├── 优先级管理
│   └── 继承机制
└── ConfigValidators (验证器)
    ├── 默认验证器
    ├── 安全验证器
    └── 性能验证器
```

## 🚀 迁移步骤

### 第1步：创建新的配置管理器

```go
// 替换原有的配置管理器
func createNewConfigManager(configFile string, logger *logrus.Logger) (*config.ViperIntegration, error) {
    // 配置选项
    options := config.DefaultIntegrationOptions()
    options.EnableHierarchy = true
    options.EnableBackup = true
    options.EnableValidation = true
    options.MaxBackups = 10
    
    // 创建集成管理器
    return config.NewViperIntegration(configFile, logger, options)
}
```

### 第2步：更新代理服务器初始化

```go
// 在 proxy.go 中更新
type Proxy struct {
    // 替换原有的配置管理器
    viperManager *config.ViperIntegration
    
    // 保留其他字段...
    logger       *logrus.Logger
    sslManager   *ssl.Manager
    // ...
}

// 更新构造函数
func NewProxy(cfg *config.Config, logger *logrus.Logger) (*Proxy, error) {
    p := &Proxy{
        logger: logger,
        // 初始化其他组件...
    }
    
    // 创建Viper配置管理器
    viperManager, err := createNewConfigManager("config.json", logger)
    if err != nil {
        return nil, fmt.Errorf("创建配置管理器失败: %w", err)
    }
    p.viperManager = viperManager
    
    return p, nil
}
```

### 第3步：更新热重载实现

```go
// 新的热重载实现
func (p *Proxy) EnableHotReload(configFile string) error {
    // 使用Viper的热重载功能
    return p.viperManager.EnableHotReload(func(oldConfig, newConfig *config.Config) error {
        return p.handleConfigReload(oldConfig, newConfig)
    })
}

// 配置重载处理函数
func (p *Proxy) handleConfigReload(oldConfig, newConfig *config.Config) error {
    p.logger.Info("开始处理配置重载...")
    
    // 1. 检查是否需要重启服务器（端口变更）
    if p.needServerRestart(oldConfig, newConfig) {
        if err := p.restartServers(newConfig); err != nil {
            return fmt.Errorf("重启服务器失败: %w", err)
        }
    }
    
    // 2. 更新SSL证书
    if p.sslManager != nil {
        p.sslManager.UpdateDomainCerts(newConfig.Sites)
        p.sslManager.ReloadCertificates()
    }
    
    // 3. 重新创建缓存管理器
    if err := p.reloadCacheManager(newConfig); err != nil {
        return fmt.Errorf("重载缓存管理器失败: %w", err)
    }
    
    // 4. 重新创建站点配置
    if err := p.reloadSites(newConfig); err != nil {
        return fmt.Errorf("重载站点配置失败: %w", err)
    }
    
    p.logger.Info("配置重载完成")
    return nil
}
```

### 第4步：配置分层设置

创建配置文件层级：

```
config/
├── config.json              # 主配置文件 (优先级: 100)
├── config.env.json          # 环境配置 (优先级: 200)
├── config.local.json        # 本地配置 (优先级: 300)
└── backups/                 # 自动备份目录
    ├── config_1640995200.json
    └── config_1640995300.json
```

### 第5步：更新主程序

```go
// 在 main.go 中更新
func main() {
    flag.Parse()
    
    // 创建Viper配置管理器
    viperManager, err := createNewConfigManager(*configFile, logger.GetLogger())
    if err != nil {
        logger.Fatal("创建配置管理器失败:", err)
    }
    defer viperManager.Close()
    
    // 加载初始配置
    cfg, err := viperManager.LoadConfig()
    if err != nil {
        logger.Fatal("加载配置失败:", err)
    }
    
    // 创建代理服务器
    proxyServer, err := proxy.NewProxyWithViperManager(cfg, viperManager, logger.GetLogger())
    if err != nil {
        logger.Fatal("创建代理服务器失败:", err)
    }
    
    // 启用热重载
    if err := proxyServer.EnableHotReload(*configFile); err != nil {
        logger.Warn("启用热重载失败:", err)
    }
    
    // 启动服务器...
}
```

## 🔧 配置分层示例

### 主配置文件 (config.json)
```json
{
  "server": {
    "http_port": 80,
    "https_port": 443,
    "max_connections": 1000
  },
  "log": {
    "level": "info",
    "format": "combined"
  },
  "sites": [
    {
      "site_id": "main-site",
      "name": "main",
      "domains": ["example.com"],
      "defaultsite": true
    }
  ]
}
```

### 环境配置文件 (config.env.json)
```json
{
  "log": {
    "level": "debug"
  },
  "monitor": {
    "enabled": true,
    "port": 8080
  }
}
```

### 本地配置文件 (config.local.json)
```json
{
  "server": {
    "max_connections": 2000
  },
  "sites": [
    {
      "site_id": "main-site",
      "debug_mode": true
    }
  ]
}
```

### 最终合并结果
```json
{
  "server": {
    "http_port": 80,
    "https_port": 443,
    "max_connections": 2000  // 来自 local
  },
  "log": {
    "level": "debug",        // 来自 env
    "format": "combined"     // 来自 main
  },
  "monitor": {
    "enabled": true,         // 来自 env
    "port": 8080            // 来自 env
  },
  "sites": [
    {
      "site_id": "main-site",
      "name": "main",
      "domains": ["example.com"],
      "defaultsite": true,
      "debug_mode": true     // 来自 local
    }
  ]
}
```

## 🎛️ 高级功能

### 1. 配置验证
```go
// 添加自定义验证器
type CustomValidator struct{}

func (v *CustomValidator) Validate(config *config.Config) error {
    // 自定义验证逻辑
    return nil
}

viperManager.AddValidator(&CustomValidator{})
```

### 2. 配置备份和回滚
```go
// 手动创建备份
err := viperManager.CreateBackup("升级前备份")

// 查看备份历史
history := viperManager.GetBackupHistory()

// 回滚到指定版本
err := viperManager.RollbackToBackup(1640995200)
```

### 3. 配置层管理
```go
// 添加新的配置层
err := viperManager.AddConfigLayer("testing", "config.test.json", "测试配置", 250)

// 禁用配置层
err := viperManager.DisableConfigLayer("testing")

// 查看配置层信息
layers := viperManager.GetHierarchyInfo()
```

## 📊 监控和调试

### 1. 配置统计
```go
stats := viperManager.GetStats()
fmt.Printf("重载次数: %d, 成功: %d, 失败: %d\n", 
    stats.TotalReloads, stats.SuccessReloads, stats.FailedReloads)
```

### 2. 配置来源追踪
```go
// 查看配置值来源
source := viperManager.GetValueSource("server.max_connections")
fmt.Printf("max_connections 来自配置层: %s\n", source)
```

## 🔄 迁移检查清单

- [ ] 创建新的Viper配置管理器
- [ ] 更新代理服务器构造函数
- [ ] 实现新的热重载处理函数
- [ ] 设置配置文件层级
- [ ] 更新主程序初始化代码
- [ ] 添加配置验证器
- [ ] 测试热重载功能
- [ ] 测试配置备份和回滚
- [ ] 验证所有功能正常工作
- [ ] 清理旧的配置管理代码

## 🎉 预期效果

迁移完成后，你将获得：

1. **稳定的热重载**：不再有莫名其妙的重载问题
2. **完整的功能更新**：所有配置变更都能正确生效
3. **类似nginx的配置体验**：分层配置、继承、覆盖
4. **强大的错误处理**：配置验证、自动备份、一键回滚
5. **更好的可维护性**：清晰的架构、完善的日志

这个新架构将彻底解决现有的热重载问题，并提供更强大、更灵活的配置管理能力！

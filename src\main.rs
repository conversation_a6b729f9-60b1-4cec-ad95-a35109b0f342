use anyhow::Result;
use clap::Parser;
use std::path::PathBuf;
use tracing::{info, error};

mod config;
mod server;
mod processor;
mod cache;
mod load_balancer;
mod ssl;
mod monitor;
mod logger;
mod utils;

use crate::config::Config;
use crate::server::ProxyServer;

#[derive(Parser)]
#[command(name = "rust-reverse-proxy")]
#[command(about = "High-performance reverse proxy server written in Rust")]
#[command(version = "0.1.0")]
struct Args {
    /// Configuration file path (JSON format)
    #[arg(short, long, default_value = "config.json")]
    config: PathBuf,

    /// Log level (trace, debug, info, warn, error)
    #[arg(short, long, default_value = "info")]
    log_level: String,
}

#[tokio::main]
async fn main() -> Result<()> {
    let args = Args::parse();
    
    // 初始化日志系统
    init_logging(&args.log_level)?;
    
    info!("Starting Rust Reverse Proxy Server v{}", env!("CARGO_PKG_VERSION"));
    info!("Config file: {}", args.config.display());
    
    // 加载配置
    let config = Config::load(&args.config).await?;
    info!("Configuration loaded successfully");
    info!("Sites configured: {}", config.sites.len());
    
    // 创建并启动代理服务器
    let server = ProxyServer::new(config).await?;
    
    // 设置信号处理
    let shutdown_signal = setup_shutdown_signal();
    
    // 启动服务器
    tokio::select! {
        result = server.run() => {
            match result {
                Ok(_) => info!("Server stopped gracefully"),
                Err(e) => error!("Server error: {}", e),
            }
        }
        _ = shutdown_signal => {
            info!("Shutdown signal received, stopping server...");
        }
    }
    
    info!("Server shutdown complete");
    Ok(())
}

fn init_logging(level: &str) -> Result<()> {
    use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt, EnvFilter};
    
    let filter = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| EnvFilter::new(level));
    
    tracing_subscriber::registry()
        .with(filter)
        .with(
            tracing_subscriber::fmt::layer()
                .with_target(false)
                .with_thread_ids(true)
                .with_file(true)
                .with_line_number(true)
        )
        .init();
    
    Ok(())
}

async fn setup_shutdown_signal() {
    use tokio::signal;
    
    #[cfg(unix)]
    {
        let mut sigterm = signal::unix::signal(signal::unix::SignalKind::terminate())
            .expect("Failed to install SIGTERM handler");
        let mut sigint = signal::unix::signal(signal::unix::SignalKind::interrupt())
            .expect("Failed to install SIGINT handler");
        
        tokio::select! {
            _ = sigterm.recv() => {},
            _ = sigint.recv() => {},
        }
    }
    
    #[cfg(windows)]
    {
        signal::ctrl_c().await.expect("Failed to listen for ctrl-c");
    }
}

package performance

import (
	"bytes"
	"net/http/httptest"
	"os"
	"path/filepath"
	"testing"
)

// TestMmapCache 测试内存映射缓存
func TestMmapCache(t *testing.T) {
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "mmap_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建测试文件
	testFile := filepath.Join(tempDir, "test.txt")
	testContent := "Hello, World! This is a test file for memory mapping."
	err = os.WriteFile(testFile, []byte(testContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	// 创建内存映射缓存
	cache := NewMmapCache(tempDir, 1024*1024) // 1MB限制
	if cache == nil {
		t.<PERSON>al("Failed to create MmapCache")
	}

	// 测试文件加载
	data, exists := cache.Get("test.txt")
	if !exists {
		t.Fatal("Failed to load file into cache")
	}

	if string(data) != testContent {
		t.<PERSON>rf("Expected %q, got %q", testContent, string(data))
	}

	// 测试重复获取（应该从缓存中获取）
	data2, exists2 := cache.Get("test.txt")
	if !exists2 {
		t.Fatal("Failed to get file from cache")
	}

	if string(data2) != testContent {
		t.Errorf("Expected %q, got %q", testContent, string(data2))
	}

	// 验证统计信息
	stats := cache.GetStats()
	if stats == nil {
		t.Fatal("Stats is nil")
	}

	fileCount, ok := stats["total_files"].(int)
	if !ok || fileCount != 1 {
		t.Errorf("Expected total_files to be 1, got %v", stats["total_files"])
	}
}

// TestMmapCacheEviction 测试缓存清理
func TestMmapCacheEviction(t *testing.T) {
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "mmap_eviction_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建小容量缓存
	cache := NewMmapCache(tempDir, 100) // 100字节限制

	// 创建多个测试文件
	files := []string{"file1.txt", "file2.txt", "file3.txt"}
	content := "This is a test file with some content that exceeds the cache limit."

	for _, filename := range files {
		testFile := filepath.Join(tempDir, filename)
		err = os.WriteFile(testFile, []byte(content), 0644)
		if err != nil {
			t.Fatalf("Failed to create test file %s: %v", filename, err)
		}
	}

	// 加载文件，应该触发清理
	for _, filename := range files {
		cache.Get(filename)
	}

	// 验证缓存状态
	stats := cache.GetStats()
	usage, ok := stats["total_usage"].(int64)
	if !ok {
		t.Fatal("total_usage stat not found")
	}

	if usage > 100 {
		t.Errorf("Cache usage %d exceeds limit 100", usage)
	}
}

// TestZeroCopyWrite 测试零拷贝写入
func TestZeroCopyWrite(t *testing.T) {
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "zero_copy_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建测试文件
	testFile := filepath.Join(tempDir, "test.txt")
	testContent := "Zero copy test content"
	err = os.WriteFile(testFile, []byte(testContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	// 创建内存映射缓存
	cache := NewMmapCache(tempDir, 1024*1024)

	// 预加载文件
	_, exists := cache.Get("test.txt")
	if !exists {
		t.Fatal("Failed to load file into cache")
	}

	// 测试写入到缓冲区
	var buffer bytes.Buffer
	err = cache.ZeroCopyWrite("test.txt", &buffer)
	if err != nil {
		t.Fatalf("ZeroCopyWrite failed: %v", err)
	}

	if buffer.String() != testContent {
		t.Errorf("Expected %q, got %q", testContent, buffer.String())
	}

	// 测试不存在的文件
	err = cache.ZeroCopyWrite("nonexistent.txt", &buffer)
	if err == nil {
		t.Error("Expected error for nonexistent file")
	}
}

// TestWriteToHTTPResponse 测试HTTP响应写入
func TestWriteToHTTPResponse(t *testing.T) {
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "http_response_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建测试文件
	testFile := filepath.Join(tempDir, "response.txt")
	testContent := "HTTP response test content"
	err = os.WriteFile(testFile, []byte(testContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	// 创建内存映射缓存
	cache := NewMmapCache(tempDir, 1024*1024)

	// 预加载文件
	_, exists := cache.Get("response.txt")
	if !exists {
		t.Fatal("Failed to load file into cache")
	}

	// 创建HTTP响应记录器
	recorder := httptest.NewRecorder()

	// 测试写入HTTP响应
	written, err := cache.WriteToHTTPResponse("response.txt", recorder)
	if err != nil {
		t.Fatalf("WriteToHTTPResponse failed: %v", err)
	}

	if written != len(testContent) {
		t.Errorf("Expected %d bytes written, got %d", len(testContent), written)
	}

	if recorder.Body.String() != testContent {
		t.Errorf("Expected %q, got %q", testContent, recorder.Body.String())
	}

	// 测试文件大小获取
	size, exists := cache.GetFileSize("response.txt")
	if !exists {
		t.Fatal("Failed to get file size")
	}

	if size != int64(len(testContent)) {
		t.Errorf("Expected size %d, got %d", len(testContent), size)
	}
}

// TestAdvancedZeroCopy 测试高级零拷贝功能
func TestAdvancedZeroCopy(t *testing.T) {
	azc := NewAdvancedZeroCopy()
	if azc == nil {
		t.Fatal("Failed to create AdvancedZeroCopy")
	}

	// 测试splice传输（Windows下简化实现）
	transferred, err := azc.SpliceTransfer(0, 1, 1024)
	if err != nil {
		t.Fatalf("SpliceTransfer failed: %v", err)
	}

	if transferred != 1024 {
		t.Errorf("Expected 1024 bytes transferred, got %d", transferred)
	}

	// 测试tee传输
	err = azc.TeeTransfer(0, 1, 2, 512)
	if err != nil {
		t.Fatalf("TeeTransfer failed: %v", err)
	}

	// 测试关闭
	err = azc.Close()
	if err != nil {
		t.Fatalf("Close failed: %v", err)
	}
}

// TestCacheStats 测试缓存统计
func TestCacheStats(t *testing.T) {
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "cache_stats_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建内存映射缓存
	cache := NewMmapCache(tempDir, 1024*1024)

	// 创建测试文件
	testFile := filepath.Join(tempDir, "stats_test.txt")
	testContent := "Statistics test content"
	err = os.WriteFile(testFile, []byte(testContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	// 加载文件
	cache.Get("stats_test.txt")

	// 获取统计信息
	stats := cache.GetStats()

	// 验证统计信息
	expectedKeys := []string{"total_files", "total_usage", "max_size"}
	for _, key := range expectedKeys {
		if _, exists := stats[key]; !exists {
			t.Errorf("Expected stat key %s not found", key)
		}
	}

	// 验证文件数量
	if fileCount, ok := stats["total_files"].(int); !ok || fileCount != 1 {
		t.Errorf("Expected total_files to be 1, got %v", stats["total_files"])
	}

	// 验证使用量
	if usage, ok := stats["total_usage"].(int64); !ok || usage <= 0 {
		t.Errorf("Expected total_usage to be positive, got %v", stats["total_usage"])
	}
}

// BenchmarkMmapCacheGet 性能测试：缓存获取
func BenchmarkMmapCacheGet(b *testing.B) {
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "bench_get_test")
	if err != nil {
		b.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建测试文件
	testFile := filepath.Join(tempDir, "bench.txt")
	testContent := "Benchmark test content for memory mapping performance"
	err = os.WriteFile(testFile, []byte(testContent), 0644)
	if err != nil {
		b.Fatalf("Failed to create test file: %v", err)
	}

	// 创建内存映射缓存
	cache := NewMmapCache(tempDir, 1024*1024)

	// 预加载文件
	cache.Get("bench.txt")

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		cache.Get("bench.txt")
	}
}

// BenchmarkZeroCopyWrite 性能测试：零拷贝写入
func BenchmarkZeroCopyWrite(b *testing.B) {
	// 创建临时目录
	tempDir, err := os.MkdirTemp("", "bench_write_test")
	if err != nil {
		b.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建测试文件
	testFile := filepath.Join(tempDir, "bench_write.txt")
	testContent := "Benchmark zero copy write test content"
	err = os.WriteFile(testFile, []byte(testContent), 0644)
	if err != nil {
		b.Fatalf("Failed to create test file: %v", err)
	}

	// 创建内存映射缓存
	cache := NewMmapCache(tempDir, 1024*1024)

	// 预加载文件
	cache.Get("bench_write.txt")

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var buffer bytes.Buffer
		cache.ZeroCopyWrite("bench_write.txt", &buffer)
	}
}

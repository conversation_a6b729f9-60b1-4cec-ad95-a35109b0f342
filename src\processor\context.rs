use bytes::Bytes;
use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::{Duration, Instant, SystemTime};
use uuid::Uuid;

use crate::config::{SiteConfig, RouteConfig};

/// 请求上下文
#[derive(Debug, Clone)]
pub struct RequestContext {
    /// 请求ID
    pub request_id: String,
    /// 请求开始时间
    pub start_time: Instant,
    /// 请求时间戳
    pub timestamp: SystemTime,
    /// 客户端地址
    pub client_addr: SocketAddr,
    /// HTTP方法
    pub method: String,
    /// 请求URI
    pub uri: String,
    /// 查询参数
    pub query_string: Option<String>,
    /// 请求头
    pub headers: HashMap<String, String>,
    /// 请求体
    pub body: Option<Bytes>,
    /// 匹配的站点配置
    pub site: Option<Arc<SiteConfig>>,
    /// 匹配的路由配置
    pub route: Option<Arc<RouteConfig>>,
    /// 上游服务器名称
    pub upstream: Option<String>,
    /// 响应上下文
    pub response: ResponseContext,
    /// 处理器间共享的数据
    pub data: HashMap<String, String>,
    /// 错误信息
    pub error: Option<ProcessError>,
    /// 是否启用缓存
    pub cache_enabled: bool,
    /// 缓存键
    pub cache_key: Option<String>,
    /// 缓存状态
    pub cache_status: Option<CacheStatus>,
    /// 处理统计
    pub stats: RequestStats,
}

impl RequestContext {
    /// 创建新的请求上下文
    pub fn new(
        method: String,
        uri: String,
        headers: HashMap<String, String>,
        client_addr: SocketAddr,
    ) -> Self {
        let request_id = Uuid::new_v4().to_string();
        let now = Instant::now();
        
        Self {
            request_id,
            start_time: now,
            timestamp: SystemTime::now(),
            client_addr,
            method,
            uri: uri.clone(),
            query_string: Self::extract_query_string(&uri),
            headers,
            body: None,
            site: None,
            route: None,
            upstream: None,
            response: ResponseContext::new(),
            data: HashMap::new(),
            error: None,
            cache_enabled: false,
            cache_key: None,
            cache_status: None,
            stats: RequestStats::new(),
        }
    }
    
    /// 提取查询字符串
    fn extract_query_string(uri: &str) -> Option<String> {
        if let Some(pos) = uri.find('?') {
            Some(uri[pos + 1..].to_string())
        } else {
            None
        }
    }
    
    /// 获取路径部分（不包含查询参数）
    pub fn path(&self) -> &str {
        if let Some(pos) = self.uri.find('?') {
            &self.uri[..pos]
        } else {
            &self.uri
        }
    }
    
    /// 获取请求头
    pub fn get_header(&self, name: &str) -> Option<&String> {
        self.headers.get(&name.to_lowercase())
    }
    
    /// 设置请求头
    pub fn set_header(&mut self, name: String, value: String) {
        self.headers.insert(name.to_lowercase(), value);
    }
    
    /// 移除请求头
    pub fn remove_header(&mut self, name: &str) {
        self.headers.remove(&name.to_lowercase());
    }
    
    /// 获取共享数据
    pub fn get_data(&self, key: &str) -> Option<&String> {
        self.data.get(key)
    }
    
    /// 设置共享数据
    pub fn set_data(&mut self, key: String, value: String) {
        self.data.insert(key, value);
    }
    
    /// 设置错误
    pub fn set_error(&mut self, status_code: u16, message: &str) {
        self.error = Some(ProcessError {
            message: message.to_string(),
            status_code,
            cause: None,
        });
        self.response.status_code = status_code;
    }
    
    /// 是否有错误
    pub fn has_error(&self) -> bool {
        self.error.is_some()
    }
    
    /// 获取处理时长
    pub fn elapsed(&self) -> Duration {
        self.start_time.elapsed()
    }
    
    /// 获取User-Agent
    pub fn user_agent(&self) -> Option<&String> {
        self.get_header("user-agent")
    }
    
    /// 获取Referer
    pub fn referer(&self) -> Option<&String> {
        self.get_header("referer")
    }
    
    /// 获取Content-Type
    pub fn content_type(&self) -> Option<&String> {
        self.get_header("content-type")
    }
    
    /// 获取Content-Length
    pub fn content_length(&self) -> Option<usize> {
        self.get_header("content-length")
            .and_then(|v| v.parse().ok())
    }
    
    /// 是否为HTTPS请求
    pub fn is_https(&self) -> bool {
        self.get_header("x-forwarded-proto")
            .map(|v| v == "https")
            .unwrap_or(false)
    }
    
    /// 获取真实客户端IP
    pub fn real_ip(&self) -> String {
        // 按优先级检查各种代理头部
        if let Some(ip) = self.get_header("x-real-ip") {
            return ip.clone();
        }
        
        if let Some(forwarded_for) = self.get_header("x-forwarded-for") {
            // X-Forwarded-For 可能包含多个IP，取第一个
            if let Some(first_ip) = forwarded_for.split(',').next() {
                return first_ip.trim().to_string();
            }
        }
        
        // 回退到连接的客户端地址
        self.client_addr.ip().to_string()
    }
}

/// 响应上下文
#[derive(Debug, Clone)]
pub struct ResponseContext {
    /// HTTP状态码
    pub status_code: u16,
    /// 响应头
    pub headers: HashMap<String, String>,
    /// 响应体
    pub body: Option<Bytes>,
    /// 响应大小
    pub size: usize,
    /// 是否来自缓存
    pub from_cache: bool,
    /// 缓存状态
    pub cache_status: CacheStatus,
}

impl ResponseContext {
    /// 创建新的响应上下文
    pub fn new() -> Self {
        Self {
            status_code: 200,
            headers: HashMap::new(),
            body: None,
            size: 0,
            from_cache: false,
            cache_status: CacheStatus::Miss,
        }
    }
    
    /// 设置响应头
    pub fn set_header(&mut self, name: String, value: String) {
        self.headers.insert(name.to_lowercase(), value);
    }
    
    /// 获取响应头
    pub fn get_header(&self, name: &str) -> Option<&String> {
        self.headers.get(&name.to_lowercase())
    }
    
    /// 移除响应头
    pub fn remove_header(&mut self, name: &str) {
        self.headers.remove(&name.to_lowercase());
    }
    
    /// 设置响应体
    pub fn set_body(&mut self, body: Bytes) {
        self.size = body.len();
        self.body = Some(body);
    }
    
    /// 设置Content-Type
    pub fn set_content_type(&mut self, content_type: &str) {
        self.set_header("content-type".to_string(), content_type.to_string());
    }
    
    /// 设置Content-Length
    pub fn set_content_length(&mut self, length: usize) {
        self.set_header("content-length".to_string(), length.to_string());
    }
    
    /// 是否为成功响应
    pub fn is_success(&self) -> bool {
        self.status_code >= 200 && self.status_code < 300
    }
    
    /// 是否为重定向响应
    pub fn is_redirect(&self) -> bool {
        self.status_code >= 300 && self.status_code < 400
    }
    
    /// 是否为客户端错误
    pub fn is_client_error(&self) -> bool {
        self.status_code >= 400 && self.status_code < 500
    }
    
    /// 是否为服务器错误
    pub fn is_server_error(&self) -> bool {
        self.status_code >= 500
    }
}

impl Default for ResponseContext {
    fn default() -> Self {
        Self::new()
    }
}

/// 缓存状态
#[derive(Debug, Clone, PartialEq)]
pub enum CacheStatus {
    /// 缓存未命中
    Miss,
    /// 缓存命中
    Hit,
    /// 缓存过期
    Expired,
    /// 缓存被绕过
    Bypass,
    /// 缓存更新
    Refresh,
}

impl std::fmt::Display for CacheStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            CacheStatus::Miss => write!(f, "MISS"),
            CacheStatus::Hit => write!(f, "HIT"),
            CacheStatus::Expired => write!(f, "EXPIRED"),
            CacheStatus::Bypass => write!(f, "BYPASS"),
            CacheStatus::Refresh => write!(f, "REFRESH"),
        }
    }
}

/// 请求统计信息
#[derive(Debug, Clone)]
pub struct RequestStats {
    /// DNS解析时间
    pub dns_time: Option<Duration>,
    /// 连接建立时间
    pub connect_time: Option<Duration>,
    /// SSL握手时间
    pub ssl_time: Option<Duration>,
    /// 请求发送时间
    pub send_time: Option<Duration>,
    /// 等待响应时间
    pub wait_time: Option<Duration>,
    /// 响应接收时间
    pub receive_time: Option<Duration>,
    /// 总处理时间
    pub total_time: Option<Duration>,
}

impl RequestStats {
    pub fn new() -> Self {
        Self {
            dns_time: None,
            connect_time: None,
            ssl_time: None,
            send_time: None,
            wait_time: None,
            receive_time: None,
            total_time: None,
        }
    }
}

impl Default for RequestStats {
    fn default() -> Self {
        Self::new()
    }
}

/// 处理错误（重新导出以避免循环依赖）
#[derive(Debug, Clone)]
pub struct ProcessError {
    pub message: String,
    pub status_code: u16,
    pub cause: Option<String>,
}

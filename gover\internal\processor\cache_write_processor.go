package processor

import (
	"net/http"
	"strings"
	"time"
	"reverse-proxy/internal/cache"
	"reverse-proxy/internal/config"
	"github.com/sirupsen/logrus"
)

// CacheWriteProcessor 缓存写入处理器 - 负责将响应内容写入缓存
type CacheWriteProcessor struct {
	*BaseProcessor
	enabled      bool
	cacheManager *cache.CacheManager
}

// NewCacheWriteProcessor 创建新的缓存写入处理器
func NewCacheWriteProcessor(enabled bool, cacheManager *cache.CacheManager, logger *logrus.Logger) *CacheWriteProcessor {
	return &CacheWriteProcessor{
		BaseProcessor: NewBaseProcessor("CacheWriteProcessor", 550, logger),
		enabled:       enabled,
		cacheManager:  cacheManager,
	}
}

// Process 处理缓存写入逻辑（在响应阶段调用）
func (cwp *CacheWriteProcessor) Process(req *RequestContext, resp *ResponseContext) *ProcessResult {
	// 使用数字标识进行高效判断
	if req.CacheWriteFlag == 0 || !cwp.enabled || !req.CacheEnabled || req.CacheKey == "" {
		cwp.LogDebug("跳过缓存写入: CacheWriteFlag=%d, enabled=%v, CacheEnabled=%v, CacheKey=%s",
			req.CacheWriteFlag, cwp.enabled, req.CacheEnabled, req.CacheKey)
		return Success()
	}

	// 只缓存成功的响应
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		cwp.LogDebug("跳过缓存写入，状态码不符合: %d", resp.StatusCode)
		return Success()
	}

	// 检查内容来源，避免重复缓存
	if req.ContentSource == "cache" {
		cwp.LogDebug("跳过缓存写入，内容来源已是缓存")
		return Success()
	}

	// 确定要缓存的响应体数据
	var bodyToCache []byte
	if resp.ResponseBody != nil && len(resp.ResponseBody) > 0 {
		// 如果有处理后的响应体数据，使用它
		bodyToCache = resp.ResponseBody
		cwp.LogDebug("使用处理后的响应体数据进行缓存: %d bytes", len(bodyToCache))
	} else {
		// 否则使用原始响应体
		bodyToCache = resp.Body
		cwp.LogDebug("使用原始响应体数据进行缓存: %d bytes", len(bodyToCache))
	}

	// 计算缓存TTL（只计算一次）
	cacheTTL := cwp.getCacheTTL(req, resp)

	// 创建缓存响应对象
	cached := &cache.CachedResponse{
		StatusCode:      resp.StatusCode,
		Headers:         make(map[string][]string),
		Body:            bodyToCache,
		ExpireAt:        time.Now().Add(cacheTTL),
		CompressionType: "none", // 暂时不压缩
		OriginalSize:    int64(len(resp.Body)),
		MinifiedSize:    int64(len(bodyToCache)),
		CompressedSize:  int64(len(bodyToCache)),
		IsMinified:      len(bodyToCache) != len(resp.Body), // 如果大小不同说明被处理过
		BackendIP:       resp.BackendIP,
		BackendPort:     resp.BackendPort,
		BackendProtocol: "http", // 根据实际情况设置
		CachedAt:        time.Now(),
	}

	// 复制响应头部
	for key, values := range resp.Headers {
		cached.Headers[key] = make([]string, len(values))
		copy(cached.Headers[key], values)
	}

	// 写入缓存
	if err := cwp.cacheManager.Set(req.CacheKey, cached); err != nil {
		cwp.LogWarn("缓存写入失败: %s, %v", req.CacheKey, err)
		return Success() // 缓存写入失败不影响响应
	}

	// 设置缓存状态
	if resp.CacheStatus == "" {
		resp.CacheStatus = "STORED"
	}

	// 如果启用了响应体优化，设置优化标志和数据
	if resp.ShouldOptimize {
		resp.UseMemoryResponse = true
		resp.ResponseBody = bodyToCache
		cwp.LogDebug("响应体优化已启用，将直接使用内存数据: %d bytes", len(bodyToCache))
	}

	cwp.LogDebug("缓存写入成功: %s (ExpireAt: %v)", req.CacheKey, cached.ExpireAt)

	return SuccessWithMetadata(map[string]interface{}{
		"cache_key":  req.CacheKey,
		"expire_at":  cached.ExpireAt,
		"cache_size": len(cached.Body),
		"ttl":        cacheTTL,
	})
}

// ShouldProcess 判断是否应该处理
func (cwp *CacheWriteProcessor) ShouldProcess(req *RequestContext) bool {
	return cwp.enabled && req.CacheEnabled
}

// getCacheTTL 获取缓存TTL（使用与proxy.go相同的规则系统）
func (cwp *CacheWriteProcessor) getCacheTTL(req *RequestContext, resp *ResponseContext) time.Duration {
	// 如果请求上下文中有TTL设置，优先使用它
	if req.CacheTTL > 0 {
		return req.CacheTTL
	}

	// 使用缓存管理器的规则系统来计算TTL
	if cwp.cacheManager != nil && req.CacheKey != "" {
		// 构造一个虚拟的HTTP请求来匹配规则
		// 新的缓存Key格式为SITE_ID|METHOD|PATH（用竖线分隔）
		parts := strings.SplitN(req.CacheKey, "|", 4)
		cwp.LogDebug("缓存Key解析: key=%s, parts=%v", req.CacheKey, parts)

		if len(parts) >= 3 {
			// parts[0] = SITE_ID, parts[1] = METHOD, parts[2] = PATH
			method := parts[1]
			urlPath := parts[2]
			cwp.LogDebug("提取的信息: method=%s, urlPath=%s", method, urlPath)

			// 创建虚拟请求用于规则匹配
			virtualReq, err := http.NewRequest(method, urlPath, nil)
			if err != nil {
				cwp.LogDebug("创建虚拟请求失败: %v", err)
			} else {
				// 设置Host（从原始请求中获取）
				if req.OriginalRequest != nil {
					virtualReq.Host = req.OriginalRequest.Host
				}
				cwp.LogDebug("虚拟请求创建成功: method=%s, url=%s, host=%s", method, urlPath, virtualReq.Host)

				// 获取站点级规则（如果有的话）
				var siteRules []config.CacheRule
				if req.Site != nil && len(req.Site.Rules) > 0 {
					siteRules = req.Site.Rules
					cwp.LogDebug("站点级缓存规则数量: %d, 站点ID: %s", len(siteRules), req.Site.SiteID)
					// 打印所有站点级规则
					for i, rule := range siteRules {
						cwp.LogDebug("站点规则 %d: pattern=%s, ttl=%v, enabled=%v", i+1, rule.Pattern, rule.TTL, rule.Enabled)
					}
				} else {
					cwp.LogDebug("没有站点级缓存规则")
				}

				// 使用缓存管理器的规则匹配系统，传入响应状态码
				ttl := cwp.cacheManager.GetCacheTTLByRule(virtualReq, siteRules, resp.StatusCode)
				cwp.LogDebug("规则匹配结果: TTL=%v, URL=%s, 状态码=%d", ttl, urlPath, resp.StatusCode)
				if ttl > 0 {
					return ttl
				}
			}
		}
	}

	// 如果无法通过规则系统获取TTL，根据内容类型设置不同的TTL
	switch req.ContentSource {
	case "static":
		return 24 * time.Hour // 静态文件缓存更长时间
	case "proxy":
		// 使用缓存管理器的默认TTL，而不是硬编码的5分钟
		if cwp.cacheManager != nil {
			return cwp.cacheManager.GetDefaultTTL()
		}
		return 2 * time.Hour // 如果缓存管理器不可用，使用2小时作为后备
	default:
		// 使用缓存管理器的默认TTL
		if cwp.cacheManager != nil {
			return cwp.cacheManager.GetDefaultTTL()
		}
		return 2 * time.Hour // 如果缓存管理器不可用，使用2小时作为后备
	}
}



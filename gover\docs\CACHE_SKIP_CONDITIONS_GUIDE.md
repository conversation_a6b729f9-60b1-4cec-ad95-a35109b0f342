# 🚀 缓存跳过条件功能指南

## 📋 **功能概述**

我已经为你实现了基于<PERSON><PERSON>、Head<PERSON>、查询参数等条件的缓存跳过功能，特别适用于WordPress等动态网站的缓存策略。

## 🔧 **新增的配置选项**

### **CacheRule 增强**
```json
{
  "pattern": "^/",
  "ttl": "2h",
  "enabled": true,
  "skip_conditions": {
    "cookies": ["wordpress_logged_in", "comment_author"],
    "headers": ["Authorization"],
    "query_params": ["preview", "customize_changeset_uuid"],
    "user_agents": [".*bot.*", ".*crawler.*"],
    "methods": ["POST", "PUT", "DELETE"],
    "custom_rules": [
      {
        "name": "skip_admin_users",
        "type": "cookie_value",
        "key": "wordpress_logged_in",
        "pattern": ".*",
        "description": "跳过已登录WordPress用户的缓存"
      }
    ]
  }
}
```

### **跳过条件类型**

#### **1. 基于Cookie**
```json
"cookies": ["wordpress_logged_in", "comment_author", "wp-settings-*"]
```
- 当请求包含这些Cookie时跳过缓存
- 支持精确匹配Cookie名称

#### **2. 基于Header**
```json
"headers": ["Authorization", "X-Requested-With"]
```
- 当请求包含这些Header时跳过缓存
- 适用于API认证、AJAX请求等

#### **3. 基于查询参数**
```json
"query_params": ["preview", "customize_changeset_uuid", "s"]
```
- 当URL包含这些查询参数时跳过缓存
- 适用于预览模式、搜索查询等

#### **4. 基于User-Agent**
```json
"user_agents": [".*bot.*", ".*crawler.*", ".*spider.*"]
```
- 支持正则表达式匹配User-Agent
- 可以针对爬虫、机器人等特殊处理

#### **5. 基于HTTP方法**
```json
"methods": ["POST", "PUT", "DELETE", "PATCH"]
```
- 指定HTTP方法时跳过缓存
- 通常用于非幂等操作

#### **6. 自定义规则**
```json
"custom_rules": [
  {
    "name": "skip_logged_in_users",
    "type": "cookie_value",
    "key": "wordpress_logged_in",
    "pattern": ".*",
    "description": "跳过已登录用户的缓存"
  },
  {
    "name": "skip_preview_mode",
    "type": "query_value",
    "key": "preview",
    "value": "true",
    "description": "跳过预览模式的缓存"
  }
]
```

## 🎯 **WordPress专用配置示例**

### **完整的WordPress缓存配置**
```json
{
  "cache": {
    "enabled": true,
    "type": "file",
    "path": "cache",
    "max_size": 1073741824,
    "ttl": "2h",
    "rules": [
      {
        "pattern": "\\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$",
        "ttl": "72h",
        "enabled": true,
        "skip_conditions": null
      },
      {
        "pattern": "\\.(html|htm)$",
        "ttl": "2h",
        "enabled": true,
        "skip_conditions": {
          "cookies": ["wordpress_logged_in", "comment_author"],
          "headers": ["Authorization"],
          "query_params": ["preview", "customize_changeset_uuid"],
          "methods": ["POST", "PUT", "DELETE"]
        }
      },
      {
        "pattern": "/wp-admin/",
        "ttl": "0s",
        "enabled": true,
        "skip_conditions": {
          "cookies": ["*"],
          "headers": ["*"],
          "methods": ["*"]
        }
      },
      {
        "pattern": "/wp-login\\.php",
        "ttl": "0s",
        "enabled": true,
        "skip_conditions": {
          "cookies": ["*"],
          "headers": ["*"],
          "methods": ["*"]
        }
      },
      {
        "pattern": "^/",
        "ttl": "2h",
        "enabled": true,
        "skip_conditions": {
          "cookies": ["wordpress_logged_in", "comment_author"],
          "query_params": ["preview", "s", "customize_changeset_uuid"],
          "methods": ["POST", "PUT", "DELETE", "PATCH"],
          "custom_rules": [
            {
              "name": "skip_search_queries",
              "type": "query_value",
              "key": "s",
              "pattern": ".*",
              "description": "跳过搜索查询的缓存"
            }
          ]
        }
      }
    ]
  }
}
```

## 📊 **缓存策略建议**

### **WordPress网站的最佳实践**

#### **1. 静态资源（长期缓存）**
```json
{
  "pattern": "\\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$",
  "ttl": "168h",
  "skip_conditions": null
}
```

#### **2. 页面内容（条件缓存）**
```json
{
  "pattern": "^/",
  "ttl": "2h",
  "skip_conditions": {
    "cookies": ["wordpress_logged_in", "comment_author"],
    "query_params": ["preview", "s"],
    "methods": ["POST", "PUT", "DELETE"]
  }
}
```

#### **3. 管理区域（不缓存）**
```json
{
  "pattern": "/wp-admin/",
  "ttl": "0s",
  "skip_conditions": {
    "cookies": ["*"],
    "methods": ["*"]
  }
}
```

#### **4. API接口（短期缓存）**
```json
{
  "pattern": "/wp-json/",
  "ttl": "5m",
  "skip_conditions": {
    "cookies": ["wordpress_logged_in"],
    "headers": ["Authorization"],
    "methods": ["POST", "PUT", "DELETE", "PATCH"]
  }
}
```

## 🔄 **工作原理**

### **缓存决策流程**
```
请求到达 → 检查跳过条件 → 匹配任一条件？
    ↓                           ↓
   否                          是
    ↓                           ↓
检查缓存                    跳过缓存，直接代理
    ↓
缓存命中？
    ↓
返回缓存 / 代理并缓存
```

### **跳过条件检查顺序**
1. **HTTP方法检查**
2. **Cookie检查**
3. **Header检查**
4. **查询参数检查**
5. **User-Agent检查**
6. **自定义规则检查**

## 💡 **使用建议**

### **1. 兜底路由 vs 精确匹配**

**推荐方式：精确匹配 + 兜底路由**
```json
[
  {
    "pattern": "\\.(css|js|png|jpg)$",
    "ttl": "72h",
    "skip_conditions": null
  },
  {
    "pattern": "/wp-admin/",
    "ttl": "0s",
    "skip_conditions": {"methods": ["*"]}
  },
  {
    "pattern": "^/",
    "ttl": "2h",
    "skip_conditions": {
      "cookies": ["wordpress_logged_in"],
      "query_params": ["preview", "s"]
    }
  }
]
```

### **2. 性能优化**
- **静态资源**：长期缓存，无跳过条件
- **动态内容**：中期缓存，基于用户状态跳过
- **管理页面**：不缓存或极短缓存

### **3. 安全考虑**
- **敏感页面**：使用多重跳过条件
- **用户相关内容**：基于登录状态跳过
- **预览功能**：基于查询参数跳过

## 🚀 **部署建议**

1. **测试环境验证**：先在测试环境验证配置
2. **逐步启用**：从静态资源开始，逐步扩展到动态内容
3. **监控缓存命中率**：观察缓存效果和跳过情况
4. **调优配置**：根据实际使用情况调整跳过条件

## 📝 **注意事项**

1. **Cookie匹配**：目前是精确匹配Cookie名称
2. **正则表达式**：User-Agent和自定义规则支持正则
3. **性能影响**：跳过条件检查会有轻微性能开销
4. **配置优先级**：站点级规则优先于全局规则

现在你可以为WordPress等动态网站配置精确的缓存策略，既保证缓存效果，又避免缓存用户相关的动态内容！

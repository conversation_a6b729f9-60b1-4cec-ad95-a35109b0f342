{"rustc": 28845397767708332, "features": "[\"Wdk\", \"Wdk_Foundation\", \"Wdk_Storage\", \"Wdk_Storage_FileSystem\", \"Wdk_System\", \"Wdk_System_IO\", \"Win32\", \"Win32_Foundation\", \"Win32_Networking\", \"Win32_Networking_WinSock\", \"Win32_Security\", \"Win32_Security_Authentication\", \"Win32_Security_Authentication_Identity\", \"Win32_Security_Credentials\", \"Win32_Security_Cryptography\", \"Win32_Storage\", \"Win32_Storage_FileSystem\", \"Win32_System\", \"Win32_System_Console\", \"Win32_System_IO\", \"Win32_System_LibraryLoader\", \"Win32_System_Memory\", \"Win32_System_Pipes\", \"Win32_System_SystemInformation\", \"Win32_System_WindowsProgramming\", \"default\"]", "target": 15832417742477283613, "profile": 4329819846447381470, "path": 12030617390801831070, "deps": [[15822374386965973425, "windows_targets", false, 1416972347588289109]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\windows-sys-47588ccd7b9a5a9e\\dep-lib-windows_sys"}}], "rustflags": [], "metadata": 10891015529656612040, "config": 2202906307356721367, "compile_kind": 0}
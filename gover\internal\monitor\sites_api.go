package monitor

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"reverse-proxy/internal/config"
	"reverse-proxy/internal/logger"
)

// SiteAPIRequest 站点API请求
type SiteAPIRequest struct {
	SiteID         string                   `json:"site_id"`
	Name           string                   `json:"name"`
	Domains        []string                 `json:"domains"`
	HTTPPort       int                      `json:"http_port,omitempty"`
	HTTPSPort      int                      `json:"https_port,omitempty"`
	SSL            config.SSLConfig         `json:"ssl,omitempty"`
	Upstreams      []config.UpstreamConfig  `json:"upstreams"`
	Routes         []config.RouteConfig     `json:"routes,omitempty"`
	ACL            config.SiteACLConfig     `json:"acl,omitempty"`
	MaxConnections int                      `json:"max_connections,omitempty"`
	LogTargets     []config.LogTargetConfig `json:"log_targets,omitempty"`
}

// SiteAPIResponse 站点API响应
type SiteAPIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// SiteListResponse 站点列表响应
type SiteListResponse struct {
	Sites []SiteInfo `json:"sites"`
	Count int        `json:"count"`
}

// SiteInfo 站点信息
type SiteInfo struct {
	SiteID  string   `json:"site_id"`
	Name    string   `json:"name"`
	Domains []string `json:"domains"`
	Enabled bool     `json:"enabled"`
}

// handleSitesAPI 处理站点API请求
func (api *APIServer) handleSitesAPI(w http.ResponseWriter, r *http.Request) {
	// 添加调试输出
	logger.Debug("handleSitesAPI called: %s %s", r.Method, r.URL.Path)

	// 认证已由 configAuthMiddleware 处理，无需重复检查
	// configAuthMiddleware 已经验证了：基础认证 + API Key + ACL

	// 设置响应头
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Cache-Control", "no-cache, no-store, must-revalidate")

	// 解析路径
	path := strings.TrimPrefix(r.URL.Path, "/api/v2/sites")
	path = strings.Trim(path, "/")

	switch r.Method {
	case "GET":
		if path == "" {
			// 获取站点列表
			api.handleGetSitesList(w, r)
		} else {
			// 获取特定站点
			api.handleGetSiteByID(w, r, path)
		}
	case "POST":
		if path == "" {
			// 创建新站点
			api.handleCreateSiteByID(w, r)
		} else {
			http.Error(w, "Method not allowed for this path", http.StatusMethodNotAllowed)
		}
	case "PUT":
		if path != "" {
			// 更新站点
			api.handleUpdateSiteByID(w, r, path)
		} else {
			http.Error(w, "Site ID required for update", http.StatusBadRequest)
		}
	case "DELETE":
		if path != "" {
			// 删除站点
			api.handleDeleteSiteByID(w, r, path)
		} else {
			http.Error(w, "Site ID required for deletion", http.StatusBadRequest)
		}
	default:
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
	}
}

// handleGetSitesList 获取站点列表
func (api *APIServer) handleGetSitesList(w http.ResponseWriter, r *http.Request) {
	sites := make([]SiteInfo, 0)

	for _, siteConfig := range api.monitor.config.Sites {
		siteInfo := SiteInfo{
			SiteID:  siteConfig.SiteID,
			Name:    siteConfig.Name,
			Domains: siteConfig.Domains,
			Enabled: true, // 假设所有站点都是启用的
		}
		sites = append(sites, siteInfo)
	}

	response := SiteAPIResponse{
		Success: true,
		Message: "获取站点列表成功",
		Data: SiteListResponse{
			Sites: sites,
			Count: len(sites),
		},
	}

	json.NewEncoder(w).Encode(response)
}

// handleGetSiteByID 获取特定站点
func (api *APIServer) handleGetSiteByID(w http.ResponseWriter, r *http.Request, siteID string) {
	var foundSite *config.SiteConfig

	// 优先使用配置管理器
	if api.monitor.configManager != nil {
		if cm, ok := api.monitor.configManager.(interface {
			GetSiteConfigByID(string) *config.SiteConfig
		}); ok {
			foundSite = cm.GetSiteConfigByID(siteID)
		}
	}

	// 如果配置管理器不可用，回退到直接查找
	if foundSite == nil && api.monitor.configManager == nil {
		for i := range api.monitor.config.Sites {
			if api.monitor.config.Sites[i].SiteID == siteID {
				foundSite = &api.monitor.config.Sites[i]
				break
			}
		}
	}

	if foundSite == nil {
		response := SiteAPIResponse{
			Success: false,
			Message: fmt.Sprintf("站点不存在: %s", siteID),
		}
		w.WriteHeader(http.StatusNotFound)
		json.NewEncoder(w).Encode(response)
		return
	}

	response := SiteAPIResponse{
		Success: true,
		Message: "获取站点信息成功",
		Data:    foundSite,
	}

	json.NewEncoder(w).Encode(response)
}

// handleCreateSiteByID 创建新站点
func (api *APIServer) handleCreateSiteByID(w http.ResponseWriter, r *http.Request) {
	var req SiteAPIRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		response := SiteAPIResponse{
			Success: false,
			Message: fmt.Sprintf("请求数据解析失败: %v", err),
		}
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(response)
		return
	}

	// 验证必填字段
	if req.SiteID == "" {
		response := SiteAPIResponse{
			Success: false,
			Message: "site_id 字段是必填的",
		}
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(response)
		return
	}

	if req.Name == "" {
		response := SiteAPIResponse{
			Success: false,
			Message: "name 字段是必填的",
		}
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(response)
		return
	}

	if len(req.Domains) == 0 {
		response := SiteAPIResponse{
			Success: false,
			Message: "domains 字段是必填的",
		}
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(response)
		return
	}

	// 检查站点ID是否已存在
	for _, siteConfig := range api.monitor.config.Sites {
		if siteConfig.SiteID == req.SiteID {
			response := SiteAPIResponse{
				Success: false,
				Message: fmt.Sprintf("站点ID已存在: %s", req.SiteID),
			}
			w.WriteHeader(http.StatusConflict)
			json.NewEncoder(w).Encode(response)
			return
		}
	}

	// 创建新站点配置
	newSite := config.SiteConfig{
		SiteID:         req.SiteID,
		Name:           req.Name,
		Domains:        req.Domains,
		HTTPPort:       req.HTTPPort,
		HTTPSPort:      req.HTTPSPort,
		SSL:            req.SSL,
		Upstreams:      req.Upstreams,
		Routes:         req.Routes,
		ACL:            req.ACL,
		MaxConnections: req.MaxConnections,
		LogTargets:     req.LogTargets,
	}

	// 验证站点配置
	if err := config.ValidateSiteConfig(&newSite); err != nil {
		response := SiteAPIResponse{
			Success: false,
			Message: fmt.Sprintf("站点配置验证失败: %v", err),
		}
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(response)
		return
	}

	// 使用配置管理器创建站点
	if api.monitor.configManager != nil {
		if cm, ok := api.monitor.configManager.(interface {
			CreateSiteFromJSONAndSave([]byte) error
		}); ok {
			// 将站点配置序列化为JSON
			siteJSON, err := json.Marshal(newSite)
			if err != nil {
				response := SiteAPIResponse{
					Success: false,
					Message: fmt.Sprintf("序列化站点配置失败: %v", err),
				}
				w.WriteHeader(http.StatusInternalServerError)
				json.NewEncoder(w).Encode(response)
				return
			}

			// 使用配置管理器创建并保存
			if err := cm.CreateSiteFromJSONAndSave(siteJSON); err != nil {
				response := SiteAPIResponse{
					Success: false,
					Message: fmt.Sprintf("创建站点失败: %v", err),
				}
				w.WriteHeader(http.StatusInternalServerError)
				json.NewEncoder(w).Encode(response)
				return
			}
		} else {
			// 配置管理器不支持，使用直接方法
			api.monitor.config.Sites = append(api.monitor.config.Sites, newSite)
			if err := api.saveConfigToFileViaManager(); err != nil {
				response := SiteAPIResponse{
					Success: false,
					Message: fmt.Sprintf("保存配置文件失败: %v", err),
				}
				w.WriteHeader(http.StatusInternalServerError)
				json.NewEncoder(w).Encode(response)
				return
			}
		}
	} else {
		// 没有配置管理器，使用直接方法
		api.monitor.config.Sites = append(api.monitor.config.Sites, newSite)
		if err := api.saveConfigToFileViaManager(); err != nil {
			response := SiteAPIResponse{
				Success: false,
				Message: fmt.Sprintf("保存配置文件失败: %v", err),
			}
			w.WriteHeader(http.StatusInternalServerError)
			json.NewEncoder(w).Encode(response)
			return
		}
	}

	response := SiteAPIResponse{
		Success: true,
		Message: fmt.Sprintf("站点创建成功: %s", req.SiteID),
		Data:    newSite,
	}

	w.WriteHeader(http.StatusCreated)
	json.NewEncoder(w).Encode(response)
}

// handleUpdateSiteByID 更新站点
func (api *APIServer) handleUpdateSiteByID(w http.ResponseWriter, r *http.Request, siteID string) {
	var req SiteAPIRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		response := SiteAPIResponse{
			Success: false,
			Message: fmt.Sprintf("请求数据解析失败: %v", err),
		}
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(response)
		return
	}

	// 构建更新的站点配置
	updatedSite := config.SiteConfig{
		SiteID:         siteID, // SiteID不允许修改
		Name:           req.Name,
		Domains:        req.Domains,
		HTTPPort:       req.HTTPPort,
		HTTPSPort:      req.HTTPSPort,
		SSL:            req.SSL,
		Upstreams:      req.Upstreams,
		Routes:         req.Routes,
		ACL:            req.ACL,
		MaxConnections: req.MaxConnections,
		LogTargets:     req.LogTargets,
	}

	// 优先使用配置管理器
	if api.monitor.configManager != nil {
		if cm, ok := api.monitor.configManager.(interface{ UpdateSiteFromJSONAndSaveByID(string, []byte) error }); ok {
			// 将站点配置序列化为JSON
			siteJSON, err := json.Marshal(updatedSite)
			if err != nil {
				response := SiteAPIResponse{
					Success: false,
					Message: fmt.Sprintf("序列化站点配置失败: %v", err),
				}
				w.WriteHeader(http.StatusInternalServerError)
				json.NewEncoder(w).Encode(response)
				return
			}

			// 使用配置管理器更新站点
			if err := cm.UpdateSiteFromJSONAndSaveByID(siteID, siteJSON); err != nil {
				response := SiteAPIResponse{
					Success: false,
					Message: fmt.Sprintf("更新站点失败: %v", err),
				}
				w.WriteHeader(http.StatusBadRequest)
				json.NewEncoder(w).Encode(response)
				return
			}
		} else {
			// 配置管理器不支持新方法，使用直接方法
			api.updateSiteDirectly(w, siteID, updatedSite)
			return
		}
	} else {
		// 没有配置管理器，使用直接方法
		api.updateSiteDirectly(w, siteID, updatedSite)
		return
	}

	response := SiteAPIResponse{
		Success: true,
		Message: fmt.Sprintf("站点更新成功: %s", siteID),
		Data:    updatedSite,
	}

	json.NewEncoder(w).Encode(response)
}

// handleDeleteSiteByID 删除站点
func (api *APIServer) handleDeleteSiteByID(w http.ResponseWriter, r *http.Request, siteID string) {
	var deletedSite *config.SiteConfig

	// 优先使用配置管理器
	if api.monitor.configManager != nil {
		if cm, ok := api.monitor.configManager.(interface{ DeleteSiteAndSaveByID(string) error }); ok {
			// 先获取站点信息用于响应
			if getSite, ok := api.monitor.configManager.(interface {
				GetSiteConfigByID(string) *config.SiteConfig
			}); ok {
				deletedSite = getSite.GetSiteConfigByID(siteID)
			}

			// 使用配置管理器删除站点
			if err := cm.DeleteSiteAndSaveByID(siteID); err != nil {
				response := SiteAPIResponse{
					Success: false,
					Message: fmt.Sprintf("删除站点失败: %v", err),
				}
				w.WriteHeader(http.StatusBadRequest)
				json.NewEncoder(w).Encode(response)
				return
			}
		} else {
			// 配置管理器不支持新方法，使用直接方法
			api.deleteSiteDirectly(w, siteID)
			return
		}
	} else {
		// 没有配置管理器，使用直接方法
		api.deleteSiteDirectly(w, siteID)
		return
	}

	response := SiteAPIResponse{
		Success: true,
		Message: fmt.Sprintf("站点删除成功: %s", siteID),
		Data:    deletedSite,
	}

	json.NewEncoder(w).Encode(response)
}

// updateSiteDirectly 直接更新站点配置（不使用配置管理器）
func (api *APIServer) updateSiteDirectly(w http.ResponseWriter, siteID string, updatedSite config.SiteConfig) {
	// 查找站点
	var siteIndex = -1
	var oldSite config.SiteConfig
	for i := range api.monitor.config.Sites {
		if api.monitor.config.Sites[i].SiteID == siteID {
			siteIndex = i
			oldSite = api.monitor.config.Sites[i]
			break
		}
	}

	if siteIndex == -1 {
		response := SiteAPIResponse{
			Success: false,
			Message: fmt.Sprintf("站点不存在: %s", siteID),
		}
		w.WriteHeader(http.StatusNotFound)
		json.NewEncoder(w).Encode(response)
		return
	}

	// 如果字段为空，保持原有值
	if updatedSite.Name == "" {
		updatedSite.Name = oldSite.Name
	}
	if len(updatedSite.Domains) == 0 {
		updatedSite.Domains = oldSite.Domains
	}
	if len(updatedSite.Upstreams) == 0 {
		updatedSite.Upstreams = oldSite.Upstreams
	}

	// 验证站点配置
	if err := config.ValidateSiteConfig(&updatedSite); err != nil {
		response := SiteAPIResponse{
			Success: false,
			Message: fmt.Sprintf("站点配置验证失败: %v", err),
		}
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(response)
		return
	}

	// 更新配置
	api.monitor.config.Sites[siteIndex] = updatedSite

	// 保存配置到文件
	if err := api.saveConfigToFileViaManager(); err != nil {
		response := SiteAPIResponse{
			Success: false,
			Message: fmt.Sprintf("保存配置文件失败: %v", err),
		}
		w.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(w).Encode(response)
		return
	}

	response := SiteAPIResponse{
		Success: true,
		Message: fmt.Sprintf("站点更新成功: %s", siteID),
		Data:    updatedSite,
	}

	json.NewEncoder(w).Encode(response)
}

// deleteSiteDirectly 直接删除站点配置（不使用配置管理器）
func (api *APIServer) deleteSiteDirectly(w http.ResponseWriter, siteID string) {
	// 查找站点
	var siteIndex = -1
	var deletedSite config.SiteConfig
	for i := range api.monitor.config.Sites {
		if api.monitor.config.Sites[i].SiteID == siteID {
			siteIndex = i
			deletedSite = api.monitor.config.Sites[i]
			break
		}
	}

	if siteIndex == -1 {
		response := SiteAPIResponse{
			Success: false,
			Message: fmt.Sprintf("站点不存在: %s", siteID),
		}
		w.WriteHeader(http.StatusNotFound)
		json.NewEncoder(w).Encode(response)
		return
	}

	// 删除站点
	api.monitor.config.Sites = append(api.monitor.config.Sites[:siteIndex], api.monitor.config.Sites[siteIndex+1:]...)

	// 保存配置到文件
	if err := api.saveConfigToFileViaManager(); err != nil {
		response := SiteAPIResponse{
			Success: false,
			Message: fmt.Sprintf("保存配置文件失败: %v", err),
		}
		w.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(w).Encode(response)
		return
	}

	response := SiteAPIResponse{
		Success: true,
		Message: fmt.Sprintf("站点删除成功: %s", siteID),
		Data:    deletedSite,
	}

	json.NewEncoder(w).Encode(response)
}

// saveConfigToFileViaManager 通过配置管理器保存配置到文件
func (api *APIServer) saveConfigToFileViaManager() error {
	// 使用配置管理器保存配置
	if api.monitor.configManager != nil {
		if cm, ok := api.monitor.configManager.(interface{ SaveConfigToFile() error }); ok {
			return cm.SaveConfigToFile()
		}
	}

	// 如果没有配置管理器，使用现有的保存方法
	return api.saveConfigToFile()
}



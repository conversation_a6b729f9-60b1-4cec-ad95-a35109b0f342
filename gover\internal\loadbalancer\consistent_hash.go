package loadbalancer

import (
	"crypto/sha256"
	"fmt"
	"sort"
	"sync"

	"reverse-proxy/internal/config"
)

// UpstreamStats 上游服务器统计信息
type UpstreamStats struct {
	Name      string `json:"name"`
	Healthy   bool   `json:"healthy"`
	Requests  int64  `json:"requests"`
	Responses int64  `json:"responses"`
	Errors    int64  `json:"errors"`
}

// ConsistentHashBalancer 一致性哈希负载均衡器
type ConsistentHashBalancer struct {
	upstreams    []*config.UpstreamConfig
	ring         map[uint32]*config.UpstreamConfig // 哈希环
	sortedHashes []uint32                          // 排序的哈希值
	virtualNodes int                               // 虚拟节点数量
	mu           sync.RWMutex
	stats        map[string]*UpstreamStats
}

// NewConsistentHashBalancer 创建一致性哈希负载均衡器
func NewConsistentHashBalancer(upstreams []*config.UpstreamConfig) *ConsistentHashBalancer {
	lb := &ConsistentHashBalancer{
		upstreams:    upstreams,
		ring:         make(map[uint32]*config.UpstreamConfig),
		virtualNodes: 150, // 默认每个节点150个虚拟节点
		stats:        make(map[string]*UpstreamStats),
	}

	// 初始化统计信息
	for _, upstream := range upstreams {
		lb.stats[upstream.Name] = &UpstreamStats{
			Name:      upstream.Name,
			Healthy:   true,
			Requests:  0,
			Responses: 0,
			Errors:    0,
		}
	}

	lb.buildRing()
	return lb
}

// buildRing 构建哈希环
func (lb *ConsistentHashBalancer) buildRing() {
	lb.mu.Lock()
	defer lb.mu.Unlock()

	// 清空现有环
	lb.ring = make(map[uint32]*config.UpstreamConfig)
	lb.sortedHashes = lb.sortedHashes[:0]

	// 为每个上游服务器创建虚拟节点
	for _, upstream := range lb.upstreams {
		// 只添加健康的节点
		if stats, exists := lb.stats[upstream.Name]; exists && !stats.Healthy {
			continue
		}

		for i := 0; i < lb.virtualNodes; i++ {
			// 创建虚拟节点标识
			virtualKey := fmt.Sprintf("%s:%d:%d", upstream.Address, upstream.Port, i)
			hash := lb.hash(virtualKey)

			lb.ring[hash] = upstream
			lb.sortedHashes = append(lb.sortedHashes, hash)
		}
	}

	// 排序哈希值
	sort.Slice(lb.sortedHashes, func(i, j int) bool {
		return lb.sortedHashes[i] < lb.sortedHashes[j]
	})
}

// hash 计算哈希值
func (lb *ConsistentHashBalancer) hash(key string) uint32 {
	h := sha256.Sum256([]byte(key))
	return uint32(h[0])<<24 | uint32(h[1])<<16 | uint32(h[2])<<8 | uint32(h[3])
}

// GetUpstream 根据键获取上游服务器
func (lb *ConsistentHashBalancer) GetUpstream(key string) (*config.UpstreamConfig, error) {
	lb.mu.RLock()
	defer lb.mu.RUnlock()

	if len(lb.sortedHashes) == 0 {
		return nil, fmt.Errorf("no healthy upstreams available")
	}

	// 计算键的哈希值
	hash := lb.hash(key)

	// 在环上查找第一个大于等于该哈希值的节点
	idx := sort.Search(len(lb.sortedHashes), func(i int) bool {
		return lb.sortedHashes[i] >= hash
	})

	// 如果没找到，则使用第一个节点（环形结构）
	if idx == len(lb.sortedHashes) {
		idx = 0
	}

	upstream := lb.ring[lb.sortedHashes[idx]]

	// 更新统计信息
	if stats, exists := lb.stats[upstream.Name]; exists {
		stats.Requests++
	}

	return upstream, nil
}

// AddUpstream 添加上游服务器
func (lb *ConsistentHashBalancer) AddUpstream(upstream *config.UpstreamConfig) {
	lb.mu.Lock()
	defer lb.mu.Unlock()

	// 检查是否已存在
	for _, existing := range lb.upstreams {
		if existing.Name == upstream.Name {
			return
		}
	}

	// 添加到列表
	lb.upstreams = append(lb.upstreams, upstream)

	// 初始化统计信息
	lb.stats[upstream.Name] = &UpstreamStats{
		Name:      upstream.Name,
		Healthy:   true,
		Requests:  0,
		Responses: 0,
		Errors:    0,
	}

	// 重建哈希环
	lb.buildRingUnlocked()
}

// RemoveUpstream 移除上游服务器
func (lb *ConsistentHashBalancer) RemoveUpstream(name string) {
	lb.mu.Lock()
	defer lb.mu.Unlock()

	// 从列表中移除
	for i, upstream := range lb.upstreams {
		if upstream.Name == name {
			lb.upstreams = append(lb.upstreams[:i], lb.upstreams[i+1:]...)
			break
		}
	}

	// 删除统计信息
	delete(lb.stats, name)

	// 重建哈希环
	lb.buildRingUnlocked()
}

// buildRingUnlocked 构建哈希环（无锁版本）
func (lb *ConsistentHashBalancer) buildRingUnlocked() {
	// 清空现有环
	lb.ring = make(map[uint32]*config.UpstreamConfig)
	lb.sortedHashes = lb.sortedHashes[:0]

	// 为每个上游服务器创建虚拟节点
	for _, upstream := range lb.upstreams {
		// 只添加健康的节点
		if stats, exists := lb.stats[upstream.Name]; exists && !stats.Healthy {
			continue
		}

		for i := 0; i < lb.virtualNodes; i++ {
			// 创建虚拟节点标识
			virtualKey := fmt.Sprintf("%s:%d:%d", upstream.Address, upstream.Port, i)
			hash := lb.hash(virtualKey)

			lb.ring[hash] = upstream
			lb.sortedHashes = append(lb.sortedHashes, hash)
		}
	}

	// 排序哈希值
	sort.Slice(lb.sortedHashes, func(i, j int) bool {
		return lb.sortedHashes[i] < lb.sortedHashes[j]
	})
}

// MarkSuccess 标记成功
func (lb *ConsistentHashBalancer) MarkSuccess(upstream *config.UpstreamConfig) {
	lb.mu.Lock()
	defer lb.mu.Unlock()

	if stats, exists := lb.stats[upstream.Name]; exists {
		stats.Responses++

		// 如果之前不健康，现在标记为健康并重建环
		if !stats.Healthy {
			stats.Healthy = true
			lb.buildRingUnlocked()
		}
	}
}

// MarkFailed 标记失败
func (lb *ConsistentHashBalancer) MarkFailed(upstream *config.UpstreamConfig) {
	lb.mu.Lock()
	defer lb.mu.Unlock()

	if stats, exists := lb.stats[upstream.Name]; exists {
		stats.Errors++

		// 简单的失败检测：连续3次失败则标记为不健康
		if stats.Errors >= 3 && stats.Healthy {
			stats.Healthy = false
			lb.buildRingUnlocked()
		}
	}
}

// GetStats 获取统计信息
func (lb *ConsistentHashBalancer) GetStats() map[string]interface{} {
	lb.mu.RLock()
	defer lb.mu.RUnlock()

	result := make(map[string]interface{})
	for name, stats := range lb.stats {
		result[name] = map[string]interface{}{
			"name":      stats.Name,
			"healthy":   stats.Healthy,
			"requests":  stats.Requests,
			"responses": stats.Responses,
			"errors":    stats.Errors,
		}
	}
	return result
}

// GetRingInfo 获取哈希环信息
func (lb *ConsistentHashBalancer) GetRingInfo() map[string]interface{} {
	lb.mu.RLock()
	defer lb.mu.RUnlock()

	info := map[string]interface{}{
		"virtual_nodes":  lb.virtualNodes,
		"total_nodes":    len(lb.sortedHashes),
		"active_servers": len(lb.upstreams),
	}

	// 统计每个服务器的虚拟节点数量
	serverNodes := make(map[string]int)
	for _, upstream := range lb.ring {
		key := fmt.Sprintf("%s:%d", upstream.Address, upstream.Port)
		serverNodes[key]++
	}
	info["server_distribution"] = serverNodes

	return info
}

// SetVirtualNodes 设置虚拟节点数量
func (lb *ConsistentHashBalancer) SetVirtualNodes(count int) {
	if count <= 0 {
		count = 150
	}

	lb.mu.Lock()
	lb.virtualNodes = count
	lb.buildRingUnlocked()
	lb.mu.Unlock()
}

// GetUpstreamByRequest 根据请求获取上游服务器（实现LoadBalancer接口）
func (lb *ConsistentHashBalancer) GetUpstreamByRequest(key string) (*config.UpstreamConfig, error) {
	return lb.GetUpstream(key)
}

// GetUpstreamByIndex 根据索引获取上游服务器（实现LoadBalancer接口）
func (lb *ConsistentHashBalancer) GetUpstreamByIndex() (*config.UpstreamConfig, error) {
	// 对于一致性哈希，使用默认键
	return lb.GetUpstream("default")
}

// Next 获取下一个上游服务器（实现LoadBalancer接口）
func (lb *ConsistentHashBalancer) Next() (*config.UpstreamConfig, error) {
	// 对于一致性哈希，使用默认键
	return lb.GetUpstream("default")
}

// TestHashDistribution 测试哈希分布（用于调试）
func (lb *ConsistentHashBalancer) TestHashDistribution(keys []string) map[string]int {
	distribution := make(map[string]int)

	for _, key := range keys {
		upstream, err := lb.GetUpstream(key)
		if err == nil {
			serverKey := fmt.Sprintf("%s:%d", upstream.Address, upstream.Port)
			distribution[serverKey]++
		}
	}

	return distribution
}

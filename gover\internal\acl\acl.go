package acl

import (
	"bufio"
	"fmt"
	"net"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// ACL 访问控制列表
type ACL struct {
	globalAllow     []net.IPNet // 合并后的允许列表（配置+文件）
	globalDeny      []net.IPNet // 合并后的拒绝列表（配置+文件）
	configAllow     []string    // 原始配置中的允许IP（用于重载时合并）
	configDeny      []string    // 原始配置中的拒绝IP（用于重载时合并）
	globalAllowFile string
	globalDenyFile  string
	fileWatcher     *FileWatcher
	mu              sync.RWMutex
	logger          *logrus.Logger
}

// SiteACL 站点访问控制列表
type SiteACL struct {
	allow       []net.IPNet // 合并后的允许列表（配置+文件）
	deny        []net.IPNet // 合并后的拒绝列表（配置+文件）
	configAllow []string    // 原始配置中的允许IP（用于重载时合并）
	configDeny  []string    // 原始配置中的拒绝IP（用于重载时合并）
	allowFile   string
	denyFile    string
	fileWatcher *FileWatcher
	mu          sync.RWMutex
}

// NewACL 创建新的ACL实例
func NewACL(globalAllow, globalDeny []string, allowFile, denyFile string, reloadInterval time.Duration, logger *logrus.Logger) *ACL {
	// 创建文件监控器
	fileWatcher, err := NewFileWatcher(logger)
	if err != nil {
		logger.Errorf("创建ACL文件监控器失败: %v", err)
		fileWatcher = nil
	}

	acl := &ACL{
		globalAllowFile: allowFile,
		globalDenyFile:  denyFile,
		configAllow:     globalAllow, // 保存原始配置
		configDeny:      globalDeny,  // 保存原始配置
		fileWatcher:     fileWatcher,
		logger:          logger,
	}

	// 初始化时合并配置IP和文件IP
	acl.rebuildIPLists()

	// 从文件加载并设置监控
	// 设置文件监控
	if allowFile != "" && fileWatcher != nil {
		if err := fileWatcher.AddFile(allowFile, func(filename string) error {
			logger.Infof("检测到全局允许列表文件变化，重新加载: %s", filename)
			acl.reloadAllowFile()
			return nil
		}); err != nil {
			logger.Warnf("添加全局允许列表文件监控失败: %v", err)
		}
	}
	if denyFile != "" && fileWatcher != nil {
		if err := fileWatcher.AddFile(denyFile, func(filename string) error {
			logger.Infof("检测到全局拒绝列表文件变化，重新加载: %s", filename)
			acl.reloadDenyFile()
			return nil
		}); err != nil {
			logger.Warnf("添加全局拒绝列表文件监控失败: %v", err)
		}
	}

	// 启动文件监控
	if fileWatcher != nil {
		if err := fileWatcher.Start(); err != nil {
			logger.Errorf("启动ACL文件监控失败: %v", err)
		}
	}

	return acl
}

// rebuildIPLists 重建IP列表（合并配置IP和文件IP）
func (a *ACL) rebuildIPLists() {
	a.mu.Lock()
	defer a.mu.Unlock()

	// 清空现有列表
	a.globalAllow = []net.IPNet{}
	a.globalDeny = []net.IPNet{}

	// 1. 添加配置中的IP
	a.globalAllow = append(a.globalAllow, parseIPNetsWithLogger(a.configAllow, a.logger)...)
	a.globalDeny = append(a.globalDeny, parseIPNetsWithLogger(a.configDeny, a.logger)...)

	// 2. 添加文件中的IP
	if a.globalAllowFile != "" {
		if fileIPs, err := loadIPsFromFile(a.globalAllowFile); err == nil {
			a.globalAllow = append(a.globalAllow, parseIPNetsWithLogger(fileIPs, a.logger)...)
			a.logger.Infof("已加载全局允许列表文件: %s, 共 %d 个IP", a.globalAllowFile, len(fileIPs))
		} else {
			a.logger.Errorf("加载全局允许列表文件失败: %v", err)
		}
	}

	if a.globalDenyFile != "" {
		if fileIPs, err := loadIPsFromFile(a.globalDenyFile); err == nil {
			a.globalDeny = append(a.globalDeny, parseIPNetsWithLogger(fileIPs, a.logger)...)
			a.logger.Infof("已加载全局拒绝列表文件: %s, 共 %d 个IP", a.globalDenyFile, len(fileIPs))
		} else {
			a.logger.Errorf("加载全局拒绝列表文件失败: %v", err)
		}
	}

	a.logger.Infof("全局ACL重建完成 - 允许列表: %d 个IP, 拒绝列表: %d 个IP",
		len(a.globalAllow), len(a.globalDeny))
}

// NewSiteACL 创建新的站点ACL实例
func NewSiteACL(allow, deny []string, allowFile, denyFile string, logger *logrus.Logger) *SiteACL {
	// 创建文件监控器
	fileWatcher, err := NewFileWatcher(logger)
	if err != nil {
		logger.Errorf("创建站点ACL文件监控器失败: %v", err)
		fileWatcher = nil
	}

	siteACL := &SiteACL{
		configAllow: allow, // 保存原始配置
		configDeny:  deny,  // 保存原始配置
		allowFile:   allowFile,
		denyFile:    denyFile,
		fileWatcher: fileWatcher,
	}

	// 初始化时合并配置IP和文件IP
	siteACL.rebuildIPLists(logger)

	// 记录站点ACL配置信息
	logger.Infof("创建站点ACL - 允许列表: %d 个IP, 拒绝列表: %d 个IP, 允许文件: %s, 拒绝文件: %s",
		len(siteACL.allow), len(siteACL.deny), allowFile, denyFile)

	// 从文件加载并设置监控
	// 设置文件监控
	if allowFile != "" && fileWatcher != nil {
		fileWatcher.AddFile(allowFile, func(filename string) error {
			fmt.Printf("检测到站点允许列表文件变化，重新加载: %s\n", filename)
			siteACL.reloadAllowFile()
			return nil
		})
	}
	if denyFile != "" && fileWatcher != nil {
		fileWatcher.AddFile(denyFile, func(filename string) error {
			fmt.Printf("检测到站点拒绝列表文件变化，重新加载: %s\n", filename)
			siteACL.reloadDenyFile()
			return nil
		})
	}

	// 启动文件监控
	if fileWatcher != nil {
		if err := fileWatcher.Start(); err != nil {
			logger.Errorf("启动站点ACL文件监控失败: %v", err)
		}
	}

	return siteACL
}

// rebuildIPLists 重建站点IP列表（合并配置IP和文件IP）
func (s *SiteACL) rebuildIPLists(logger *logrus.Logger) {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 清空现有列表
	s.allow = []net.IPNet{}
	s.deny = []net.IPNet{}

	// 1. 添加配置中的IP
	s.allow = append(s.allow, parseIPNetsWithLogger(s.configAllow, logger)...)
	s.deny = append(s.deny, parseIPNetsWithLogger(s.configDeny, logger)...)

	// 2. 添加文件中的IP
	if s.allowFile != "" {
		if fileIPs, err := loadIPsFromFile(s.allowFile); err == nil {
			s.allow = append(s.allow, parseIPNetsWithLogger(fileIPs, logger)...)
			if logger != nil {
				logger.Infof("已加载站点允许列表文件: %s, 共 %d 个IP", s.allowFile, len(fileIPs))
			}
		} else {
			if logger != nil {
				logger.Errorf("加载站点允许列表文件失败: %s, 错误: %v", s.allowFile, err)
			}
		}
	}

	if s.denyFile != "" {
		if fileIPs, err := loadIPsFromFile(s.denyFile); err == nil {
			s.deny = append(s.deny, parseIPNetsWithLogger(fileIPs, logger)...)
			if logger != nil {
				logger.Infof("已加载站点拒绝列表文件: %s, 共 %d 个IP", s.denyFile, len(fileIPs))
			}
		} else {
			if logger != nil {
				logger.Errorf("加载站点拒绝列表文件失败: %s, 错误: %v", s.denyFile, err)
			}
		}
	}

	if logger != nil {
		logger.Infof("站点ACL重建完成 - 允许列表: %d 个IP, 拒绝列表: %d 个IP",
			len(s.allow), len(s.deny))
	}
}

// loadAllowFile 从文件加载全局允许列表
func (a *ACL) loadAllowFile() {
	if a.globalAllowFile == "" {
		return
	}

	ips, err := loadIPsFromFile(a.globalAllowFile)
	if err != nil {
		a.logger.Errorf("加载全局允许列表文件失败: %v", err)
		return
	}

	a.mu.Lock()
	a.globalAllow = append(a.globalAllow, parseIPNetsWithLogger(ips, a.logger)...)
	a.mu.Unlock()

	a.logger.Infof("已加载全局允许列表文件: %s, 共 %d 个IP", a.globalAllowFile, len(ips))
}

// reloadAllowFile 重新加载全局允许列表文件
func (a *ACL) reloadAllowFile() {
	a.logger.Infof("检测到全局允许列表文件变化，重新构建IP列表: %s", a.globalAllowFile)
	// 重新构建整个IP列表（配置+文件）
	a.rebuildIPLists()
}

// reloadDenyFile 重新加载全局拒绝列表文件
func (a *ACL) reloadDenyFile() {
	a.logger.Infof("检测到全局拒绝列表文件变化，重新构建IP列表: %s", a.globalDenyFile)
	// 重新构建整个IP列表（配置+文件）
	a.rebuildIPLists()
}

// reloadAllowFile 重新加载站点允许列表文件
func (s *SiteACL) reloadAllowFile() {
	fmt.Printf("检测到站点允许列表文件变化，重新构建IP列表: %s\n", s.allowFile)
	// 重新构建整个IP列表（配置+文件）
	s.rebuildIPLists(nil)
}

// reloadDenyFile 重新加载站点拒绝列表文件
func (s *SiteACL) reloadDenyFile() {
	fmt.Printf("检测到站点拒绝列表文件变化，重新构建IP列表: %s\n", s.denyFile)
	// 重新构建整个IP列表（配置+文件）
	s.rebuildIPLists(nil)
}

// Close 关闭ACL，停止文件监控
func (a *ACL) Close() error {
	if a.fileWatcher != nil {
		return a.fileWatcher.Stop()
	}
	return nil
}

// Close 关闭站点ACL，停止文件监控
func (s *SiteACL) Close() error {
	if s.fileWatcher != nil {
		return s.fileWatcher.Stop()
	}
	return nil
}

// ReloadFiles 手动重载文件
func (a *ACL) ReloadFiles() {
	a.reloadAllowFile()
	a.reloadDenyFile()
}

// ReloadFiles 手动重载站点文件
func (s *SiteACL) ReloadFiles() {
	s.reloadAllowFile()
	s.reloadDenyFile()
}

// IsAllowed 检查IP是否允许访问
func (a *ACL) IsAllowed(ipStr string) bool {
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return false
	}

	a.mu.RLock()
	defer a.mu.RUnlock()

	// 先检查全局拒绝列表
	for _, denyNet := range a.globalDeny {
		if denyNet.Contains(ip) {
			return false
		}
	}

	// 如果有全局允许列表，检查是否在允许列表中
	if len(a.globalAllow) > 0 {
		for _, allowNet := range a.globalAllow {
			if allowNet.Contains(ip) {
				return true
			}
		}
		return false // 不在允许列表中
	}

	// 没有全局允许列表，默认允许
	return true
}

// GetDebugInfo 获取ACL调试信息
func (a *ACL) GetDebugInfo() string {
	a.mu.RLock()
	defer a.mu.RUnlock()

	return fmt.Sprintf("全局ACL[配置允许:%v, 配置拒绝:%v, 文件允许:%s, 文件拒绝:%s, 实际允许:%d个, 实际拒绝:%d个]",
		a.configAllow, a.configDeny, a.globalAllowFile, a.globalDenyFile, len(a.globalAllow), len(a.globalDeny))
}

// IsAllowed 检查IP是否允许访问站点
func (s *SiteACL) IsAllowed(ipStr string) bool {
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return false
	}

	s.mu.RLock()
	defer s.mu.RUnlock()

	// 先检查站点拒绝列表
	for _, denyNet := range s.deny {
		if denyNet.Contains(ip) {
			return false
		}
	}

	// 如果有站点允许列表，检查是否在允许列表中
	if len(s.allow) > 0 {
		for _, allowNet := range s.allow {
			if allowNet.Contains(ip) {
				return true
			}
		}
		return false // 不在允许列表中
	}

	// 没有站点允许列表，默认允许
	return true
}

// GetDebugInfo 获取站点ACL调试信息
func (s *SiteACL) GetDebugInfo() string {
	s.mu.RLock()
	defer s.mu.RUnlock()

	return fmt.Sprintf("站点ACL[实例:%p, 配置允许:%v, 配置拒绝:%v, 文件允许:%s, 文件拒绝:%s, 实际允许:%d个, 实际拒绝:%d个]",
		s, s.configAllow, s.configDeny, s.allowFile, s.denyFile, len(s.allow), len(s.deny))
}

// parseIPNets 解析IP网络列表
func parseIPNets(ips []string) []net.IPNet {
	return parseIPNetsWithLogger(ips, nil)
}

// parseIPNetsWithLogger 解析IP网络列表（带日志记录）
func parseIPNetsWithLogger(ips []string, logger *logrus.Logger) []net.IPNet {
	var nets []net.IPNet
	var invalidIPs []string

	for _, ip := range ips {
		ip = strings.TrimSpace(ip)
		if ip == "" {
			continue
		}

		// 检查是否是CIDR格式
		if strings.Contains(ip, "/") {
			_, ipNet, err := net.ParseCIDR(ip)
			if err != nil {
				invalidIPs = append(invalidIPs, ip)
				continue
			}
			nets = append(nets, *ipNet)
		} else {
			// 单个IP地址
			parsedIP := net.ParseIP(ip)
			if parsedIP == nil {
				invalidIPs = append(invalidIPs, ip)
				continue
			}
			// 转换为/32或/128网络
			if parsedIP.To4() != nil {
				// IPv4
				_, ipNet, _ := net.ParseCIDR(ip + "/32")
				nets = append(nets, *ipNet)
			} else {
				// IPv6
				_, ipNet, _ := net.ParseCIDR(ip + "/128")
				nets = append(nets, *ipNet)
			}
		}
	}

	// 记录无效IP
	if logger != nil && len(invalidIPs) > 0 {
		logger.Warnf("发现 %d 个无效IP格式，已跳过: %v", len(invalidIPs), invalidIPs)
	}

	return nets
}

// loadIPsFromFile 从文件加载IP列表
func loadIPsFromFile(filename string) ([]string, error) {
	// 获取绝对路径
	absPath, err := filepath.Abs(filename)
	if err != nil {
		return nil, fmt.Errorf("获取文件绝对路径失败: %v", err)
	}

	file, err := os.Open(absPath)
	if err != nil {
		return nil, fmt.Errorf("打开文件失败: %v", err)
	}
	defer file.Close()

	var ips []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}
		ips = append(ips, line)
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("读取文件失败: %v", err)
	}

	return ips, nil
}

# rate_limit配置命名修复

## 🎯 问题描述

在配置文件中发现了两个不同功能但名称相同的`rate_limit`设置，造成了混淆：

### ❌ 修复前的混淆配置

#### 1. 全局rate_limit（请求频率限制）
```json
{
  "rate_limit": {
    "enabled": true,
    "global_rps": 10000,
    "ip_rps": 100,
    "site_rps": 1000,
    "burst": 50
  }
}
```

#### 2. 站点/路由级rate_limit（带宽限制）
```json
{
  "sites": [{
    "name": "tingtao_monitor",
    "rate_limit": "1MB",    // ❌ 命名冲突
    "routes": [{
      "pattern": "^/testfile/",
      "rate_limit": "1MB"   // ❌ 命名冲突
    }]
  }]
}
```

## ✅ 修复后的清晰配置

### 1. 全局rate_limit（请求频率限制）- 保持不变
```json
{
  "rate_limit": {
    "enabled": true,
    "global_rps": 10000,    // 全局每秒请求数限制
    "ip_rps": 100,          // 单IP每秒请求数限制
    "site_rps": 1000,       // 单站点每秒请求数限制
    "burst": 50             // 突发请求容量
  }
}
```
**功能**：控制**请求频率**（QPS），防止DDoS攻击

### 2. 站点/路由级bandwidth_limit（带宽限制）- 重命名
```json
{
  "sites": [{
    "name": "tingtao_monitor",
    "bandwidth_limit": "1MB",    // ✅ 重命名为bandwidth_limit
    "routes": [{
      "pattern": "^/testfile/",
      "bandwidth_limit": "1MB"   // ✅ 重命名为bandwidth_limit
    }]
  }]
}
```
**功能**：控制**传输速度**（带宽），限制下载/上传速度

## 📋 配置对比表

| 配置项 | 旧名称 | 新名称 | 功能 | 单位 | 作用范围 |
|--------|--------|--------|------|------|----------|
| 请求频率限制 | `rate_limit` | `rate_limit` | QPS限制 | 请求/秒 | 全局/IP/站点 |
| 带宽限制 | `rate_limit` | `bandwidth_limit` | 传输速度限制 | MB/s | 站点/路由 |

## 🔧 配置迁移指南

### 需要修改的配置文件

#### 1. 站点级配置
```json
// 修改前
{
  "sites": [{
    "name": "example_site",
    "rate_limit": "1MB"     // ❌ 旧配置
  }]
}

// 修改后
{
  "sites": [{
    "name": "example_site", 
    "bandwidth_limit": "1MB" // ✅ 新配置
  }]
}
```

#### 2. 路由级配置
```json
// 修改前
{
  "routes": [{
    "pattern": "^/download/",
    "static_dir": "/files",
    "rate_limit": "2MB"     // ❌ 旧配置
  }]
}

// 修改后
{
  "routes": [{
    "pattern": "^/download/",
    "static_dir": "/files",
    "bandwidth_limit": "2MB" // ✅ 新配置
  }]
}
```

## 📊 实际配置示例

### 完整的修复后配置
```json
{
  "rate_limit": {
    "enabled": true,
    "global_rps": 10000,
    "ip_rps": 100,
    "site_rps": 1000,
    "burst": 50
  },
  "sites": [
    {
      "name": "high_traffic_api",
      "domains": ["api.example.com"],
      "bandwidth_limit": "10MB",
      "upstreams": [...],
      "routes": [
        {
          "pattern": "^/api/",
          "upstream": "api_group",
          "cache": false
        },
        {
          "pattern": "^/download/",
          "static_dir": "/files",
          "bandwidth_limit": "5MB",
          "cache": true
        }
      ]
    },
    {
      "name": "file_server",
      "domains": ["files.example.com"],
      "bandwidth_limit": "50MB",
      "routes": [
        {
          "pattern": "^/uploads/",
          "static_dir": "/uploads",
          "bandwidth_limit": "20MB"
        },
        {
          "pattern": "^/downloads/",
          "static_dir": "/downloads", 
          "bandwidth_limit": "100MB"
        }
      ]
    }
  ]
}
```

## 🎯 功能区别详解

### 1. rate_limit（请求频率限制）

**用途**：防止DDoS攻击，控制请求频率
**工作原理**：令牌桶算法
**限制对象**：HTTP请求数量

```json
{
  "rate_limit": {
    "enabled": true,
    "global_rps": 10000,    // 整个服务器每秒最多处理10000个请求
    "ip_rps": 100,          // 单个IP每秒最多发送100个请求
    "site_rps": 1000,       // 单个站点每秒最多处理1000个请求
    "burst": 50             // 允许50个突发请求
  }
}
```

**效果**：
- 防止恶意请求洪水攻击
- 保护服务器不被过多请求压垮
- 确保服务的可用性

### 2. bandwidth_limit（带宽限制）

**用途**：控制文件传输速度，管理带宽使用
**工作原理**：流量整形
**限制对象**：数据传输速度

```json
{
  "sites": [{
    "bandwidth_limit": "10MB",    // 站点总带宽限制为10MB/s
    "routes": [{
      "pattern": "^/download/",
      "bandwidth_limit": "5MB"    // 下载路由限制为5MB/s
    }]
  }]
}
```

**效果**：
- 防止大文件下载占用过多带宽
- 确保多用户公平使用带宽
- 控制服务器出口流量

## 🚀 升级建议

### 1. 配置文件检查
检查现有配置文件中是否使用了站点级或路由级的`rate_limit`配置

### 2. 批量替换
使用文本编辑器批量替换：
- 查找：`"rate_limit": "` （在sites或routes中）
- 替换：`"bandwidth_limit": "`

### 3. 功能验证
- **请求频率限制**：使用压测工具验证QPS限制
- **带宽限制**：下载大文件验证传输速度限制

### 4. 监控调整
- 观察请求频率限制的触发情况
- 监控带宽使用情况和用户体验
- 根据实际需求调整限制值

## 📝 最佳实践

### 1. 命名规范
- **频率限制**：使用`rate_limit`、`rps`、`qps`等
- **带宽限制**：使用`bandwidth_limit`、`speed_limit`、`transfer_rate`等

### 2. 配置建议
```json
{
  "rate_limit": {
    "global_rps": 10000,     // 根据服务器性能设置
    "ip_rps": 100,           // 防止单IP攻击
    "site_rps": 1000         // 根据业务需求设置
  },
  "sites": [{
    "bandwidth_limit": "10MB", // 根据带宽容量设置
    "routes": [{
      "bandwidth_limit": "5MB" // 重要文件可设置更高限制
    }]
  }]
}
```

### 3. 监控指标
- **请求频率**：监控QPS、拒绝率、响应时间
- **带宽使用**：监控传输速度、带宽利用率、用户体验

## 🎉 总结

这次修复解决了配置命名混淆问题：

### ✅ 修复效果
- **清晰区分**：请求频率限制 vs 带宽限制
- **命名规范**：`rate_limit` vs `bandwidth_limit`
- **功能明确**：QPS控制 vs 传输速度控制
- **配置简化**：避免混淆，提高可维护性

### 🎯 使用指导
- **DDoS防护**：使用`rate_limit`配置
- **带宽管理**：使用`bandwidth_limit`配置
- **组合使用**：两者可以同时配置，互不冲突

现在配置更加清晰明确，避免了功能混淆！🚀

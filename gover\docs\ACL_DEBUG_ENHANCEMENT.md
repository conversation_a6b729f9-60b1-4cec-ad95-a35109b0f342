# 🔧 ACL调试功能增强

## 📋 问题背景

用户报告IP `************` 被站点ACL拒绝，但根据配置文件，该IP应该被允许访问。为了帮助调试ACL问题，我们增强了ACL拒绝时的日志输出。

## 🎯 增强功能

### **1. 新增ACL调试信息方法**

#### **全局ACL调试信息**
```go
// GetDebugInfo 获取ACL调试信息
func (a *ACL) GetDebugInfo() string {
    a.mu.RLock()
    defer a.mu.RUnlock()
    
    return fmt.Sprintf("全局ACL[配置允许:%v, 配置拒绝:%v, 文件允许:%s, 文件拒绝:%s, 实际允许:%d个, 实际拒绝:%d个]",
        a.configAllow, a.configDeny, a.globalAllowFile, a.globalDenyFile, len(a.globalAllow), len(a.globalDeny))
}
```

#### **站点ACL调试信息**
```go
// GetDebugInfo 获取站点ACL调试信息
func (s *SiteACL) GetDebugInfo() string {
    s.mu.RLock()
    defer s.mu.RUnlock()
    
    return fmt.Sprintf("站点ACL[配置允许:%v, 配置拒绝:%v, 文件允许:%s, 文件拒绝:%s, 实际允许:%d个, 实际拒绝:%d个]",
        s.configAllow, s.configDeny, s.allowFile, s.denyFile, len(s.allow), len(s.deny))
}
```

### **2. 增强ACL拒绝日志**

#### **全局ACL拒绝日志**
```go
// 修改前
p.logger.Warnf("客户端IP被全局ACL拒绝: TCP客户端IP=%s, 日志IP=%s", tcpClientIP, clientIP)

// 修改后
p.logger.Warnf("客户端IP被全局ACL拒绝: TCP客户端IP=%s, 日志IP=%s, %s", 
    tcpClientIP, clientIP, p.acl.GetDebugInfo())
```

#### **站点ACL拒绝日志**
```go
// 修改前
p.logger.Warnf("客户端IP被站点ACL拒绝: TCP客户端IP=%s, 日志IP=%s, 站点=%s", 
    tcpClientIP, clientIP, site.config.Name)

// 修改后
globalACLInfo := "无全局ACL"
if p.acl != nil {
    globalACLInfo = p.acl.GetDebugInfo()
}
p.logger.Warnf("客户端IP被站点ACL拒绝: TCP客户端IP=%s, 日志IP=%s, 站点=%s, %s, %s", 
    tcpClientIP, clientIP, site.config.Name, globalACLInfo, site.acl.GetDebugInfo())
```

## 📊 **调试信息输出示例**

### **正常情况（配置为空）**
```
time="2025-06-30 09:35:34" level=warning msg="客户端IP被站点ACL拒绝: TCP客户端IP=************, 日志IP=************, 站点=qiank, 全局ACL[配置允许:[], 配置拒绝:[], 文件允许:, 文件拒绝:, 实际允许:0个, 实际拒绝:0个], 站点ACL[配置允许:[], 配置拒绝:[], 文件允许:, 文件拒绝:, 实际允许:0个, 实际拒绝:0个]"
```

### **异常情况（意外加载文件）**
```
time="2025-06-30 09:35:34" level=warning msg="客户端IP被站点ACL拒绝: TCP客户端IP=************, 日志IP=************, 站点=qiank, 全局ACL[配置允许:[], 配置拒绝:[], 文件允许:acl/global_allow.txt, 文件拒绝:, 实际允许:5个, 实际拒绝:0个], 站点ACL[配置允许:[], 配置拒绝:[], 文件允许:, 文件拒绝:, 实际允许:0个, 实际拒绝:0个]"
```

## 🔍 **调试信息解读**

### **字段说明**
- **配置允许/拒绝**：配置文件中直接定义的IP列表
- **文件允许/拒绝**：外部ACL文件的路径
- **实际允许/拒绝**：最终生效的IP规则数量

### **问题诊断**
1. **配置不一致**：如果文件路径不为空但配置中为空，说明有bug
2. **文件意外加载**：如果实际规则数量>0但配置为空，说明加载了不应该加载的文件
3. **规则冲突**：可以看到全局和站点级规则的具体内容

## 🚨 **可能的Bug场景**

### **场景1：硬编码文件路径**
```
全局ACL[配置允许:[], 配置拒绝:[], 文件允许:acl/global_allow.txt, 文件拒绝:, 实际允许:5个, 实际拒绝:0个]
```
**问题**：配置中文件路径为空，但实际加载了`acl/global_allow.txt`

### **场景2：配置解析错误**
```
全局ACL[配置允许:[***********/24], 配置拒绝:[], 文件允许:, 文件拒绝:, 实际允许:0个, 实际拒绝:0个]
```
**问题**：配置中有IP但实际规则为0，说明解析失败

### **场景3：文件监控异常**
```
站点ACL[配置允许:[], 配置拒绝:[], 文件允许:acl/site_allow.txt, 文件拒绝:, 实际允许:10个, 实际拒绝:0个]
```
**问题**：站点配置了文件但用户认为没有配置

## 🎯 **使用方法**

### **1. 重新编译并部署**
```bash
go build -o reverse-proxy.exe main.go
```

### **2. 重启反向代理服务**
```bash
./reverse-proxy.exe
```

### **3. 触发ACL拒绝**
访问被拒绝的IP，观察日志输出

### **4. 分析调试信息**
根据日志中的调试信息，判断：
- 配置是否正确加载
- 是否有意外的文件被加载
- ACL规则的实际生效情况

## 📋 **预期效果**

通过这个增强，当IP被ACL拒绝时，日志会显示：

1. **完整的配置信息**：能看到配置文件中的原始设置
2. **文件加载状态**：能看到哪些文件被加载了
3. **实际规则数量**：能看到最终生效的规则数量
4. **全局+站点信息**：能同时看到两个层级的ACL状态

这将大大提高ACL问题的调试效率，快速定位配置不一致或代码bug的问题。

## 🔧 **下一步**

1. **部署新版本**并观察日志输出
2. **分析调试信息**确定问题根源
3. **根据发现的问题**进行针对性修复
4. **验证修复效果**确保问题解决

现在当IP `************` 再次被拒绝时，日志会显示详细的ACL配置信息，帮助我们快速定位问题所在。

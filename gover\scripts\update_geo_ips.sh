#!/bin/bash

# 地域IP列表更新脚本
# 用于自动更新国家/地区IP段列表

set -e

# 配置
ACL_DIR="acl"
TEMP_DIR="/tmp/geo_ips"
LOG_FILE="logs/geo_update.log"

# 创建必要目录
mkdir -p "$ACL_DIR" "$TEMP_DIR" "$(dirname "$LOG_FILE")"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 下载国家IP列表
download_country_ips() {
    local country=$1
    local output_file=$2
    local url="https://www.ipdeny.com/ipblocks/data/countries/${country}.zone"
    
    log "下载 ${country} IP列表..."
    
    if curl -s -f "$url" > "$TEMP_DIR/${country}.zone"; then
        # 添加注释头
        {
            echo "# ${country^^} 国家IP段列表"
            echo "# 更新时间: $(date)"
            echo "# 数据源: ipdeny.com"
            echo ""
            cat "$TEMP_DIR/${country}.zone"
        } > "$output_file"
        
        local count=$(grep -v '^#' "$output_file" | grep -v '^$' | wc -l)
        log "成功更新 ${country} IP列表，共 $count 个网段"
    else
        log "ERROR: 下载 ${country} IP列表失败"
        return 1
    fi
}

# 更新恶意IP列表
update_malicious_ips() {
    local output_file="$ACL_DIR/malicious_ips.txt"
    
    log "更新恶意IP列表..."
    
    # 这里可以集成多个恶意IP数据源
    # 示例：从威胁情报源获取
    {
        echo "# 恶意IP列表"
        echo "# 更新时间: $(date)"
        echo "# 包含已知攻击者、僵尸网络、恶意扫描器等"
        echo ""
        
        # 示例恶意IP（实际使用时应该从真实数据源获取）
        echo "# 已知攻击IP"
        echo "*******"
        echo "*******"
        echo "**********"
        
        echo ""
        echo "# 恶意网段"
        echo "************/24"
        echo "**********/24"
        
    } > "$output_file"
    
    log "恶意IP列表更新完成"
}

# 合并多个国家IP到拒绝列表
create_blocked_countries() {
    local output_file="$ACL_DIR/blocked_countries.txt"
    
    log "创建封锁国家IP列表..."
    
    {
        echo "# 封锁国家IP列表"
        echo "# 更新时间: $(date)"
        echo "# 包含需要封锁的国家/地区IP段"
        echo ""
    } > "$output_file"
    
    # 示例：封锁特定国家（根据实际需求修改）
    local blocked_countries=("xx" "yy")  # 替换为实际国家代码
    
    for country in "${blocked_countries[@]}"; do
        if download_country_ips "$country" "$TEMP_DIR/${country}_temp.txt"; then
            echo "# ${country^^} 国家IP段" >> "$output_file"
            grep -v '^#' "$TEMP_DIR/${country}_temp.txt" | grep -v '^$' >> "$output_file"
            echo "" >> "$output_file"
        fi
    done
    
    local total_count=$(grep -v '^#' "$output_file" | grep -v '^$' | wc -l)
    log "封锁国家IP列表创建完成，共 $total_count 个网段"
}

# 创建允许国家IP列表
create_allowed_countries() {
    local output_file="$ACL_DIR/allowed_countries.txt"
    
    log "创建允许国家IP列表..."
    
    # 示例：只允许特定国家访问
    local allowed_countries=("us" "ca" "gb")  # 美国、加拿大、英国
    
    {
        echo "# 允许国家IP列表"
        echo "# 更新时间: $(date)"
        echo "# 只有这些国家/地区的IP可以访问"
        echo ""
    } > "$output_file"
    
    for country in "${allowed_countries[@]}"; do
        if download_country_ips "$country" "$TEMP_DIR/${country}_temp.txt"; then
            echo "# ${country^^} 国家IP段" >> "$output_file"
            grep -v '^#' "$TEMP_DIR/${country}_temp.txt" | grep -v '^$' >> "$output_file"
            echo "" >> "$output_file"
        fi
    done
    
    local total_count=$(grep -v '^#' "$output_file" | grep -v '^$' | wc -l)
    log "允许国家IP列表创建完成，共 $total_count 个网段"
}

# 验证IP格式
validate_ip_files() {
    log "验证IP文件格式..."
    
    local error_count=0
    
    for file in "$ACL_DIR"/*.txt; do
        if [[ -f "$file" ]]; then
            log "验证文件: $file"
            
            while IFS= read -r line; do
                # 跳过注释和空行
                [[ "$line" =~ ^[[:space:]]*# ]] && continue
                [[ "$line" =~ ^[[:space:]]*$ ]] && continue
                
                # 验证IP格式
                if ! [[ "$line" =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}(/[0-9]{1,2})?$ ]]; then
                    log "WARNING: 无效IP格式 in $file: $line"
                    ((error_count++))
                fi
            done < "$file"
        fi
    done
    
    if [[ $error_count -eq 0 ]]; then
        log "所有IP文件格式验证通过"
    else
        log "WARNING: 发现 $error_count 个格式错误"
    fi
}

# 显示统计信息
show_statistics() {
    log "=== IP列表统计 ==="
    
    for file in "$ACL_DIR"/*.txt; do
        if [[ -f "$file" ]]; then
            local count=$(grep -v '^#' "$file" | grep -v '^$' | wc -l)
            local filename=$(basename "$file")
            log "$filename: $count 个IP/网段"
        fi
    done
}

# 清理临时文件
cleanup() {
    log "清理临时文件..."
    rm -rf "$TEMP_DIR"
}

# 主函数
main() {
    log "开始更新地域IP列表..."
    
    # 创建临时目录
    mkdir -p "$TEMP_DIR"
    
    # 设置清理陷阱
    trap cleanup EXIT
    
    # 执行更新
    case "${1:-all}" in
        "malicious")
            update_malicious_ips
            ;;
        "blocked")
            create_blocked_countries
            ;;
        "allowed")
            create_allowed_countries
            ;;
        "all")
            update_malicious_ips
            create_blocked_countries
            create_allowed_countries
            ;;
        *)
            echo "用法: $0 [malicious|blocked|allowed|all]"
            exit 1
            ;;
    esac
    
    # 验证和统计
    validate_ip_files
    show_statistics
    
    log "地域IP列表更新完成"
}

# 执行主函数
main "$@"

package config

import (
	"fmt"
	"os"
	"time"

	"github.com/sirupsen/logrus"
)

// ViperIntegration Viper集成管理器
type ViperIntegration struct {
	configManager *ViperConfigManager
	hierarchy     *ConfigHierarchy
	logger        *logrus.Logger
	
	// 配置选项
	enableHierarchy bool
	enableBackup    bool
	enableValidation bool
}

// IntegrationOptions 集成选项
type IntegrationOptions struct {
	EnableHierarchy  bool
	EnableBackup     bool
	EnableValidation bool
	BackupDir        string
	MaxBackups       int
	CheckInterval    time.Duration
}

// DefaultIntegrationOptions 默认集成选项
func DefaultIntegrationOptions() IntegrationOptions {
	return IntegrationOptions{
		EnableHierarchy:  true,
		EnableBackup:     true,
		EnableValidation: true,
		BackupDir:        "",
		MaxBackups:       10,
		CheckInterval:    5 * time.Second,
	}
}

// NewViperIntegration 创建Viper集成管理器
func NewViperIntegration(configFile string, logger *logrus.Logger, options IntegrationOptions) (*ViperIntegration, error) {
	// 创建配置管理器
	configManager, err := NewViperConfigManager(configFile, logger)
	if err != nil {
		return nil, fmt.Errorf("创建配置管理器失败: %w", err)
	}

	// 配置备份选项
	if options.EnableBackup {
		configManager.backupEnabled = true
		if options.BackupDir != "" {
			configManager.backupDir = options.BackupDir
		}
		if options.MaxBackups > 0 {
			configManager.maxBackups = options.MaxBackups
		}
	}

	vi := &ViperIntegration{
		configManager:    configManager,
		logger:           logger,
		enableHierarchy:  options.EnableHierarchy,
		enableBackup:     options.EnableBackup,
		enableValidation: options.EnableValidation,
	}

	// 初始化配置层级
	if options.EnableHierarchy {
		hierarchy, err := CreateStandardHierarchy(configFile)
		if err != nil {
			logger.Warnf("创建配置层级失败，将使用单文件模式: %v", err)
		} else {
			vi.hierarchy = hierarchy
		}
	}

	// 添加默认验证器
	if options.EnableValidation {
		vi.configManager.AddValidator(&DefaultConfigValidator{})
		vi.configManager.AddValidator(&SecurityConfigValidator{})
		vi.configManager.AddValidator(&PerformanceConfigValidator{})
	}

	return vi, nil
}

// LoadConfig 加载配置
func (vi *ViperIntegration) LoadConfig() (*Config, error) {
	if vi.enableHierarchy && vi.hierarchy != nil {
		// 使用分层配置
		return vi.loadHierarchicalConfig()
	} else {
		// 使用单文件配置
		return vi.configManager.LoadConfig()
	}
}

// loadHierarchicalConfig 加载分层配置
func (vi *ViperIntegration) loadHierarchicalConfig() (*Config, error) {
	vi.logger.Info("使用分层配置模式加载配置...")

	// 验证配置层级
	if err := vi.hierarchy.ValidateHierarchy(); err != nil {
		return nil, fmt.Errorf("配置层级验证失败: %w", err)
	}

	// 获取合并后的配置
	config, err := vi.hierarchy.GetMergedConfig()
	if err != nil {
		return nil, fmt.Errorf("获取合并配置失败: %w", err)
	}

	// 验证配置
	if vi.enableValidation {
		if err := vi.configManager.validateConfig(config); err != nil {
			return nil, fmt.Errorf("配置验证失败: %w", err)
		}
	}

	// 后处理配置
	if err := vi.configManager.postProcessConfig(config); err != nil {
		return nil, fmt.Errorf("配置后处理失败: %w", err)
	}

	// 更新配置管理器的当前配置
	vi.configManager.currentConfig.Store(config)

	vi.logger.Infof("分层配置加载成功，站点数量: %d", len(config.Sites))
	return config, nil
}

// EnableHotReload 启用热重载
func (vi *ViperIntegration) EnableHotReload(callback func(*Config, *Config) error) error {
	// 包装回调函数以支持分层配置
	wrappedCallback := func(oldConfig, newConfig *Config) error {
		vi.logger.Info("开始执行配置热重载...")

		// 如果启用了分层配置，重新加载所有层
		if vi.enableHierarchy && vi.hierarchy != nil {
			if err := vi.hierarchy.ReloadAllLayers(); err != nil {
				return fmt.Errorf("重新加载配置层失败: %w", err)
			}

			// 重新获取合并后的配置
			mergedConfig, err := vi.hierarchy.GetMergedConfig()
			if err != nil {
				return fmt.Errorf("重新获取合并配置失败: %w", err)
			}

			// 验证配置
			if vi.enableValidation {
				if err := vi.configManager.validateConfig(mergedConfig); err != nil {
					return fmt.Errorf("配置验证失败: %w", err)
				}
			}

			// 后处理配置
			if err := vi.configManager.postProcessConfig(mergedConfig); err != nil {
				return fmt.Errorf("配置后处理失败: %w", err)
			}

			// 更新配置管理器的当前配置
			vi.configManager.currentConfig.Store(mergedConfig)
			newConfig = mergedConfig
		}

		// 执行原始回调
		if callback != nil {
			return callback(oldConfig, newConfig)
		}

		return nil
	}

	return vi.configManager.EnableHotReload(wrappedCallback)
}

// DisableHotReload 禁用热重载
func (vi *ViperIntegration) DisableHotReload() {
	vi.configManager.DisableHotReload()
}

// GetCurrentConfig 获取当前配置
func (vi *ViperIntegration) GetCurrentConfig() *Config {
	return vi.configManager.GetCurrentConfig()
}

// GetConfigVersion 获取配置版本
func (vi *ViperIntegration) GetConfigVersion() int64 {
	return vi.configManager.GetConfigVersion()
}

// GetStats 获取统计信息
func (vi *ViperIntegration) GetStats() ConfigStats {
	return vi.configManager.GetStats()
}

// CreateBackup 创建配置备份
func (vi *ViperIntegration) CreateBackup(comment string) error {
	if !vi.enableBackup {
		return fmt.Errorf("备份功能未启用")
	}

	// 确保备份目录存在
	if err := vi.ensureBackupDir(); err != nil {
		return fmt.Errorf("确保备份目录存在失败: %w", err)
	}

	return vi.configManager.CreateBackup(comment)
}

// RollbackToBackup 回滚到指定备份
func (vi *ViperIntegration) RollbackToBackup(version int64) error {
	if !vi.enableBackup {
		return fmt.Errorf("备份功能未启用")
	}

	// 在分层模式下，回滚后需要重新加载分层配置
	if err := vi.configManager.RollbackToBackup(version); err != nil {
		return err
	}

	// 如果启用了分层配置，重新加载分层配置
	if vi.enableHierarchy && vi.hierarchy != nil {
		_, err := vi.loadHierarchicalConfig()
		return err
	}

	return nil
}

// GetBackupHistory 获取备份历史
func (vi *ViperIntegration) GetBackupHistory() []BackupInfo {
	if !vi.enableBackup {
		return nil
	}
	return vi.configManager.GetBackupHistory()
}

// ValidateConfigFile 验证配置文件
func (vi *ViperIntegration) ValidateConfigFile(configFile string) error {
	return vi.configManager.ValidateConfigFile(configFile)
}

// ReloadFromFile 从文件重载配置
func (vi *ViperIntegration) ReloadFromFile(configFile string) error {
	return vi.configManager.ReloadFromFile(configFile)
}

// GetHierarchyInfo 获取配置层级信息
func (vi *ViperIntegration) GetHierarchyInfo() []ConfigLayer {
	if vi.hierarchy == nil {
		return nil
	}
	return vi.hierarchy.GetLayerInfo()
}

// GetEffectiveValue 获取有效配置值
func (vi *ViperIntegration) GetEffectiveValue(key string) interface{} {
	if vi.hierarchy != nil {
		return vi.hierarchy.GetEffectiveValue(key)
	}
	
	// 单文件模式下从当前配置获取
	config := vi.GetCurrentConfig()
	if config != nil {
		// 这里需要根据key路径解析配置值
		// 简化实现，实际应该支持嵌套路径
		return nil
	}
	
	return nil
}

// GetValueSource 获取配置值来源
func (vi *ViperIntegration) GetValueSource(key string) string {
	if vi.hierarchy != nil {
		return vi.hierarchy.GetValueSource(key)
	}
	return "main"
}

// AddConfigLayer 添加配置层
func (vi *ViperIntegration) AddConfigLayer(name, path, description string, priority int) error {
	if vi.hierarchy == nil {
		return fmt.Errorf("分层配置未启用")
	}
	return vi.hierarchy.AddLayer(name, path, description, priority)
}

// RemoveConfigLayer 移除配置层
func (vi *ViperIntegration) RemoveConfigLayer(name string) error {
	if vi.hierarchy == nil {
		return fmt.Errorf("分层配置未启用")
	}
	return vi.hierarchy.RemoveLayer(name)
}

// EnableConfigLayer 启用配置层
func (vi *ViperIntegration) EnableConfigLayer(name string) error {
	if vi.hierarchy == nil {
		return fmt.Errorf("分层配置未启用")
	}
	return vi.hierarchy.EnableLayer(name)
}

// DisableConfigLayer 禁用配置层
func (vi *ViperIntegration) DisableConfigLayer(name string) error {
	if vi.hierarchy == nil {
		return fmt.Errorf("分层配置未启用")
	}
	return vi.hierarchy.DisableLayer(name)
}

// IsHierarchyEnabled 检查是否启用了配置分层
func (vi *ViperIntegration) IsHierarchyEnabled() bool {
	return vi.enableHierarchy && vi.hierarchy != nil
}

// GetValue 获取配置值
func (vi *ViperIntegration) GetValue(key string) interface{} {
	if vi.hierarchy != nil {
		mergedViper := vi.hierarchy.GetMergedViper()
		if mergedViper != nil {
			return mergedViper.Get(key)
		}
	}

	// 单文件模式下从当前配置获取
	config := vi.GetCurrentConfig()
	if config != nil {
		// 这里需要根据key路径解析配置值
		// 简化实现，实际应该支持嵌套路径
		switch key {
		case "server.http_port":
			return config.Server.HTTPPort
		case "server.https_port":
			return config.Server.HTTPSPort
		case "log.level":
			return config.Log.Level
		case "cache.max_size":
			return config.Cache.MaxSize
		case "monitor.enabled":
			return config.Monitor.Enabled
		case "monitor.port":
			return config.Monitor.Port
		}
	}

	return nil
}

// RollbackToLastBackup 回滚到最后一个备份
func (vi *ViperIntegration) RollbackToLastBackup() error {
	if !vi.enableBackup {
		return fmt.Errorf("备份功能未启用")
	}

	// 在分层模式下，回滚后需要重新加载分层配置
	if err := vi.configManager.RollbackToLastBackup(); err != nil {
		return err
	}

	// 如果启用了分层配置，重新加载分层配置
	if vi.enableHierarchy && vi.hierarchy != nil {
		_, err := vi.loadHierarchicalConfig()
		return err
	}

	return nil
}

// GetConfigStats 获取配置统计信息
func (vi *ViperIntegration) GetConfigStats() ConfigStats {
	return vi.configManager.GetStats()
}

// ensureBackupDir 确保备份目录存在
func (vi *ViperIntegration) ensureBackupDir() error {
	// 获取备份目录路径
	backupDir := vi.configManager.GetBackupDir()
	if backupDir == "" {
		return fmt.Errorf("备份目录未配置")
	}

	// 创建备份目录
	if err := os.MkdirAll(backupDir, 0755); err != nil {
		return fmt.Errorf("创建备份目录失败: %w", err)
	}

	return nil
}

// Close 关闭集成管理器
func (vi *ViperIntegration) Close() error {
	return vi.configManager.Close()
}

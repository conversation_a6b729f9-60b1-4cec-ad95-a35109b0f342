package hotreload

import (
	"context"
	"fmt"
	"os"
	"sync"
	"time"

	"reverse-proxy/internal/config"
)

// ConfigWatcher 配置文件监控器
type ConfigWatcher struct {
	configFile    string
	lastMod       time.Time
	lastSize      int64
	checkInterval time.Duration
	callbacks     []ReloadCallback
	ctx           context.Context
	cancel        context.CancelFunc
	wg            sync.WaitGroup
	mu            sync.RWMutex
	stats         WatcherStats
}

// ReloadCallback 重载回调函数
type ReloadCallback func(*config.Config) error

// WatcherStats 监控统计
type WatcherStats struct {
	TotalReloads   int64     `json:"total_reloads"`
	SuccessReloads int64     `json:"success_reloads"`
	FailedReloads  int64     `json:"failed_reloads"`
	LastReloadTime time.Time `json:"last_reload_time"`
	LastError      string    `json:"last_error"`
	WatchStartTime time.Time `json:"watch_start_time"`
}

// NewConfigWatcher 创建配置监控器
func NewConfigWatcher(configFile string, checkInterval time.Duration) *ConfigWatcher {
	ctx, cancel := context.WithCancel(context.Background())

	if checkInterval <= 0 {
		checkInterval = 5 * time.Second // 默认5秒检查一次
	}

	return &ConfigWatcher{
		configFile:    configFile,
		checkInterval: checkInterval,
		ctx:           ctx,
		cancel:        cancel,
		stats: WatcherStats{
			WatchStartTime: time.Now(),
		},
	}
}

// AddCallback 添加重载回调
func (cw *ConfigWatcher) AddCallback(callback ReloadCallback) {
	cw.mu.Lock()
	defer cw.mu.Unlock()
	cw.callbacks = append(cw.callbacks, callback)
}

// Start 开始监控
func (cw *ConfigWatcher) Start() error {
	// 获取初始文件信息
	if err := cw.updateFileInfo(); err != nil {
		return fmt.Errorf("获取配置文件信息失败: %w", err)
	}

	// 启动监控协程
	cw.wg.Add(1)
	go cw.watchLoop()

	return nil
}

// Stop 停止监控
func (cw *ConfigWatcher) Stop() {
	cw.cancel()
	cw.wg.Wait()
}

// watchLoop 监控循环
func (cw *ConfigWatcher) watchLoop() {
	defer cw.wg.Done()

	ticker := time.NewTicker(cw.checkInterval)
	defer ticker.Stop()

	for {
		select {
		case <-cw.ctx.Done():
			return
		case <-ticker.C:
			if err := cw.checkAndReload(); err != nil {
				cw.updateStats(false, err.Error())
				fmt.Printf("[配置热重载] 检查配置文件失败: %v\n", err)
			}
		}
	}
}

// checkAndReload 检查并重载配置
func (cw *ConfigWatcher) checkAndReload() error {
	stat, err := os.Stat(cw.configFile)
	if err != nil {
		return fmt.Errorf("获取文件状态失败: %w", err)
	}

	// 更强的文件变化检测：使用修改时间和大小，并增加内容哈希验证
	modTimeChanged := !stat.ModTime().Equal(cw.lastMod)
	sizeChanged := stat.Size() != cw.lastSize

	if !modTimeChanged && !sizeChanged {
		return nil // 文件未变化
	}

	fmt.Printf("[配置热重载] 检测到配置文件变化 (时间变化:%v, 大小变化:%v)，开始重载...\n", modTimeChanged, sizeChanged)

	// 等待文件写入完成（避免读取到不完整的文件）
	time.Sleep(100 * time.Millisecond)

	// 再次检查文件状态，确保文件写入完成
	stat2, err := os.Stat(cw.configFile)
	if err != nil {
		return fmt.Errorf("二次获取文件状态失败: %w", err)
	}

	// 如果文件还在变化，等待更长时间
	if !stat2.ModTime().Equal(stat.ModTime()) || stat2.Size() != stat.Size() {
		fmt.Printf("[配置热重载] 文件仍在变化，等待写入完成...\n")
		time.Sleep(500 * time.Millisecond)
	}

	// 加载新配置
	newConfig, err := config.Load(cw.configFile)
	if err != nil {
		return fmt.Errorf("加载新配置失败: %w", err)
	}

	// 验证配置
	if err := cw.validateConfig(newConfig); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}

	// 执行回调
	cw.mu.RLock()
	callbacks := make([]ReloadCallback, len(cw.callbacks))
	copy(callbacks, cw.callbacks)
	cw.mu.RUnlock()

	fmt.Printf("[配置热重载] 开始执行 %d 个重载回调...\n", len(callbacks))

	for i, callback := range callbacks {
		fmt.Printf("[配置热重载] 执行第 %d 个回调...\n", i+1)
		if err := callback(newConfig); err != nil {
			return fmt.Errorf("执行第%d个重载回调失败: %w", i+1, err)
		}
		fmt.Printf("[配置热重载] 第 %d 个回调执行成功\n", i+1)
	}

	// 更新文件信息
	cw.lastMod = stat2.ModTime()
	cw.lastSize = stat2.Size()

	cw.updateStats(true, "")
	fmt.Printf("[配置热重载] 配置重载成功完成\n")

	return nil
}

// updateFileInfo 更新文件信息
func (cw *ConfigWatcher) updateFileInfo() error {
	stat, err := os.Stat(cw.configFile)
	if err != nil {
		return err
	}

	cw.lastMod = stat.ModTime()
	cw.lastSize = stat.Size()
	return nil
}

// validateConfig 验证配置
func (cw *ConfigWatcher) validateConfig(cfg *config.Config) error {
	// 基本验证
	if len(cfg.Sites) == 0 {
		return fmt.Errorf("至少需要配置一个站点")
	}

	// 验证端口
	if cfg.Server.HTTPPort <= 0 && cfg.Server.HTTPSPort <= 0 {
		return fmt.Errorf("至少需要配置HTTP或HTTPS端口")
	}

	// 验证站点配置
	for _, site := range cfg.Sites {
		if site.Name == "" {
			return fmt.Errorf("站点名称不能为空")
		}

		if len(site.Domains) == 0 {
			return fmt.Errorf("站点 %s (ID: %s) 至少需要配置一个域名", site.Name, site.SiteID)
		}

		// 检查是否为纯静态文件站点
		hasStaticRoutes := false
		hasProxyRoutes := false
		for _, route := range site.Routes {
			if route.StaticDir != "" {
				hasStaticRoutes = true
			}
			if route.Upstream != "" {
				hasProxyRoutes = true
			}
		}

		// 如果有代理路由但没有上游服务器配置，则报错
		if hasProxyRoutes && len(site.Upstreams) == 0 {
			return fmt.Errorf("站点 %s (ID: %s) 有代理路由但缺少上游服务器配置", site.Name, site.SiteID)
		}

		// 如果既没有静态路由也没有代理路由，则报错
		if !hasStaticRoutes && !hasProxyRoutes {
			return fmt.Errorf("站点 %s (ID: %s) 必须配置静态文件路由或代理路由", site.Name, site.SiteID)
		}

		// 验证SSL配置
		if site.SSL.Enabled {
			if site.SSL.CertFile == "" || site.SSL.KeyFile == "" {
				return fmt.Errorf("站点 %s (ID: %s) 启用SSL但未配置证书文件", site.Name, site.SiteID)
			}

			// 检查证书文件是否存在
			if _, err := os.Stat(site.SSL.CertFile); err != nil {
				return fmt.Errorf("站点 %s (ID: %s) 证书文件不存在: %s", site.Name, site.SiteID, site.SSL.CertFile)
			}

			if _, err := os.Stat(site.SSL.KeyFile); err != nil {
				return fmt.Errorf("站点 %s (ID: %s) 私钥文件不存在: %s", site.Name, site.SiteID, site.SSL.KeyFile)
			}
		}
	}

	return nil
}

// updateStats 更新统计信息
func (cw *ConfigWatcher) updateStats(success bool, errorMsg string) {
	cw.mu.Lock()
	defer cw.mu.Unlock()

	cw.stats.TotalReloads++
	cw.stats.LastReloadTime = time.Now()

	if success {
		cw.stats.SuccessReloads++
		cw.stats.LastError = ""
	} else {
		cw.stats.FailedReloads++
		cw.stats.LastError = errorMsg
	}
}

// GetStats 获取统计信息
func (cw *ConfigWatcher) GetStats() WatcherStats {
	cw.mu.RLock()
	defer cw.mu.RUnlock()
	return cw.stats
}

// ForceReload 强制重载配置
func (cw *ConfigWatcher) ForceReload() error {
	fmt.Printf("[配置热重载] 强制重载配置...\n")
	return cw.checkAndReload()
}

// HotReloadConfig 热重载配置
type HotReloadConfig struct {
	Enabled       bool          `mapstructure:"enabled" json:"enabled"`
	CheckInterval time.Duration `mapstructure:"check_interval" json:"check_interval"`
	WatchFiles    []string      `mapstructure:"watch_files" json:"watch_files"` // 额外监控的文件
}

// MultiFileWatcher 多文件监控器
type MultiFileWatcher struct {
	watchers map[string]*ConfigWatcher
	mu       sync.RWMutex
}

// NewMultiFileWatcher 创建多文件监控器
func NewMultiFileWatcher() *MultiFileWatcher {
	return &MultiFileWatcher{
		watchers: make(map[string]*ConfigWatcher),
	}
}

// AddFile 添加文件监控
func (mfw *MultiFileWatcher) AddFile(file string, checkInterval time.Duration, callback ReloadCallback) error {
	mfw.mu.Lock()
	defer mfw.mu.Unlock()

	if _, exists := mfw.watchers[file]; exists {
		return fmt.Errorf("文件 %s 已在监控中", file)
	}

	watcher := NewConfigWatcher(file, checkInterval)
	watcher.AddCallback(callback)

	if err := watcher.Start(); err != nil {
		return err
	}

	mfw.watchers[file] = watcher
	return nil
}

// RemoveFile 移除文件监控
func (mfw *MultiFileWatcher) RemoveFile(file string) {
	mfw.mu.Lock()
	defer mfw.mu.Unlock()

	if watcher, exists := mfw.watchers[file]; exists {
		watcher.Stop()
		delete(mfw.watchers, file)
	}
}

// StopAll 停止所有监控
func (mfw *MultiFileWatcher) StopAll() {
	mfw.mu.Lock()
	defer mfw.mu.Unlock()

	for _, watcher := range mfw.watchers {
		watcher.Stop()
	}
	mfw.watchers = make(map[string]*ConfigWatcher)
}

// GetAllStats 获取所有监控器统计
func (mfw *MultiFileWatcher) GetAllStats() map[string]WatcherStats {
	mfw.mu.RLock()
	defer mfw.mu.RUnlock()

	stats := make(map[string]WatcherStats)
	for file, watcher := range mfw.watchers {
		stats[file] = watcher.GetStats()
	}
	return stats
}

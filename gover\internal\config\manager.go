package config

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"sync"
	"sync/atomic"
	"time"

	"github.com/sirupsen/logrus"
)

// ConfigManager 配置管理器 - 支持分级配置和平滑热重载
type ConfigManager struct {
	// 当前配置（原子操作）
	currentConfig  atomic.Value // *Config
	globalSettings atomic.Value // *GlobalSettings

	// 配置版本管理
	configVersion int64
	globalVersion int64
	siteVersions  sync.Map // map[string]int64

	// 重载回调
	globalCallbacks    []GlobalConfigCallback
	siteCallbacks      []SiteConfigCallback
	batchSiteCallbacks []BatchSiteConfigCallback
	mu                 sync.RWMutex

	// 平滑重载控制
	reloadInProgress int32
	reloadQueue      chan *ReloadRequest
	ctx              context.Context
	cancel           context.CancelFunc
	wg               sync.WaitGroup

	// 配置文件路径
	configFilePath string

	logger *logrus.Logger
}

// GlobalSettings 全局设置（系统级配置，不包含站点）
type GlobalSettings struct {
	Version   int64     `json:"version"`
	UpdatedAt time.Time `json:"updated_at"`

	// 服务器配置
	Server ServerConfig `json:"server"`

	// 全局ACL
	ACL ACLConfig `json:"acl"`

	// 全局限流
	RateLimit RateLimitConfig `json:"rate_limit"`

	// 监控配置
	Monitor MonitorConfig `json:"monitor"`

	// 缓存配置
	Cache CacheConfig `json:"cache"`

	// 压缩配置
	Compression CompressionConfig `json:"compression"`

	// 热重载配置
	HotReload HotReloadConfig `json:"hot_reload"`
}

// ReloadRequest 重载请求
type ReloadRequest struct {
	Type       ReloadType  `json:"type"`
	SiteName   string      `json:"site_name,omitempty"`
	Config     interface{} `json:"config"`
	Version    int64       `json:"version"`
	Force      bool        `json:"force"`
	ResponseCh chan error  `json:"-"`
}

// ReloadType 重载类型
type ReloadType int

const (
	ReloadTypeGlobal ReloadType = iota
	ReloadTypeSite
	ReloadTypeComplete
)

// 回调函数类型
type GlobalConfigCallback func(oldSettings, newSettings *GlobalSettings) error
type SiteConfigCallback func(siteName string, oldConfig, newConfig *SiteConfig) error
type BatchSiteConfigCallback func(sites []SiteConfig) error

// NewConfigManager 创建配置管理器
func NewConfigManager(configFilePath string, logger *logrus.Logger) *ConfigManager {
	ctx, cancel := context.WithCancel(context.Background())

	cm := &ConfigManager{
		reloadQueue:    make(chan *ReloadRequest, 100),
		ctx:            ctx,
		cancel:         cancel,
		configFilePath: configFilePath,
		logger:         logger,
	}

	// 启动重载处理协程
	cm.wg.Add(1)
	go cm.reloadProcessor()

	return cm
}

// LoadInitialConfig 初次加载配置
func (cm *ConfigManager) LoadInitialConfig(configFile string) error {
	cm.logger.Info("开始初次加载配置...")

	// 加载完整配置
	fullConfig, err := Load(configFile)
	if err != nil {
		return fmt.Errorf("加载配置文件失败: %w", err)
	}

	// 设置版本号
	now := time.Now().Unix()

	// 直接存储配置，不触发热重载逻辑
	cm.currentConfig.Store(fullConfig)
	atomic.StoreInt64(&cm.configVersion, now)

	// 分离并存储全局设置
	globalSettings := cm.extractGlobalSettings(fullConfig)
	globalSettings.Version = now
	globalSettings.UpdatedAt = time.Now()
	cm.globalSettings.Store(globalSettings)
	atomic.StoreInt64(&cm.globalVersion, now)

	// 更新站点版本
	for _, site := range fullConfig.Sites {
		cm.siteVersions.Store(site.Name, now)
	}

	cm.logger.Infof("初次配置加载完成，版本: %d, 站点数量: %d", now, len(fullConfig.Sites))
	return nil
}

// GetCurrentConfig 获取当前完整配置
func (cm *ConfigManager) GetCurrentConfig() *Config {
	if config := cm.currentConfig.Load(); config != nil {
		return config.(*Config)
	}
	return nil
}

// GetSiteConfig 获取站点配置（通过名称，保持向后兼容）
func (cm *ConfigManager) GetSiteConfig(siteName string) *SiteConfig {
	config := cm.GetCurrentConfig()
	if config == nil {
		return nil
	}

	for i := range config.Sites {
		if config.Sites[i].Name == siteName {
			return &config.Sites[i]
		}
	}
	return nil
}

// GetSiteConfigByID 获取站点配置（通过站点ID）
func (cm *ConfigManager) GetSiteConfigByID(siteID string) *SiteConfig {
	config := cm.GetCurrentConfig()
	if config == nil {
		return nil
	}

	for i := range config.Sites {
		if config.Sites[i].SiteID == siteID {
			return &config.Sites[i]
		}
	}
	return nil
}

// GetAllSiteConfigs 获取所有站点配置（以名称为key，保持向后兼容）
func (cm *ConfigManager) GetAllSiteConfigs() map[string]*SiteConfig {
	result := make(map[string]*SiteConfig)
	config := cm.GetCurrentConfig()
	if config == nil {
		return result
	}

	for i := range config.Sites {
		result[config.Sites[i].Name] = &config.Sites[i]
	}
	return result
}

// GetAllSiteConfigsByID 获取所有站点配置（以站点ID为key）
func (cm *ConfigManager) GetAllSiteConfigsByID() map[string]*SiteConfig {
	result := make(map[string]*SiteConfig)
	config := cm.GetCurrentConfig()
	if config == nil {
		return result
	}

	for i := range config.Sites {
		result[config.Sites[i].SiteID] = &config.Sites[i]
	}
	return result
}

// GetGlobalSettings 获取全局设置
func (cm *ConfigManager) GetGlobalSettings() *GlobalSettings {
	if settings := cm.globalSettings.Load(); settings != nil {
		return settings.(*GlobalSettings)
	}
	return nil
}

// extractGlobalSettings 从完整配置中提取全局设置（排除站点）
func (cm *ConfigManager) extractGlobalSettings(fullConfig *Config) *GlobalSettings {
	return &GlobalSettings{
		Server:      fullConfig.Server,
		ACL:         fullConfig.ACL,
		RateLimit:   fullConfig.RateLimit,
		Monitor:     fullConfig.Monitor,
		Cache:       fullConfig.Cache,
		Compression: fullConfig.Compression,
		HotReload:   fullConfig.HotReload,
	}
}

// ReloadFromFile 从文件重载配置（包装函数：全局+站点）
func (cm *ConfigManager) ReloadFromFile(configFile string) error {
	cm.logger.Info("开始从文件重载配置...")

	// 加载完整配置
	fullConfig, err := Load(configFile)
	if err != nil {
		return fmt.Errorf("加载配置文件失败: %w", err)
	}

	// 设置版本号
	now := time.Now().Unix()

	// 先更新完整配置引用，确保后续操作能获取到配置
	cm.currentConfig.Store(fullConfig)
	atomic.StoreInt64(&cm.configVersion, now)

	// 分离并重载全局设置
	globalSettings := cm.extractGlobalSettings(fullConfig)
	globalSettings.Version = now
	globalSettings.UpdatedAt = time.Now()

	// 直接调用处理方法，避免队列异步问题
	if err := cm.processGlobalReload(globalSettings, now); err != nil {
		return fmt.Errorf("重载全局设置失败: %w", err)
	}

	// 执行批量站点配置回调
	cm.mu.RLock()
	batchSiteCallbacks := make([]BatchSiteConfigCallback, len(cm.batchSiteCallbacks))
	copy(batchSiteCallbacks, cm.batchSiteCallbacks)
	cm.mu.RUnlock()

	// 执行批量站点配置回调
	for i, callback := range batchSiteCallbacks {
		cm.logger.Debugf("执行批量站点配置回调 %d/%d", i+1, len(batchSiteCallbacks))
		if err := callback(fullConfig.Sites); err != nil {
			return fmt.Errorf("执行批量站点配置回调失败: %w", err)
		}
	}

	// 更新全局设置引用
	cm.globalSettings.Store(globalSettings)

	// 更新站点版本
	for _, site := range fullConfig.Sites {
		cm.siteVersions.Store(site.Name, now)
	}

	cm.logger.Infof("文件配置重载完成，版本: %d, 站点数量: %d", now, len(fullConfig.Sites))
	return nil
}

// ReloadGlobalSettings 重载全局设置（API接口用）
func (cm *ConfigManager) ReloadGlobalSettings(newSettings *GlobalSettings) error {
	req := &ReloadRequest{
		Type:       ReloadTypeGlobal,
		Config:     newSettings,
		Version:    newSettings.Version,
		Force:      false,
		ResponseCh: make(chan error, 1),
	}

	select {
	case cm.reloadQueue <- req:
		return <-req.ResponseCh
	case <-cm.ctx.Done():
		return fmt.Errorf("配置管理器已关闭")
	case <-time.After(30 * time.Second):
		return fmt.Errorf("重载请求超时")
	}
}

// AddGlobalConfigCallback 添加全局配置变更回调
func (cm *ConfigManager) AddGlobalConfigCallback(callback GlobalConfigCallback) {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	cm.globalCallbacks = append(cm.globalCallbacks, callback)
}

// AddSiteConfigCallback 添加站点配置变更回调
func (cm *ConfigManager) AddSiteConfigCallback(callback SiteConfigCallback) {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	cm.siteCallbacks = append(cm.siteCallbacks, callback)
}

// AddBatchSiteConfigCallback 添加批量站点配置变更回调
func (cm *ConfigManager) AddBatchSiteConfigCallback(callback BatchSiteConfigCallback) {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	cm.batchSiteCallbacks = append(cm.batchSiteCallbacks, callback)
}

// ReloadCompleteConfig 重载完整配置（文件热重载用）
func (cm *ConfigManager) ReloadCompleteConfig(configFile string) error {
	// 加载新的完整配置
	newFullConfig, err := Load(configFile)
	if err != nil {
		return fmt.Errorf("加载新配置失败: %w", err)
	}

	req := &ReloadRequest{
		Type:       ReloadTypeComplete,
		Config:     newFullConfig,
		Version:    time.Now().Unix(),
		Force:      false,
		ResponseCh: make(chan error, 1),
	}

	select {
	case cm.reloadQueue <- req:
		return <-req.ResponseCh
	case <-cm.ctx.Done():
		return fmt.Errorf("配置管理器已关闭")
	case <-time.After(30 * time.Second):
		return fmt.Errorf("重载请求超时")
	}
}

// ReloadSiteConfig 重载单个站点配置（API接口用）
func (cm *ConfigManager) ReloadSiteConfig(siteName string, newSiteConfig *SiteConfig) error {
	req := &ReloadRequest{
		Type:       ReloadTypeSite,
		SiteName:   siteName,
		Config:     newSiteConfig,
		Version:    time.Now().Unix(),
		Force:      false,
		ResponseCh: make(chan error, 1),
	}

	select {
	case cm.reloadQueue <- req:
		return <-req.ResponseCh
	case <-cm.ctx.Done():
		return fmt.Errorf("配置管理器已关闭")
	case <-time.After(30 * time.Second):
		return fmt.Errorf("重载请求超时")
	}
}

// reloadProcessor 重载处理协程
func (cm *ConfigManager) reloadProcessor() {
	defer cm.wg.Done()

	for {
		select {
		case <-cm.ctx.Done():
			return
		case req := <-cm.reloadQueue:
			// 设置重载进行中标志
			if !atomic.CompareAndSwapInt32(&cm.reloadInProgress, 0, 1) {
				req.ResponseCh <- fmt.Errorf("另一个重载操作正在进行中")
				continue
			}

			// 处理重载请求
			err := cm.processReloadRequest(req)
			req.ResponseCh <- err

			// 清除重载进行中标志
			atomic.StoreInt32(&cm.reloadInProgress, 0)
		}
	}
}

// processReloadRequest 处理重载请求
func (cm *ConfigManager) processReloadRequest(req *ReloadRequest) error {
	switch req.Type {
	case ReloadTypeGlobal:
		return cm.processGlobalReload(req.Config.(*GlobalSettings), req.Version)
	case ReloadTypeSite:
		return cm.processSiteReload(req.SiteName, req.Config.(*SiteConfig), req.Version)
	case ReloadTypeComplete:
		return cm.processCompleteReload(req.Config.(*Config), req.Version)
	default:
		return fmt.Errorf("未知的重载类型: %d", req.Type)
	}
}

// processGlobalReload 处理全局设置重载
func (cm *ConfigManager) processGlobalReload(newSettings *GlobalSettings, version int64) error {
	cm.logger.Info("开始处理全局设置重载...")

	// 获取当前全局设置
	currentSettings := cm.GetGlobalSettings()

	// 执行全局设置回调
	cm.mu.RLock()
	globalCallbacks := make([]GlobalConfigCallback, len(cm.globalCallbacks))
	copy(globalCallbacks, cm.globalCallbacks)
	cm.mu.RUnlock()

	for i, callback := range globalCallbacks {
		cm.logger.Debugf("执行全局设置回调 %d/%d", i+1, len(globalCallbacks))
		if err := callback(currentSettings, newSettings); err != nil {
			return fmt.Errorf("执行全局设置回调失败: %w", err)
		}
	}

	// 原子更新全局设置
	cm.globalSettings.Store(newSettings)
	atomic.StoreInt64(&cm.globalVersion, version)

	cm.logger.Infof("全局设置重载完成，版本: %d", version)
	return nil
}

// processCompleteReload 处理完整配置重载
func (cm *ConfigManager) processCompleteReload(newFullConfig *Config, version int64) error {
	cm.logger.Info("开始处理完整配置重载...")

	// 获取当前配置
	currentConfig := cm.GetCurrentConfig()
	if currentConfig == nil {
		return fmt.Errorf("当前配置为空")
	}

	// 分离全局设置并执行全局设置回调
	currentGlobalSettings := cm.GetGlobalSettings()
	newGlobalSettings := cm.extractGlobalSettings(newFullConfig)
	newGlobalSettings.Version = version
	newGlobalSettings.UpdatedAt = time.Now()

	cm.mu.RLock()
	globalCallbacks := make([]GlobalConfigCallback, len(cm.globalCallbacks))
	copy(globalCallbacks, cm.globalCallbacks)
	cm.mu.RUnlock()

	for i, callback := range globalCallbacks {
		cm.logger.Debugf("执行全局设置回调 %d/%d", i+1, len(globalCallbacks))
		if err := callback(currentGlobalSettings, newGlobalSettings); err != nil {
			return fmt.Errorf("执行全局设置回调失败: %w", err)
		}
	}

	// 执行站点配置回调
	cm.mu.RLock()
	siteCallbacks := make([]SiteConfigCallback, len(cm.siteCallbacks))
	copy(siteCallbacks, cm.siteCallbacks)
	cm.mu.RUnlock()

	// 获取当前站点配置映射
	currentSites := make(map[string]*SiteConfig)
	for i := range currentConfig.Sites {
		currentSites[currentConfig.Sites[i].Name] = &currentConfig.Sites[i]
	}

	// 处理新增和更新的站点
	for i := range newFullConfig.Sites {
		newSite := &newFullConfig.Sites[i]
		currentSite := currentSites[newSite.Name]

		for j, callback := range siteCallbacks {
			cm.logger.Debugf("执行站点配置回调 %d/%d (站点: %s)", j+1, len(siteCallbacks), newSite.Name)
			if err := callback(newSite.Name, currentSite, newSite); err != nil {
				return fmt.Errorf("执行站点配置回调失败: %w", err)
			}
		}
	}

	// 处理删除的站点
	newSites := make(map[string]bool)
	for i := range newFullConfig.Sites {
		newSites[newFullConfig.Sites[i].Name] = true
	}

	for siteName, currentSite := range currentSites {
		if !newSites[siteName] {
			// 站点被删除，执行删除回调
			for j, callback := range siteCallbacks {
				cm.logger.Debugf("执行站点删除回调 %d/%d (站点: %s)", j+1, len(siteCallbacks), siteName)
				if err := callback(siteName, currentSite, nil); err != nil {
					return fmt.Errorf("执行站点删除回调失败: %w", err)
				}
			}
		}
	}

	// 原子更新配置
	cm.currentConfig.Store(newFullConfig)
	cm.globalSettings.Store(newGlobalSettings)
	atomic.StoreInt64(&cm.configVersion, version)
	atomic.StoreInt64(&cm.globalVersion, version)

	// 更新站点版本
	cm.siteVersions.Range(func(key, value interface{}) bool {
		cm.siteVersions.Delete(key)
		return true
	})

	for i := range newFullConfig.Sites {
		cm.siteVersions.Store(newFullConfig.Sites[i].Name, version)
	}

	cm.logger.Infof("完整配置重载完成，版本: %d, 站点数量: %d", version, len(newFullConfig.Sites))
	return nil
}

// processBatchSiteReload 批量处理站点配置重载
func (cm *ConfigManager) processBatchSiteReload(sites []SiteConfig, version int64) error {
	cm.logger.Infof("开始批量处理站点配置重载，站点数量: %d", len(sites))

	// 获取当前配置
	currentConfig := cm.GetCurrentConfig()
	if currentConfig == nil {
		return fmt.Errorf("当前配置为空")
	}

	// 执行站点配置回调
	cm.mu.RLock()
	siteCallbacks := make([]SiteConfigCallback, len(cm.siteCallbacks))
	copy(siteCallbacks, cm.siteCallbacks)
	cm.mu.RUnlock()

	// 获取当前站点配置映射
	currentSites := make(map[string]*SiteConfig)
	for i := range currentConfig.Sites {
		currentSites[currentConfig.Sites[i].Name] = &currentConfig.Sites[i]
	}

	// 批量处理所有站点配置回调
	for _, site := range sites {
		currentSiteConfig := currentSites[site.Name]

		for i, callback := range siteCallbacks {
			cm.logger.Debugf("执行站点配置回调 %d/%d (站点: %s)", i+1, len(siteCallbacks), site.Name)
			if err := callback(site.Name, currentSiteConfig, &site); err != nil {
				return fmt.Errorf("执行站点配置回调失败 (站点: %s): %w", site.Name, err)
			}
		}
	}

	// 更新站点版本
	for _, site := range sites {
		cm.siteVersions.Store(site.Name, version)
	}

	cm.logger.Infof("批量站点配置重载完成，站点数量: %d", len(sites))
	return nil
}

// processSiteReload 处理单个站点配置重载
func (cm *ConfigManager) processSiteReload(siteName string, newSiteConfig *SiteConfig, version int64) error {
	cm.logger.Infof("开始处理站点配置重载: %s", siteName)

	// 获取当前配置
	currentConfig := cm.GetCurrentConfig()
	if currentConfig == nil {
		return fmt.Errorf("当前配置为空")
	}

	// 创建新的配置副本
	newFullConfig := *currentConfig
	newFullConfig.Sites = make([]SiteConfig, len(currentConfig.Sites))
	copy(newFullConfig.Sites, currentConfig.Sites)

	// 查找并更新站点配置
	var currentSiteConfig *SiteConfig
	siteIndex := -1
	for i := range newFullConfig.Sites {
		if newFullConfig.Sites[i].Name == siteName {
			currentSiteConfig = &currentConfig.Sites[i]
			siteIndex = i
			break
		}
	}

	// 执行站点配置回调
	cm.mu.RLock()
	siteCallbacks := make([]SiteConfigCallback, len(cm.siteCallbacks))
	copy(siteCallbacks, cm.siteCallbacks)
	cm.mu.RUnlock()

	for i, callback := range siteCallbacks {
		cm.logger.Debugf("执行站点配置回调 %d/%d", i+1, len(siteCallbacks))
		if err := callback(siteName, currentSiteConfig, newSiteConfig); err != nil {
			return fmt.Errorf("执行站点配置回调失败: %w", err)
		}
	}

	// 更新站点配置
	if siteIndex >= 0 {
		// 更新现有站点
		newFullConfig.Sites[siteIndex] = *newSiteConfig
	} else {
		// 添加新站点
		newFullConfig.Sites = append(newFullConfig.Sites, *newSiteConfig)
	}

	// 原子更新配置
	cm.currentConfig.Store(&newFullConfig)
	cm.siteVersions.Store(siteName, version)

	if currentSiteConfig != nil {
		cm.logger.Infof("站点配置重载完成: %s", siteName)
	} else {
		cm.logger.Infof("新站点配置加载完成: %s", siteName)
	}

	return nil
}

// UpdateSiteFromJSON 从JSON更新站点配置（便捷接口）
func (cm *ConfigManager) UpdateSiteFromJSON(siteName string, jsonData []byte) error {
	var siteConfig SiteConfig
	if err := json.Unmarshal(jsonData, &siteConfig); err != nil {
		return fmt.Errorf("解析站点配置JSON失败: %w", err)
	}

	// 确保站点名称一致
	siteConfig.Name = siteName

	// 验证站点配置
	if err := cm.validateSiteConfig(&siteConfig); err != nil {
		return fmt.Errorf("站点配置验证失败: %w", err)
	}

	return cm.ReloadSiteConfig(siteName, &siteConfig)
}

// UpdateSiteFromJSONByID 从JSON更新站点配置（通过站点ID）
func (cm *ConfigManager) UpdateSiteFromJSONByID(siteID string, jsonData []byte) error {
	var siteConfig SiteConfig
	if err := json.Unmarshal(jsonData, &siteConfig); err != nil {
		return fmt.Errorf("解析站点配置JSON失败: %w", err)
	}

	// 获取现有站点配置以保持 site_id 和 name 的一致性
	existingSite := cm.GetSiteConfigByID(siteID)
	if existingSite == nil {
		return fmt.Errorf("站点 %s 不存在", siteID)
	}

	// 确保站点ID不变，但允许更新名称
	siteConfig.SiteID = siteID
	if siteConfig.Name == "" {
		siteConfig.Name = existingSite.Name // 如果没有提供名称，保持原有名称
	}

	// 验证站点配置
	if err := cm.validateSiteConfig(&siteConfig); err != nil {
		return fmt.Errorf("站点配置验证失败: %w", err)
	}

	return cm.ReloadSiteConfig(existingSite.Name, &siteConfig)
}

// CreateSiteFromJSON 从JSON创建新站点（便捷接口）
func (cm *ConfigManager) CreateSiteFromJSON(jsonData []byte) error {
	var siteConfig SiteConfig
	if err := json.Unmarshal(jsonData, &siteConfig); err != nil {
		return fmt.Errorf("解析站点配置JSON失败: %w", err)
	}

	// 验证站点配置
	if err := cm.validateSiteConfig(&siteConfig); err != nil {
		return fmt.Errorf("站点配置验证失败: %w", err)
	}

	// 检查站点是否已存在（通过名称和ID都检查）
	if cm.GetSiteConfig(siteConfig.Name) != nil {
		return fmt.Errorf("站点名称 %s 已存在，请使用更新接口", siteConfig.Name)
	}
	if siteConfig.SiteID != "" && cm.GetSiteConfigByID(siteConfig.SiteID) != nil {
		return fmt.Errorf("站点ID %s 已存在，请使用更新接口", siteConfig.SiteID)
	}

	return cm.ReloadSiteConfig(siteConfig.Name, &siteConfig)
}

// DeleteSite 删除站点配置
func (cm *ConfigManager) DeleteSite(siteName string) error {
	// 获取当前配置
	currentConfig := cm.GetCurrentConfig()
	if currentConfig == nil {
		return fmt.Errorf("当前配置为空")
	}

	// 查找站点
	siteIndex := -1
	var currentSiteConfig *SiteConfig
	for i := range currentConfig.Sites {
		if currentConfig.Sites[i].Name == siteName {
			currentSiteConfig = &currentConfig.Sites[i]
			siteIndex = i
			break
		}
	}

	if siteIndex == -1 {
		return fmt.Errorf("站点 %s 不存在", siteName)
	}

	// 执行删除回调（传递nil表示删除）
	cm.mu.RLock()
	siteCallbacks := make([]SiteConfigCallback, len(cm.siteCallbacks))
	copy(siteCallbacks, cm.siteCallbacks)
	cm.mu.RUnlock()

	for i, callback := range siteCallbacks {
		cm.logger.Debugf("执行站点删除回调 %d/%d", i+1, len(siteCallbacks))
		if err := callback(siteName, currentSiteConfig, nil); err != nil {
			return fmt.Errorf("执行站点删除回调失败: %w", err)
		}
	}

	// 创建新配置（移除站点）
	newFullConfig := *currentConfig
	newFullConfig.Sites = make([]SiteConfig, 0, len(currentConfig.Sites)-1)
	for i, site := range currentConfig.Sites {
		if i != siteIndex {
			newFullConfig.Sites = append(newFullConfig.Sites, site)
		}
	}

	// 原子更新配置
	cm.currentConfig.Store(&newFullConfig)
	cm.siteVersions.Delete(siteName)

	cm.logger.Infof("站点删除完成: %s", siteName)
	return nil
}

// DeleteSiteByID 删除站点配置（通过站点ID）
func (cm *ConfigManager) DeleteSiteByID(siteID string) error {
	// 获取当前配置
	currentConfig := cm.GetCurrentConfig()
	if currentConfig == nil {
		return fmt.Errorf("当前配置为空")
	}

	// 查找站点
	siteIndex := -1
	var currentSiteConfig *SiteConfig
	for i := range currentConfig.Sites {
		if currentConfig.Sites[i].SiteID == siteID {
			currentSiteConfig = &currentConfig.Sites[i]
			siteIndex = i
			break
		}
	}

	if siteIndex == -1 {
		return fmt.Errorf("站点 %s 不存在", siteID)
	}

	// 执行删除回调（传递nil表示删除）
	cm.mu.RLock()
	siteCallbacks := make([]SiteConfigCallback, len(cm.siteCallbacks))
	copy(siteCallbacks, cm.siteCallbacks)
	cm.mu.RUnlock()

	for i, callback := range siteCallbacks {
		cm.logger.Debugf("执行站点删除回调 %d/%d", i+1, len(siteCallbacks))
		// 注意：回调函数的第一个参数仍然使用 siteName 以保持兼容性
		if err := callback(currentSiteConfig.Name, currentSiteConfig, nil); err != nil {
			return fmt.Errorf("执行站点删除回调失败: %w", err)
		}
	}

	// 创建新配置（移除站点）
	newFullConfig := *currentConfig
	newFullConfig.Sites = make([]SiteConfig, 0, len(currentConfig.Sites)-1)
	for i, site := range currentConfig.Sites {
		if i != siteIndex {
			newFullConfig.Sites = append(newFullConfig.Sites, site)
		}
	}

	// 原子更新配置
	cm.currentConfig.Store(&newFullConfig)
	cm.siteVersions.Delete(currentSiteConfig.Name) // 仍使用 name 作为版本key以保持兼容性

	cm.logger.Infof("站点删除完成: %s (ID: %s)", currentSiteConfig.Name, siteID)
	return nil
}

// ListSites 获取所有站点名称列表（保持向后兼容）
func (cm *ConfigManager) ListSites() []string {
	currentConfig := cm.GetCurrentConfig()
	if currentConfig == nil {
		return nil
	}

	sites := make([]string, len(currentConfig.Sites))
	for i, site := range currentConfig.Sites {
		sites[i] = site.Name
	}
	return sites
}

// ListSiteIDs 获取所有站点ID列表
func (cm *ConfigManager) ListSiteIDs() []string {
	currentConfig := cm.GetCurrentConfig()
	if currentConfig == nil {
		return nil
	}

	siteIDs := make([]string, len(currentConfig.Sites))
	for i, site := range currentConfig.Sites {
		siteIDs[i] = site.SiteID
	}
	return siteIDs
}

// ListSiteInfo 获取所有站点基本信息（ID和名称）
func (cm *ConfigManager) ListSiteInfo() []map[string]string {
	currentConfig := cm.GetCurrentConfig()
	if currentConfig == nil {
		return nil
	}

	sites := make([]map[string]string, len(currentConfig.Sites))
	for i, site := range currentConfig.Sites {
		sites[i] = map[string]string{
			"site_id": site.SiteID,
			"name":    site.Name,
		}
	}
	return sites
}

// validateSiteConfig 验证站点配置
func (cm *ConfigManager) validateSiteConfig(siteConfig *SiteConfig) error {
	if siteConfig.Name == "" {
		return fmt.Errorf("站点名称不能为空")
	}

	if len(siteConfig.Domains) == 0 {
		return fmt.Errorf("站点域名列表不能为空")
	}

	// 检查是否为纯静态文件站点
	hasStaticRoutes := false
	hasProxyRoutes := false
	for _, route := range siteConfig.Routes {
		if route.StaticDir != "" {
			hasStaticRoutes = true
		}
		if route.Upstream != "" {
			hasProxyRoutes = true
		}
	}

	// 如果有代理路由但没有上游服务器配置，则报错
	if hasProxyRoutes && len(siteConfig.Upstreams) == 0 {
		return fmt.Errorf("站点有代理路由但上游服务器列表为空")
	}

	// 如果既没有静态路由也没有代理路由，则报错
	if !hasStaticRoutes && !hasProxyRoutes {
		return fmt.Errorf("站点必须配置静态文件路由或代理路由")
	}

	// 验证域名格式
	for _, domain := range siteConfig.Domains {
		if domain == "" {
			return fmt.Errorf("域名不能为空")
		}
	}

	// 验证上游服务器格式
	for _, upstream := range siteConfig.Upstreams {
		if upstream.Address == "" {
			return fmt.Errorf("上游服务器地址不能为空")
		}
	}

	return nil
}

// SaveConfigToFile 保存当前配置到文件
func (cm *ConfigManager) SaveConfigToFile() error {
	if cm.configFilePath == "" {
		return fmt.Errorf("配置文件路径未设置")
	}

	currentConfig := cm.GetCurrentConfig()
	if currentConfig == nil {
		return fmt.Errorf("当前配置为空")
	}

	// 将配置序列化为JSON
	data, err := json.MarshalIndent(currentConfig, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化配置失败: %w", err)
	}

	// 写入临时文件
	tempFile := cm.configFilePath + ".tmp"
	if err := os.WriteFile(tempFile, data, 0644); err != nil {
		return fmt.Errorf("写入临时文件失败: %w", err)
	}

	// 原子性替换配置文件
	if err := os.Rename(tempFile, cm.configFilePath); err != nil {
		os.Remove(tempFile) // 清理临时文件
		return fmt.Errorf("替换配置文件失败: %w", err)
	}

	cm.logger.Infof("配置已保存到文件: %s", cm.configFilePath)
	return nil
}

// UpdateSiteFromJSONAndSave 从JSON更新站点配置并保存到文件
func (cm *ConfigManager) UpdateSiteFromJSONAndSave(siteName string, jsonData []byte) error {
	if err := cm.UpdateSiteFromJSON(siteName, jsonData); err != nil {
		return err
	}
	return cm.SaveConfigToFile()
}

// CreateSiteFromJSONAndSave 从JSON创建新站点并保存到文件
func (cm *ConfigManager) CreateSiteFromJSONAndSave(jsonData []byte) error {
	if err := cm.CreateSiteFromJSON(jsonData); err != nil {
		return err
	}
	return cm.SaveConfigToFile()
}

// UpdateSiteFromJSONAndSaveByID 从JSON更新站点配置并保存到文件（通过站点ID）
func (cm *ConfigManager) UpdateSiteFromJSONAndSaveByID(siteID string, jsonData []byte) error {
	if err := cm.UpdateSiteFromJSONByID(siteID, jsonData); err != nil {
		return err
	}
	return cm.SaveConfigToFile()
}

// DeleteSiteAndSave 删除站点配置并保存到文件（通过名称，保持向后兼容）
func (cm *ConfigManager) DeleteSiteAndSave(siteName string) error {
	if err := cm.DeleteSite(siteName); err != nil {
		return err
	}
	return cm.SaveConfigToFile()
}

// DeleteSiteAndSaveByID 删除站点配置并保存到文件（通过站点ID）
func (cm *ConfigManager) DeleteSiteAndSaveByID(siteID string) error {
	if err := cm.DeleteSiteByID(siteID); err != nil {
		return err
	}
	return cm.SaveConfigToFile()
}

// SetConfigFilePath 设置配置文件路径
func (cm *ConfigManager) SetConfigFilePath(configFilePath string) {
	cm.configFilePath = configFilePath
}

// IsReloadInProgress 检查是否有重载操作正在进行
func (cm *ConfigManager) IsReloadInProgress() bool {
	return atomic.LoadInt32(&cm.reloadInProgress) == 1
}

// GetConfigVersion 获取配置版本
func (cm *ConfigManager) GetConfigVersion() int64 {
	return atomic.LoadInt64(&cm.configVersion)
}

// GetGlobalVersion 获取全局配置版本
func (cm *ConfigManager) GetGlobalVersion() int64 {
	return atomic.LoadInt64(&cm.globalVersion)
}

// GetSiteVersion 获取站点配置版本
func (cm *ConfigManager) GetSiteVersion(siteName string) int64 {
	if version, ok := cm.siteVersions.Load(siteName); ok {
		return version.(int64)
	}
	return 0
}

// Close 关闭配置管理器
func (cm *ConfigManager) Close() error {
	cm.cancel()
	cm.wg.Wait()
	close(cm.reloadQueue)
	return nil
}

package cache

import (
	"crypto/md5"
	"encoding/gob"
	"encoding/hex"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"reverse-proxy/internal/config"
	"reverse-proxy/internal/logger"
)

// CachedResponse 缓存响应结构
type CachedResponse struct {
	StatusCode      int                 `json:"status_code"`
	Headers         map[string][]string `json:"headers"`
	Body            []byte              `json:"body"`
	ExpireAt        time.Time           `json:"expire_at"`
	CompressionType string              `json:"compression_type"` // 存储时使用的压缩算法: zstd, br, gzip, deflate, none
	OriginalSize    int64               `json:"original_size"`    // 原始大小（minify前）
	MinifiedSize    int64               `json:"minified_size"`    // minify后大小
	CompressedSize  int64               `json:"compressed_size"`  // 压缩后大小
	IsMinified      bool                `json:"is_minified"`      // 是否已经minify
	// 新增：后端服务器信息
	BackendIP       string              `json:"backend_ip"`       // 后端服务器IP地址
	BackendPort     int                 `json:"backend_port"`     // 后端服务器端口
	BackendProtocol string              `json:"backend_protocol"` // 后端服务器协议 (http/https)
	CachedAt        time.Time           `json:"cached_at"`        // 缓存创建时间
}

// Cache 缓存接口
type Cache interface {
	Get(key string) (*CachedResponse, bool)
	Set(key string, response *CachedResponse) error
	Delete(key string) error
	Clear() error
	Size() int64
	Close() error
}

// FileCache 文件缓存实现
type FileCache struct {
	config      config.CacheConfig
	path        string
	maxSize     int64
	currentSize int64
	mutex       sync.RWMutex
	rules       []CacheRule
	cleaner     *CacheCleaner // 异步清理器
	manager     *CacheManager // 对缓存管理器的引用，用于获取站点名称
}

// CacheRule 缓存规则
type CacheRule struct {
	Pattern  *regexp.Regexp
	FileType string
	MimeType string
	TTL      time.Duration
	Enabled  bool
}

// NewFileCache 创建文件缓存
func NewFileCache(cfg config.CacheConfig) (*FileCache, error) {
	cache := &FileCache{
		config:  cfg,
		path:    cfg.Path,
		maxSize: int64(cfg.MaxSize),
	}

	// 创建缓存目录
	if err := os.MkdirAll(cache.path, 0755); err != nil {
		return nil, fmt.Errorf("创建缓存目录失败: %w", err)
	}

	// 编译缓存规则
	if err := cache.compileRules(); err != nil {
		return nil, fmt.Errorf("编译缓存规则失败: %w", err)
	}

	// 计算当前缓存大小
	if err := cache.calculateSize(); err != nil {
		return nil, fmt.Errorf("计算缓存大小失败: %w", err)
	}

	// 初始化异步清理器
	if cfg.EnableAsyncCleanup {
		cache.cleaner = NewCacheCleaner(cfg, cache.path, &cache.currentSize)
		cache.cleaner.Start()
	}

	return cache, nil
}

// compileRules 编译缓存规则
func (c *FileCache) compileRules() error {
	c.rules = make([]CacheRule, 0, len(c.config.Rules))

	for _, rule := range c.config.Rules {
		if !rule.Enabled {
			continue
		}

		var pattern *regexp.Regexp
		if rule.Pattern != "" {
			var err error
			pattern, err = regexp.Compile(rule.Pattern)
			if err != nil {
				return fmt.Errorf("编译正则表达式失败 %s: %w", rule.Pattern, err)
			}
		}

		c.rules = append(c.rules, CacheRule{
			Pattern:  pattern,
			FileType: rule.FileType,
			MimeType: rule.MimeType,
			TTL:      rule.TTL,
			Enabled:  rule.Enabled,
		})
	}

	return nil
}

// calculateSize 计算当前缓存大小
func (c *FileCache) calculateSize() error {
	var size int64

	err := filepath.Walk(c.path, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() {
			size += info.Size()
		}
		return nil
	})

	if err != nil {
		return err
	}

	c.currentSize = size
	return nil
}

// Get 获取缓存响应
func (c *FileCache) Get(key string) (*CachedResponse, bool) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	filename := c.getFilename(key)

	file, err := os.Open(filename)
	if err != nil {
		return nil, false
	}
	defer file.Close()

	// 读取缓存响应
	var response CachedResponse
	decoder := gob.NewDecoder(file)
	if err := decoder.Decode(&response); err != nil {
		// 如果解码失败，删除损坏的缓存文件
		file.Close()
		os.Remove(filename)
		return nil, false
	}

	// 检查是否过期
	now := time.Now()
	if now.After(response.ExpireAt) {
		// 缓存已过期，删除文件
		logger.Debugf("缓存已过期，key=%s，过期时间=%v，当前时间=%v", key, response.ExpireAt, now)
		file.Close()
		os.Remove(filename)

		// 通知清理器移除文件
		if c.cleaner != nil {
			c.cleaner.RemoveFile(filename)
		}

		return nil, false
	}

	logger.Debugf("缓存命中，key=%s，状态码=%d，过期时间=%v，后端服务器=%s:%d (%s)",
		key, response.StatusCode, response.ExpireAt, response.BackendIP, response.BackendPort, response.BackendProtocol)

	// 通知清理器更新访问信息
	if c.cleaner != nil {
		c.cleaner.UpdateAccess(filename)
	}

	return &response, true
}

// Set 设置缓存响应
func (c *FileCache) Set(key string, response *CachedResponse) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	logger.Debugf("写入缓存，key=%s，状态码=%d，过期时间=%v，后端服务器=%s:%d (%s)",
		key, response.StatusCode, response.ExpireAt, response.BackendIP, response.BackendPort, response.BackendProtocol)

	// 检查缓存大小限制（估算编码后的大小）
	estimatedSize := int64(len(response.Body) + 200) // 响应体 + 头部等开销
	if c.currentSize+estimatedSize > c.maxSize {
		if c.cleaner != nil && c.config.EnableAsyncCleanup {
			// 异步清理：不阻塞写入，让后台清理器处理
			// 这里可以选择继续写入或者拒绝写入
			// 为了保证服务可用性，我们选择继续写入
		} else {
			// 同步清理（原有逻辑）
			if err := c.evictOldFiles(); err != nil {
				logger.Debugf("清理旧缓存文件失败: %v", err)
				return fmt.Errorf("清理旧缓存文件失败: %w", err)
			}
		}
	}

	filename := c.getFilename(key)
	logger.Debugf("写入文件: %s", filename)

	// 创建目录
	dir := filepath.Dir(filename)
	if err := os.MkdirAll(dir, 0755); err != nil {
		//fmt.Printf("[DEBUG][CACHE] 创建缓存目录失败: %v\n", err)
		return fmt.Errorf("创建缓存目录失败: %w", err)
	}

	// 写入文件
	file, err := os.Create(filename)
	if err != nil {
		logger.Debugf("创建缓存文件失败: %v", err)
		return fmt.Errorf("创建缓存文件失败: %w", err)
	}
	defer file.Close()

	// 使用gob编码写入缓存响应
	encoder := gob.NewEncoder(file)
	if err := encoder.Encode(response); err != nil {
		logger.Debugf("编码缓存响应失败: %v", err)
		return fmt.Errorf("编码缓存响应失败: %w", err)
	}

	// 更新大小
	c.currentSize += estimatedSize
	logger.Debugf("写入成功, key=%s, status=%d, size=%d, backend=%s:%d (%s)",
		key, response.StatusCode, estimatedSize, response.BackendIP, response.BackendPort, response.BackendProtocol)

	// 通知清理器添加新文件
	if c.cleaner != nil {
		c.cleaner.AddFile(filename, estimatedSize, response.ExpireAt)
	}

	return nil
}

// Delete 删除缓存
func (c *FileCache) Delete(key string) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	filename := c.getFilename(key)

	info, err := os.Stat(filename)
	if err != nil {
		return nil // 文件不存在
	}

	if err := os.Remove(filename); err != nil {
		return fmt.Errorf("删除缓存文件失败: %w", err)
	}

	c.currentSize -= info.Size()
	return nil
}

// Clear 清空缓存
func (c *FileCache) Clear() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if err := os.RemoveAll(c.path); err != nil {
		return fmt.Errorf("清空缓存目录失败: %w", err)
	}

	if err := os.MkdirAll(c.path, 0755); err != nil {
		return fmt.Errorf("重新创建缓存目录失败: %w", err)
	}

	c.currentSize = 0
	return nil
}

// Size 获取缓存大小
func (c *FileCache) Size() int64 {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.currentSize
}

// Close 关闭缓存
func (c *FileCache) Close() error {
	if c.cleaner != nil {
		c.cleaner.Stop()
	}
	return nil
}

// shouldCache 判断是否应该缓存
func (c *FileCache) shouldCache(key string, ttl time.Duration) bool {
	// 如果没有规则，使用默认TTL
	if len(c.rules) == 0 {
		return ttl > 0
	}

	// 提取URL部分
	// 新的key格式为SITE_ID|METHOD|PATH（用竖线分隔）
	// 旧的key格式为SCHEME:METHOD:HOST:PATH（用冒号分隔）
	urlPathWithQuery := key

	// 先尝试新格式（竖线分隔）
	if strings.Contains(key, "|") {
		parts := strings.SplitN(key, "|", 4)
		if len(parts) >= 3 {
			// parts[0] = SITE_ID, parts[1] = METHOD, parts[2] = PATH
			urlPathWithQuery = parts[2]
		}
	} else {
		// 兼容旧格式（冒号分隔）
		parts := strings.SplitN(key, ":", 4)
		if len(parts) >= 4 {
			// parts[3]是URL路径
			urlPathWithQuery = parts[3]
		}
	}

	// 新增：移除URL中的查询参数，以便正则匹配
	urlPath := urlPathWithQuery
	if queryIndex := strings.Index(urlPath, "?"); queryIndex != -1 {
		urlPath = urlPath[:queryIndex]
	}

	// 检查规则匹配
	for _, rule := range c.rules {
		if rule.Pattern != nil && rule.Pattern.MatchString(urlPath) {
			return rule.TTL > 0
		}

		// 检查文件类型
		if rule.FileType != "" && strings.HasSuffix(urlPath, rule.FileType) {
			return rule.TTL > 0
		}
	}

	return false
}

// getRuleTTL 获取匹配规则的TTL
func (c *FileCache) getRuleTTL(key string) (time.Duration, bool) {
	// 如果没有规则，返回0
	if len(c.rules) == 0 {
		return 0, false
	}

	// 提取URL部分
	// 新的key格式为SITE_ID|METHOD|PATH（用竖线分隔）
	// 旧的key格式为SCHEME:METHOD:HOST:PATH（用冒号分隔）
	urlPathWithQuery := key

	// 先尝试新格式（竖线分隔）
	if strings.Contains(key, "|") {
		parts := strings.SplitN(key, "|", 4)
		if len(parts) >= 3 {
			// parts[0] = SITE_ID, parts[1] = METHOD, parts[2] = PATH
			urlPathWithQuery = parts[2]
		}
	} else {
		// 兼容旧格式（冒号分隔）
		parts := strings.SplitN(key, ":", 4)
		if len(parts) >= 4 {
			// parts[3]是URL路径
			urlPathWithQuery = parts[3]
		}
	}

	// 移除URL中的查询参数，以便正则匹配
	urlPath := urlPathWithQuery
	if queryIndex := strings.Index(urlPath, "?"); queryIndex != -1 {
		urlPath = urlPath[:queryIndex]
	}

	// 检查规则匹配
	for _, rule := range c.rules {
		if rule.Pattern != nil && rule.Pattern.MatchString(urlPath) {
			if rule.TTL > 0 {
				return rule.TTL, true
			}
		}

		// 检查文件类型
		if rule.FileType != "" && strings.HasSuffix(urlPath, rule.FileType) {
			if rule.TTL > 0 {
				return rule.TTL, true
			}
		}
	}
	return 0, false
}

// getSiteRuleTTL 获取站点级缓存规则的TTL
func (c *FileCache) getSiteRuleTTL(key string, siteRules []config.CacheRule) (time.Duration, bool) {
	// 如果没有站点规则，返回0
	if len(siteRules) == 0 {
		return 0, false
	}

	// 提取URL部分
	// 新的key格式为SITE_ID|METHOD|PATH（用竖线分隔）
	// 旧的key格式为SCHEME:METHOD:HOST:PATH（用冒号分隔）
	urlPathWithQuery := key

	// 先尝试新格式（竖线分隔）
	if strings.Contains(key, "|") {
		parts := strings.SplitN(key, "|", 4)
		if len(parts) >= 3 {
			// parts[0] = SITE_ID, parts[1] = METHOD, parts[2] = PATH
			urlPathWithQuery = parts[2]
		}
	} else {
		// 兼容旧格式（冒号分隔）
		parts := strings.SplitN(key, ":", 4)
		if len(parts) >= 4 {
			// parts[3]是URL路径
			urlPathWithQuery = parts[3]
		}
	}

	// 移除URL中的查询参数，以便正则匹配
	urlPath := urlPathWithQuery
	if queryIndex := strings.Index(urlPath, "?"); queryIndex != -1 {
		urlPath = urlPath[:queryIndex]
	}

	// 检查站点规则匹配
	logger.Debugf("[站点规则TTL] 开始匹配，key=%s，urlPath=%s，规则数量=%d", key, urlPath, len(siteRules))
	for i, rule := range siteRules {
		logger.Debugf("[站点规则TTL] 检查规则 %d: pattern=%s，enabled=%v，ttl=%v", i+1, rule.Pattern, rule.Enabled, rule.TTL)
		if !rule.Enabled {
			logger.Debugf("[站点规则TTL] 规则 %d 已禁用，跳过", i+1)
			continue
		}

		// 使用预编译的正则表达式（如果有的话）
		if rule.Pattern != "" {
			// 尝试从缓存规则中获取预编译的正则表达式
			// 这里需要修改config.CacheRule结构来包含编译后的正则表达式
			// 暂时保持原有逻辑，但添加TODO注释
			// TODO: 优化 - 使用预编译的正则表达式
			pattern, err := regexp.Compile(rule.Pattern)
			if err != nil {
				logger.Debugf("[站点规则TTL] 正则表达式编译失败：pattern=%s, error=%v", rule.Pattern, err)
				continue // 跳过无效的正则表达式
			}

			matched := pattern.MatchString(urlPath)
			logger.Debugf("[站点规则TTL] 正则匹配结果: pattern=%s, urlPath=%s, matched=%v", rule.Pattern, urlPath, matched)

			if matched && rule.TTL > 0 {
				logger.Debugf("[站点规则TTL] 站点规则匹配成功：pattern=%s，urlPath=%s，ttl=%v", rule.Pattern, urlPath, rule.TTL)
				return rule.TTL, true
			}
		}

		// 检查文件类型
		if rule.FileType != "" && strings.HasSuffix(urlPath, rule.FileType) && rule.TTL > 0 {
			return rule.TTL, true
		}
	}

	return 0, false
}

// ShouldSkipCache 检查是否应该跳过缓存
func (c *FileCache) ShouldSkipCache(r *http.Request, siteRules []config.CacheRule) bool {
	// 检查全局配置规则（从config.CacheRule转换而来）
	for _, configRule := range c.config.Rules {
		if configRule.SkipConditions != nil && c.checkSkipConditions(r, configRule.SkipConditions) {
			return true
		}
	}

	// 检查站点规则
	for _, rule := range siteRules {
		if rule.SkipConditions != nil && c.checkSkipConditions(r, rule.SkipConditions) {
			return true
		}
	}

	return false
}

// checkSkipConditions 检查跳过条件
func (c *FileCache) checkSkipConditions(r *http.Request, skipConfig *config.CacheSkipConfig) bool {
	// 检查HTTP方法
	for _, method := range skipConfig.Methods {
		if strings.EqualFold(r.Method, method) {
			return true
		}
	}

	// 检查Cookie
	for _, cookieName := range skipConfig.Cookies {
		if _, err := r.Cookie(cookieName); err == nil {
			return true
		}
	}

	// 检查Header
	for _, headerName := range skipConfig.Headers {
		if r.Header.Get(headerName) != "" {
			return true
		}
	}

	// 检查查询参数
	for _, paramName := range skipConfig.QueryParams {
		if r.URL.Query().Get(paramName) != "" {
			return true
		}
	}

	// 检查User-Agent
	userAgent := r.Header.Get("User-Agent")
	for _, pattern := range skipConfig.UserAgents {
		if matched, _ := regexp.MatchString(pattern, userAgent); matched {
			return true
		}
	}

	// 检查自定义规则
	for _, rule := range skipConfig.CustomRules {
		if c.checkCustomSkipRule(r, &rule) {
			return true
		}
	}

	return false
}

// checkCustomSkipRule 检查自定义跳过规则
func (c *FileCache) checkCustomSkipRule(r *http.Request, rule *config.CacheSkipRule) bool {
	var value string

	switch rule.Type {
	case "cookie_value":
		if cookie, err := r.Cookie(rule.Key); err == nil {
			value = cookie.Value
		}
	case "header_value":
		value = r.Header.Get(rule.Key)
	case "query_value":
		value = r.URL.Query().Get(rule.Key)
	default:
		return false
	}

	// 如果没有值，跳过检查
	if value == "" {
		return false
	}

	// 检查精确匹配
	if rule.Value != "" && value == rule.Value {
		return true
	}

	// 检查正则匹配
	if rule.Pattern != "" {
		if matched, _ := regexp.MatchString(rule.Pattern, value); matched {
			return true
		}
	}

	return false
}

// ShouldCacheWithRule 检查请求是否匹配特定缓存规则
func (c *FileCache) ShouldCacheWithRule(r *http.Request, rule config.CacheRule) bool {
	logger.Debugf("[规则匹配] 开始检查规则: pattern=%s, url=%s", rule.Pattern, r.URL.Path)

	// 首先检查URL模式匹配
	if rule.Pattern != "" {
		// 编译正则表达式
		pattern, err := regexp.Compile(rule.Pattern)
		if err != nil {
			logger.Debugf("[规则匹配] 正则表达式编译失败: pattern=%s, error=%v", rule.Pattern, err)
			return false
		}

		matched := pattern.MatchString(r.URL.Path)
		logger.Debugf("[规则匹配] 正则匹配结果: pattern=%s, path=%s, matched=%v", rule.Pattern, r.URL.Path, matched)

		if !matched {
			return false
		}
	}

	// 检查匹配条件
	if rule.MatchConditions != nil {
		result := c.checkMatchConditions(r, rule.MatchConditions)
		logger.Debugf("[规则匹配] 匹配条件检查结果: %v", result)
		return result
	}

	// 如果没有匹配条件，只要URL模式匹配就可以缓存
	logger.Debugf("[规则匹配] 无匹配条件，规则匹配成功")
	return true
}

// checkMatchConditions 检查匹配条件
func (c *FileCache) checkMatchConditions(r *http.Request, matchConfig *config.CacheMatchConfig) bool {
	// 检查HTTP方法
	if len(matchConfig.Methods) > 0 {
		methodMatched := false
		for _, method := range matchConfig.Methods {
			if strings.EqualFold(r.Method, method) {
				methodMatched = true
				break
			}
		}
		if !methodMatched {
			return false
		}
	}

	// 检查Content-Type
	if len(matchConfig.ContentType) > 0 {
		contentType := r.Header.Get("Content-Type")
		contentTypeMatched := false
		for _, ct := range matchConfig.ContentType {
			if matched, _ := regexp.MatchString(ct, contentType); matched {
				contentTypeMatched = true
				break
			}
		}
		if !contentTypeMatched {
			return false
		}
	}

	// 检查User-Agent
	if len(matchConfig.UserAgents) > 0 {
		userAgent := r.Header.Get("User-Agent")
		userAgentMatched := false
		for _, pattern := range matchConfig.UserAgents {
			if matched, _ := regexp.MatchString(pattern, userAgent); matched {
				userAgentMatched = true
				break
			}
		}
		if !userAgentMatched {
			return false
		}
	}

	// 检查HTTP头部
	for _, headerMatch := range matchConfig.Headers {
		if !c.checkHeaderMatch(r, &headerMatch) {
			return false
		}
	}

	// 检查Cookie
	for _, cookieMatch := range matchConfig.Cookies {
		if !c.checkCookieMatch(r, &cookieMatch) {
			return false
		}
	}

	// 检查查询参数
	for _, queryMatch := range matchConfig.QueryParams {
		if !c.checkQueryMatch(r, &queryMatch) {
			return false
		}
	}

	return true
}

// checkHeaderMatch 检查HTTP头部匹配
func (c *FileCache) checkHeaderMatch(r *http.Request, match *config.CacheHeaderMatch) bool {
	headerValue := r.Header.Get(match.Name)

	// 如果只检查存在性
	if match.Exists {
		return headerValue != ""
	}

	// 如果头部不存在
	if headerValue == "" {
		return false
	}

	// 精确值匹配
	if match.Value != "" {
		return headerValue == match.Value
	}

	// 正则模式匹配
	if match.Pattern != "" {
		if matched, _ := regexp.MatchString(match.Pattern, headerValue); matched {
			return true
		}
	}

	return false
}

// checkCookieMatch 检查Cookie匹配
func (c *FileCache) checkCookieMatch(r *http.Request, match *config.CacheCookieMatch) bool {
	cookie, err := r.Cookie(match.Name)

	// 如果只检查存在性
	if match.Exists {
		return err == nil
	}

	// 如果Cookie不存在
	if err != nil {
		return false
	}

	cookieValue := cookie.Value

	// 精确值匹配
	if match.Value != "" {
		return cookieValue == match.Value
	}

	// 正则模式匹配
	if match.Pattern != "" {
		if matched, _ := regexp.MatchString(match.Pattern, cookieValue); matched {
			return true
		}
	}

	return false
}

// checkQueryMatch 检查查询参数匹配
func (c *FileCache) checkQueryMatch(r *http.Request, match *config.CacheQueryMatch) bool {
	queryValue := r.URL.Query().Get(match.Name)

	// 如果只检查存在性
	if match.Exists {
		return queryValue != ""
	}

	// 如果参数不存在
	if queryValue == "" {
		return false
	}

	// 精确值匹配
	if match.Value != "" {
		return queryValue == match.Value
	}

	// 正则模式匹配
	if match.Pattern != "" {
		if matched, _ := regexp.MatchString(match.Pattern, queryValue); matched {
			return true
		}
	}

	return false
}

// getFilename 获取缓存文件名
func (c *FileCache) getFilename(key string) string {
	hash := md5.Sum([]byte(key))
	hashStr := hex.EncodeToString(hash[:])

	// 从缓存键中提取站点ID
	var siteName string
	if strings.Contains(key, "|") {
		// 新格式：SITE_ID|METHOD|PATH
		parts := strings.SplitN(key, "|", 2)
		if len(parts) >= 1 {
			siteID := parts[0]
			// 通过缓存管理器获取站点名称
			if c.manager != nil {
				siteName = c.manager.GetSiteName(siteID)
			} else {
				siteName = siteID
			}
		}
	}

	// 如果没有站点名称，使用默认路径
	if siteName == "" {
		return filepath.Join(c.path, hashStr[:2], hashStr[2:4], hashStr)
	}

	// 使用站点名称创建子目录
	return filepath.Join(c.path, siteName, hashStr[:2], hashStr[2:4], hashStr)
}

// evictOldFiles 清理旧文件
func (c *FileCache) evictOldFiles() error {
	var files []fileInfo

	err := filepath.Walk(c.path, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() {
			files = append(files, fileInfo{
				path:    path,
				size:    info.Size(),
				modTime: info.ModTime(),
			})
		}
		return nil
	})

	if err != nil {
		return err
	}

	// 按修改时间排序
	for i := 0; i < len(files)-1; i++ {
		for j := i + 1; j < len(files); j++ {
			if files[i].modTime.After(files[j].modTime) {
				files[i], files[j] = files[j], files[i]
			}
		}
	}

	// 删除最旧的文件直到有足够空间
	targetSize := c.maxSize * 8 / 10 // 保留80%空间
	for _, file := range files {
		if c.currentSize <= targetSize {
			break
		}

		if err := os.Remove(file.path); err != nil {
			continue
		}
		c.currentSize -= file.size
	}

	return nil
}

// fileInfo 文件信息
type fileInfo struct {
	path    string
	size    int64
	modTime time.Time
}

// CacheManager 缓存管理器
type CacheManager struct {
	cache           Cache
	config          config.CacheConfig
	siteNameMapping map[string]string // 站点ID到站点名称的映射
	mu              sync.RWMutex      // 保护站点名称映射的读写锁
}

// NewCacheManager 创建缓存管理器
func NewCacheManager(cfg config.CacheConfig) (*CacheManager, error) {
	var cache Cache
	var err error

	switch cfg.Type {
	case "file":
		cache, err = NewFileCache(cfg)
	default:
		return nil, fmt.Errorf("不支持的缓存类型: %s", cfg.Type)
	}

	if err != nil {
		return nil, err
	}

	manager := &CacheManager{
		cache:           cache,
		config:          cfg,
		siteNameMapping: make(map[string]string),
	}

	// 设置 FileCache 对缓存管理器的引用
	if fileCache, ok := cache.(*FileCache); ok {
		fileCache.manager = manager
	}

	return manager, nil
}

// Get 获取缓存响应
func (cm *CacheManager) Get(key string) (*CachedResponse, bool) {
	if !cm.config.Enabled {
		return nil, false
	}
	return cm.cache.Get(key)
}

// Set 设置缓存响应
func (cm *CacheManager) Set(key string, response *CachedResponse) error {
	if !cm.config.Enabled {
		return nil
	}
	return cm.cache.Set(key, response)
}

// Delete 删除缓存
func (cm *CacheManager) Delete(key string) error {
	if !cm.config.Enabled {
		return nil
	}
	return cm.cache.Delete(key)
}

// Clear 清空缓存
func (cm *CacheManager) Clear() error {
	if !cm.config.Enabled {
		return nil
	}
	return cm.cache.Clear()
}

// Size 获取缓存大小
func (cm *CacheManager) Size() int64 {
	if !cm.config.Enabled {
		return 0
	}
	return cm.cache.Size()
}

// GetRuleTTL 获取匹配规则的TTL（全局规则）
func (cm *CacheManager) GetRuleTTL(key string) (time.Duration, bool) {
	if !cm.config.Enabled {
		return 0, false
	}
	if fileCache, ok := cm.cache.(*FileCache); ok {
		return fileCache.getRuleTTL(key)
	}
	return 0, false
}

// GetSiteRuleTTL 获取站点级缓存规则的TTL
func (cm *CacheManager) GetSiteRuleTTL(key string, siteRules []config.CacheRule) (time.Duration, bool) {
	if !cm.config.Enabled {
		return 0, false
	}
	if fileCache, ok := cm.cache.(*FileCache); ok {
		return fileCache.getSiteRuleTTL(key, siteRules)
	}
	return 0, false
}

// GetCleanupStats 获取清理器统计信息
func (cm *CacheManager) GetCleanupStats() map[string]interface{} {
	if !cm.config.Enabled {
		return map[string]interface{}{
			"enabled": false,
		}
	}

	if fileCache, ok := cm.cache.(*FileCache); ok && fileCache.cleaner != nil {
		stats := fileCache.cleaner.GetStats()
		stats["enabled"] = true
		stats["async_cleanup"] = cm.config.EnableAsyncCleanup
		return stats
	}

	return map[string]interface{}{
		"enabled":       true,
		"async_cleanup": false,
		"mode":          "sync",
	}
}

// GetCacheStats 获取缓存系统统计信息
func (cm *CacheManager) GetCacheStats() map[string]interface{} {
	stats := map[string]interface{}{
		"enabled": cm.config.Enabled,
		"type":    cm.config.Type,
	}

	if !cm.config.Enabled {
		return stats
	}

	if fileCache, ok := cm.cache.(*FileCache); ok {
		stats["total_size"] = atomic.LoadInt64(&fileCache.currentSize)
		stats["max_size"] = fileCache.maxSize

		// 如果有清理器，获取文件数量
		if fileCache.cleaner != nil {
			cleanerStats := fileCache.cleaner.GetStats()
			if indexedFiles, ok := cleanerStats["indexed_files"].(int); ok {
				stats["file_count"] = int64(indexedFiles)
			}
		} else {
			// 没有清理器时，通过扫描目录获取文件数量
			stats["file_count"] = fileCache.getFileCount()
		}
	}

	return stats
}

// getFileCount 获取缓存文件数量（用于没有清理器的情况）
func (c *FileCache) getFileCount() int64 {
	var count int64
	filepath.Walk(c.path, func(path string, info os.FileInfo, err error) error {
		if err == nil && !info.IsDir() {
			count++
		}
		return nil
	})
	return count
}

// Close 关闭缓存管理器
func (cm *CacheManager) Close() error {
	if cm.cache != nil {
		return cm.cache.Close()
	}
	return nil
}

// ShouldSkipCache 检查是否应该跳过缓存
func (cm *CacheManager) ShouldSkipCache(r *http.Request, siteRules []config.CacheRule) bool {
	if !cm.config.Enabled {
		return false
	}
	if fileCache, ok := cm.cache.(*FileCache); ok {
		return fileCache.ShouldSkipCache(r, siteRules)
	}
	return false
}

// FindMatchingCacheRule 查找匹配的缓存规则
func (cm *CacheManager) FindMatchingCacheRule(r *http.Request, siteRules []config.CacheRule) *config.CacheRule {
	if !cm.config.Enabled {
		return nil
	}

	fileCache, ok := cm.cache.(*FileCache)
	if !ok {
		return nil
	}

	logger.Debugf("[规则匹配] 开始匹配规则: url=%s, 站点规则数量=%d, 全局规则数量=%d", r.URL.Path, len(siteRules), len(cm.config.Rules))

	// 首先检查站点级规则
	for i, rule := range siteRules {
		logger.Debugf("[规则匹配] 检查站点规则 %d: pattern=%s, enabled=%v", i+1, rule.Pattern, rule.Enabled)
		if rule.Enabled && fileCache.ShouldCacheWithRule(r, rule) {
			logger.Debugf("[规则匹配] 站点规则匹配成功: pattern=%s, ttl=%v", rule.Pattern, rule.TTL)
			return &rule
		}
	}

	// 然后检查全局规则
	for i, rule := range cm.config.Rules {
		logger.Debugf("[规则匹配] 检查全局规则 %d: pattern=%s, enabled=%v", i+1, rule.Pattern, rule.Enabled)
		if rule.Enabled && fileCache.ShouldCacheWithRule(r, rule) {
			logger.Debugf("[规则匹配] 全局规则匹配成功: pattern=%s, ttl=%v", rule.Pattern, rule.TTL)
			return &rule
		}
	}

	logger.Debugf("[规则匹配] 没有找到匹配的规则")
	return nil
}

// GetCacheTTLByRule 根据匹配的规则获取缓存TTL
func (cm *CacheManager) GetCacheTTLByRule(r *http.Request, siteRules []config.CacheRule, statusCode int) time.Duration {
	// 查找匹配的规则
	rule := cm.FindMatchingCacheRule(r, siteRules)
	if rule != nil && rule.TTL > 0 {
		logger.Debugf("[缓存TTL] 匹配到规则: pattern=%s, ttl=%v, url=%s", rule.Pattern, rule.TTL, r.URL.Path)
		return rule.TTL
	}

	logger.Debugf("[缓存TTL] 没有匹配的规则，使用状态码TTL: url=%s, 状态码=%d", r.URL.Path, statusCode)

	// 使用默认的状态码TTL
	statusCodeStr := fmt.Sprintf("%d", statusCode)
	if statusTTL, exists := cm.config.StatusTTL[statusCodeStr]; exists {
		logger.Debugf("[缓存TTL] 使用状态码TTL: %s=%v", statusCodeStr, statusTTL)
		return statusTTL
	}

	if defaultTTL, exists := cm.config.StatusTTL["default"]; exists {
		logger.Debugf("[缓存TTL] 使用默认状态码TTL: %v", defaultTTL)
		return defaultTTL
	}

	logger.Debugf("[缓存TTL] 使用全局默认TTL: %v", cm.config.TTL)
	return cm.config.TTL
}

// GetDefaultTTL 获取默认的缓存TTL
func (cm *CacheManager) GetDefaultTTL() time.Duration {
	if !cm.config.Enabled {
		return 0
	}
	return cm.config.TTL
}

// UpdateSiteNameMapping 更新站点名称映射
func (cm *CacheManager) UpdateSiteNameMapping(siteID, siteName string) {
	cm.mu.Lock()
	defer cm.mu.Unlock()
	cm.siteNameMapping[siteID] = siteName
	logger.Debugf("更新缓存管理器站点名称映射: %s -> %s", siteID, siteName)
}

// GetSiteName 获取站点名称
func (cm *CacheManager) GetSiteName(siteID string) string {
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	if siteName, exists := cm.siteNameMapping[siteID]; exists {
		return siteName
	}
	return siteID // 如果没有映射，返回站点ID
}

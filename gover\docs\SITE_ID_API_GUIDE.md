# 🚀 基于Site ID的站点管理API指南

## 📋 **API概述**

新的站点管理API基于 `site_id` 设计，提供了完整的CRUD操作，支持站点的创建、读取、更新和删除。

### **API端点**
```
GET    /api/sites/           # 获取所有站点列表
GET    /api/sites/{site_id}  # 获取指定站点配置
POST   /api/sites/           # 创建新站点
PUT    /api/sites/{site_id}  # 更新指定站点
DELETE /api/sites/{site_id}  # 删除指定站点
```

### **认证要求**
- **API Key**: 通过 `X-API-Key` 头部或 `api_key` 查询参数
- **ACL检查**: 基于客户端IP的访问控制

## 🔧 **API使用示例**

### **1. 获取站点列表**
```bash
curl -X GET "http://localhost:8080/api/sites/" \
  -H "X-API-Key: config-api-key-2024"
```

**响应示例**:
```json
{
  "success": true,
  "message": "获取站点列表成功",
  "data": {
    "sites": [
      {
        "site_id": "qiank-main-site",
        "name": "qiank",
        "domains": ["qiank.com", "www.qiank.com"],
        "enabled": true
      },
      {
        "site_id": "tingtao-main-site",
        "name": "tingtao",
        "domains": ["tingtao.org", "www.tingtao.org"],
        "enabled": true
      }
    ],
    "count": 2
  }
}
```

### **2. 获取指定站点配置**
```bash
curl -X GET "http://localhost:8080/api/sites/qiank-main-site" \
  -H "X-API-Key: config-api-key-2024"
```

**响应示例**:
```json
{
  "success": true,
  "message": "获取站点信息成功",
  "data": {
    "site_id": "qiank-main-site",
    "name": "qiank",
    "domains": ["qiank.com", "www.qiank.com"],
    "upstreams": [
      {
        "name": "backend1",
        "address": "*************",
        "port": 8080,
        "weight": 1
      }
    ],
    "routes": [
      {
        "pattern": "^/",
        "upstream": "backend_group"
      }
    ],
    "acl": {
      "allow": ["***********/24"],
      "deny": []
    }
  }
}
```

### **3. 创建新站点**
```bash
curl -X POST "http://localhost:8080/api/sites/" \
  -H "X-API-Key: config-api-key-2024" \
  -H "Content-Type: application/json" \
  -d '{
    "site_id": "new-test-site",
    "name": "新测试站点",
    "domains": ["test.example.com"],
    "upstreams": [
      {
        "name": "test_backend",
        "address": "*************",
        "port": 8080,
        "protocol": "http",
        "weight": 1,
        "load_balance_group": "test_group"
      }
    ],
    "routes": [
      {
        "pattern": "^/",
        "upstream": "test_group"
      }
    ],
    "acl": {
      "allow": ["***********/24"],
      "deny": []
    },
    "max_connections": 1000
  }'
```

**响应示例**:
```json
{
  "success": true,
  "message": "站点创建成功: new-test-site",
  "data": {
    "site_id": "new-test-site",
    "name": "新测试站点",
    "domains": ["test.example.com"],
    ...
  }
}
```

### **4. 更新站点配置**
```bash
curl -X PUT "http://localhost:8080/api/sites/new-test-site" \
  -H "X-API-Key: config-api-key-2024" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "更新后的测试站点",
    "domains": ["test.example.com", "test2.example.com"],
    "max_connections": 2000
  }'
```

**响应示例**:
```json
{
  "success": true,
  "message": "站点更新成功: new-test-site",
  "data": {
    "site_id": "new-test-site",
    "name": "更新后的测试站点",
    "domains": ["test.example.com", "test2.example.com"],
    ...
  }
}
```

### **5. 删除站点**
```bash
curl -X DELETE "http://localhost:8080/api/sites/new-test-site" \
  -H "X-API-Key: config-api-key-2024"
```

**响应示例**:
```json
{
  "success": true,
  "message": "站点删除成功: new-test-site",
  "data": {
    "site_id": "new-test-site",
    "name": "更新后的测试站点",
    ...
  }
}
```

## 🎯 **API特性**

### **✅ 优势**
- **基于site_id**: 使用稳定的站点ID，不受名称变更影响
- **自动保存**: 所有操作自动持久化到配置文件
- **配置验证**: 创建和更新时自动验证配置正确性
- **冲突检查**: 创建时检查site_id是否已存在
- **部分更新**: 更新时支持只传递需要修改的字段

### **🔒 安全特性**
- **API Key认证**: 防止未授权访问
- **ACL控制**: 基于IP的访问控制
- **配置验证**: 防止无效配置破坏系统

### **📝 注意事项**
- `site_id` 字段在创建后不可修改
- 删除站点会立即生效，请谨慎操作
- 所有操作都会触发配置热重载
- 建议在生产环境中先备份配置文件

## 🚀 **与现有API的区别**

### **传统API (基于name)**
```bash
GET /api/sites?name=qiank
PUT /api/sites?name=qiank
```

### **新API (基于site_id)**
```bash
GET /api/sites/qiank-main-site
PUT /api/sites/qiank-main-site
```

### **优势对比**
| 特性 | 传统API | 新API |
|------|---------|-------|
| 标识符稳定性 | ❌ 名称可变 | ✅ ID不变 |
| 数据连续性 | ❌ 重命名丢失 | ✅ 保持连续 |
| URL友好性 | ✅ 简洁 | ✅ RESTful |
| 监控一致性 | ❌ 不一致 | ✅ 完全一致 |

这个新的API为站点管理提供了更稳定、更可靠的解决方案！🎉

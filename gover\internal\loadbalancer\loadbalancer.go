package loadbalancer

import (
	"fmt"
	"math/rand"
	"sync"
	"sync/atomic"
	"time"

	"reverse-proxy/internal/config"
)

// HealthChecker 健康检查器接口
type HealthChecker interface {
	IsHealthy(address string, port int) bool
}

// LoadBalancer 负载均衡器接口
type LoadBalancer interface {
	Next() (*config.UpstreamConfig, error)
	GetStats() map[string]interface{}
	// 移除健康状态管理方法，由监控器统一管理
}

// BaseLoadBalancer 基础负载均衡器
type BaseLoadBalancer struct {
	upstreams     []*UpstreamServer
	mutex         sync.RWMutex
	healthChecker HealthChecker // 健康检查器
}

// UpstreamServer 上游服务器
type UpstreamServer struct {
	config *config.UpstreamConfig
	weight int
	backup bool
	// 移除健康状态管理字段，由监控器统一管理
}

// BackendPool 全局后端池
var backendPool = NewBackendPool()

type BackendKey struct {
	Protocol string
	Address  string
	Port     int
}

func (k Backend<PERSON>ey) String() string {
	return fmt.Sprintf("%s://%s:%d", k.Protocol, k.Address, k.Port)
}

type Backend struct {
	Config  *config.UpstreamConfig
	Fails   int32
	Healthy bool
	Mutex   sync.RWMutex
	// 可扩展：健康检查定时器等
}

type BackendPool struct {
	pool map[string]*Backend
	mu   sync.RWMutex
}

func NewBackendPool() *BackendPool {
	return &BackendPool{pool: make(map[string]*Backend)}
}

func (bp *BackendPool) GetOrCreate(cfg *config.UpstreamConfig) *Backend {
	// 使用address+port+https_port生成唯一标识
	key := fmt.Sprintf("%s:%d:%d", cfg.Address, cfg.Port, cfg.HTTPSPort)
	bp.mu.RLock()
	backend, ok := bp.pool[key]
	bp.mu.RUnlock()
	if ok {
		return backend
	}
	bp.mu.Lock()
	defer bp.mu.Unlock()
	// 双重检查
	if backend, ok := bp.pool[key]; ok {
		return backend
	}
	backend = &Backend{Config: cfg, Healthy: true}
	bp.pool[key] = backend
	return backend
}

// GetUpstreamProtocol 获取上游协议
func GetUpstreamProtocol(cfg *config.UpstreamConfig) string {
	// 只区分http/https/auto/passthrough三种
	if cfg.Protocol == "https" {
		return "https"
	}
	if cfg.Protocol == "http" {
		return "http"
	}
	// passthrough/auto时，优先https端口
	return "auto"
}

// GetUpstreamPort 获取上游端口
func GetUpstreamPort(cfg *config.UpstreamConfig) int {
	if cfg.Protocol == "https" {
		return cfg.HTTPSPort
	}
	if cfg.Protocol == "http" {
		return cfg.Port
	}
	// passthrough/auto时，优先https端口
	if cfg.HTTPSPort > 0 {
		return cfg.HTTPSPort
	}
	return cfg.Port
}

// NewUpstreamServer 创建上游服务器
func NewUpstreamServer(cfg *config.UpstreamConfig) *UpstreamServer {
	return &UpstreamServer{
		config: cfg,
		weight: cfg.Weight,
		backup: cfg.Backup,
		// 移除健康状态字段，由监控器统一管理
	}
}

// RoundRobinLoadBalancer 轮询负载均衡器
type RoundRobinLoadBalancer struct {
	BaseLoadBalancer
	current int32
}

// NewRoundRobinLoadBalancer 创建轮询负载均衡器
func NewRoundRobinLoadBalancer(upstreams []*config.UpstreamConfig, healthChecker HealthChecker) *RoundRobinLoadBalancer {
	lb := &RoundRobinLoadBalancer{}
	lb.healthChecker = healthChecker

	for _, cfg := range upstreams {
		uc := cfg // 修复循环变量陷阱
		server := NewUpstreamServer(uc)
		lb.upstreams = append(lb.upstreams, server)
	}

	return lb
}

// Next 获取下一个服务器
func (lb *RoundRobinLoadBalancer) Next() (*config.UpstreamConfig, error) {
	lb.mutex.RLock()
	defer lb.mutex.RUnlock()

	if len(lb.upstreams) == 0 {
		return nil, fmt.Errorf("没有可用的上游服务器")
	}

	// 找到下一个健康的服务器
	for i := 0; i < len(lb.upstreams); i++ {
		current := int(atomic.AddInt32(&lb.current, 1)) % len(lb.upstreams)
		server := lb.upstreams[current]

		// 通过健康检查器查询健康状态
		if lb.isServerHealthy(server) && !server.backup {
			return server.config, nil
		}
	}

	// 如果没有主服务器，尝试备用服务器
	for _, server := range lb.upstreams {
		if lb.isServerHealthy(server) && server.backup {
			return server.config, nil
		}
	}

	return nil, fmt.Errorf("没有可用的上游服务器")
}

// isServerHealthy 检查服务器是否健康
func (lb *RoundRobinLoadBalancer) isServerHealthy(server *UpstreamServer) bool {
	if lb.healthChecker == nil {
		// 如果没有健康检查器，默认认为健康
		return true
	}
	return lb.healthChecker.IsHealthy(server.config.Address, server.config.Port)
}

// 移除MarkFailed和MarkSuccess方法，健康状态由监控器统一管理

// GetStats 获取统计信息
func (lb *RoundRobinLoadBalancer) GetStats() map[string]interface{} {
	lb.mutex.RLock()
	defer lb.mutex.RUnlock()

	stats := make(map[string]interface{})
	stats["type"] = "round_robin"
	stats["current"] = lb.current

	var servers []map[string]interface{}
	for _, server := range lb.upstreams {
		serverStats := map[string]interface{}{
			"address": server.config.Address,
			"port":    server.config.Port,
			"weight":  server.weight,
			"backup":  server.backup,
			"healthy": lb.isServerHealthy(server), // 通过健康检查器获取状态
		}
		servers = append(servers, serverStats)
	}
	stats["servers"] = servers

	return stats
}

// WeightedLoadBalancer 权重负载均衡器
type WeightedLoadBalancer struct {
	BaseLoadBalancer
	totalWeight int
}

// NewWeightedLoadBalancer 创建权重负载均衡器
func NewWeightedLoadBalancer(upstreams []*config.UpstreamConfig, healthChecker HealthChecker) *WeightedLoadBalancer {
	lb := &WeightedLoadBalancer{}
	lb.healthChecker = healthChecker

	for _, cfg := range upstreams {
		uc := cfg // 修复循环变量陷阱
		server := NewUpstreamServer(uc)
		lb.upstreams = append(lb.upstreams, server)
		lb.totalWeight += server.weight
	}

	return lb
}

// Next 获取下一个服务器
func (lb *WeightedLoadBalancer) Next() (*config.UpstreamConfig, error) {
	lb.mutex.RLock()
	defer lb.mutex.RUnlock()

	if len(lb.upstreams) == 0 {
		return nil, fmt.Errorf("没有可用的上游服务器")
	}

	// 计算可用服务器的总权重
	availableWeight := 0
	for _, server := range lb.upstreams {
		if lb.isServerHealthy(server) && !server.backup {
			availableWeight += server.weight
		}
	}

	if availableWeight == 0 {
		// 尝试备用服务器
		for _, server := range lb.upstreams {
			if lb.isServerHealthy(server) && server.backup {
				return server.config, nil
			}
		}
		return nil, fmt.Errorf("没有可用的上游服务器")
	}

	// 根据权重随机选择
	rand.Seed(time.Now().UnixNano())
	r := rand.Intn(availableWeight)

	currentWeight := 0
	for _, server := range lb.upstreams {
		if lb.isServerHealthy(server) && !server.backup {
			currentWeight += server.weight
			if r < currentWeight {
				return server.config, nil
			}
		}
	}

	return nil, fmt.Errorf("权重计算错误")
}

// isServerHealthy 检查服务器是否健康
func (lb *WeightedLoadBalancer) isServerHealthy(server *UpstreamServer) bool {
	if lb.healthChecker == nil {
		return true
	}
	return lb.healthChecker.IsHealthy(server.config.Address, server.config.Port)
}

// 移除MarkFailed和MarkSuccess方法，健康状态由监控器统一管理

// GetStats 获取统计信息
func (lb *WeightedLoadBalancer) GetStats() map[string]interface{} {
	lb.mutex.RLock()
	defer lb.mutex.RUnlock()

	stats := make(map[string]interface{})
	stats["type"] = "weighted"
	stats["total_weight"] = lb.totalWeight

	var servers []map[string]interface{}
	for _, server := range lb.upstreams {
		serverStats := map[string]interface{}{
			"address": server.config.Address,
			"port":    server.config.Port,
			"weight":  server.weight,
			"backup":  server.backup,
			"healthy": lb.isServerHealthy(server), // 通过健康检查器获取状态
		}
		servers = append(servers, serverStats)
	}
	stats["servers"] = servers

	return stats
}

// LeastConnectionsLoadBalancer 最少连接负载均衡器
type LeastConnectionsLoadBalancer struct {
	BaseLoadBalancer
	connections map[*config.UpstreamConfig]int32
}

// NewLeastConnectionsLoadBalancer 创建最少连接负载均衡器
func NewLeastConnectionsLoadBalancer(upstreams []*config.UpstreamConfig, healthChecker HealthChecker) *LeastConnectionsLoadBalancer {
	lb := &LeastConnectionsLoadBalancer{
		connections: make(map[*config.UpstreamConfig]int32),
	}
	lb.healthChecker = healthChecker

	for _, cfg := range upstreams {
		uc := cfg // 修复循环变量陷阱
		server := NewUpstreamServer(uc)
		lb.upstreams = append(lb.upstreams, server)
		lb.connections[uc] = 0
	}

	return lb
}

// Next 获取下一个服务器
func (lb *LeastConnectionsLoadBalancer) Next() (*config.UpstreamConfig, error) {
	lb.mutex.Lock()
	defer lb.mutex.Unlock()

	if len(lb.upstreams) == 0 {
		return nil, fmt.Errorf("没有可用的上游服务器")
	}

	var selectedServer *UpstreamServer
	var minConnections int32 = 1<<31 - 1

	// 找到连接数最少的健康服务器
	for _, server := range lb.upstreams {
		if lb.isServerHealthy(server) && !server.backup {
			conns := lb.connections[server.config]
			if conns < minConnections {
				minConnections = conns
				selectedServer = server
			}
		}
	}

	if selectedServer == nil {
		// 尝试备用服务器
		for _, server := range lb.upstreams {
			if lb.isServerHealthy(server) && server.backup {
				conns := lb.connections[server.config]
				if conns < minConnections {
					minConnections = conns
					selectedServer = server
				}
			}
		}
	}

	if selectedServer == nil {
		return nil, fmt.Errorf("没有可用的上游服务器")
	}

	// 增加连接数
	lb.connections[selectedServer.config]++

	return selectedServer.config, nil
}

// isServerHealthy 检查服务器是否健康
func (lb *LeastConnectionsLoadBalancer) isServerHealthy(server *UpstreamServer) bool {
	if lb.healthChecker == nil {
		return true
	}
	return lb.healthChecker.IsHealthy(server.config.Address, server.config.Port)
}

// 移除MarkFailed和MarkSuccess方法，健康状态由监控器统一管理

// ReleaseConnection 释放连接
func (lb *LeastConnectionsLoadBalancer) ReleaseConnection(upstream *config.UpstreamConfig) {
	lb.mutex.Lock()
	defer lb.mutex.Unlock()

	if conns, exists := lb.connections[upstream]; exists && conns > 0 {
		lb.connections[upstream] = conns - 1
	}
}

// GetStats 获取统计信息
func (lb *LeastConnectionsLoadBalancer) GetStats() map[string]interface{} {
	lb.mutex.RLock()
	defer lb.mutex.RUnlock()

	stats := make(map[string]interface{})
	stats["type"] = "least_connections"

	var servers []map[string]interface{}
	for _, server := range lb.upstreams {
		serverStats := map[string]interface{}{
			"address":     server.config.Address,
			"port":        server.config.Port,
			"weight":      server.weight,
			"backup":      server.backup,
			"healthy":     lb.isServerHealthy(server), // 通过健康检查器获取状态
			"connections": lb.connections[server.config],
		}
		servers = append(servers, serverStats)
	}
	stats["servers"] = servers

	return stats
}

// LoadBalancerFactory 负载均衡器工厂
func NewLoadBalancer(strategy string, upstreams []*config.UpstreamConfig, healthChecker HealthChecker) (LoadBalancer, error) {
	switch strategy {
	case "round_robin":
		return NewRoundRobinLoadBalancer(upstreams, healthChecker), nil
	case "weighted":
		return NewWeightedLoadBalancer(upstreams, healthChecker), nil
	case "least_connections":
		return NewLeastConnectionsLoadBalancer(upstreams, healthChecker), nil
	case "smooth_weighted":
		return NewAdvancedLoadBalancer("smooth_weighted", upstreams, config.LoadBalancerConfig{})
	case "consistent_hash":
		return NewConsistentHashBalancer(upstreams), nil
	case "response_time":
		return NewAdvancedLoadBalancer("response_time", upstreams, config.LoadBalancerConfig{})
	default:
		return nil, fmt.Errorf("不支持的负载均衡策略: %s", strategy)
	}
}

// NewAdvancedLoadBalancer 创建高级负载均衡器
func NewAdvancedLoadBalancer(algorithm string, upstreams []*config.UpstreamConfig, cfg config.LoadBalancerConfig) (LoadBalancer, error) {
	switch algorithm {
	case "smooth_weighted":
		return &AdvancedLoadBalancer{
			algorithm: NewSmoothWeightedRoundRobin(upstreams),
			config:    cfg,
		}, nil
	case "consistent_hash":
		virtualNodes := cfg.VirtualNodes
		if virtualNodes <= 0 {
			virtualNodes = 100
		}
		return &AdvancedLoadBalancer{
			algorithm: NewConsistentHash(upstreams, virtualNodes),
			config:    cfg,
		}, nil
	case "response_time":
		return &AdvancedLoadBalancer{
			algorithm: NewResponseTimeWeighted(upstreams),
			config:    cfg,
		}, nil
	default:
		return nil, fmt.Errorf("不支持的高级负载均衡策略: %s", algorithm)
	}
}

// AdvancedLoadBalancer 高级负载均衡器适配器
type AdvancedLoadBalancer struct {
	algorithm interface{}
	config    config.LoadBalancerConfig
}

// Next 获取下一个服务器
func (alb *AdvancedLoadBalancer) Next() (*config.UpstreamConfig, error) {
	switch algo := alb.algorithm.(type) {
	case *SmoothWeightedRoundRobin:
		upstream := algo.Select()
		if upstream == nil {
			return nil, fmt.Errorf("没有可用的上游服务器")
		}
		return upstream, nil
	case *ConsistentHash:
		// 一致性哈希需要key，这里使用默认key
		upstream := algo.Select("default")
		if upstream == nil {
			return nil, fmt.Errorf("没有可用的上游服务器")
		}
		return upstream, nil
	case *ResponseTimeWeighted:
		upstream := algo.Select()
		if upstream == nil {
			return nil, fmt.Errorf("没有可用的上游服务器")
		}
		return upstream, nil
	default:
		return nil, fmt.Errorf("未知的负载均衡算法类型")
	}
}

// NextWithKey 根据key获取下一个服务器（用于一致性哈希）
func (alb *AdvancedLoadBalancer) NextWithKey(key string) (*config.UpstreamConfig, error) {
	switch algo := alb.algorithm.(type) {
	case *ConsistentHash:
		upstream := algo.Select(key)
		if upstream == nil {
			return nil, fmt.Errorf("没有可用的上游服务器")
		}
		return upstream, nil
	default:
		// 其他算法忽略key
		return alb.Next()
	}
}

// MarkFailed 标记失败
func (alb *AdvancedLoadBalancer) MarkFailed(upstream *config.UpstreamConfig) {
	switch algo := alb.algorithm.(type) {
	case *SmoothWeightedRoundRobin:
		algo.UpdateHealth(upstream, false)
	case *ConsistentHash:
		upstream.Healthy = false
		algo.UpdateHealth(upstream, false)
	case *ResponseTimeWeighted:
		upstream.Healthy = false
	}
}

// MarkSuccess 标记成功
func (alb *AdvancedLoadBalancer) MarkSuccess(upstream *config.UpstreamConfig) {
	switch algo := alb.algorithm.(type) {
	case *SmoothWeightedRoundRobin:
		algo.UpdateHealth(upstream, true)
	case *ConsistentHash:
		upstream.Healthy = true
		algo.UpdateHealth(upstream, true)
	case *ResponseTimeWeighted:
		upstream.Healthy = true
	}
}

// UpdateResponseTime 更新响应时间（用于响应时间加权算法）
func (alb *AdvancedLoadBalancer) UpdateResponseTime(upstream *config.UpstreamConfig, responseTime time.Duration) {
	if rtw, ok := alb.algorithm.(*ResponseTimeWeighted); ok {
		rtw.UpdateResponseTime(upstream, responseTime)
	}
}

// GetStats 获取统计信息
func (alb *AdvancedLoadBalancer) GetStats() map[string]interface{} {
	stats := map[string]interface{}{
		"algorithm": alb.config.Algorithm,
	}

	switch algo := alb.algorithm.(type) {
	case *ResponseTimeWeighted:
		stats["response_time_stats"] = algo.GetStats()
	case *ConsistentHash:
		stats["virtual_nodes"] = alb.config.VirtualNodes
		stats["hash_key"] = alb.config.HashKey
	}

	return stats
}

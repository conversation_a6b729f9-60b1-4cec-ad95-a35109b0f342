用go语言做一个web反向代理服务器，要求：
1，支持多站点，多后端，主备切换、轮询、负载平衡、权重等，支持http/https/https+http各种形式站点并存，支持http1.1/2.0/3.0，http+https并存的站点，需要可以根据协议路由到不同的上游服务器
2，支持文件型缓存，这部分要做合理封装，以后可能扩展其他存储形式。支持根据文件类型、mime类型、url匹配等进行缓存规则设置。缓存的上限等可以设置化。
3，支持根据不同的uri设置后端地址
4，支持访问日志记录到文件、syslog服务器，格式可设置，像nginx那样可以预定义几个格式，每个站点分别选用对应的格式
5，支持全局和站点基于ip的acl，可以兼容ip和cidr格式，兼容ipv4和v6，可以指定acl列表文件，并且文件改变时动态重载列表
6，配置格式为json，要求各部分的性能指标可配置化
7，灵活的http头设置，包括忽略部分请求头、响应头，以及与上游之间的头部设置

大概来说，就是需要nginx的反向代理能力

